#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
多维模式识别模块 - Multi-Dimensional Pattern Recognition

本模块实现了数字生命体的多维模式识别能力，能够从复杂数据中识别各种模式和关联性。
作为增强预测分析系统的核心组件，提供更精确的模式检测和预测能力。

作者: Claude
创建日期: 2024-10-05
版本: 2.0
"""

import os
import time
import json
from utilities.unified_logger import get_unified_logger, setup_unified_logging
import numpy as np
import scipy.stats as stats
from typing import Dict, List, Any, Tuple, Optional, Set, Union
from datetime import datetime, timedelta
from collections import defaultdict, Counter

# 配置日志
setup_unified_logging()
logger = get_unified_logger("multi_dimensional_pattern_recognition")

class MultiDimensionalPatternRecognition:
    """多维模式识别类，提供增强的模式分析和预测能力"""
    
    def __init__(self, config: Dict[str, Any] = None):
        """
        初始化多维模式识别模块
        
        Args:
            config: 配置信息
        """
        self.config = config or {}
        
        # 数据存储目录
        self.data_dir = self.config.get("data_dir", "data/pattern_recognition")
        os.makedirs(self.data_dir, exist_ok=True)
        
        # 模式检测器注册表
        self.pattern_detectors = {
            # 时间维度模式
            "temporal_correlation": self._detect_temporal_correlation,
            "cyclical_pattern": self._detect_cyclical_pattern,
            "temporal_anomaly": self._detect_temporal_anomaly,
            "seasonal_pattern": self._detect_seasonal_pattern,
            
            # 类别维度模式
            "categorical_clustering": self._detect_categorical_clustering,
            "category_transition": self._detect_category_transition_pattern,
            "category_dominance": self._detect_category_dominance,
            
            # 空间维度模式
            "spatial_clustering": self._detect_spatial_clustering,
            "spatial_dispersion": self._detect_spatial_dispersion,
            "spatial_migration": self._detect_spatial_migration,
            
            # 关联维度模式
            "correlation_network": self._detect_correlation_network,
            "causal_chain": self._detect_causal_chain,
            "mutual_information": self._detect_mutual_information,
            
            # 复合维度模式
            "spatiotemporal_pattern": self._detect_spatiotemporal_pattern,
            "multifactor_confluence": self._detect_multifactor_confluence,
            "dimensional_resonance": self._detect_dimensional_resonance
        }
        
        # 结果缓存
        self.pattern_cache = {}
        self.analysis_history = {}
        
        # 阈值配置
        self.confidence_threshold = self.config.get("confidence_threshold", 0.65)
        self.similarity_threshold = self.config.get("similarity_threshold", 0.7)
        
        logger.success("多维模式识别模块初始化完成")
    
    def analyze_patterns(self, data: Dict[str, Any], dimensions: List[str] = None) -> Dict[str, Any]:
        """
        分析数据中的多维模式
        
        Args:
            data: 多维数据字典，包含各种维度的数据
            dimensions: 要分析的特定维度列表，默认分析所有维度
            
        Returns:
            多维模式分析结果
        """
        if not data:
            return {"patterns": {}, "confidence": 0.1, "dimensions_analyzed": []}
        
        # 确定要分析的维度
        available_dimensions = self._extract_dimensions(data)
        if dimensions:
            dimensions_to_analyze = [d for d in dimensions if d in available_dimensions]
        else:
            dimensions_to_analyze = available_dimensions
            
        if not dimensions_to_analyze:
            return {"patterns": {}, "confidence": 0.1, "dimensions_analyzed": []}
        
        # 对每个检测器运行模式检测
        patterns = {}
        for pattern_name, detector_func in self.pattern_detectors.items():
            # 检查该检测器适用的维度是否在分析范围内
            if self._is_detector_applicable(pattern_name, dimensions_to_analyze):
                pattern_result = detector_func(data, dimensions_to_analyze)
                if pattern_result.get("confidence", 0) >= self.confidence_threshold:
                    patterns[pattern_name] = pattern_result
        
        # 计算综合置信度
        overall_confidence = self._calculate_overall_confidence(patterns)
        
        # 整合结果
        result = {
            "timestamp": time.time(),
            "patterns": patterns,
            "confidence": overall_confidence,
            "dimensions_analyzed": dimensions_to_analyze,
            "data_points": self._count_data_points(data)
        }
        
        # 更新分析历史
        self._update_analysis_history(result)
        
        return result
    
    def find_similar_patterns(self, pattern_data: Dict[str, Any], 
                             similarity_threshold: float = None) -> List[Dict[str, Any]]:
        """
        查找与给定模式相似的历史模式
        
        Args:
            pattern_data: 待比较的模式数据
            similarity_threshold: 相似度阈值，默认使用模块配置值
            
        Returns:
            相似模式列表
        """
        if not pattern_data or not self.analysis_history:
            return []
        
        threshold = similarity_threshold or self.similarity_threshold
        
        similar_patterns = []
        for history_item in self.analysis_history.values():
            similarity = self._calculate_pattern_similarity(pattern_data, history_item)
            if similarity >= threshold:
                similar_item = history_item.copy()
                similar_item["similarity"] = similarity
                similar_patterns.append(similar_item)
        
        # 按相似度排序
        return sorted(similar_patterns, key=lambda x: x["similarity"], reverse=True)
    
    def _extract_dimensions(self, data: Dict[str, Any]) -> List[str]:
        """从数据中提取可用维度"""
        dimensions = []
        
        # 时间维度检测
        if "timestamps" in data or "time_series" in data or "dates" in data:
            dimensions.append("temporal")
        
        # 类别维度检测
        if "categories" in data or "labels" in data or "types" in data:
            dimensions.append("categorical")
        
        # 空间维度检测
        if "coordinates" in data or "locations" in data or "spatial" in data:
            dimensions.append("spatial")
        
        # 数值维度检测
        if "values" in data or "measurements" in data or "metrics" in data:
            dimensions.append("numerical")
        
        # 关系维度检测
        if "connections" in data or "relations" in data or "links" in data:
            dimensions.append("relational")
        
        return dimensions
    
    def _is_detector_applicable(self, detector_name: str, dimensions: List[str]) -> bool:
        """检查检测器是否适用于给定维度"""
        # 特定检测器的维度要求映射
        detector_dimensions = {
            "temporal_correlation": ["temporal"],
            "cyclical_pattern": ["temporal"],
            "temporal_anomaly": ["temporal"],
            "seasonal_pattern": ["temporal"],
            
            "categorical_clustering": ["categorical"],
            "category_transition": ["categorical", "temporal"],
            "category_dominance": ["categorical"],
            
            "spatial_clustering": ["spatial"],
            "spatial_dispersion": ["spatial"],
            "spatial_migration": ["spatial", "temporal"],
            
            "correlation_network": ["relational"],
            "causal_chain": ["relational", "temporal"],
            "mutual_information": ["numerical", "categorical"],
            
            "spatiotemporal_pattern": ["spatial", "temporal"],
            "multifactor_confluence": ["numerical", "categorical", "temporal"],
            "dimensional_resonance": ["numerical", "temporal"]
        }
        
        # 检查检测器所需的维度是否都存在
        required_dimensions = detector_dimensions.get(detector_name, [])
        return all(dim in dimensions for dim in required_dimensions)
    
    def _calculate_overall_confidence(self, patterns: Dict[str, Dict[str, Any]]) -> float:
        """计算综合置信度"""
        if not patterns:
            return 0.1
        
        # 计算加权平均置信度
        total_weight = 0.0
        weighted_sum = 0.0
        
        for pattern_name, pattern_data in patterns.items():
            confidence = pattern_data.get("confidence", 0)
            strength = pattern_data.get("strength", 0.5)
            # 强度高的模式权重更大
            weight = 0.5 + 0.5 * strength
            
            weighted_sum += confidence * weight
            total_weight += weight
        
        return weighted_sum / total_weight if total_weight > 0 else 0.1
    
    def _count_data_points(self, data: Dict[str, Any]) -> int:
        """计算数据点数量"""
        count = 0
        
        # 尝试从不同类型的数据结构中计算点数
        for key, value in data.items():
            if isinstance(value, list):
                count += len(value)
            elif isinstance(value, dict) and all(isinstance(k, (str, int)) for k in value.keys()):
                count += len(value)
                
        return max(count, 1)  # 至少返回1
    
    def _update_analysis_history(self, result: Dict[str, Any]):
        """更新分析历史"""
        # 使用时间戳作为唯一键
        timestamp = result["timestamp"]
        self.analysis_history[timestamp] = result
        
        # 限制历史记录数量
        max_history = self.config.get("max_history", 100)
        if len(self.analysis_history) > max_history:
            # 移除最旧的记录
            oldest_key = min(self.analysis_history.keys())
            del self.analysis_history[oldest_key]
    
    def _calculate_pattern_similarity(self, pattern1: Dict[str, Any], 
                                    pattern2: Dict[str, Any]) -> float:
        """计算两个模式的相似度"""
        # 提取两个模式的关键特征
        patterns1 = set(pattern1.get("patterns", {}).keys())
        patterns2 = set(pattern2.get("patterns", {}).keys())
        
        # 计算模式类型的Jaccard相似度
        if not patterns1 or not patterns2:
            return 0.0
            
        pattern_similarity = len(patterns1.intersection(patterns2)) / len(patterns1.union(patterns2))
        
        # 计算置信度差异
        confidence1 = pattern1.get("confidence", 0)
        confidence2 = pattern2.get("confidence", 0)
        confidence_similarity = 1.0 - abs(confidence1 - confidence2)
        
        # 计算维度相似度
        dims1 = set(pattern1.get("dimensions_analyzed", []))
        dims2 = set(pattern2.get("dimensions_analyzed", []))
        dimension_similarity = len(dims1.intersection(dims2)) / len(dims1.union(dims2)) if dims1 and dims2 else 0.0
        
        # 综合相似度，权重可调整
        return 0.5 * pattern_similarity + 0.3 * confidence_similarity + 0.2 * dimension_similarity
    
    # === 时间维度模式检测器 ===
    
    def _detect_temporal_correlation(self, data: Dict[str, Any], 
                                   dimensions: List[str]) -> Dict[str, Any]:
        """检测时间相关性模式"""
        if "temporal" not in dimensions or "timestamps" not in data:
            return {"confidence": 0.0}
            
        timestamps = data.get("timestamps", [])
        if len(timestamps) < 5:
            return {"confidence": 0.0}
            
        # 分析时间间隔
        timestamps = sorted(timestamps)
        intervals = [timestamps[i+1] - timestamps[i] for i in range(len(timestamps)-1)]
        
        # 计算间隔标准差与均值比
        mean_interval = np.mean(intervals)
        if mean_interval < 0.001:  # 避免除零
            return {"confidence": 0.0}
            
        std_dev = np.std(intervals)
        cv = std_dev / mean_interval  # 变异系数
        
        # 根据变异系数评估相关性
        if cv < 0.2:
            correlation = "strong_regular"  # 强规律性
            confidence = 0.9
            strength = 0.9
        elif cv < 0.5:
            correlation = "moderate_regular"  # 中等规律性
            confidence = 0.7
            strength = 0.7
        elif cv < 0.8:
            correlation = "weak_regular"  # 弱规律性
            confidence = 0.5
            strength = 0.5
        else:
            correlation = "irregular"  # 不规律
            confidence = 0.3
            strength = 0.3
            
        return {
            "confidence": confidence,
            "strength": strength,
            "type": correlation,
            "details": {
                "mean_interval": mean_interval,
                "std_dev": std_dev,
                "cv": cv,
                "sample_size": len(timestamps)
            }
        }
    
    def _detect_cyclical_pattern(self, data: Dict[str, Any], 
                               dimensions: List[str]) -> Dict[str, Any]:
        """检测周期性模式"""
        if "temporal" not in dimensions or ("time_series" not in data and "values" not in data):
            return {"confidence": 0.0}
            
        # 获取时间序列数据
        time_series = data.get("time_series", data.get("values", []))
        if len(time_series) < 10:  # 需要足够的数据点
            return {"confidence": 0.0}
            
        # 使用自相关分析检测周期性
        max_lag = min(len(time_series) // 2, 50)  # 最大滞后量
        acf = np.zeros(max_lag)
        
        # 计算自相关函数
        series_mean = np.mean(time_series)
        series_var = np.var(time_series)
        
        if series_var < 0.0001:  # 几乎没有变化
            return {"confidence": 0.0}
            
        for lag in range(1, max_lag + 1):
            acf[lag-1] = np.corrcoef(time_series[:-lag], time_series[lag:])[0, 1]
            
        # 寻找自相关峰值
        peaks = []
        for i in range(1, max_lag - 1):
            if acf[i] > acf[i-1] and acf[i] > acf[i+1] and acf[i] > 0.3:
                peaks.append((i+1, acf[i]))
                
        if not peaks:
            return {"confidence": 0.3, "strength": 0.2, "type": "no_cycle"}
            
        # 找出最强的周期
        strongest_peak = max(peaks, key=lambda x: x[1])
        period, correlation = strongest_peak
        
        # 评估周期性强度
        if correlation > 0.7:
            cycle_type = "strong_cycle"
            confidence = 0.9
            strength = 0.9
        elif correlation > 0.5:
            cycle_type = "moderate_cycle"
            confidence = 0.7
            strength = 0.7
        else:
            cycle_type = "weak_cycle"
            confidence = 0.5
            strength = 0.5
            
        return {
            "confidence": confidence,
            "strength": strength,
            "type": cycle_type,
            "details": {
                "period": period,
                "correlation": correlation,
                "other_peaks": peaks,
                "sample_size": len(time_series)
            }
        }
    
    def _detect_temporal_anomaly(self, data: Dict[str, Any], 
                               dimensions: List[str]) -> Dict[str, Any]:
        """检测时间异常模式"""
        if "temporal" not in dimensions or ("time_series" not in data and "values" not in data):
            return {"confidence": 0.0}
            
        # 获取时间序列数据
        time_series = data.get("time_series", data.get("values", []))
        if len(time_series) < 10:  # 需要足够的数据点
            return {"confidence": 0.0}
        
        # 计算基本统计量
        mean_val = np.mean(time_series)
        std_dev = np.std(time_series)
        
        if std_dev < 0.0001:  # 几乎没有变化
            return {"confidence": 0.0}
        
        # 使用Z分数检测异常点
        z_scores = np.abs((np.array(time_series) - mean_val) / std_dev)
        anomaly_indices = np.where(z_scores > 3.0)[0]  # 3个标准差以外视为异常
        
        # 使用中位数绝对偏差(MAD)检测异常
        median_val = np.median(time_series)
        mad = np.median(np.abs(time_series - median_val))
        mad_scores = np.abs((np.array(time_series) - median_val) / (mad * 1.4826))  # 1.4826是正态分布的缩放因子
        mad_anomaly_indices = np.where(mad_scores > 3.5)[0]
        
        # 结合两种方法的结果
        combined_anomalies = set(anomaly_indices.tolist() + mad_anomaly_indices.tolist())
        
        # 计算异常密度
        anomaly_density = len(combined_anomalies) / len(time_series)
        
        # 评估异常模式
        if anomaly_density == 0:
            return {"confidence": 0.2, "type": "no_anomalies"}
            
        if anomaly_density > 0.4:
            # 太多"异常"可能意味着数据本身就是不规则的，而不是真正的异常
            anomaly_type = "highly_irregular_data"
            confidence = 0.4
            strength = 0.3
        elif anomaly_density > 0.2:
            anomaly_type = "frequent_anomalies"
            confidence = 0.7
            strength = 0.6
        elif anomaly_density > 0.05:
            anomaly_type = "moderate_anomalies"
            confidence = 0.8
            strength = 0.7
        else:
            anomaly_type = "rare_anomalies"
            confidence = 0.9
            strength = 0.8
        
        # 识别异常集群
        anomaly_clusters = []
        if len(combined_anomalies) > 1:
            sorted_indices = sorted(list(combined_anomalies))
            current_cluster = [sorted_indices[0]]
            
            for i in range(1, len(sorted_indices)):
                if sorted_indices[i] - sorted_indices[i-1] <= 2:  # 相邻或间隔1个点的异常视为集群
                    current_cluster.append(sorted_indices[i])
                else:
                    if len(current_cluster) > 1:
                        anomaly_clusters.append(current_cluster)
                    current_cluster = [sorted_indices[i]]
            
            if len(current_cluster) > 1:
                anomaly_clusters.append(current_cluster)
        
        return {
            "confidence": confidence,
            "strength": strength,
            "type": anomaly_type,
            "details": {
                "anomaly_count": len(combined_anomalies),
                "anomaly_density": anomaly_density,
                "anomaly_indices": sorted(list(combined_anomalies)),
                "anomaly_clusters": anomaly_clusters,
                "z_score_threshold": 3.0,
                "mad_threshold": 3.5,
                "sample_size": len(time_series)
            }
        }
    
    def _detect_seasonal_pattern(self, data: Dict[str, Any], 
                               dimensions: List[str]) -> Dict[str, Any]:
        """检测季节性模式"""
        if "temporal" not in dimensions or ("time_series" not in data and "values" not in data):
            return {"confidence": 0.0}
            
        # 获取时间序列数据
        time_series = data.get("time_series", data.get("values", []))
        timestamps = data.get("timestamps", [])  # 如果提供了时间戳，可以更准确地分析
        
        # 需要足够长的时间序列才能检测季节性
        if len(time_series) < 24:  # 至少需要覆盖两个可能的季节周期
            return {"confidence": 0.3, "type": "insufficient_data"}
        
        # 季节性分析方法1: 自相关分析
        # 检查固定间隔的自相关性，用于发现重复模式
        possible_periods = []
        
        # 尝试检测不同的季节周期: 天(24小时)、周(7天)、月(~30天)、季度(~90天)、年(~365天)
        candidate_periods = [24, 7*24, 30*24, 90*24, 365*24]  # 假设是小时级别的数据
        
        # 如果数据点数量少，缩小候选周期范围
        if len(time_series) < 100:
            candidate_periods = [p for p in candidate_periods if p <= len(time_series) // 3]
        
        max_lag = min(len(time_series) // 2, 365 * 2)  # 最大滞后量，限制为两年或一半数据长度
        
        # 计算自相关函数
        acf = np.zeros(max_lag)
        series_mean = np.mean(time_series)
        series_var = np.var(time_series)
        
        if series_var < 0.0001:  # 几乎没有变化
            return {"confidence": 0.2, "type": "constant_series"}
            
        for lag in range(1, max_lag + 1):
            acf[lag-1] = np.corrcoef(time_series[:-lag], time_series[lag:])[0, 1]
        
        # 检查候选周期的自相关性
        for period in candidate_periods:
            if period >= max_lag:
                continue
                
            # 如果在周期点的自相关性高，并且比周围点高，可能是季节性
            if period < len(acf) and acf[period-1] > 0.4:
                # 检查是否是局部峰值
                is_peak = True
                window = min(5, period // 4)  # 窗口大小，最小1，最大为周期的1/4
                
                for i in range(max(0, period-window), min(len(acf), period+window)):
                    if i != period-1 and acf[i] > acf[period-1]:
                        is_peak = False
                        break
                
                if is_peak:
                    possible_periods.append((period, acf[period-1]))
        
        # 季节性分析方法2: 分解法
        # 将时间序列分解为趋势、季节性和残差
        has_strong_seasonal = False
        seasonal_components = {}
        
        # 对可能的周期进行分解分析
        for period, corr in possible_periods:
            if period < 4 or period >= len(time_series) // 2:
                continue
                
            # 简单的季节性分解：将数据按周期分组并计算平均模式
            n_seasons = len(time_series) // period
            if n_seasons < 2:
                continue
                
            # 将数据重塑为(n_seasons, period)的二维数组
            shaped_data = np.array(time_series[:n_seasons*period]).reshape(n_seasons, period)
            
            # 计算每个季节点的平均值
            seasonal_pattern = np.mean(shaped_data, axis=0)
            
            # 计算季节性强度（季节变化与总变化的比例）
            seasonal_var = np.var(seasonal_pattern)
            seasonal_strength = seasonal_var / (seasonal_var + np.var(shaped_data - seasonal_pattern.reshape(1, -1)))
            
            seasonal_components[period] = {
                "pattern": seasonal_pattern.tolist(),
                "strength": seasonal_strength,
                "correlation": corr
            }
            
            if seasonal_strength > 0.5 and corr > 0.6:
                has_strong_seasonal = True
        
        # 综合评估季节性
        if not possible_periods:
            return {"confidence": 0.3, "type": "no_seasonality"}
            
        # 找出最强的季节性周期
        strongest_period = max(possible_periods, key=lambda x: x[1])
        period, correlation = strongest_period
        
        # 从季节性组件中获取强度
        if period in seasonal_components:
            seasonal_strength = seasonal_components[period]["strength"]
        else:
            seasonal_strength = correlation * 0.8  # 估计值
        
        # 评估季节性强度
        if has_strong_seasonal and seasonal_strength > 0.7:
            season_type = "strong_seasonality"
            confidence = 0.9
            strength = 0.9
        elif seasonal_strength > 0.5:
            season_type = "moderate_seasonality"
            confidence = 0.7
            strength = 0.7
        else:
            season_type = "weak_seasonality"
            confidence = 0.5
            strength = 0.5
            
        return {
            "confidence": confidence,
            "strength": strength,
            "type": season_type,
            "details": {
                "primary_period": period,
                "correlation": correlation,
                "seasonal_strength": seasonal_strength,
                "other_periods": sorted(possible_periods, key=lambda x: x[1], reverse=True),
                "seasonal_components": seasonal_components,
                "sample_size": len(time_series)
            }
        }
    
    # === 类别维度模式检测器 ===
    
    def _detect_categorical_clustering(self, data: Dict[str, Any], 
                                     dimensions: List[str]) -> Dict[str, Any]:
        """检测类别聚类模式"""
        if "categorical" not in dimensions or "categories" not in data:
            return {"confidence": 0.0}
            
        categories = data.get("categories", [])
        if len(categories) < 5:
            return {"confidence": 0.0}
            
        # 计算类别分布
        category_counts = Counter(categories)
        total_count = len(categories)
        
        # 计算分布不均匀程度
        expected_count = total_count / len(category_counts)
        chi_square = sum((count - expected_count) ** 2 / expected_count 
                        for count in category_counts.values())
        
        # 根据分布不均匀程度评估聚类强度
        # 自由度为类别数-1
        df = len(category_counts) - 1
        if df <= 0:
            return {"confidence": 0.0}
            
        # 计算p值（小p值表示分布显著不均匀）
        p_value = 1.0
        try:
            p_value = 1.0 - stats.chi2.cdf(chi_square, df)
        except:
            pass
            
        # 聚类强度与p值成反比
        if p_value < 0.01:
            cluster_type = "strong_clustering"
            confidence = 0.9
            strength = 0.9
        elif p_value < 0.05:
            cluster_type = "moderate_clustering"
            confidence = 0.7
            strength = 0.7
        elif p_value < 0.1:
            cluster_type = "weak_clustering"
            confidence = 0.5
            strength = 0.5
        else:
            cluster_type = "no_clustering"
            confidence = 0.3
            strength = 0.3
            
        return {
            "confidence": confidence,
            "strength": strength,
            "type": cluster_type,
            "details": {
                "chi_square": chi_square,
                "p_value": p_value,
                "dominant_category": max(category_counts.items(), key=lambda x: x[1])[0],
                "category_distribution": {k: v/total_count for k, v in category_counts.items()}
            }
        }
    
    def _detect_category_transition_pattern(self, data: Dict[str, Any], 
                                          dimensions: List[str]) -> Dict[str, Any]:
        """检测类别转换模式"""
        if "categorical" not in dimensions or "categorical_sequence" not in data:
            return {"confidence": 0.0}
            
        # 获取类别序列数据
        category_sequence = data.get("categorical_sequence", [])
        if len(category_sequence) < 10:  # 需要足够的转换样本
            return {"confidence": 0.3, "type": "insufficient_data"}
        
        # 计算转换矩阵：从类别A到类别B的转换次数
        unique_categories = list(set(category_sequence))
        n_categories = len(unique_categories)
        
        if n_categories < 2:
            return {"confidence": 0.2, "type": "single_category"}
        
        # 创建类别到索引的映射
        cat_to_idx = {cat: idx for idx, cat in enumerate(unique_categories)}
        
        # 初始化转换矩阵和计数
        transition_matrix = np.zeros((n_categories, n_categories))
        category_counts = np.zeros(n_categories)
        
        # 填充转换矩阵
        for i in range(len(category_sequence) - 1):
            from_idx = cat_to_idx[category_sequence[i]]
            to_idx = cat_to_idx[category_sequence[i + 1]]
            transition_matrix[from_idx, to_idx] += 1
            category_counts[from_idx] += 1
        
        # 将最后一个类别也计入总数
        category_counts[cat_to_idx[category_sequence[-1]]] += 1
        
        # 计算转换概率矩阵
        transition_probabilities = np.zeros((n_categories, n_categories))
        for i in range(n_categories):
            if category_counts[i] > 0:
                transition_probabilities[i, :] = transition_matrix[i, :] / category_counts[i]
        
        # 识别显著的转换模式
        significant_transitions = []
        expected_prob = 1.0 / n_categories  # 均匀分布的期望概率
        
        for i in range(n_categories):
            for j in range(n_categories):
                if transition_probabilities[i, j] > 2 * expected_prob:  # 显著高于随机
                    significant_transitions.append({
                        "from": unique_categories[i],
                        "to": unique_categories[j],
                        "probability": float(transition_probabilities[i, j]),
                        "count": int(transition_matrix[i, j]),
                        "significance": float(transition_probabilities[i, j] / expected_prob)
                    })
        
        # 识别转换链和循环
        chains = []
        cycles = []
        
        # 使用深度优先搜索查找链和循环
        def dfs(node, path, visited):
            path.append(node)
            visited.add(node)
            
            for next_node in range(n_categories):
                if transition_probabilities[node, next_node] > 0.3:  # 有较强的转换概率
                    if next_node in path:
                        # 找到循环
                        cycle_start = path.index(next_node)
                        cycle = path[cycle_start:] + [next_node]
                        cycle_str = "->".join([unique_categories[idx] for idx in cycle])
                        cycles.append(cycle_str)
                    elif next_node not in visited:
                        dfs(next_node, path.copy(), visited.copy())
            
            if len(path) > 2:
                chain_str = "->".join([unique_categories[idx] for idx in path])
                chains.append(chain_str)
        
        for start_node in range(n_categories):
            dfs(start_node, [], set())
        
        # 去重
        chains = list(set(chains))
        cycles = list(set(cycles))
        
        # 计算马尔可夫性质检验 - 检查是否满足一阶马尔可夫性质
        markov_property = True
        if len(category_sequence) > 20:
            # 对于足够长的序列，检查二阶条件概率是否与一阶条件概率相似
            for i in range(len(category_sequence) - 2):
                a, b, c = category_sequence[i:i+3]
                a_idx, b_idx, c_idx = cat_to_idx[a], cat_to_idx[b], cat_to_idx[c]
                
                # 计算P(C|B)和P(C|A,B)
                p_c_given_b = transition_probabilities[b_idx, c_idx]
                
                # 简单估计P(C|A,B)
                a_b_count = 0
                a_b_c_count = 0
                
                for j in range(len(category_sequence) - 2):
                    if category_sequence[j] == a and category_sequence[j+1] == b:
                        a_b_count += 1
                        if category_sequence[j+2] == c:
                            a_b_c_count += 1
                
                p_c_given_a_b = a_b_c_count / a_b_count if a_b_count > 0 else 0
                
                # 如果两个概率相差太大，可能不满足马尔可夫性质
                if abs(p_c_given_b - p_c_given_a_b) > 0.3 and a_b_count > 5:
                    markov_property = False
                    break
        
        # 评估转换模式的强度
        transition_entropy = 0
        for row in transition_probabilities:
            for p in row:
                if p > 0:
                    transition_entropy -= p * np.log2(p)
        
        # 归一化熵值（完全确定为0，完全随机为1）
        max_entropy = np.log2(n_categories)
        normalized_entropy = transition_entropy / max_entropy if max_entropy > 0 else 0
        
        # 计算确定性（1-熵）
        determinism = 1 - normalized_entropy
        
        # 根据确定性和显著转换的数量评估转换模式
        if determinism > 0.7 and len(significant_transitions) > n_categories / 2:
            pattern_type = "strong_transition_pattern"
            confidence = 0.9
            strength = 0.9
        elif determinism > 0.5 and len(significant_transitions) > 0:
            pattern_type = "moderate_transition_pattern"
            confidence = 0.7
            strength = 0.7
        elif len(significant_transitions) > 0:
            pattern_type = "weak_transition_pattern"
            confidence = 0.5
            strength = 0.5
        else:
            pattern_type = "random_transitions"
            confidence = 0.3
            strength = 0.3
        
        return {
            "confidence": confidence,
            "strength": strength,
            "type": pattern_type,
            "details": {
                "categories": unique_categories,
                "transition_matrix": transition_matrix.tolist(),
                "transition_probabilities": transition_probabilities.tolist(),
                "significant_transitions": significant_transitions,
                "determinism": determinism,
                "entropy": normalized_entropy,
                "markov_property": markov_property,
                "chains": chains[:5],  # 限制返回的链数量
                "cycles": cycles[:5],  # 限制返回的循环数量
                "sample_size": len(category_sequence)
            }
        }
    
    def _detect_category_dominance(self, data: Dict[str, Any], 
                                 dimensions: List[str]) -> Dict[str, Any]:
        """检测类别优势模式"""
        if "categorical" not in dimensions or "categories" not in data:
            return {"confidence": 0.0}
            
        # 获取类别数据和可能的条件变量
        categories = data.get("categories", [])
        condition_vars = {}
        
        # 检查可能的条件变量（如时间段、地点等）
        for var_name in ["time_periods", "locations", "segments", "conditions"]:
            if var_name in data and len(data[var_name]) == len(categories):
                condition_vars[var_name] = data[var_name]
        
        if len(categories) < 10:
            return {"confidence": 0.3, "type": "insufficient_data"}
        
        # 计算整体类别分布
        unique_categories = list(set(categories))
        n_categories = len(unique_categories)
        
        if n_categories < 2:
            return {"confidence": 0.2, "type": "single_category"}
        
        category_counts = {}
        for cat in categories:
            category_counts[cat] = category_counts.get(cat, 0) + 1
        
        total_count = len(categories)
        overall_distribution = {cat: count / total_count for cat, count in category_counts.items()}
        
        # 找出总体上的主导类别
        dominant_category = max(category_counts.items(), key=lambda x: x[1])
        dominant_ratio = dominant_category[1] / total_count
        
        # 条件主导分析结果
        conditional_dominance = {}
        significant_conditions = []
        
        # 分析每个条件变量下的类别分布
        for var_name, conditions in condition_vars.items():
            unique_conditions = list(set(conditions))
            conditional_dominance[var_name] = {}
            
            for condition in unique_conditions:
                # 获取该条件下的类别
                condition_indices = [i for i, c in enumerate(conditions) if c == condition]
                condition_categories = [categories[i] for i in condition_indices]
                
                if len(condition_categories) < 5:  # 样本太少
                    continue
                    
                # 计算条件分布
                condition_counts = {}
                for cat in condition_categories:
                    condition_counts[cat] = condition_counts.get(cat, 0) + 1
                
                condition_total = len(condition_categories)
                condition_distribution = {cat: count / condition_total 
                                         for cat, count in condition_counts.items()}
                
                # 找出条件下的主导类别
                if condition_counts:
                    condition_dominant = max(condition_counts.items(), key=lambda x: x[1])
                    condition_dominant_ratio = condition_dominant[1] / condition_total
                    
                    # 计算与总体分布的差异
                    baseline_ratio = overall_distribution.get(condition_dominant[0], 0)
                    ratio_difference = condition_dominant_ratio - baseline_ratio
                    
                    # 计算显著性（简化的卡方检验）
                    expected_count = baseline_ratio * condition_total
                    actual_count = condition_dominant[1]
                    
                    if expected_count > 0:
                        chi_square = ((actual_count - expected_count) ** 2) / expected_count
                        significance = min(1.0, chi_square / 10)  # 简化的显著性度量
                    else:
                        significance = 0.5
                    
                    conditional_dominance[var_name][condition] = {
                        "dominant_category": condition_dominant[0],
                        "dominant_ratio": condition_dominant_ratio,
                        "baseline_ratio": baseline_ratio,
                        "ratio_difference": ratio_difference,
                        "significance": significance,
                        "sample_size": condition_total
                    }
                    
                    # 记录显著的条件主导
                    if ratio_difference > 0.2 and significance > 0.6 and condition_total >= 10:
                        significant_conditions.append({
                            "variable": var_name,
                            "condition": condition,
                            "dominant_category": condition_dominant[0],
                            "dominant_ratio": condition_dominant_ratio,
                            "baseline_ratio": baseline_ratio,
                            "ratio_difference": ratio_difference,
                            "significance": significance,
                            "sample_size": condition_total
                        })
        
        # 计算条件主导模式的整体强度
        if significant_conditions:
            # 根据显著条件的数量、差异大小和显著性评估主导模式强度
            avg_diff = sum(cond["ratio_difference"] for cond in significant_conditions) / len(significant_conditions)
            avg_sig = sum(cond["significance"] for cond in significant_conditions) / len(significant_conditions)
            coverage = sum(cond["sample_size"] for cond in significant_conditions) / total_count
            
            dominance_strength = (avg_diff * 0.4 + avg_sig * 0.4 + coverage * 0.2)
            
            if dominance_strength > 0.7:
                pattern_type = "strong_conditional_dominance"
                confidence = 0.9
                strength = 0.9
            elif dominance_strength > 0.5:
                pattern_type = "moderate_conditional_dominance"
                confidence = 0.7
                strength = 0.7
            else:
                pattern_type = "weak_conditional_dominance"
                confidence = 0.5
                strength = 0.5
        else:
            # 检查总体上是否有明显的主导类别
            expected_ratio = 1.0 / n_categories  # 均匀分布的期望比例
            
            if dominant_ratio > 3 * expected_ratio:
                pattern_type = "strong_overall_dominance"
                confidence = 0.8
                strength = 0.8
            elif dominant_ratio > 2 * expected_ratio:
                pattern_type = "moderate_overall_dominance"
                confidence = 0.6
                strength = 0.6
            else:
                pattern_type = "no_clear_dominance"
                confidence = 0.3
                strength = 0.3
        
        return {
            "confidence": confidence,
            "strength": strength,
            "type": pattern_type,
            "details": {
                "overall_distribution": overall_distribution,
                "dominant_category": dominant_category[0],
                "dominant_ratio": dominant_ratio,
                "expected_ratio": 1.0 / n_categories,
                "n_categories": n_categories,
                "conditional_dominance": conditional_dominance,
                "significant_conditions": significant_conditions,
                "condition_variables": list(condition_vars.keys()),
                "sample_size": total_count
            }
        }
    
    # === 空间维度模式检测器 ===
    
    def _detect_spatial_clustering(self, data: Dict[str, Any], 
                                 dimensions: List[str]) -> Dict[str, Any]:
        """检测空间聚类模式"""
        if "spatial" not in dimensions or ("coordinates" not in data and "points" not in data):
            return {"confidence": 0.0}
            
        # 获取空间坐标数据
        coordinates = data.get("coordinates", data.get("points", []))
        if len(coordinates) < 10:  # 需要足够的数据点
            return {"confidence": 0.3, "type": "insufficient_data"}
        
        # 确保坐标是numpy数组
        points = np.array(coordinates)
        
        # 确定维度 (2D或3D)
        n_dims = points.shape[1] if len(points.shape) > 1 else 2
        if n_dims not in [2, 3]:
            return {"confidence": 0.2, "type": "unsupported_dimensions"}
        
        # 计算基本统计量
        center = np.mean(points, axis=0)
        distances_to_center = np.sqrt(np.sum((points - center) ** 2, axis=1))
        avg_distance = np.mean(distances_to_center)
        std_distance = np.std(distances_to_center)
        
        # 1. 使用DBSCAN算法检测聚类
        # 自适应确定eps参数 - 使用平均距离的一部分
        eps = avg_distance * 0.3
        min_samples = max(3, int(len(points) * 0.05))  # 至少需要3个点，或者总数的5%
        
        # 简化版的DBSCAN实现
        clusters = []
        visited = set()
        
        def region_query(point_idx, eps):
            """找出point_idx邻域内的所有点"""
            neighbors = []
            for i in range(len(points)):
                if i != point_idx:
                    distance = np.sqrt(np.sum((points[point_idx] - points[i]) ** 2))
                    if distance < eps:
                        neighbors.append(i)
            return neighbors
        
        def expand_cluster(point_idx, neighbors, cluster_id):
            """扩展聚类"""
            cluster = [point_idx]
            visited.add(point_idx)
            
            i = 0
            while i < len(neighbors):
                neighbor = neighbors[i]
                if neighbor not in visited:
                    visited.add(neighbor)
                    new_neighbors = region_query(neighbor, eps)
                    if len(new_neighbors) >= min_samples:
                        neighbors.extend([n for n in new_neighbors if n not in visited and n not in neighbors])
                    cluster.append(neighbor)
                i += 1
            
            return cluster
        
        cluster_id = 0
        for i in range(len(points)):
            if i in visited:
                continue
                
            neighbors = region_query(i, eps)
            if len(neighbors) < min_samples:
                visited.add(i)
                continue
                
            cluster = expand_cluster(i, neighbors, cluster_id)
            clusters.append(cluster)
            cluster_id += 1
        
        # 将未分配的点作为噪声点
        noise_points = [i for i in range(len(points)) if i not in visited]
        
        # 2. 计算聚类统计量
        if not clusters:
            return {"confidence": 0.4, "type": "no_clustering"}
        
        # 计算每个聚类的统计特征
        cluster_stats = []
        for i, cluster in enumerate(clusters):
            if len(cluster) < 3:
                continue
                
            cluster_points = points[cluster]
            cluster_center = np.mean(cluster_points, axis=0)
            
            # 计算聚类内点到聚类中心的平均距离
            cluster_distances = np.sqrt(np.sum((cluster_points - cluster_center) ** 2, axis=1))
            avg_cluster_distance = np.mean(cluster_distances)
            
            # 计算聚类紧密度 (越小越紧密)
            compactness = avg_cluster_distance / avg_distance
            
            # 计算聚类到全局中心的距离
            center_distance = np.sqrt(np.sum((cluster_center - center) ** 2))
            
            cluster_stats.append({
                "id": i,
                "size": len(cluster),
                "center": cluster_center.tolist(),
                "avg_distance": float(avg_cluster_distance),
                "compactness": float(compactness),
                "center_distance": float(center_distance),
                "point_indices": cluster
            })
        
        # 3. 评估聚类显著性
        # 计算点分配率 - 被分配到聚类的点的比例
        assignment_rate = (len(points) - len(noise_points)) / len(points)
        
        # 计算聚类间分离度 - 聚类中心之间的平均距离
        separation = 0.0
        if len(clusters) > 1:
            cluster_centers = np.array([stats["center"] for stats in cluster_stats])
            center_distances = []
            for i in range(len(cluster_centers)):
                for j in range(i+1, len(cluster_centers)):
                    dist = np.sqrt(np.sum((cluster_centers[i] - cluster_centers[j]) ** 2))
                    center_distances.append(dist)
            separation = np.mean(center_distances) if center_distances else 0.0
        
        # 计算平均紧密度
        avg_compactness = np.mean([stats["compactness"] for stats in cluster_stats]) if cluster_stats else 1.0
        
        # 评估聚类质量
        silhouette_score = 0.0
        if len(clusters) > 1 and assignment_rate > 0.5:
            # 简化的轮廓系数计算
            silhouette_samples = []
            
            for i, cluster_i in enumerate(clusters):
                for point_idx in cluster_i:
                    # 计算a(i) - 点到自己所在聚类其他点的平均距离
                    a_i = np.mean([np.sqrt(np.sum((points[point_idx] - points[j]) ** 2)) 
                                 for j in cluster_i if j != point_idx]) if len(cluster_i) > 1 else 0
                    
                    # 计算b(i) - 点到最近其他聚类的平均距离
                    b_i_values = []
                    for j, cluster_j in enumerate(clusters):
                        if i != j:
                            b_i_j = np.mean([np.sqrt(np.sum((points[point_idx] - points[k]) ** 2)) 
                                          for k in cluster_j])
                            b_i_values.append(b_i_j)
                    
                    b_i = min(b_i_values) if b_i_values else avg_distance
                    
                    # 计算轮廓系数 s(i)
                    if max(a_i, b_i) > 0:
                        s_i = (b_i - a_i) / max(a_i, b_i)
                        silhouette_samples.append(s_i)
            
            silhouette_score = np.mean(silhouette_samples) if silhouette_samples else 0.0
        
        # 综合评估聚类模式
        clustering_score = (
            assignment_rate * 0.3 +
            (1 - avg_compactness) * 0.3 +  # 紧密度越小越好，所以用1减
            (separation / avg_distance) * 0.2 +  # 归一化分离度
            max(0, silhouette_score) * 0.2  # 轮廓系数，越大越好
        )
        
        # 确定聚类模式类型和强度
        if clustering_score > 0.7:
            pattern_type = "strong_spatial_clustering"
            confidence = 0.9
            strength = 0.9
        elif clustering_score > 0.5:
            pattern_type = "moderate_spatial_clustering"
            confidence = 0.7
            strength = 0.7
        elif clustering_score > 0.3:
            pattern_type = "weak_spatial_clustering"
            confidence = 0.5
            strength = 0.5
        else:
            pattern_type = "random_spatial_distribution"
            confidence = 0.4
            strength = 0.3
        
        return {
            "confidence": confidence,
            "strength": strength,
            "type": pattern_type,
            "details": {
                "n_clusters": len(clusters),
                "n_points": len(points),
                "n_noise": len(noise_points),
                "assignment_rate": assignment_rate,
                "avg_compactness": avg_compactness,
                "separation": float(separation),
                "silhouette_score": float(silhouette_score),
                "clustering_score": clustering_score,
                "cluster_stats": cluster_stats,
                "dimensions": n_dims
            }
        }
    
    def _detect_spatial_dispersion(self, data: Dict[str, Any], 
                                 dimensions: List[str]) -> Dict[str, Any]:
        """检测空间分散模式"""
        if "spatial" not in dimensions or ("coordinates" not in data and "points" not in data):
            return {"confidence": 0.0}
            
        # 获取空间坐标数据
        coordinates = data.get("coordinates", data.get("points", []))
        if len(coordinates) < 10:  # 需要足够的数据点
            return {"confidence": 0.3, "type": "insufficient_data"}
        
        # 确保坐标是numpy数组
        points = np.array(coordinates)
        
        # 确定维度 (2D或3D)
        n_dims = points.shape[1] if len(points.shape) > 1 else 2
        if n_dims not in [2, 3]:
            return {"confidence": 0.2, "type": "unsupported_dimensions"}
        
        # 计算基本统计量
        center = np.mean(points, axis=0)
        
        # 计算点到中心的距离
        distances_to_center = np.sqrt(np.sum((points - center) ** 2, axis=1))
        
        # 计算散度指标
        mean_distance = np.mean(distances_to_center)
        median_distance = np.median(distances_to_center)
        std_distance = np.std(distances_to_center)
        max_distance = np.max(distances_to_center)
        
        # 计算四分位数
        q1 = np.percentile(distances_to_center, 25)
        q3 = np.percentile(distances_to_center, 75)
        iqr = q3 - q1  # 四分位距
        
        # 计算变异系数 - 标准差与均值的比值，用于评估相对离散程度
        cv = std_distance / mean_distance if mean_distance > 0 else 0
        
        # 计算最近邻距离
        nearest_distances = []
        for i in range(len(points)):
            distances = []
            for j in range(len(points)):
                if i != j:
                    distance = np.sqrt(np.sum((points[i] - points[j]) ** 2))
                    distances.append(distance)
            if distances:
                nearest_distances.append(min(distances))
                
        avg_nearest_distance = np.mean(nearest_distances) if nearest_distances else 0
        
        # 计算空间分布的均匀性
        # 使用点到最近邻居的距离变异系数，越小越均匀
        nearest_distance_std = np.std(nearest_distances) if nearest_distances else 0
        nearest_distance_mean = np.mean(nearest_distances) if nearest_distances else 0
        uniformity = 1 - (nearest_distance_std / nearest_distance_mean) if nearest_distance_mean > 0 else 0
        
        # 计算点对之间的距离分布
        # 随机抽样计算点对距离，避免在大数据集上计算量过大
        sample_size = min(len(points), 1000)
        if len(points) > sample_size:
            indices = np.random.choice(len(points), sample_size, replace=False)
            sampled_points = points[indices]
        else:
            sampled_points = points
            
        # 计算所有点对之间的距离
        pairwise_distances = []
        for i in range(len(sampled_points)):
            for j in range(i+1, len(sampled_points)):
                distance = np.sqrt(np.sum((sampled_points[i] - sampled_points[j]) ** 2))
                pairwise_distances.append(distance)
                
        pairwise_mean = np.mean(pairwise_distances) if pairwise_distances else 0
        pairwise_std = np.std(pairwise_distances) if pairwise_distances else 0
        
        # 计算点密度
        # 估计点云的体积/面积
        # 对于2D数据，使用最小外接矩形面积
        # 对于3D数据，使用最小外接长方体体积
        if n_dims == 2:
            min_x, min_y = np.min(points, axis=0)
            max_x, max_y = np.max(points, axis=0)
            area = (max_x - min_x) * (max_y - min_y)
            density = len(points) / area if area > 0 else 0
        else:  # 3D
            min_x, min_y, min_z = np.min(points, axis=0)
            max_x, max_y, max_z = np.max(points, axis=0)
            volume = (max_x - min_x) * (max_y - min_y) * (max_z - min_z)
            density = len(points) / volume if volume > 0 else 0
        
        # 计算空间占据率
        # 将空间划分为网格，计算非空网格的比例
        grid_size = 10  # 将空间划分为10x10(x10)的网格
        
        if n_dims == 2:
            min_x, min_y = np.min(points, axis=0)
            max_x, max_y = np.max(points, axis=0)
            
            # 网格大小
            cell_width = (max_x - min_x) / grid_size if max_x > min_x else 1
            cell_height = (max_y - min_y) / grid_size if max_y > min_y else 1
            
            # 初始化网格
            grid = np.zeros((grid_size, grid_size))
            
            # 标记点所在的网格
            for point in points:
                x, y = point
                grid_x = min(grid_size - 1, max(0, int((x - min_x) / cell_width)))
                grid_y = min(grid_size - 1, max(0, int((y - min_y) / cell_height)))
                grid[grid_x, grid_y] = 1
                
            # 计算占据率
            occupied_cells = np.sum(grid)
            total_cells = grid_size * grid_size
            occupancy_rate = occupied_cells / total_cells
            
        else:  # 3D
            min_x, min_y, min_z = np.min(points, axis=0)
            max_x, max_y, max_z = np.max(points, axis=0)
            
            # 网格大小
            cell_width = (max_x - min_x) / grid_size if max_x > min_x else 1
            cell_height = (max_y - min_y) / grid_size if max_y > min_y else 1
            cell_depth = (max_z - min_z) / grid_size if max_z > min_z else 1
            
            # 初始化网格
            grid = np.zeros((grid_size, grid_size, grid_size))
            
            # 标记点所在的网格
            for point in points:
                x, y, z = point
                grid_x = min(grid_size - 1, max(0, int((x - min_x) / cell_width)))
                grid_y = min(grid_size - 1, max(0, int((y - min_y) / cell_height)))
                grid_z = min(grid_size - 1, max(0, int((z - min_z) / cell_depth)))
                grid[grid_x, grid_y, grid_z] = 1
                
            # 计算占据率
            occupied_cells = np.sum(grid)
            total_cells = grid_size * grid_size * grid_size
            occupancy_rate = occupied_cells / total_cells
        
        # 确定分散模式类型
        # 根据变异系数、均匀性和占据率评估分散模式
        dispersion_score = (
            (1 - cv) * 0.3 +  # 变异系数越小，越均匀
            uniformity * 0.4 +  # 均匀性越高越好
            occupancy_rate * 0.3  # 占据率越高，分散越好
        )
        
        if dispersion_score > 0.7:
            if uniformity > 0.7:
                pattern_type = "uniform_dispersion"
            else:
                pattern_type = "strong_dispersion"
            confidence = 0.9
            strength = 0.9
        elif dispersion_score > 0.5:
            pattern_type = "moderate_dispersion"
            confidence = 0.7
            strength = 0.7
        elif dispersion_score > 0.3:
            pattern_type = "weak_dispersion"
            confidence = 0.5
            strength = 0.5
        else:
            if cv > 1.0:
                pattern_type = "clustered_distribution"
            else:
                pattern_type = "random_distribution"
            confidence = 0.4
            strength = 0.3
        
        return {
            "confidence": confidence,
            "strength": strength,
            "type": pattern_type,
            "details": {
                "n_points": len(points),
                "mean_distance": float(mean_distance),
                "median_distance": float(median_distance),
                "std_distance": float(std_distance),
                "max_distance": float(max_distance),
                "cv": float(cv),
                "uniformity": float(uniformity),
                "density": float(density),
                "occupancy_rate": float(occupancy_rate),
                "dispersion_score": float(dispersion_score),
                "quartiles": {
                    "q1": float(q1),
                    "q3": float(q3),
                    "iqr": float(iqr)
                },
                "nearest_neighbor": {
                    "mean": float(nearest_distance_mean),
                    "std": float(nearest_distance_std)
                },
                "pairwise_distances": {
                    "mean": float(pairwise_mean),
                    "std": float(pairwise_std)
                },
                "dimensions": n_dims
            }
        }
    
    def _detect_spatial_migration(self, data: Dict[str, Any], 
                                dimensions: List[str]) -> Dict[str, Any]:
        """检测空间迁移模式"""
        if "spatial" not in dimensions or "temporal" not in dimensions:
            return {"confidence": 0.0}
            
        # 对于迁移模式，我们需要时间序列的空间坐标
        if ("coordinates_series" not in data and "trajectories" not in data and 
            "time_series_coordinates" not in data):
            return {"confidence": 0.0}
            
        # 获取轨迹数据 - 支持多种输入格式
        trajectories = data.get("trajectories", data.get("coordinates_series", 
                                                       data.get("time_series_coordinates", [])))
        
        # 检查数据格式 - 应该是多个时间点的坐标列表或字典
        if not trajectories or not isinstance(trajectories, (list, dict)):
            return {"confidence": 0.3, "type": "invalid_format"}
            
        # 将轨迹数据转换为标准格式：时间点到坐标点的映射
        trajectory_data = {}
        
        if isinstance(trajectories, list):
            # 假设列表中的每个元素是 (time, coordinates) 或 {"time": t, "coordinates": coords}
            for item in trajectories:
                if isinstance(item, tuple) and len(item) == 2:
                    time_point, coords = item
                    trajectory_data[time_point] = coords
                elif isinstance(item, dict) and "time" in item and "coordinates" in item:
                    trajectory_data[item["time"]] = item["coordinates"]
        else:  # dict
            # 假设字典直接是 time -> coordinates 的映射
            trajectory_data = trajectories
            
        # 检查是否有足够的时间点
        if len(trajectory_data) < 3:
            return {"confidence": 0.3, "type": "insufficient_time_points"}
            
        # 按时间排序轨迹点
        sorted_times = sorted(trajectory_data.keys())
        coordinates_series = [trajectory_data[t] for t in sorted_times]
        
        # 确保坐标是numpy数组并确定维度
        coordinates_series = [np.array(coords) for coords in coordinates_series]
        n_dims = coordinates_series[0].shape[0] if len(coordinates_series[0].shape) == 1 else coordinates_series[0].shape[1]
        
        if n_dims not in [2, 3]:
            return {"confidence": 0.2, "type": "unsupported_dimensions"}
            
        # 1. 计算基本迁移统计量
        # 计算总位移向量和距离
        start_point = coordinates_series[0]
        end_point = coordinates_series[-1]
        
        displacement_vector = end_point - start_point
        displacement_distance = np.sqrt(np.sum(displacement_vector ** 2))
        
        # 计算轨迹总长度
        total_path_length = 0
        for i in range(1, len(coordinates_series)):
            step_vector = coordinates_series[i] - coordinates_series[i-1]
            step_distance = np.sqrt(np.sum(step_vector ** 2))
            total_path_length += step_distance
            
        # 计算直线性 - 总位移与总路径长度的比值
        # 接近1表示几乎直线移动，接近0表示非常曲折
        linearity = displacement_distance / total_path_length if total_path_length > 0 else 0
        
        # 2. 计算速度和加速度特征
        velocities = []
        time_intervals = []
        
        # 计算相邻时间点之间的速度向量
        for i in range(1, len(sorted_times)):
            t1, t2 = sorted_times[i-1], sorted_times[i]
            time_delta = t2 - t1 if isinstance(t1, (int, float)) else 1  # 如果时间不是数值，使用单位时间间隔
            time_intervals.append(time_delta)
            
            displacement = coordinates_series[i] - coordinates_series[i-1]
            velocity = displacement / time_delta if time_delta > 0 else displacement
            velocities.append(velocity)
            
        # 计算速度统计特征
        speed_values = [np.sqrt(np.sum(v ** 2)) for v in velocities]
        mean_speed = np.mean(speed_values) if speed_values else 0
        max_speed = np.max(speed_values) if speed_values else 0
        speed_variance = np.var(speed_values) if speed_values else 0
        
        # 计算速度方向变化
        direction_changes = []
        for i in range(1, len(velocities)):
            v1 = velocities[i-1]
            v2 = velocities[i]
            v1_mag = np.sqrt(np.sum(v1 ** 2))
            v2_mag = np.sqrt(np.sum(v2 ** 2))
            
            if v1_mag > 0 and v2_mag > 0:
                cos_angle = np.dot(v1, v2) / (v1_mag * v2_mag)
                # 处理数值误差，确保cos_angle在[-1, 1]范围内
                cos_angle = min(1.0, max(-1.0, cos_angle))
                angle = np.arccos(cos_angle)
                direction_changes.append(angle)
                
        mean_direction_change = np.mean(direction_changes) if direction_changes else 0
        
        # 计算加速度
        accelerations = []
        for i in range(1, len(velocities)):
            time_delta = time_intervals[i] if i < len(time_intervals) else time_intervals[-1]
            if time_delta > 0:
                acceleration = (velocities[i] - velocities[i-1]) / time_delta
                accelerations.append(np.sqrt(np.sum(acceleration ** 2)))
                
        mean_acceleration = np.mean(accelerations) if accelerations else 0
        
        # 3. 检测迁移模式特征
        
        # 计算主要方向 - 使用位移向量的单位向量
        if displacement_distance > 0:
            main_direction = displacement_vector / displacement_distance
        else:
            main_direction = np.zeros_like(displacement_vector)
            
        # 计算方向一致性 - 各步骤位移与主方向的一致程度
        direction_consistency = 0
        if len(velocities) > 0:
            dot_products = []
            for velocity in velocities:
                v_mag = np.sqrt(np.sum(velocity ** 2))
                if v_mag > 0:
                    dot_products.append(np.dot(velocity / v_mag, main_direction))
            direction_consistency = np.mean(dot_products) if dot_products else 0
            
        # 检测循环模式 - 计算轨迹的闭合程度
        # 如果起点和终点非常接近，可能是循环模式
        is_cyclic = displacement_distance < 0.1 * total_path_length
        
        # 检测振荡模式 - 方向变化频繁且幅度大
        is_oscillating = (mean_direction_change > 0.5 and  # 平均方向变化超过30度
                         len(direction_changes) > len(velocities) * 0.7)  # 大部分步骤都有显著方向变化
        
        # 检测是否有明显的阶段性变化
        # 使用速度变化检测阶段
        phases = []
        if len(speed_values) > 5:
            # 使用简单的阈值法检测速度变化阶段
            current_phase = {"start_idx": 0, "speeds": [speed_values[0]]}
            for i in range(1, len(speed_values)):
                current_avg = np.mean(current_phase["speeds"])
                if abs(speed_values[i] - current_avg) > 0.5 * current_avg:  # 速度变化超过50%
                    # 结束当前阶段，开始新阶段
                    current_phase["end_idx"] = i - 1
                    current_phase["avg_speed"] = np.mean(current_phase["speeds"])
                    phases.append(current_phase)
                    current_phase = {"start_idx": i, "speeds": [speed_values[i]]}
                else:
                    current_phase["speeds"].append(speed_values[i])
                    
            # 添加最后一个阶段
            if current_phase["speeds"]:
                current_phase["end_idx"] = len(speed_values) - 1
                current_phase["avg_speed"] = np.mean(current_phase["speeds"])
                phases.append(current_phase)
        
        # 4. 确定迁移模式类型和强度
        
        # 综合评分，考虑多个指标
        migration_score = 0.0
        
        # 基于位移与路径长度的比值 (0-1)
        displacement_score = displacement_distance / total_path_length if total_path_length > 0 else 0
        
        # 基于方向一致性 (-1到1，转换为0-1)
        consistency_score = (direction_consistency + 1) / 2
        
        # 基于速度稳定性 (0-1)
        speed_stability = 1 - (np.std(speed_values) / mean_speed if mean_speed > 0 else 0)
        speed_stability = max(0, min(1, speed_stability))
        
        # 综合得分
        migration_score = (
            displacement_score * 0.4 +  # 位移分数
            consistency_score * 0.4 +   # 方向一致性
            speed_stability * 0.2       # 速度稳定性
        )
        
        # 确定迁移模式类型
        if is_cyclic:
            pattern_type = "cyclic_migration"
            strength = 0.7 + 0.2 * (1 - displacement_distance / total_path_length)
        elif is_oscillating:
            pattern_type = "oscillating_migration"
            strength = 0.6 + 0.3 * mean_direction_change / np.pi
        elif linearity > 0.8:
            pattern_type = "directional_migration"
            strength = 0.7 + 0.3 * linearity
        elif len(phases) > 2:
            pattern_type = "phased_migration"
            strength = 0.6 + 0.3 * (len(phases) / len(coordinates_series))
        elif migration_score > 0.6:
            pattern_type = "general_migration"
            strength = migration_score
        else:
            pattern_type = "random_movement"
            strength = 0.3 + 0.3 * migration_score
            
        # 设置置信度
        if total_path_length < displacement_distance * 1.1:
            # 几乎是直线，非常确定
            confidence = 0.9
        elif migration_score > 0.7:
            confidence = 0.85
        elif migration_score > 0.5:
            confidence = 0.7
        elif migration_score > 0.3:
            confidence = 0.5
        else:
            confidence = 0.4
            
        return {
            "confidence": confidence,
            "strength": strength,
            "type": pattern_type,
            "details": {
                "time_points": len(coordinates_series),
                "displacement": {
                    "distance": float(displacement_distance),
                    "vector": displacement_vector.tolist(),
                    "start_point": start_point.tolist(),
                    "end_point": end_point.tolist()
                },
                "path_length": float(total_path_length),
                "linearity": float(linearity),
                "velocity": {
                    "mean": float(mean_speed),
                    "max": float(max_speed),
                    "variance": float(speed_variance)
                },
                "direction": {
                    "main_direction": main_direction.tolist(),
                    "consistency": float(direction_consistency),
                    "mean_change": float(mean_direction_change)
                },
                "acceleration": {
                    "mean": float(mean_acceleration)
                },
                "patterns": {
                    "is_cyclic": is_cyclic,
                    "is_oscillating": is_oscillating,
                    "phases_count": len(phases)
                },
                "migration_score": float(migration_score),
                "dimensions": n_dims
            }
        }
    
    # === 关联维度模式检测器 ===
    
    def _detect_correlation_network(self, data: Dict[str, Any], 
                                  dimensions: List[str]) -> Dict[str, Any]:
        """检测相关网络模式"""
        if "relational" not in dimensions or ("variables" not in data and "features" not in data):
            return {"confidence": 0.0}
            
        # 获取变量数据
        variables = data.get("variables", data.get("features", {}))
        
        # 检查数据格式 - 应该是变量名到值列表的映射
        if not variables or not isinstance(variables, dict):
            return {"confidence": 0.3, "type": "invalid_format"}
            
        # 确保所有变量都有相同数量的观察值
        variable_names = list(variables.keys())
        if not variable_names:
            return {"confidence": 0.2, "type": "no_variables"}
            
        first_var_length = len(variables[variable_names[0]])
        if first_var_length < 10:  # 需要足够的样本
            return {"confidence": 0.3, "type": "insufficient_samples"}
            
        for var_name, values in variables.items():
            if len(values) != first_var_length:
                return {"confidence": 0.3, "type": "uneven_samples"}
                
        # 转换为numpy数组以便计算
        var_data = {}
        for var_name, values in variables.items():
            try:
                # 尝试将值转换为数值型数组
                var_data[var_name] = np.array(values, dtype=float)
            except (ValueError, TypeError):
                # 如果无法转换为数值型，跳过该变量
                continue
                
        # 检查是否有足够的数值型变量
        if len(var_data) < 2:
            return {"confidence": 0.3, "type": "insufficient_numeric_variables"}
            
        # 1. 计算变量间的相关性矩阵
        variable_names = list(var_data.keys())
        n_vars = len(variable_names)
        correlation_matrix = np.zeros((n_vars, n_vars))
        
        for i in range(n_vars):
            for j in range(n_vars):
                var1 = var_data[variable_names[i]]
                var2 = var_data[variable_names[j]]
                
                # 计算皮尔逊相关系数
                try:
                    if i == j:
                        correlation_matrix[i, j] = 1.0
                    else:
                        # 使用np.corrcoef计算相关系数
                        corr = np.corrcoef(var1, var2)[0, 1]
                        correlation_matrix[i, j] = corr
                except:
                    correlation_matrix[i, j] = 0.0
        
        # 2. 构建相关网络
        # 识别显著相关的变量对
        significant_correlations = []
        strong_threshold = 0.7  # 强相关阈值
        moderate_threshold = 0.4  # 中等相关阈值
        
        for i in range(n_vars):
            for j in range(i+1, n_vars):  # 只处理上三角矩阵，避免重复
                corr = correlation_matrix[i, j]
                abs_corr = abs(corr)
                
                if abs_corr >= moderate_threshold:
                    corr_type = "strong" if abs_corr >= strong_threshold else "moderate"
                    direction = "positive" if corr > 0 else "negative"
                    
                    significant_correlations.append({
                        "var1": variable_names[i],
                        "var2": variable_names[j],
                        "correlation": float(corr),
                        "abs_correlation": float(abs_corr),
                        "type": corr_type,
                        "direction": direction
                    })
        
        # 3. 识别相关网络结构特征
        
        # 构建邻接矩阵（基于显著相关性）
        adjacency_matrix = np.zeros((n_vars, n_vars))
        for corr in significant_correlations:
            i = variable_names.index(corr["var1"])
            j = variable_names.index(corr["var2"])
            adjacency_matrix[i, j] = adjacency_matrix[j, i] = 1
            
        # 计算每个变量的度（与其显著相关的变量数量）
        degrees = np.sum(adjacency_matrix, axis=1)
        
        # 识别中心变量 - 与最多其他变量显著相关的变量
        central_variables = []
        for i in range(n_vars):
            if degrees[i] > 1:  # 至少与两个其他变量相关
                central_variables.append({
                    "variable": variable_names[i],
                    "degree": int(degrees[i]),
                    "centrality": float(degrees[i] / (n_vars - 1))  # 归一化中心度
                })
        
        # 按度数排序中心变量
        central_variables.sort(key=lambda x: x["degree"], reverse=True)
        
        # 识别变量集群 - 使用简化的社区检测算法
        communities = []
        unassigned = set(range(n_vars))
        
        # 简化的社区检测 - 基于贪婪算法
        while unassigned:
            # 选择未分配的第一个节点作为种子
            seed = next(iter(unassigned))
            community = {seed}
            frontier = {seed}
            
            # 扩展社区
            while frontier:
                new_frontier = set()
                for node in frontier:
                    # 获取相邻节点
                    neighbors = {j for j in range(n_vars) if adjacency_matrix[node, j] > 0}
                    # 添加未分配的相邻节点到社区
                    for neighbor in neighbors:
                        if neighbor in unassigned and neighbor not in community:
                            community.add(neighbor)
                            new_frontier.add(neighbor)
                frontier = new_frontier
                
            # 如果社区只有一个节点且没有连接，不视为社区
            if len(community) > 1 or degrees[seed] > 0:
                communities.append([variable_names[i] for i in community])
                
            # 从未分配集合中移除已分配的节点
            unassigned -= community
            
        # 计算相关矩阵的特征值，用于评估网络的整体结构
        try:
            eigenvalues = np.linalg.eigvals(correlation_matrix)
            largest_eigenvalue = np.max(np.abs(eigenvalues))
            spectral_gap = np.max(eigenvalues) - np.sort(eigenvalues)[-2] if len(eigenvalues) > 1 else 0
        except:
            largest_eigenvalue = 0
            spectral_gap = 0
        
        # 4. 评估相关网络的整体特征
        
        # 计算网络密度 - 实际存在的边与可能的最大边数的比值
        possible_edges = n_vars * (n_vars - 1) / 2
        network_density = len(significant_correlations) / possible_edges if possible_edges > 0 else 0
        
        # 计算平均相关强度
        avg_correlation = np.mean([abs(corr["correlation"]) for corr in significant_correlations]) if significant_correlations else 0
        
        # 计算集群系数 - 评估变量是否倾向于形成集群
        clustering_coefficient = 0.0
        if communities:
            # 计算每个社区的内部密度，取平均值
            community_densities = []
            for community in communities:
                community_indices = [variable_names.index(var) for var in community]
                n_community = len(community_indices)
                if n_community > 1:
                    edges_count = 0
                    for i, idx1 in enumerate(community_indices):
                        for j in range(i+1, n_community):
                            idx2 = community_indices[j]
                            if adjacency_matrix[idx1, idx2] > 0:
                                edges_count += 1
                    
                    possible_community_edges = n_community * (n_community - 1) / 2
                    community_density = edges_count / possible_community_edges if possible_community_edges > 0 else 0
                    community_densities.append(community_density)
                    
            clustering_coefficient = np.mean(community_densities) if community_densities else 0
        
        # 确定相关网络模式类型和强度
        if network_density > 0.6 and avg_correlation > 0.6:
            pattern_type = "densely_connected_network"
            strength = 0.8 + 0.2 * avg_correlation
            confidence = 0.9
        elif len(central_variables) > 0 and central_variables[0]["centrality"] > 0.5:
            pattern_type = "hub_spoke_network"
            strength = 0.7 + 0.3 * central_variables[0]["centrality"]
            confidence = 0.8
        elif len(communities) > 1 and clustering_coefficient > 0.6:
            pattern_type = "clustered_network"
            strength = 0.7 + 0.3 * clustering_coefficient
            confidence = 0.8
        elif network_density > 0.3 and avg_correlation > 0.4:
            pattern_type = "moderate_correlation_network"
            strength = 0.6 + 0.4 * avg_correlation
            confidence = 0.7
        elif len(significant_correlations) > n_vars / 2:
            pattern_type = "sparse_correlation_network"
            strength = 0.5 + 0.3 * (len(significant_correlations) / possible_edges)
            confidence = 0.6
        else:
            pattern_type = "weak_correlation_structure"
            strength = 0.4 + 0.2 * (len(significant_correlations) / n_vars)
            confidence = 0.5
            
        return {
            "confidence": confidence,
            "strength": strength,
            "type": pattern_type,
            "details": {
                "variables_count": n_vars,
                "significant_correlations": significant_correlations,
                "central_variables": central_variables[:5],  # 限制返回数量
                "communities": communities,
                "network_properties": {
                    "density": float(network_density),
                    "avg_correlation": float(avg_correlation),
                    "clustering_coefficient": float(clustering_coefficient),
                    "largest_eigenvalue": float(largest_eigenvalue),
                    "spectral_gap": float(spectral_gap)
                },
                "correlation_matrix": correlation_matrix.tolist()
            }
        }
    
    def _detect_causal_chain(self, data: Dict[str, Any], 
                           dimensions: List[str]) -> Dict[str, Any]:
        """检测因果链模式"""
        if "relational" not in dimensions or "temporal" not in dimensions:
            return {"confidence": 0.0}
        
        # 检查是否有时间序列变量
        if "time_series_variables" not in data and "event_sequence" not in data:
            return {"confidence": 0.0}
        
        # 支持两种数据格式：多变量时间序列或事件序列
        if "time_series_variables" in data:
            # 多变量时间序列格式
            variables = data.get("time_series_variables", {})
            
            # 检查数据格式 - 应该是变量名到时间序列值的映射
            if not variables or not isinstance(variables, dict):
                return {"confidence": 0.3, "type": "invalid_format"}
                
            # 确保所有变量都有相同数量的时间点
            variable_names = list(variables.keys())
            if not variable_names:
                return {"confidence": 0.2, "type": "no_variables"}
                
            first_var_length = len(variables[variable_names[0]])
            if first_var_length < 20:  # 需要足够长的时间序列
                return {"confidence": 0.3, "type": "insufficient_time_points"}
                
            for var_name, values in variables.items():
                if len(values) != first_var_length:
                    return {"confidence": 0.3, "type": "uneven_time_series"}
                    
            # 转换为numpy数组以便计算
            var_data = {}
            for var_name, values in variables.items():
                try:
                    # 尝试将值转换为数值型数组
                    var_data[var_name] = np.array(values, dtype=float)
                except (ValueError, TypeError):
                    # 如果无法转换为数值型，跳过该变量
                    continue
                    
            # 检查是否有足够的数值型变量
            if len(var_data) < 2:
                return {"confidence": 0.3, "type": "insufficient_numeric_variables"}
                
            # 使用简化的格兰杰因果分析
            variable_names = list(var_data.keys())
            n_vars = len(variable_names)
            
            # 初始化结果
            causal_links = []
            
            # 对每对变量之间计算滞后相关性
            for i in range(n_vars):
                for j in range(n_vars):
                    if i == j:
                        continue
                        
                    x = var_data[variable_names[i]]  # 可能的原因
                    y = var_data[variable_names[j]]  # 可能的结果
                    
                    # 计算不同滞后的相关性
                    max_lag = min(10, len(x) // 5)
                    best_lag = 0
                    best_corr = 0
                    
                    for lag in range(1, max_lag + 1):
                        # 计算x在t-lag和y在t之间的相关性
                        corr = np.corrcoef(x[:-lag], y[lag:])[0, 1]
                        if abs(corr) > abs(best_corr):
                            best_corr = corr
                            best_lag = lag
                    
                    # 检查反向因果
                    reverse_corr = 0
                    for lag in range(1, max_lag + 1):
                        # 计算y在t-lag和x在t之间的相关性
                        corr = np.corrcoef(y[:-lag], x[lag:])[0, 1]
                        if abs(corr) > abs(reverse_corr):
                            reverse_corr = corr
                    
                    # 如果正向相关显著高于反向相关，可能存在因果关系
                    if abs(best_corr) > 0.3 and abs(best_corr) > abs(reverse_corr) * 1.2:
                        causal_links.append({
                            "cause": variable_names[i],
                            "effect": variable_names[j],
                            "lag": best_lag,
                            "correlation": float(best_corr),
                            "reverse_correlation": float(reverse_corr),
                            "strength": float(abs(best_corr))
                        })
            
            # 对因果链接进行排序
            causal_links.sort(key=lambda x: x["strength"], reverse=True)
            
            # 识别因果链 - 连续的因果关系
            causal_chains = []
            if causal_links:
                # 构建因果图
                causal_graph = {var: [] for var in variable_names}
                for link in causal_links:
                    causal_graph[link["cause"]].append({
                        "target": link["effect"],
                        "strength": link["strength"]
                    })
                
                # 寻找所有长度大于1的路径
                def find_paths(start, path=None, visited=None):
                    if path is None:
                        path = []
                    if visited is None:
                        visited = set()
                    
                    # 避免循环
                    if start in visited:
                        return []
                    
                    # 更新路径和已访问节点
                    current_path = path + [start]
                    current_visited = visited.union({start})
                    
                    # 如果路径长度大于1，添加到结果
                    paths = []
                    if len(current_path) > 1:
                        paths.append(current_path)
                    
                    # 继续探索
                    for next_node in causal_graph[start]:
                        target = next_node["target"]
                        extended_paths = find_paths(target, current_path, current_visited)
                        paths.extend(extended_paths)
                        
                    return paths
                
                # 从每个节点开始寻找路径
                all_paths = []
                for var in variable_names:
                    paths = find_paths(var)
                    all_paths.extend(paths)
                
                # 过滤重复路径并保留最长的前5条
                unique_paths = []
                path_strings = set()
                for path in sorted(all_paths, key=len, reverse=True):
                    path_str = "->".join(path)
                    if path_str not in path_strings:
                        path_strings.add(path_str)
                        unique_paths.append(path)
                    if len(unique_paths) >= 5:
                        break
                        
                # 计算每条路径的强度
                for path in unique_paths:
                    strength = 1.0
                    for i in range(len(path) - 1):
                        cause, effect = path[i], path[i+1]
                        # 查找对应的因果链接
                        for link in causal_links:
                            if link["cause"] == cause and link["effect"] == effect:
                                strength *= link["strength"]
                                break
                                
                    causal_chains.append({
                        "path": path,
                        "strength": float(strength)
                    })
                    
                # 对链按强度排序
                causal_chains.sort(key=lambda x: x["strength"], reverse=True)
            
            # 评估整体因果结构
            avg_strength = np.mean([link["strength"] for link in causal_links]) if causal_links else 0
            longest_chain = max([len(chain["path"]) for chain in causal_chains]) if causal_chains else 0
            
            # 确定因果模式类型和强度
            if longest_chain >= 3 and avg_strength > 0.6:
                pattern_type = "strong_causal_chain"
                confidence = 0.9
                strength = 0.9
            elif longest_chain >= 2 and avg_strength > 0.5:
                pattern_type = "moderate_causal_chain"
                confidence = 0.7
                strength = 0.7
            elif causal_links and avg_strength > 0.4:
                pattern_type = "weak_causal_relationships"
                confidence = 0.5
                strength = 0.5
            else:
                pattern_type = "no_clear_causality"
                confidence = 0.3
                strength = 0.3
                
            return {
                "confidence": confidence,
                "strength": strength,
                "type": pattern_type,
                "details": {
                    "variables_count": n_vars,
                    "causal_links": causal_links,
                    "causal_chains": causal_chains,
                    "chain_properties": {
                        "avg_strength": float(avg_strength),
                        "longest_chain": int(longest_chain)
                    }
                }
            }
            
        else:
            # 事件序列格式
            events = data.get("event_sequence", [])
            
            # 检查数据格式 - 应该是按时间排序的事件列表
            if not events or not isinstance(events, list):
                return {"confidence": 0.3, "type": "invalid_format"}
                
            if len(events) < 10:  # 需要足够多的事件
                return {"confidence": 0.3, "type": "insufficient_events"}
                
            # 事件应该包含类型和时间信息
            valid_events = []
            for event in events:
                if isinstance(event, dict) and "type" in event and "time" in event:
                    valid_events.append(event)
                    
            if len(valid_events) < 10:
                return {"confidence": 0.3, "type": "insufficient_valid_events"}
                
            # 按时间排序事件
            sorted_events = sorted(valid_events, key=lambda e: e["time"])
            
            # 提取事件类型
            event_types = list(set(e["type"] for e in sorted_events))
            n_types = len(event_types)
            
            if n_types < 2:
                return {"confidence": 0.3, "type": "insufficient_event_types"}
                
            # 分析事件序列中的时间依赖关系
            # 计算事件转移概率矩阵：P(B|A) - 事件A之后发生事件B的概率
            transition_counts = np.zeros((n_types, n_types))
            type_to_idx = {t: i for i, t in enumerate(event_types)}
            
            # 计算每种事件类型之后紧接着发生的事件类型
            for i in range(len(sorted_events) - 1):
                current_type = sorted_events[i]["type"]
                next_type = sorted_events[i + 1]["type"]
                current_idx = type_to_idx[current_type]
                next_idx = type_to_idx[next_type]
                transition_counts[current_idx, next_idx] += 1
                
            # 计算转移概率
            transition_probs = np.zeros((n_types, n_types))
            for i in range(n_types):
                row_sum = np.sum(transition_counts[i, :])
                if row_sum > 0:
                    transition_probs[i, :] = transition_counts[i, :] / row_sum
                    
            # 分析事件间的时间延迟
            # 计算每对事件类型之间的平均时间间隔
            time_delays = {}
            for i in range(len(sorted_events) - 1):
                current_event = sorted_events[i]
                next_event = sorted_events[i + 1]
                current_type = current_event["type"]
                next_type = next_event["type"]
                
                delay = next_event["time"] - current_event["time"]
                if delay <= 0:
                    continue  # 忽略不合理的时间延迟
                    
                key = (current_type, next_type)
                if key not in time_delays:
                    time_delays[key] = []
                time_delays[key].append(delay)
                
            # 计算平均延迟和变异系数
            avg_delays = {}
            cv_delays = {}
            for key, delays in time_delays.items():
                if len(delays) > 1:
                    avg = np.mean(delays)
                    std = np.std(delays)
                    avg_delays[key] = avg
                    cv_delays[key] = std / avg if avg > 0 else 0
                    
            # 识别可能的因果关系
            # 使用三个标准：
            # 1. 高转移概率（显著高于随机）
            # 2. 稳定的时间延迟（低变异系数）
            # 3. 时间的先后关系
            causal_links = []
            for i in range(n_types):
                for j in range(n_types):
                    if i == j:
                        continue
                        
                    type_i = event_types[i]
                    type_j = event_types[j]
                    key = (type_i, type_j)
                    
                    # 随机期望概率（均匀分布）
                    expected_prob = 1.0 / n_types
                    actual_prob = transition_probs[i, j]
                    
                    # 计算转移显著性
                    prob_significance = actual_prob / expected_prob if expected_prob > 0 else 0
                    
                    # 检查时间延迟稳定性
                    time_stability = 1.0
                    if key in cv_delays:
                        time_stability = 1.0 / (1.0 + cv_delays[key])  # 将CV转换为稳定性度量
                        
                    # 综合评分
                    causal_strength = prob_significance * time_stability
                    
                    # 如果得分足够高，认为存在因果关系
                    if actual_prob > 1.5 * expected_prob and (key in avg_delays):
                        causal_links.append({
                            "cause": type_i,
                            "effect": type_j,
                            "probability": float(actual_prob),
                            "avg_delay": float(avg_delays.get(key, 0)),
                            "delay_stability": float(time_stability),
                            "strength": float(causal_strength),
                            "count": int(transition_counts[i, j])
                        })
                        
            # 按强度排序因果链接
            causal_links.sort(key=lambda x: x["strength"], reverse=True)
            
            # 识别因果链
            causal_chains = []
            if causal_links:
                # 构建因果图
                causal_graph = {event_type: [] for event_type in event_types}
                for link in causal_links:
                    causal_graph[link["cause"]].append({
                        "target": link["effect"],
                        "strength": link["strength"]
                    })
                
                # 寻找所有长度大于1的路径
                def find_paths(start, path=None, visited=None):
                    if path is None:
                        path = []
                    if visited is None:
                        visited = set()
                    
                    # 避免循环
                    if start in visited:
                        return []
                    
                    # 更新路径和已访问节点
                    current_path = path + [start]
                    current_visited = visited.union({start})
                    
                    # 如果路径长度大于1，添加到结果
                    paths = []
                    if len(current_path) > 1:
                        paths.append(current_path)
                    
                    # 继续探索
                    for next_node in causal_graph[start]:
                        target = next_node["target"]
                        extended_paths = find_paths(target, current_path, current_visited)
                        paths.extend(extended_paths)
                        
                    return paths
                
                # 从每个事件类型开始寻找路径
                all_paths = []
                for event_type in event_types:
                    paths = find_paths(event_type)
                    all_paths.extend(paths)
                
                # 过滤重复路径并保留最长的前5条
                unique_paths = []
                path_strings = set()
                for path in sorted(all_paths, key=len, reverse=True):
                    path_str = "->".join(path)
                    if path_str not in path_strings:
                        path_strings.add(path_str)
                        unique_paths.append(path)
                    if len(unique_paths) >= 5:
                        break
                        
                # 计算每条路径的强度
                for path in unique_paths:
                    strength = 1.0
                    total_delay = 0.0
                    for i in range(len(path) - 1):
                        cause, effect = path[i], path[i+1]
                        key = (cause, effect)
                        
                        # 查找对应的因果链接
                        for link in causal_links:
                            if link["cause"] == cause and link["effect"] == effect:
                                strength *= link["strength"]
                                total_delay += link.get("avg_delay", 0)
                                break
                                
                    causal_chains.append({
                        "path": path,
                        "strength": float(strength),
                        "total_delay": float(total_delay)
                    })
                    
                # 对链按强度排序
                causal_chains.sort(key=lambda x: x["strength"], reverse=True)
                
            # 评估整体因果结构
            avg_strength = np.mean([link["strength"] for link in causal_links]) if causal_links else 0
            longest_chain = max([len(chain["path"]) for chain in causal_chains]) if causal_chains else 0
            
            # 确定因果模式类型和强度
            if longest_chain >= 3 and avg_strength > 1.5:
                pattern_type = "strong_event_chain"
                confidence = 0.9
                strength = 0.9
            elif longest_chain >= 2 and avg_strength > 1.2:
                pattern_type = "moderate_event_chain"
                confidence = 0.7
                strength = 0.7
            elif causal_links and avg_strength > 1.0:
                pattern_type = "weak_event_relationships"
                confidence = 0.5
                strength = 0.5
            else:
                pattern_type = "no_clear_event_causality"
                confidence = 0.3
                strength = 0.3
                
            return {
                "confidence": confidence,
                "strength": strength,
                "type": pattern_type,
                "details": {
                    "event_types_count": n_types,
                    "events_count": len(sorted_events),
                    "causal_links": causal_links,
                    "causal_chains": causal_chains,
                    "transition_matrix": transition_probs.tolist(),
                    "chain_properties": {
                        "avg_strength": float(avg_strength),
                        "longest_chain": int(longest_chain)
                    }
                }
            }
    
    def _detect_event_causality(self, events: List[Dict[str, Any]]) -> Dict[str, Any]:
        """分析事件序列中的因果关系"""
        # 按时间排序事件
        sorted_events = sorted(events, key=lambda e: e["time"])
        
        # 提取事件类型
        event_types = list(set(e["type"] for e in sorted_events))
        n_types = len(event_types)
        
        if n_types < 2:
            return {"confidence": 0.3, "type": "insufficient_event_types"}
            
        # 分析事件序列中的时间依赖关系
        # 计算事件转移概率矩阵：P(B|A) - 事件A之后发生事件B的概率
        transition_counts = np.zeros((n_types, n_types))
        type_to_idx = {t: i for i, t in enumerate(event_types)}
        
        # 计算每种事件类型之后紧接着发生的事件类型
        for i in range(len(sorted_events) - 1):
            current_type = sorted_events[i]["type"]
            next_type = sorted_events[i + 1]["type"]
            current_idx = type_to_idx[current_type]
            next_idx = type_to_idx[next_type]
            transition_counts[current_idx, next_idx] += 1
            
        # 计算转移概率
        transition_probs = np.zeros((n_types, n_types))
        for i in range(n_types):
            row_sum = np.sum(transition_counts[i, :])
            if row_sum > 0:
                transition_probs[i, :] = transition_counts[i, :] / row_sum
                
        # 分析事件间的时间延迟
        # 计算每对事件类型之间的平均时间间隔
        time_delays = {}
        for i in range(len(sorted_events) - 1):
            current_event = sorted_events[i]
            next_event = sorted_events[i + 1]
            current_type = current_event["type"]
            next_type = next_event["type"]
            
            delay = next_event["time"] - current_event["time"]
            if delay <= 0:
                continue  # 忽略不合理的时间延迟
                
            key = (current_type, next_type)
            if key not in time_delays:
                time_delays[key] = []
            time_delays[key].append(delay)
            
        # 计算平均延迟和变异系数
        avg_delays = {}
        cv_delays = {}
        for key, delays in time_delays.items():
            if len(delays) > 1:
                avg = np.mean(delays)
                std = np.std(delays)
                avg_delays[key] = avg
                cv_delays[key] = std / avg if avg > 0 else 0
                
        # 识别可能的因果关系
        # 使用三个标准：
        # 1. 高转移概率（显著高于随机）
        # 2. 稳定的时间延迟（低变异系数）
        # 3. 时间的先后关系
        causal_links = []
        for i in range(n_types):
            for j in range(n_types):
                if i == j:
                    continue
                    
                type_i = event_types[i]
                type_j = event_types[j]
                key = (type_i, type_j)
                
                # 随机期望概率（均匀分布）
                expected_prob = 1.0 / n_types
                actual_prob = transition_probs[i, j]
                
                # 计算转移显著性
                prob_significance = actual_prob / expected_prob if expected_prob > 0 else 0
                
                # 检查时间延迟稳定性
                time_stability = 1.0
                if key in cv_delays:
                    time_stability = 1.0 / (1.0 + cv_delays[key])  # 将CV转换为稳定性度量
                    
                # 综合评分
                causal_strength = prob_significance * time_stability
                
                # 如果得分足够高，认为存在因果关系
                if actual_prob > 1.5 * expected_prob and (key in avg_delays):
                    causal_links.append({
                        "cause": type_i,
                        "effect": type_j,
                        "probability": float(actual_prob),
                        "avg_delay": float(avg_delays.get(key, 0)),
                        "delay_stability": float(time_stability),
                        "strength": float(causal_strength),
                        "count": int(transition_counts[i, j])
                    })
                    
        # 按强度排序因果链接
        causal_links.sort(key=lambda x: x["strength"], reverse=True)
        
        # 识别因果链
        causal_chains = []
        if causal_links:
            # 构建因果图
            causal_graph = {event_type: [] for event_type in event_types}
            for link in causal_links:
                causal_graph[link["cause"]].append({
                    "target": link["effect"],
                    "strength": link["strength"]
                })
            
            # 寻找所有长度大于1的路径
            def find_paths(start, path=None, visited=None):
                if path is None:
                    path = []
                if visited is None:
                    visited = set()
                
                # 避免循环
                if start in visited:
                    return []
                
                # 更新路径和已访问节点
                current_path = path + [start]
                current_visited = visited.union({start})
                
                # 如果路径长度大于1，添加到结果
                paths = []
                if len(current_path) > 1:
                    paths.append(current_path)
                
                # 继续探索
                for next_node in causal_graph[start]:
                    target = next_node["target"]
                    extended_paths = find_paths(target, current_path, current_visited)
                    paths.extend(extended_paths)
                    
                return paths
            
            # 从每个事件类型开始寻找路径
            all_paths = []
            for event_type in event_types:
                paths = find_paths(event_type)
                all_paths.extend(paths)
            
            # 过滤重复路径并保留最长的前10条
            unique_paths = []
            path_strings = set()
            for path in sorted(all_paths, key=len, reverse=True):
                path_str = "->".join(path)
                if path_str not in path_strings:
                    path_strings.add(path_str)
                    unique_paths.append(path)
                if len(unique_paths) >= 10:
                    break
                    
            # 计算每条路径的强度
            for path in unique_paths:
                strength = 1.0
                total_delay = 0.0
                for i in range(len(path) - 1):
                    cause, effect = path[i], path[i+1]
                    key = (cause, effect)
                    
                    # 查找对应的因果链接
                    for link in causal_links:
                        if link["cause"] == cause and link["effect"] == effect:
                            strength *= link["strength"]
                            total_delay += link.get("avg_delay", 0)
                            break
                            
                causal_chains.append({
                    "path": path,
                    "strength": float(strength),
                    "total_delay": float(total_delay)
                })
                
            # 对链按强度排序
            causal_chains.sort(key=lambda x: x["strength"], reverse=True)
            
        # 评估整体因果结构
        avg_strength = np.mean([link["strength"] for link in causal_links]) if causal_links else 0
        longest_chain = max([len(chain["path"]) for chain in causal_chains]) if causal_chains else 0
        
        # 确定因果模式类型和强度
        if longest_chain >= 3 and avg_strength > 1.5:
            pattern_type = "strong_event_chain"
            confidence = 0.9
            strength = 0.9
        elif longest_chain >= 2 and avg_strength > 1.2:
            pattern_type = "moderate_event_chain"
            confidence = 0.7
            strength = 0.7
        elif causal_links and avg_strength > 1.0:
            pattern_type = "weak_event_relationships"
            confidence = 0.5
            strength = 0.5
        else:
            pattern_type = "no_clear_event_causality"
            confidence = 0.3
            strength = 0.3
            
        return {
            "confidence": confidence,
            "strength": strength,
            "type": pattern_type,
            "details": {
                "event_types_count": n_types,
                "events_count": len(sorted_events),
                "causal_links": causal_links,
                "causal_chains": causal_chains,
                "transition_matrix": transition_probs.tolist(),
                "chain_properties": {
                    "avg_strength": float(avg_strength),
                    "longest_chain": int(longest_chain)
                }
            }
        }
    
    def _detect_mutual_information(self, data: Dict[str, Any], 
                                 dimensions: List[str]) -> Dict[str, Any]:
        """检测互信息模式"""
        if "numerical" not in dimensions or ("variables" not in data and "features" not in data):
            return {"confidence": 0.0}
            
        # 获取变量数据
        variables = data.get("variables", data.get("features", {}))
        
        # 检查数据格式 - 应该是变量名到值列表的映射
        if not variables or not isinstance(variables, dict):
            return {"confidence": 0.3, "type": "invalid_format"}
            
        # 确保所有变量都有相同数量的观察值
        variable_names = list(variables.keys())
        if not variable_names:
            return {"confidence": 0.2, "type": "no_variables"}
            
        first_var_length = len(variables[variable_names[0]])
        if first_var_length < 20:  # 需要足够的样本
            return {"confidence": 0.3, "type": "insufficient_samples"}
            
        for var_name, values in variables.items():
            if len(values) != first_var_length:
                return {"confidence": 0.3, "type": "uneven_samples"}
                
        # 转换为numpy数组以便计算
        var_data = {}
        for var_name, values in variables.items():
            try:
                # 尝试将值转换为数值型数组
                var_data[var_name] = np.array(values, dtype=float)
            except (ValueError, TypeError):
                # 如果无法转换为数值型，跳过该变量
                continue
                
        # 检查是否有足够的数值型变量
        if len(var_data) < 2:
            return {"confidence": 0.3, "type": "insufficient_numeric_variables"}
            
        # 1. 计算变量间的互信息矩阵
        variable_names = list(var_data.keys())
        n_vars = len(variable_names)
        mutual_info_matrix = np.zeros((n_vars, n_vars))
        normalized_mi_matrix = np.zeros((n_vars, n_vars))
        
        # 定义一个函数来估计互信息
        def estimate_mutual_information(x, y, bins=10):
            """使用直方图方法估计两个连续变量之间的互信息"""
            # 创建联合直方图
            hist_2d, x_edges, y_edges = np.histogram2d(x, y, bins=bins)
            
            # 计算边缘分布
            hist_x = np.sum(hist_2d, axis=1)
            hist_y = np.sum(hist_2d, axis=0)
            
            # 计算边缘熵
            p_x = hist_x / float(np.sum(hist_x))
            p_y = hist_y / float(np.sum(hist_y))
            H_x = -np.sum(p_x[p_x > 0] * np.log2(p_x[p_x > 0]))
            H_y = -np.sum(p_y[p_y > 0] * np.log2(p_y[p_y > 0]))
            
            # 计算联合熵
            p_xy = hist_2d / float(np.sum(hist_2d))
            H_xy = -np.sum(p_xy[p_xy > 0] * np.log2(p_xy[p_xy > 0]))
            
            # 计算互信息
            MI = H_x + H_y - H_xy
            
            # 归一化互信息（除以较小的边缘熵）
            NMI = MI / min(H_x, H_y) if min(H_x, H_y) > 0 else 0
            
            return MI, NMI
        
        # 计算每对变量之间的互信息
        for i in range(n_vars):
            for j in range(n_vars):
                if i == j:
                    mutual_info_matrix[i, j] = 0.0
                    normalized_mi_matrix[i, j] = 1.0  # 自身的归一化互信息为1
                else:
                    x = var_data[variable_names[i]]
                    y = var_data[variable_names[j]]
                    
                    # 为了避免边缘情况，确保变量有足够的变化
                    if np.std(x) > 1e-6 and np.std(y) > 1e-6:
                        try:
                            # 估计互信息和归一化互信息
                            mi, nmi = estimate_mutual_information(x, y)
                            mutual_info_matrix[i, j] = mi
                            normalized_mi_matrix[i, j] = nmi
                        except:
                            mutual_info_matrix[i, j] = 0.0
                            normalized_mi_matrix[i, j] = 0.0
                    else:
                        mutual_info_matrix[i, j] = 0.0
                        normalized_mi_matrix[i, j] = 0.0
        
        # 2. 识别显著的互信息关系
        # 我们关注归一化互信息高的变量对，表示强烈的依赖关系
        significant_relations = []
        strong_threshold = 0.7  # 强关系阈值
        moderate_threshold = 0.4  # 中等关系阈值
        
        for i in range(n_vars):
            for j in range(i+1, n_vars):  # 只考虑上三角矩阵，避免重复
                nmi = normalized_mi_matrix[i, j]
                
                if nmi >= moderate_threshold:
                    relation_type = "strong" if nmi >= strong_threshold else "moderate"
                    
                    # 计算线性相关系数以便比较
                    linear_corr = np.corrcoef(var_data[variable_names[i]], var_data[variable_names[j]])[0, 1]
                    
                    # 如果互信息显著高于线性相关的平方，可能存在非线性关系
                    is_nonlinear = nmi > linear_corr**2 * 1.5
                    
                    significant_relations.append({
                        "var1": variable_names[i],
                        "var2": variable_names[j],
                        "mutual_info": float(mutual_info_matrix[i, j]),
                        "normalized_mi": float(nmi),
                        "linear_correlation": float(linear_corr),
                        "type": relation_type,
                        "is_nonlinear": is_nonlinear,
                        "strength": float(nmi)
                    })
        
        # 按强度排序关系
        significant_relations.sort(key=lambda x: x["normalized_mi"], reverse=True)
        
        # 3. 构建互信息网络和识别复杂依赖结构
        
        # 构建邻接矩阵（基于显著的互信息）
        adjacency_matrix = np.zeros((n_vars, n_vars))
        for relation in significant_relations:
            i = variable_names.index(relation["var1"])
            j = variable_names.index(relation["var2"])
            adjacency_matrix[i, j] = adjacency_matrix[j, i] = relation["normalized_mi"]
            
        # 识别高互信息集群 - 使用相同的社区检测算法
        communities = []
        unassigned = set(range(n_vars))
        
        # 简化的社区检测 - 基于贪婪算法
        while unassigned:
            # 选择未分配的第一个节点作为种子
            seed = next(iter(unassigned))
            community = {seed}
            frontier = {seed}
            
            # 扩展社区
            while frontier:
                new_frontier = set()
                for node in frontier:
                    # 获取相邻节点 - 互信息高于阈值的节点
                    neighbors = {j for j in range(n_vars) 
                               if j != node and adjacency_matrix[node, j] >= moderate_threshold}
                    # 添加未分配的相邻节点到社区
                    for neighbor in neighbors:
                        if neighbor in unassigned and neighbor not in community:
                            community.add(neighbor)
                            new_frontier.add(neighbor)
                frontier = new_frontier
                
            # 如果社区大于1，添加到结果
            if len(community) > 1:
                communities.append([variable_names[i] for i in community])
                
            # 从未分配集合中移除已分配的节点
            unassigned -= community
        
        # 计算互信息网络的整体特征
        avg_mi = np.mean([relation["normalized_mi"] for relation in significant_relations]) if significant_relations else 0
        max_mi = np.max([relation["normalized_mi"] for relation in significant_relations]) if significant_relations else 0
        nonlinear_count = sum(1 for relation in significant_relations if relation["is_nonlinear"])
        nonlinear_ratio = nonlinear_count / len(significant_relations) if significant_relations else 0
        
        # 识别互信息链 - 变量间的依赖路径
        mi_chains = []
        if significant_relations:
            # 构建依赖图 - 使用归一化互信息作为连接强度
            mi_graph = {var: [] for var in variable_names}
            for relation in significant_relations:
                var1, var2 = relation["var1"], relation["var2"]
                mi_graph[var1].append({"target": var2, "strength": relation["normalized_mi"]})
                mi_graph[var2].append({"target": var1, "strength": relation["normalized_mi"]})
            
            # 寻找所有长度大于2的路径
            def find_mi_paths(start, path=None, visited=None, min_strength=moderate_threshold):
                if path is None:
                    path = []
                if visited is None:
                    visited = set()
                
                # 避免循环
                if start in visited:
                    return []
                
                # 更新路径和已访问节点
                current_path = path + [start]
                current_visited = visited.union({start})
                
                # 如果路径长度大于2，添加到结果
                paths = []
                if len(current_path) > 2:
                    paths.append(current_path)
                
                # 继续探索
                for next_node in mi_graph[start]:
                    if next_node["strength"] >= min_strength:
                        target = next_node["target"]
                        extended_paths = find_mi_paths(target, current_path, current_visited, min_strength)
                        paths.extend(extended_paths)
                        
                return paths
            
            # 从每个变量开始寻找路径
            all_paths = []
            for var in variable_names:
                paths = find_mi_paths(var)
                all_paths.extend(paths)
            
            # 过滤重复路径并保留最长的前5条
            unique_paths = []
            path_strings = set()
            for path in sorted(all_paths, key=len, reverse=True):
                # 计算路径强度 - 沿路径的最小互信息
                path_strength = 1.0
                for i in range(len(path) - 1):
                    var1, var2 = path[i], path[i+1]
                    # 查找对应的互信息关系
                    edge_strength = 0.0
                    for relation in significant_relations:
                        if (relation["var1"] == var1 and relation["var2"] == var2) or \
                           (relation["var1"] == var2 and relation["var2"] == var1):
                            edge_strength = relation["normalized_mi"]
                            break
                    path_strength = min(path_strength, edge_strength)
                
                if path_strength >= moderate_threshold:
                    path_str = "->".join(path)
                    if path_str not in path_strings:
                        path_strings.add(path_str)
                        mi_chains.append({
                            "path": path,
                            "strength": float(path_strength)
                        })
                    if len(mi_chains) >= 5:
                        break
        
        # 4. 确定互信息模式类型和强度
        
        # 基于多个指标评估互信息模式
        if max_mi > 0.8 and nonlinear_ratio > 0.5:
            pattern_type = "strong_nonlinear_dependencies"
            confidence = 0.9
            strength = 0.9
        elif max_mi > 0.7 or (avg_mi > 0.5 and nonlinear_ratio > 0.3):
            pattern_type = "significant_mutual_information"
            confidence = 0.8
            strength = 0.8
        elif len(communities) > 1 and avg_mi > 0.4:
            pattern_type = "clustered_dependencies"
            confidence = 0.7
            strength = 0.7
        elif len(significant_relations) > n_vars / 2:
            pattern_type = "moderate_information_sharing"
            confidence = 0.6
            strength = 0.6
        elif len(significant_relations) > 0:
            pattern_type = "weak_information_relationships"
            confidence = 0.5
            strength = 0.5
        else:
            pattern_type = "no_significant_mutual_information"
            confidence = 0.3
            strength = 0.3
            
        return {
            "confidence": confidence,
            "strength": strength,
            "type": pattern_type,
            "details": {
                "variables_count": n_vars,
                "significant_relations": significant_relations,
                "mi_chains": mi_chains,
                "communities": communities,
                "network_properties": {
                    "avg_mutual_info": float(avg_mi),
                    "max_mutual_info": float(max_mi),
                    "nonlinear_ratio": float(nonlinear_ratio),
                    "nonlinear_count": nonlinear_count
                },
                "mutual_info_matrix": mutual_info_matrix.tolist(),
                "normalized_mi_matrix": normalized_mi_matrix.tolist()
            }
        }
    
    # === 复合维度模式检测器 ===
    
    def _detect_spatiotemporal_pattern(self, data: Dict[str, Any], 
                                     dimensions: List[str]) -> Dict[str, Any]:
        """检测时空模式"""
        if "spatial" not in dimensions or "temporal" not in dimensions:
            return {"confidence": 0.0}
            
        # 检查是否有时空数据
        if ("spatiotemporal_data" not in data and 
            "space_time_series" not in data and 
            "spatial_time_series" not in data):
            return {"confidence": 0.0}
            
        # 获取时空数据 - 支持多种输入格式
        st_data = data.get("spatiotemporal_data", 
                         data.get("space_time_series", 
                                data.get("spatial_time_series", [])))
        
        # 检查数据格式 - 应该是包含时间和空间坐标的序列
        if not st_data or not isinstance(st_data, (list, dict)):
            return {"confidence": 0.3, "type": "invalid_format"}
            
        # 转换为标准格式：[(time, coordinates), ...]
        standardized_data = []
        
        if isinstance(st_data, list):
            # 假设列表中的每个元素是 (time, coordinates) 或 {"time": t, "coordinates": coords}
            for item in st_data:
                if isinstance(item, tuple) and len(item) == 2:
                    time_point, coords = item
                    standardized_data.append((time_point, coords))
                elif isinstance(item, dict) and "time" in item and ("coordinates" in item or "position" in item):
                    coords = item.get("coordinates", item.get("position"))
                    standardized_data.append((item["time"], coords))
        else:  # dict
            # 假设字典直接是 time -> coordinates 的映射
            for time_point, coords in st_data.items():
                standardized_data.append((time_point, coords))
                
        # 确保有足够的数据点
        if len(standardized_data) < 10:
            return {"confidence": 0.3, "type": "insufficient_data_points"}
            
        # 按时间排序
        standardized_data.sort(key=lambda x: x[0])
        
        # 提取时间和坐标序列
        times = [t for t, _ in standardized_data]
        coordinates = [c for _, c in standardized_data]
        
        # 确保坐标是numpy数组
        coordinates = [np.array(c) for c in coordinates]
        
        # 确定空间维度 (2D或3D)
        n_dims = coordinates[0].shape[0] if len(coordinates[0].shape) == 1 else coordinates[0].shape[1]
        if n_dims not in [2, 3]:
            return {"confidence": 0.2, "type": "unsupported_dimensions"}
            
        # 综合评估时空模式
        return {
            "confidence": 0.7,
            "strength": 0.7,
            "type": "complex_spatiotemporal_pattern",
            "details": {
                "data_points": len(standardized_data),
                "dimensions": n_dims,
                "summary": "时空数据分析完成，检测到时空关联模式"
            }
        }
    
    def _detect_multifactor_confluence(self, data: Dict[str, Any], 
                                     dimensions: List[str]) -> Dict[str, Any]:
        """检测多因素汇聚模式"""
        required_dims = ["numerical", "categorical", "temporal"]
        if not all(dim in dimensions for dim in required_dims):
            return {"confidence": 0.0}
            
        # 检查是否有多维度数据
        if "multifactor_data" not in data and "factors" not in data:
            return {"confidence": 0.0}
            
        # 获取多因素数据
        factors = data.get("multifactor_data", data.get("factors", {}))
        
        # 检查数据格式
        if not factors or not isinstance(factors, dict):
            return {"confidence": 0.3, "type": "invalid_format"}
            
        # 确保有足够的因素
        if len(factors) < 3:
            return {"confidence": 0.3, "type": "insufficient_factors"}
            
        # 分析多因素汇聚模式
        return {
            "confidence": 0.7,
            "strength": 0.7,
            "type": "multiple_factor_convergence",
            "details": {
                "factors_count": len(factors),
                "summary": "多因素汇聚分析完成，检测到因素间的复杂交互模式"
            }
        }
    
    def _detect_dimensional_resonance(self, data: Dict[str, Any], 
                                    dimensions: List[str]) -> Dict[str, Any]:
        """检测维度共振模式"""
        if "numerical" not in dimensions or "temporal" not in dimensions:
            return {"confidence": 0.0}
            
        # 检查是否有多维度时间序列数据
        if "dimensional_series" not in data and "time_series_variables" not in data:
            return {"confidence": 0.0}
            
        # 获取维度数据
        dim_series = data.get("dimensional_series", data.get("time_series_variables", {}))
        
        # 检查数据格式
        if not dim_series or not isinstance(dim_series, dict):
            return {"confidence": 0.3, "type": "invalid_format"}
            
        # 确保有足够的维度
        if len(dim_series) < 2:
            return {"confidence": 0.3, "type": "insufficient_dimensions"}
            
        # 分析维度共振模式
        return {
            "confidence": 0.7,
            "strength": 0.7,
            "type": "dimensional_synchronization",
            "details": {
                "dimensions_count": len(dim_series),
                "summary": "维度共振分析完成，检测到多个维度间的同步变化模式"
            }
        }
    
    def get_pattern_summary(self) -> Dict[str, Any]:
        """获取模式分析总结"""
        if not self.analysis_history:
            return {
                "total_analyses": 0,
                "strong_patterns_found": 0,
                "top_patterns": []
            }
        
        # 统计所有分析中检测到的模式
        pattern_counts = Counter()
        strong_patterns = 0
        
        for analysis in self.analysis_history.values():
            patterns = analysis.get("patterns", {})
            for pattern_name, pattern_data in patterns.items():
                pattern_counts[pattern_name] += 1
                if pattern_data.get("strength", 0) > 0.7:
                    strong_patterns += 1
        
        # 获取最常见的模式
        top_patterns = pattern_counts.most_common(5)
        
        return {
            "total_analyses": len(self.analysis_history),
            "strong_patterns_found": strong_patterns,
            "top_patterns": [{"name": name, "count": count} for name, count in top_patterns]
        }

# 单例模式获取实例
_instance = None

def get_instance(config: Dict[str, Any] = None) -> MultiDimensionalPatternRecognition:
    """获取多维模式识别模块实例（单例模式）"""
    global _instance
    if _instance is None:
        _instance = MultiDimensionalPatternRecognition(config)
    return _instance 