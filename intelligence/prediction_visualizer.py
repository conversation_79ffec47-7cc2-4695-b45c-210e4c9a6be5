#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
预测可视化模块 - Prediction Visualizer

本模块负责将预测分析结果以各种直观的可视化方式呈现，
支持多种图表类型、实时更新和交互式展示。

作者: Claude
创建日期: 2024-10-06
版本: 1.0
"""

import os
import time
import json
from utilities.unified_logger import get_unified_logger, setup_unified_logging
import numpy as np
import matplotlib.pyplot as plt
import matplotlib.colors as mcolors
from matplotlib.figure import Figure
from matplotlib.backends.backend_agg import FigureCanvasAgg
from typing import Dict, List, Any, Tuple, Optional, Union
from datetime import datetime, timedelta
import io
from collections import defaultdict

# 配置日志
setup_unified_logging()
logger = get_unified_logger("prediction_visualizer")

class PredictionVisualizer:
    """预测可视化类，提供各种预测结果的可视化方法"""
    
    def __init__(self, config: Dict[str, Any] = None):
        """
        初始化预测可视化模块
        
        Args:
            config: 配置信息
        """
        self.config = config or {}
        
        # 图表样式配置
        self.chart_style = self.config.get("chart_style", "ggplot")  # 使用matplotlib内置样式
        try:
            plt.style.use(self.chart_style)
        except Exception as e:
            logger.warning_status(f"无法设置样式 {self.chart_style}, 使用默认样式。错误: {str(e)}")
            plt.style.use('default')
        
        # 颜色主题
        self.color_theme = self.config.get("color_theme", {
            "primary": "#1f77b4",
            "secondary": "#ff7f0e",
            "success": "#2ca02c",
            "danger": "#d62728",
            "warning": "#bcbd22",
            "info": "#17becf",
            "light": "#7f7f7f",
            "dark": "#333333"
        })
        
        # 图表输出目录
        self.output_dir = self.config.get("output_dir", "data/visualizations")
        os.makedirs(self.output_dir, exist_ok=True)
        
        # 图表默认尺寸
        self.default_figsize = self.config.get("default_figsize", (10, 6))
        
        # DPI配置
        self.dpi = self.config.get("dpi", 100)
        
        # 动画帧率
        self.fps = self.config.get("fps", 5)
        
        # 缓存最近的可视化结果
        self.visualization_cache = {}
        self.max_cache_size = self.config.get("max_cache_size", 20)
        
        logger.success("预测可视化模块初始化完成")
    
    def visualize_prediction_probability(self, prediction_data: Dict[str, Any],
                                        title: str = "故障预测概率", 
                                        output_file: str = None) -> str:
        """
        可视化预测概率
        
        Args:
            prediction_data: 预测数据
            title: 图表标题
            output_file: 输出文件路径
            
        Returns:
            生成的图表文件路径
        """
        # 提取预测概率
        probability = prediction_data.get("probability", 0)
        confidence = prediction_data.get("confidence", 0)
        service_id = prediction_data.get("service_id", "未知服务")
        
        # 创建图形
        fig, ax = plt.subplots(figsize=self.default_figsize)
        
        # 绘制概率计量表
        self._draw_gauge(ax, probability, "预测概率", 
                         cmap="RdYlGn_r", vmin=0, vmax=1)
        
        # 绘制置信度指示器
        confidence_size = 0.4
        confidence_pos = [0.75, 0.5]
        confidence_ax = fig.add_axes([confidence_pos[0], confidence_pos[1], 
                                     confidence_size, confidence_size])
        self._draw_gauge(confidence_ax, confidence, "置信度",
                        cmap="Blues", vmin=0, vmax=1, start_angle=180, end_angle=540)
        
        # 添加标题和服务信息
        timestamp = prediction_data.get("timestamp", time.time())
        dt_str = datetime.fromtimestamp(timestamp).strftime("%Y-%m-%d %H:%M:%S")
        fig.suptitle(f"{title}", fontsize=16)
        fig.text(0.5, 0.95, f"服务: {service_id} | 时间: {dt_str}", 
                ha='center', fontsize=12)
        
        # 添加因子信息
        factors = prediction_data.get("factors", {})
        if factors:
            factor_pos = 0.05
            fig.text(0.05, factor_pos, "影响因子:", fontsize=10, fontweight='bold')
            factor_pos -= 0.025
            
            for factor_name, factor_value in factors.items():
                factor_pos -= 0.025
                color = self._get_color_for_value(factor_value)
                fig.text(0.05, factor_pos, f"{factor_name}: {factor_value:.2f}", 
                        fontsize=9, color=color)
        
        # 调整布局
        plt.tight_layout(rect=[0, 0, 1, 0.9])
        
        # 保存图表
        if not output_file:
            output_file = os.path.join(
                self.output_dir, 
                f"prediction_{service_id}_{int(timestamp)}.png"
            )
        
        plt.savefig(output_file, dpi=self.dpi)
        plt.close(fig)
        
        # 缓存结果
        self._cache_visualization("probability", prediction_data, output_file)
        
        return output_file
    
    def visualize_prediction_history(self, prediction_history: List[Dict[str, Any]],
                                    title: str = "预测历史趋势",
                                    output_file: str = None) -> str:
        """
        可视化预测历史趋势
        
        Args:
            prediction_history: 预测历史数据列表
            title: 图表标题
            output_file: 输出文件路径
            
        Returns:
            生成的图表文件路径
        """
        if not prediction_history:
            logger.warning_status("没有预测历史数据可供可视化")
            return ""
        
        # 提取数据
        timestamps = [entry.get("timestamp", 0) for entry in prediction_history]
        probabilities = [entry.get("probability", 0) for entry in prediction_history]
        confidences = [entry.get("confidence", 0) for entry in prediction_history]
        
        # 转换时间戳为日期时间
        dates = [datetime.fromtimestamp(ts) for ts in timestamps]
        
        # 提取服务ID
        service_id = prediction_history[0].get("service_id", "未知服务")
        
        # 创建图形
        fig, ax1 = plt.subplots(figsize=self.default_figsize)
        
        # 绘制概率线
        color = self.color_theme["primary"]
        ax1.set_xlabel('时间')
        ax1.set_ylabel('预测概率', color=color)
        line1 = ax1.plot(dates, probabilities, 'o-', color=color, label='预测概率')
        ax1.tick_params(axis='y', labelcolor=color)
        ax1.set_ylim(0, 1)
        
        # 使用渐变色填充概率线下方区域
        xy = np.column_stack([np.array(dates), np.array(probabilities)])
        xy = np.vstack([[xy[0, 0], 0], xy, [xy[-1, 0], 0]])
        ax1.add_patch(plt.Polygon(xy, color=color, alpha=0.2))
        
        # 创建第二个Y轴绘制置信度
        ax2 = ax1.twinx()
        color = self.color_theme["secondary"]
        ax2.set_ylabel('置信度', color=color)
        line2 = ax2.plot(dates, confidences, 's--', color=color, label='置信度')
        ax2.tick_params(axis='y', labelcolor=color)
        ax2.set_ylim(0, 1)
        
        # 添加图例
        lines = line1 + line2
        labels = [l.get_label() for l in lines]
        ax1.legend(lines, labels, loc='upper left')
        
        # 添加标题和信息
        fig.suptitle(f"{title}", fontsize=16)
        fig.text(0.5, 0.95, f"服务: {service_id} | 数据点: {len(prediction_history)}", 
                ha='center', fontsize=12)
        
        # 添加高概率区域的背景高亮
        ax1.axhspan(0.7, 1.0, color=self.color_theme["danger"], alpha=0.1, label='高风险区')
        ax1.axhspan(0.4, 0.7, color=self.color_theme["warning"], alpha=0.1, label='中风险区')
        
        # 格式化x轴日期显示
        fig.autofmt_xdate()
        
        # 调整布局
        plt.tight_layout(rect=[0, 0, 1, 0.9])
        
        # 保存图表
        if not output_file:
            output_file = os.path.join(
                self.output_dir, 
                f"prediction_history_{service_id}_{int(time.time())}.png"
            )
        
        plt.savefig(output_file, dpi=self.dpi)
        plt.close(fig)
        
        # 缓存结果
        self._cache_visualization("history", prediction_history, output_file)
        
        return output_file
    
    def visualize_pattern_strength(self, pattern_data: Dict[str, Dict[str, Any]],
                                  title: str = "检测到的模式强度",
                                  output_file: str = None) -> str:
        """
        可视化各种模式的强度
        
        Args:
            pattern_data: 模式数据字典
            title: 图表标题
            output_file: 输出文件路径
            
        Returns:
            生成的图表文件路径
        """
        if not pattern_data:
            logger.warning_status("没有模式数据可供可视化")
            return ""
        
        # 提取模式名称和强度
        pattern_names = []
        pattern_strengths = []
        pattern_confidences = []
        
        for name, data in pattern_data.items():
            pattern_names.append(name)
            pattern_strengths.append(data.get("strength", 0))
            pattern_confidences.append(data.get("confidence", 0))
        
        # 创建图形
        fig, ax = plt.subplots(figsize=self.default_figsize)
        
        # 设置柱状图位置
        x = np.arange(len(pattern_names))
        width = 0.35
        
        # 绘制强度柱状图
        rects1 = ax.bar(x - width/2, pattern_strengths, width, 
                       label='强度', color=self.color_theme["primary"])
        
        # 绘制置信度柱状图
        rects2 = ax.bar(x + width/2, pattern_confidences, width, 
                       label='置信度', color=self.color_theme["secondary"])
        
        # 添加标签和标题
        ax.set_xlabel('模式类型')
        ax.set_ylabel('值')
        ax.set_title(title)
        ax.set_xticks(x)
        ax.set_xticklabels([name.replace('_', ' ').title() for name in pattern_names])
        ax.legend()
        
        # 旋转x轴标签以避免重叠
        plt.setp(ax.get_xticklabels(), rotation=45, ha="right", rotation_mode="anchor")
        
        # 为每个柱添加数值标签
        self._add_labels_to_bars(ax, rects1)
        self._add_labels_to_bars(ax, rects2)
        
        # 调整布局
        plt.tight_layout()
        
        # 保存图表
        if not output_file:
            output_file = os.path.join(
                self.output_dir, 
                f"pattern_strength_{int(time.time())}.png"
            )
        
        plt.savefig(output_file, dpi=self.dpi)
        plt.close(fig)
        
        # 缓存结果
        self._cache_visualization("pattern", pattern_data, output_file)
        
        return output_file
    
    def visualize_system_risk_heatmap(self, service_predictions: Dict[str, Dict[str, Any]],
                                     title: str = "系统风险热图",
                                     output_file: str = None) -> str:
        """
        可视化系统各服务的风险热图
        
        Args:
            service_predictions: 各服务的预测数据
            title: 图表标题
            output_file: 输出文件路径
            
        Returns:
            生成的图表文件路径
        """
        if not service_predictions:
            logger.warning_status("没有服务预测数据可供可视化")
            return ""
        
        # 提取服务ID和预测概率
        service_ids = []
        probabilities = []
        confidences = []
        
        for service_id, prediction in service_predictions.items():
            service_ids.append(service_id)
            probabilities.append(prediction.get("probability", 0))
            confidences.append(prediction.get("confidence", 0))
        
        # 创建图形
        fig, ax = plt.subplots(figsize=self.default_figsize)
        
        # 创建热图数据矩阵
        data = np.zeros((len(service_ids), 1))
        for i, prob in enumerate(probabilities):
            data[i, 0] = prob
        
        # 绘制热图
        cmap = plt.cm.get_cmap('RdYlGn_r')
        im = ax.imshow(data, cmap=cmap, aspect='auto', vmin=0, vmax=1)
        
        # 添加服务ID标签
        ax.set_yticks(np.arange(len(service_ids)))
        ax.set_yticklabels(service_ids)
        
        # 添加颜色条
        cbar = ax.figure.colorbar(im, ax=ax)
        cbar.ax.set_ylabel("故障概率", rotation=-90, va="bottom")
        
        # 在每个单元格中添加概率值
        for i in range(len(service_ids)):
            text_color = "white" if probabilities[i] > 0.5 else "black"
            ax.text(0, i, f"{probabilities[i]:.2f}", 
                   ha="center", va="center", color=text_color, fontweight="bold")
            
        # 添加标题
        plt.title(title)
        
        # 调整布局
        plt.tight_layout()
        
        # 保存图表
        if not output_file:
            output_file = os.path.join(
                self.output_dir, 
                f"risk_heatmap_{int(time.time())}.png"
            )
        
        plt.savefig(output_file, dpi=self.dpi)
        plt.close(fig)
        
        # 缓存结果
        self._cache_visualization("heatmap", service_predictions, output_file)
        
        return output_file
    
    def visualize_correlation_network(self, correlations: Dict[str, List[Tuple[str, float]]],
                                     title: str = "服务相关性网络",
                                     output_file: str = None) -> str:
        """
        可视化服务间相关性网络
        
        Args:
            correlations: 服务间相关性数据
            title: 图表标题
            output_file: 输出文件路径
            
        Returns:
            生成的图表文件路径
        """
        if not correlations:
            logger.warning_status("没有相关性数据可供可视化")
            return ""
        
        try:
            import networkx as nx
            
            # 创建图形
            fig, ax = plt.subplots(figsize=self.default_figsize)
            
            # 创建有向图
            G = nx.DiGraph()
            
            # 添加节点和边
            for source, targets in correlations.items():
                G.add_node(source)
                for target, weight in targets:
                    if weight > 0.1:  # 仅添加权重大于阈值的边
                        G.add_edge(source, target, weight=weight)
            
            # 计算节点位置
            pos = nx.spring_layout(G, seed=42)
            
            # 获取边权重用于设置边的宽度和颜色
            edges = G.edges()
            weights = [G[u][v]['weight'] * 3 for u, v in edges]
            
            # 绘制节点
            nx.draw_networkx_nodes(G, pos, node_size=500, 
                                 node_color=self.color_theme["primary"], alpha=0.8)
            
            # 绘制边
            edge_colors = [self._get_color_for_value(w/3) for w in weights]
            nx.draw_networkx_edges(G, pos, width=weights, alpha=0.7, 
                                  edge_color=edge_colors, arrows=True, 
                                  arrowsize=10, arrowstyle='->')
            
            # 绘制标签
            nx.draw_networkx_labels(G, pos, font_size=10, font_family='sans-serif')
            
            # 添加边权重标签
            edge_labels = {(u, v): f"{G[u][v]['weight']:.2f}" for u, v in G.edges()}
            nx.draw_networkx_edge_labels(G, pos, edge_labels=edge_labels, font_size=8)
            
            # 添加标题
            plt.title(title)
            plt.axis('off')
            
            # 调整布局
            plt.tight_layout()
            
            # 保存图表
            if not output_file:
                output_file = os.path.join(
                    self.output_dir, 
                    f"correlation_network_{int(time.time())}.png"
                )
            
            plt.savefig(output_file, dpi=self.dpi)
            plt.close(fig)
            
            # 缓存结果
            self._cache_visualization("network", correlations, output_file)
            
            return output_file
        except ImportError:
            logger.error_status("绘制相关性网络图需要安装networkx库")
            return ""
    
    def visualize_multi_service_predictions(self, service_predictions: Dict[str, List[Dict[str, Any]]],
                                          title: str = "多服务预测比较",
                                          output_file: str = None) -> str:
        """
        可视化多个服务的预测历史比较
        
        Args:
            service_predictions: 服务ID到预测历史的映射
            title: 图表标题
            output_file: 输出文件路径
            
        Returns:
            生成的图表文件路径
        """
        if not service_predictions:
            logger.warning_status("没有服务预测数据可供可视化")
            return ""
        
        # 创建图形
        fig, ax = plt.subplots(figsize=self.default_figsize)
        
        # 使用不同颜色绘制各服务的预测趋势
        colors = list(mcolors.TABLEAU_COLORS)
        color_idx = 0
        
        for service_id, predictions in service_predictions.items():
            if not predictions:
                continue
                
            # 提取数据
            timestamps = [entry.get("timestamp", 0) for entry in predictions]
            probabilities = [entry.get("probability", 0) for entry in predictions]
            
            # 转换时间戳为日期时间
            dates = [datetime.fromtimestamp(ts) for ts in timestamps]
            
            # 选择颜色
            color = colors[color_idx % len(colors)]
            color_idx += 1
            
            # 绘制预测线
            ax.plot(dates, probabilities, 'o-', color=color, label=service_id)
        
        # 添加标签和标题
        ax.set_xlabel('时间')
        ax.set_ylabel('预测故障概率')
        ax.set_title(title)
        ax.set_ylim(0, 1)
        ax.legend(loc='upper left')
        
        # 添加高概率区域的背景高亮
        ax.axhspan(0.7, 1.0, color=self.color_theme["danger"], alpha=0.1, label='高风险区')
        ax.axhspan(0.4, 0.7, color=self.color_theme["warning"], alpha=0.1, label='中风险区')
        
        # 格式化x轴日期显示
        fig.autofmt_xdate()
        
        # 调整布局
        plt.tight_layout()
        
        # 保存图表
        if not output_file:
            output_file = os.path.join(
                self.output_dir, 
                f"multi_service_predictions_{int(time.time())}.png"
            )
        
        plt.savefig(output_file, dpi=self.dpi)
        plt.close(fig)
        
        # 缓存结果
        self._cache_visualization("multi_service", service_predictions, output_file)
        
        return output_file
    
    def _draw_gauge(self, ax, value, label, cmap='RdYlGn_r', vmin=0, vmax=1, 
                   start_angle=0, end_angle=180):
        """绘制计量表"""
        # 设置角度范围
        theta = np.linspace(np.radians(start_angle), np.radians(end_angle), 100)
        
        # 计算圆弧点坐标
        r = 0.8
        x = r * np.cos(theta)
        y = r * np.sin(theta)
        
        # 绘制底环
        ax.plot(x, y, color='gray', linewidth=10, alpha=0.3)
        
        # 为计量表获取颜色映射
        cmap = plt.cm.get_cmap(cmap)
        norm = plt.Normalize(vmin, vmax)
        
        # 绘制表盘的填充部分
        value_angle = start_angle + (end_angle - start_angle) * value / (vmax - vmin)
        value_theta = np.linspace(np.radians(start_angle), np.radians(value_angle), 100)
        x_value = r * np.cos(value_theta)
        y_value = r * np.sin(value_theta)
        
        # 使用渐变色绘制
        points = np.array([x_value, y_value]).T.reshape(-1, 1, 2)
        segments = np.concatenate([points[:-1], points[1:]], axis=1)
        
        lc = plt.matplotlib.collections.LineCollection(
            segments, colors=[cmap(norm(value))], linewidth=10)
        ax.add_collection(lc)
        
        # 添加指针
        pointer_length = 0.7 * r
        pointer_angle = np.radians(value_angle)
        ax.arrow(0, 0, pointer_length * np.cos(pointer_angle), 
                pointer_length * np.sin(pointer_angle),
                head_width=0.05, head_length=0.1, fc=cmap(norm(value)), ec=cmap(norm(value)))
        
        # 添加中心点
        ax.scatter(0, 0, color='white', s=100, zorder=10, edgecolor='gray')
        
        # 添加刻度标签
        n_ticks = 5
        tick_angles = np.linspace(start_angle, end_angle, n_ticks)
        tick_values = np.linspace(vmin, vmax, n_ticks)
        
        for angle, val in zip(tick_angles, tick_values):
            theta_rad = np.radians(angle)
            tick_x = 1.1 * r * np.cos(theta_rad)
            tick_y = 1.1 * r * np.sin(theta_rad)
            ax.text(tick_x, tick_y, f"{val:.1f}", 
                   ha='center', va='center', fontsize=9)
        
        # 添加标签
        ax.text(0, -0.2, f"{label}: {value:.2f}", 
               ha='center', va='center', fontsize=12, fontweight='bold')
        
        # 设置图表属性
        ax.set_xlim(-1.2, 1.2)
        ax.set_ylim(-0.5, 1.2)
        ax.axis('off')
        ax.set_aspect('equal')
    
    def _add_labels_to_bars(self, ax, rects):
        """为柱状图添加数值标签"""
        for rect in rects:
            height = rect.get_height()
            ax.annotate(f'{height:.2f}',
                       xy=(rect.get_x() + rect.get_width() / 2, height),
                       xytext=(0, 3),  # 3点垂直偏移
                       textcoords="offset points",
                       ha='center', va='bottom', fontsize=8)
    
    def _get_color_for_value(self, value: float) -> str:
        """根据值获取对应的颜色"""
        if value >= 0.7:
            return self.color_theme["danger"]
        elif value >= 0.4:
            return self.color_theme["warning"]
        else:
            return self.color_theme["success"]
    
    def _cache_visualization(self, viz_type: str, data: Any, file_path: str):
        """缓存可视化结果"""
        cache_key = f"{viz_type}_{hash(str(data))}"
        self.visualization_cache[cache_key] = {
            "type": viz_type,
            "data": data,
            "file_path": file_path,
            "timestamp": time.time()
        }
        
        # 限制缓存大小
        if len(self.visualization_cache) > self.max_cache_size:
            oldest_key = min(self.visualization_cache.items(), 
                           key=lambda x: x[1]["timestamp"])[0]
            del self.visualization_cache[oldest_key]
    
    def get_cached_visualization(self, viz_type: str, data: Any) -> Optional[str]:
        """从缓存获取可视化结果"""
        cache_key = f"{viz_type}_{hash(str(data))}"
        cached = self.visualization_cache.get(cache_key)
        if cached:
            return cached["file_path"]
        return None
    
    def get_visualization_as_bytes(self, fig: Figure) -> bytes:
        """将图表转换为字节流"""
        buf = io.BytesIO()
        canvas = FigureCanvasAgg(fig)
        canvas.print_png(buf)
        buf.seek(0)
        return buf.getvalue()

# 单例模式获取实例
_instance = None

def get_instance(config: Dict[str, Any] = None) -> PredictionVisualizer:
    """获取预测可视化模块实例（单例模式）"""
    global _instance
    if _instance is None:
        _instance = PredictionVisualizer(config)
    return _instance 