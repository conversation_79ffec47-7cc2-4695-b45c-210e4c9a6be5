#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
多维度模式识别模块 - 增强预测性故障检测功能

本模块实现了更复杂的错误模式识别算法，支持多维度数据分析，
能够识别更复杂的错误模式和相关性，提高预测性故障检测的准确性。

作者: Claude
创建日期: 2024-10-04
"""

import os
import time
import json
from utilities.unified_logger import get_unified_logger, setup_unified_logging
import numpy as np
from typing import Dict, List, Any, Tuple, Optional, Set
from datetime import datetime, timedelta
from collections import defaultdict

# 配置日志
setup_unified_logging()
logger = get_unified_logger("multidimensional_pattern_recognition")

class MultidimensionalPatternRecognition:
    """多维度模式识别类，提供增强的错误模式分析能力"""
    
    def __init__(self, config: Dict[str, Any] = None):
        """
        初始化多维度模式识别模块
        
        Args:
            config: 配置信息
        """
        self.config = config or {}
        
        # 数据存储目录
        self.data_dir = self.config.get("data_dir", "data/pattern_recognition")
        os.makedirs(self.data_dir, exist_ok=True)
        
        # 多维度错误模式库
        self.pattern_detectors = {
            "correlated_services": self._detect_correlated_services_pattern,
            "error_chain": self._detect_error_chain_pattern,
            "attribute_clustering": self._detect_attribute_clustering,
            "temporal_density": self._detect_temporal_density_pattern,
            "severity_escalation": self._detect_severity_escalation_pattern,
            "context_similarity": self._detect_context_similarity_pattern
        }
        
        # 模式识别结果缓存
        self.recognition_results_cache = {}
        
        logger.success("多维度模式识别模块初始化完成")
    
    def analyze_multidimensional_patterns(self, service_errors: Dict[str, List[Dict[str, Any]]]) -> Dict[str, Any]:
        """
        分析多个服务的错误数据，识别跨服务的复杂模式
        
        Args:
            service_errors: 服务ID到错误列表的映射
            
        Returns:
            多维度错误模式分析结果
        """
        # 首先检查数据是否足够
        valid_services = {svc_id: errors for svc_id, errors in service_errors.items() if len(errors) >= 3}
        if len(valid_services) < 2:
            return {"patterns": {}, "confidence": 0.1}
        
        # 分析各种多维度错误模式
        patterns = {}
        for pattern_name, detector_func in self.pattern_detectors.items():
            pattern_result = detector_func(valid_services)
            if pattern_result["probability"] > 0.15:  # 只记录概率较高的模式
                patterns[pattern_name] = pattern_result
        
        # 计算综合可信度
        confidence = self._calculate_overall_confidence(patterns)
        
        # 准备结果
        result = {
            "timestamp": time.time(),
            "patterns": patterns,
            "confidence": confidence,
            "services_analyzed": list(valid_services.keys()),
            "total_errors": sum(len(errors) for errors in valid_services.values())
        }
        
        # 更新缓存
        cache_key = "-".join(sorted(valid_services.keys()))
        self.recognition_results_cache[cache_key] = result
        
        return result
    
    def get_service_correlations(self, service_errors: Dict[str, List[Dict[str, Any]]]) -> Dict[str, List[str]]:
        """
        分析服务间错误相关性
        
        Args:
            service_errors: 服务ID到错误列表的映射
            
        Returns:
            服务ID到相关服务列表的映射
        """
        correlations = {}
        
        # 识别相关服务
        pattern_result = self._detect_correlated_services_pattern(service_errors)
        service_correlations = pattern_result.get("details", {}).get("correlations", {})
        
        # 整理结果格式
        for service_id in service_errors.keys():
            correlations[service_id] = [
                related_svc for related_svc, score in 
                sorted(service_correlations.get(service_id, {}).items(), key=lambda x: x[1], reverse=True)
                if score >= 0.3  # 只保留相关性较强的服务
            ]
        
        return correlations
    
    def identify_error_chains(self, service_errors: Dict[str, List[Dict[str, Any]]]) -> List[Dict[str, Any]]:
        """
        识别错误链（一系列相关联的错误序列）
        
        Args:
            service_errors: 服务ID到错误列表的映射
            
        Returns:
            错误链列表
        """
        # 分析错误链模式
        pattern_result = self._detect_error_chain_pattern(service_errors)
        
        # 提取识别到的错误链
        error_chains = pattern_result.get("details", {}).get("chains", [])
        
        return error_chains
    
    def _calculate_overall_confidence(self, patterns: Dict[str, Dict[str, Any]]) -> float:
        """计算综合模式识别可信度"""
        if not patterns:
            return 0.1
        
        # 结合概率和每个模式的可信度
        weighted_confidence = sum(
            pattern["probability"] * pattern.get("confidence", 0.5)
            for pattern in patterns.values()
        )
        
        # 归一化
        return min(weighted_confidence / len(patterns), 1.0)
    
    def _detect_correlated_services_pattern(self, service_errors: Dict[str, List[Dict[str, Any]]]) -> Dict[str, Any]:
        """
        检测服务间错误相关性模式
        
        分析多个服务的错误时间分布，识别错误发生时间相近的服务组
        """
        # 需要至少两个服务才能分析相关性
        if len(service_errors) < 2:
            return {"probability": 0.0, "confidence": 0.0, "details": {}}
        
        # 计算服务间的错误时间相关性
        correlations = defaultdict(dict)
        services = list(service_errors.keys())
        
        for i, service1 in enumerate(services):
            for service2 in services[i+1:]:
                # 提取错误时间戳
                timestamps1 = [e["timestamp"] for e in service_errors[service1]]
                timestamps2 = [e["timestamp"] for e in service_errors[service2]]
                
                # 计算时间相关性
                correlation = self._calculate_temporal_correlation(timestamps1, timestamps2)
                
                if correlation > 0:
                    correlations[service1][service2] = correlation
                    correlations[service2][service1] = correlation
        
        # 确定是否存在相关性
        correlation_values = [
            score for svc_corr in correlations.values() 
            for score in svc_corr.values()
        ]
        
        avg_correlation = np.mean(correlation_values) if correlation_values else 0
        max_correlation = max(correlation_values) if correlation_values else 0
        
        # 判断相关性强度
        probability = max_correlation * 0.7 + avg_correlation * 0.3
        
        return {
            "probability": probability,
            "confidence": 0.6 + avg_correlation * 0.4,
            "details": {
                "correlations": dict(correlations),
                "max_correlation": max_correlation,
                "avg_correlation": avg_correlation
            }
        }
    
    def _detect_error_chain_pattern(self, service_errors: Dict[str, List[Dict[str, Any]]]) -> Dict[str, Any]:
        """
        检测错误链模式
        
        分析多个服务的错误序列，识别可能的因果关系链
        """
        # 合并所有服务的错误，并按时间排序
        all_errors = []
        for service_id, errors in service_errors.items():
            for error in errors:
                all_errors.append({
                    "service_id": service_id,
                    "timestamp": error["timestamp"],
                    "data": error.get("data", {})
                })
        
        all_errors.sort(key=lambda e: e["timestamp"])
        
        # 如果错误数量太少，无法形成链
        if len(all_errors) < 4:
            return {"probability": 0.0, "confidence": 0.0, "details": {}}
        
        # 识别可能的错误链
        chains = []
        
        # 时间窗口内的错误视为可能的链
        current_chain = []
        for error in all_errors:
            if not current_chain or error["timestamp"] - current_chain[-1]["timestamp"] < 300:  # 5分钟内
                current_chain.append(error)
            else:
                if len(current_chain) >= 3:  # 至少3个错误形成链
                    chains.append(current_chain)
                current_chain = [error]
        
        # 处理最后一个链
        if len(current_chain) >= 3:
            chains.append(current_chain)
        
        # 计算链的强度
        chain_scores = []
        for chain in chains:
            # 计算服务多样性
            services_in_chain = set(error["service_id"] for error in chain)
            service_diversity = len(services_in_chain) / len(service_errors)
            
            # 计算时间紧凑性
            time_span = chain[-1]["timestamp"] - chain[0]["timestamp"]
            time_compactness = min(1.0, 300 / (time_span + 1))  # 规范化，最高1.0
            
            chain_scores.append(service_diversity * 0.6 + time_compactness * 0.4)
        
        # 判断模式强度
        if not chain_scores:
            return {"probability": 0.0, "confidence": 0.0, "details": {}}
        
        probability = max(chain_scores) * 0.8
        
        return {
            "probability": probability,
            "confidence": 0.5 + min(len(chains) / 5, 0.5),  # 链越多，可信度越高，但最高1.0
            "details": {
                "chains": [{
                    "services": list(set(error["service_id"] for error in chain)),
                    "start_time": chain[0]["timestamp"],
                    "end_time": chain[-1]["timestamp"],
                    "length": len(chain),
                    "score": score
                } for chain, score in zip(chains, chain_scores)]
            }
        }
    
    def _detect_attribute_clustering(self, service_errors: Dict[str, List[Dict[str, Any]]]) -> Dict[str, Any]:
        """
        检测属性聚类模式
        
        分析错误中的属性值，识别特定属性组合的聚类
        """
        # 提取所有错误属性
        all_attributes = {}
        attribute_counts = defaultdict(int)
        
        for service_id, errors in service_errors.items():
            for error in errors:
                error_data = error.get("data", {})
                for key, value in error_data.items():
                    attribute = f"{key}:{value}"
                    all_attributes[attribute] = all_attributes.get(attribute, 0) + 1
                    attribute_counts[key] += 1
        
        # 如果属性太少，无法形成聚类
        if len(all_attributes) < 3:
            return {"probability": 0.0, "confidence": 0.0, "details": {}}
        
        # 找出频繁属性
        total_errors = sum(len(errors) for errors in service_errors.values())
        frequent_attributes = {
            attr: count for attr, count in all_attributes.items()
            if count > total_errors * 0.2  # 出现在至少20%的错误中
        }
        
        # 计算聚类强度
        if not frequent_attributes:
            return {"probability": 0.0, "confidence": 0.0, "details": {}}
        
        # 计算属性聚类概率
        cluster_ratio = len(frequent_attributes) / len(all_attributes)
        cluster_coverage = sum(frequent_attributes.values()) / total_errors
        
        probability = cluster_ratio * 0.3 + cluster_coverage * 0.7
        
        return {
            "probability": probability,
            "confidence": 0.5 + min(cluster_coverage, 0.5),
            "details": {
                "frequent_attributes": frequent_attributes,
                "cluster_ratio": cluster_ratio,
                "cluster_coverage": cluster_coverage
            }
        }
    
    def _detect_temporal_density_pattern(self, service_errors: Dict[str, List[Dict[str, Any]]]) -> Dict[str, Any]:
        """
        检测时间密度模式
        
        分析错误的时间分布密度，识别错误高发时段
        """
        # 合并所有服务的错误时间戳
        all_timestamps = []
        for errors in service_errors.values():
            all_timestamps.extend(error["timestamp"] for error in errors)
        
        if len(all_timestamps) < 5:
            return {"probability": 0.0, "confidence": 0.0, "details": {}}
        
        # 对时间戳进行排序
        all_timestamps.sort()
        
        # 计算时间窗口内的错误密度
        window_size = 600  # 10分钟窗口
        densities = []
        
        for i in range(len(all_timestamps)):
            window_start = all_timestamps[i]
            window_end = window_start + window_size
            
            # 计算窗口内的错误数
            errors_in_window = sum(1 for t in all_timestamps if window_start <= t <= window_end)
            
            densities.append(errors_in_window)
        
        if not densities:
            return {"probability": 0.0, "confidence": 0.0, "details": {}}
        
        # 计算密度指标
        max_density = max(densities)
        avg_density = sum(densities) / len(densities)
        density_ratio = max_density / avg_density if avg_density > 0 else 1.0
        
        # 判断密度是否形成模式
        probability = min((density_ratio - 1) / 3, 0.9)  # 规范化，最高0.9
        probability = max(probability, 0.0)  # 确保非负
        
        return {
            "probability": probability,
            "confidence": 0.4 + min(max_density / 10, 0.6),  # 密度越高，可信度越高，但最高1.0
            "details": {
                "max_density": max_density,
                "avg_density": avg_density,
                "density_ratio": density_ratio
            }
        }
    
    def _detect_severity_escalation_pattern(self, service_errors: Dict[str, List[Dict[str, Any]]]) -> Dict[str, Any]:
        """
        检测严重度升级模式
        
        分析错误严重度的时间变化，识别严重度不断升高的模式
        """
        # 严重度权重
        severity_weights = {
            "info": 1,
            "warning": 2,
            "error": 3,
            "critical": 4
        }
        
        # 分析每个服务的严重度变化
        escalation_scores = []
        
        for service_id, errors in service_errors.items():
            if len(errors) < 3:
                continue
            
            # 按时间排序
            sorted_errors = sorted(errors, key=lambda e: e["timestamp"])
            
            # 提取严重度序列
            severity_sequence = []
            for error in sorted_errors:
                severity = error.get("data", {}).get("severity", "info")
                weight = severity_weights.get(severity, 1)
                severity_sequence.append(weight)
            
            # 计算严重度变化趋势
            changes = [severity_sequence[i] - severity_sequence[i-1] for i in range(1, len(severity_sequence))]
            
            # 计算升级分数
            positive_changes = sum(1 for c in changes if c > 0)
            escalation_ratio = positive_changes / len(changes) if changes else 0
            
            if escalation_ratio > 0.5:  # 超过一半是升级
                escalation_scores.append(escalation_ratio)
        
        # 如果没有服务显示升级模式
        if not escalation_scores:
            return {"probability": 0.0, "confidence": 0.0, "details": {}}
        
        # 计算综合升级概率
        max_escalation = max(escalation_scores)
        avg_escalation = sum(escalation_scores) / len(escalation_scores)
        
        probability = max_escalation * 0.7 + avg_escalation * 0.3
        
        return {
            "probability": probability,
            "confidence": 0.4 + min(len(escalation_scores) / len(service_errors), 0.6),
            "details": {
                "max_escalation": max_escalation,
                "avg_escalation": avg_escalation,
                "services_with_escalation": len(escalation_scores)
            }
        }
    
    def _detect_context_similarity_pattern(self, service_errors: Dict[str, List[Dict[str, Any]]]) -> Dict[str, Any]:
        """
        检测上下文相似性模式
        
        分析错误上下文信息的相似度，识别共同上下文因素
        """
        # 提取所有错误上下文
        contexts = []
        for service_id, errors in service_errors.items():
            for error in errors:
                context = error.get("data", {}).get("context", {})
                if context:
                    contexts.append(context)
        
        # 如果上下文信息太少
        if len(contexts) < 3:
            return {"probability": 0.0, "confidence": 0.0, "details": {}}
        
        # 分析上下文键的共现频率
        context_keys = set()
        for context in contexts:
            context_keys.update(context.keys())
        
        # 计算每个键的出现频率
        key_frequencies = {}
        for key in context_keys:
            frequency = sum(1 for context in contexts if key in context) / len(contexts)
            key_frequencies[key] = frequency
        
        # 找出高频键
        common_keys = {k: v for k, v in key_frequencies.items() if v >= 0.5}
        
        # 如果没有共同上下文
        if not common_keys:
            return {"probability": 0.0, "confidence": 0.0, "details": {}}
        
        # 计算上下文相似度
        key_similarity = sum(common_keys.values()) / len(common_keys)
        
        # 分析共同键的值相似度
        value_similarities = []
        for key in common_keys:
            # 收集此键的所有值
            values = [context.get(key) for context in contexts if key in context]
            unique_values = set(str(v) for v in values if v is not None)
            
            # 计算值的多样性
            value_similarity = 1.0 if len(unique_values) <= 1 else 1.0 / len(unique_values)
            value_similarities.append(value_similarity)
        
        avg_value_similarity = sum(value_similarities) / len(value_similarities) if value_similarities else 0
        
        # 计算综合相似度
        similarity = key_similarity * 0.6 + avg_value_similarity * 0.4
        
        return {
            "probability": similarity,
            "confidence": 0.5 + min(len(common_keys) / 5, 0.5),
            "details": {
                "common_keys": common_keys,
                "key_similarity": key_similarity,
                "value_similarity": avg_value_similarity
            }
        }
    
    def _calculate_temporal_correlation(self, timestamps1: List[float], timestamps2: List[float], 
                                       window: float = 300) -> float:
        """
        计算两个时间序列的相关性
        
        Args:
            timestamps1: 第一个时间戳列表
            timestamps2: 第二个时间戳列表
            window: 相关窗口大小(秒)
            
        Returns:
            相关性分数(0-1)
        """
        if not timestamps1 or not timestamps2:
            return 0.0
        
        # 对时间戳排序
        timestamps1 = sorted(timestamps1)
        timestamps2 = sorted(timestamps2)
        
        # 计算相近时间戳的数量
        correlations = 0
        for t1 in timestamps1:
            for t2 in timestamps2:
                if abs(t1 - t2) <= window:
                    correlations += 1
                    break
        
        # 计算相关性比例
        correlation = correlations / len(timestamps1)
        
        return correlation 