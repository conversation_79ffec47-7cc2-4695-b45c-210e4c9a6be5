#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
预测性分析模块 - 提供高级预测性故障检测和模式识别能力

本模块实现了更复杂的故障预测算法，支持时间序列分析、模式识别和趋势预测，
是韧性自愈系统(Resilience)预测能力的高级扩展。

作者: Claude
创建日期: 2024-09-23
"""

import os
import time
import json
from utilities.unified_logger import get_unified_logger, setup_unified_logging
import numpy as np
from typing import Dict, List, Any, Tuple, Optional
from datetime import datetime, timedelta
import random

# 导入多维度模式识别模块
from intelligence.multidimensional_pattern_recognition import MultidimensionalPatternRecognition

# 配置日志
setup_unified_logging()
logger = get_unified_logger("predictive_analytics")

# 单例模式
_instance = None

def get_instance(config: Dict[str, Any] = None) -> 'PredictiveAnalytics':
    """
    获取预测性分析模块实例（单例模式）
    
    Args:
        config: 配置信息
        
    Returns:
        预测性分析模块实例
    """
    global _instance
    if _instance is None:
        _instance = PredictiveAnalytics(config)
    return _instance

class PredictiveAnalytics:
    """预测性分析模块，提供高级故障预测和模式识别能力"""
    
    def __init__(self, config: Dict[str, Any] = None):
        """
        初始化预测性分析模块
        
        Args:
            config: 配置信息
        """
        self.config = config or {}
        
        # 数据存储目录
        self.data_dir = self.config.get("data_dir", "data/analytics")
        os.makedirs(self.data_dir, exist_ok=True)
        
        # 错误模式库
        self.error_patterns = {
            "cyclic": self._detect_cyclic_pattern,
            "cascade": self._detect_cascade_pattern,
            "gradual": self._detect_gradual_pattern,
            "burst": self._detect_burst_pattern,
            "time_dependent": self._detect_time_dependent_pattern,
            "oscillating": self._detect_oscillating_pattern
        }
        
        # 服务错误历史缓存
        self.error_history_cache = {}
        
        # 预测结果历史
        self.prediction_history = {}
        
        # 多维度模式识别
        self.multidimensional_recognition = MultidimensionalPatternRecognition(config)
        
        logger.success("预测性分析模块初始化完成")
    
    def analyze_error_patterns(self, service_id: str, errors: List[Dict[str, Any]]) -> Dict[str, float]:
        """
        分析服务错误模式
        
        Args:
            service_id: 服务ID
            errors: 错误历史列表
            
        Returns:
            检测到的错误模式及其概率字典
        """
        # 更新缓存
        self.error_history_cache[service_id] = errors
        
        # 如果错误数量不足，返回空结果
        if len(errors) < 3:
            return {}
        
        # 检测各种错误模式
        patterns = {}
        for pattern_name, detector_func in self.error_patterns.items():
            probability = detector_func(errors)
            if probability > 0.1:  # 只记录概率大于0.1的模式
                patterns[pattern_name] = probability
        
        # 记录分析结果
        self._save_pattern_analysis(service_id, patterns)
        
        return patterns
    
    def analyze_multidimensional_patterns(self, service_errors: Dict[str, List[Dict[str, Any]]]) -> Dict[str, Any]:
        """
        分析多个服务的错误数据，识别跨服务的复杂模式
        
        Args:
            service_errors: 服务ID到错误列表的映射
            
        Returns:
            多维度错误模式分析结果
        """
        # 使用多维度模式识别模块进行分析
        return self.multidimensional_recognition.analyze_multidimensional_patterns(service_errors)
    
    def get_service_correlations(self, service_errors: Dict[str, List[Dict[str, Any]]]) -> Dict[str, List[str]]:
        """
        分析服务间错误相关性
        
        Args:
            service_errors: 服务ID到错误列表的映射
            
        Returns:
            服务ID到相关服务列表的映射
        """
        return self.multidimensional_recognition.get_service_correlations(service_errors)
    
    def identify_error_chains(self, service_errors: Dict[str, List[Dict[str, Any]]]) -> List[Dict[str, Any]]:
        """
        识别错误链（一系列相关联的错误序列）
        
        Args:
            service_errors: 服务ID到错误列表的映射
            
        Returns:
            错误链列表
        """
        return self.multidimensional_recognition.identify_error_chains(service_errors)
    
    def predict_failure_probability(self, service_id: str, errors: List[Dict[str, Any]], 
                                   time_horizon: int = 3600) -> Dict[str, Any]:
        """
        预测未来故障概率
        
        Args:
            service_id: 服务ID
            errors: 错误历史列表
            time_horizon: 预测时间范围(秒)，默认1小时
            
        Returns:
            预测结果字典
        """
        # 如果错误数量不足，返回低概率
        if len(errors) < 3:
            return {
                "probability": 0.1,
                "confidence": 0.2,
                "horizon": time_horizon,
                "factors": {},
                "patterns": {}
            }
        
        # 分析错误模式
        patterns = self.analyze_error_patterns(service_id, errors)
        
        # 如果有多服务错误数据，进行多维度模式分析
        multidimensional_patterns = {}
        multidimensional_factor = 0.0
        
        if service_id in self.error_history_cache and len(self.error_history_cache) > 1:
            # 多维度分析
            md_result = self.analyze_multidimensional_patterns(self.error_history_cache)
            if md_result and md_result.get("patterns"):
                multidimensional_patterns = md_result.get("patterns", {})
                # 取最高概率作为因子
                multidimensional_factor = max(
                    [p.get("probability", 0) for p in multidimensional_patterns.values()], 
                    default=0.0
                )
        
        # 计算故障概率
        # 1. 基于错误频率
        now = time.time()
        recent_errors = [e for e in errors if e["timestamp"] > now - 3600]
        frequency_factor = min(len(recent_errors) / 10.0, 1.0)
        
        # 2. 基于错误严重度
        severity_factor = self._calculate_severity_factor(errors)
        
        # 3. 基于错误模式
        pattern_factor = max(patterns.values()) if patterns else 0.0
        
        # 4. 基于历史准确率调整
        history_factor = self._calculate_history_factor(service_id)
        
        # 计算加权概率
        weights = {
            "frequency": 0.2,
            "severity": 0.25,
            "pattern": 0.25,
            "multidimensional": 0.2,
            "history": 0.1
        }
        
        factors = {
            "frequency": frequency_factor,
            "severity": severity_factor,
            "pattern": pattern_factor,
            "multidimensional": multidimensional_factor,
            "history": history_factor
        }
        
        probability = sum(factor * weights[name] for name, factor in factors.items())
        
        # 计算置信度
        confidence = self._calculate_prediction_confidence(errors, patterns, multidimensional_patterns)
        
        # 准备预测结果
        prediction = {
            "timestamp": now,
            "service_id": service_id,
            "probability": probability,
            "confidence": confidence,
            "horizon": time_horizon,
            "factors": factors,
            "patterns": patterns,
            "multidimensional_patterns": {k: v.get("probability", 0) for k, v in multidimensional_patterns.items()}
        }
        
        # 保存预测历史
        self._save_prediction(service_id, prediction)
        
        return prediction
    
    def _calculate_severity_factor(self, errors: List[Dict[str, Any]]) -> float:
        """计算错误严重度因子"""
        severity_weights = {
            "info": 0.1,
            "warning": 0.3,
            "error": 0.7,
            "critical": 1.0
        }
        
        if not errors:
            return 0.0
        
        # 按时间排序，越新的错误权重越高
        sorted_errors = sorted(errors, key=lambda e: e["timestamp"])
        
        # 计算加权严重度
        total_weight = 0.0
        weighted_sum = 0.0
        
        for i, error in enumerate(sorted_errors):
            # 时间权重：新的错误权重高
            time_weight = 0.5 + 0.5 * (i / len(sorted_errors))
            
            # 获取严重度
            severity = error.get("data", {}).get("severity", "warning")
            severity_weight = severity_weights.get(severity, 0.3)
            
            weighted_sum += severity_weight * time_weight
            total_weight += time_weight
        
        return weighted_sum / total_weight if total_weight > 0 else 0.0
    
    def _calculate_history_factor(self, service_id: str) -> float:
        """计算历史准确率调整因子"""
        if service_id not in self.prediction_history:
            return 0.5  # 默认中等因子
        
        # 获取历史预测记录
        history = self.prediction_history[service_id]
        if not history:
            return 0.5
        
        # 使用最新的几条记录评估
        recent_history = history[-5:] if len(history) > 5 else history
        
        # 简单处理：如果历史预测倾向于高概率，提高因子
        avg_probability = sum(pred["probability"] for pred in recent_history) / len(recent_history)
        
        # 映射到0.3-0.7范围
        return 0.3 + avg_probability * 0.4
    
    def _calculate_prediction_confidence(self, errors: List[Dict[str, Any]], 
                                       patterns: Dict[str, float],
                                       multidimensional_patterns: Dict[str, Any] = None) -> float:
        """计算预测结果置信度"""
        # 基础置信度计算
        if not errors:
            return 0.2
        
        # 根据错误数量增加置信度
        error_count_factor = min(len(errors) / 20.0, 0.5)
        
        # 根据模式强度增加置信度
        pattern_strength = max(patterns.values()) if patterns else 0.0
        pattern_factor = pattern_strength * 0.3
        
        # 多维度模式因子
        md_factor = 0.0
        if multidimensional_patterns:
            # 提取多维度模式的置信度
            md_confidences = [p.get("confidence", 0) for p in multidimensional_patterns.values()]
            if md_confidences:
                md_factor = max(md_confidences) * 0.2
        
        # 综合置信度
        confidence = 0.3 + error_count_factor + pattern_factor + md_factor
        
        return min(confidence, 1.0)
    
    def _detect_cyclic_pattern(self, errors: List[Dict[str, Any]]) -> float:
        """检测周期性错误模式"""
        if len(errors) < 5:
            return 0.0
        
        # 提取时间戳并排序
        timestamps = sorted([e["timestamp"] for e in errors])
        
        # 计算时间间隔
        intervals = [timestamps[i+1] - timestamps[i] for i in range(len(timestamps)-1)]
        
        # 检查间隔的一致性
        if not intervals:
            return 0.0
            
        mean_interval = sum(intervals) / len(intervals)
        if mean_interval < 1:  # 间隔太小，不太可能是周期性
            return 0.0
            
        # 计算间隔变异系数
        std_dev = np.std(intervals) if len(intervals) > 1 else 0
        cv = std_dev / mean_interval if mean_interval > 0 else float('inf')
        
        # 变异系数低表示间隔一致，可能是周期性
        if cv < 0.3:
            return 0.9  # 高度周期性
        elif cv < 0.5:
            return 0.7  # 中度周期性
        elif cv < 0.7:
            return 0.4  # 弱周期性
        else:
            return 0.0  # 无周期性
    
    def _detect_cascade_pattern(self, errors: List[Dict[str, Any]]) -> float:
        """检测级联故障模式"""
        if len(errors) < 3:
            return 0.0
        
        # 提取时间戳并排序
        timestamps = sorted([e["timestamp"] for e in errors])
        
        # 计算短时间内的错误密度
        short_intervals = 0
        for i in range(len(timestamps) - 1):
            if timestamps[i+1] - timestamps[i] < 2.0:  # 2秒内的间隔
                short_intervals += 1
        
        # 计算短间隔比例
        short_ratio = short_intervals / (len(timestamps) - 1) if len(timestamps) > 1 else 0
        
        # 根据短间隔比例判断级联可能性
        if short_ratio > 0.7:
            return 0.9  # 高度级联
        elif short_ratio > 0.5:
            return 0.7  # 中度级联
        elif short_ratio > 0.3:
            return 0.4  # 弱级联
        else:
            return 0.0  # 无级联特征
    
    def _detect_gradual_pattern(self, errors: List[Dict[str, Any]]) -> float:
        """检测渐进式故障模式"""
        if len(errors) < 4:
            return 0.0
        
        # 按时间排序
        sorted_errors = sorted(errors, key=lambda e: e["timestamp"])
        
        # 检查错误严重度是否呈现上升趋势
        severity_levels = {
            "info": 1,
            "warning": 2,
            "error": 3,
            "critical": 4
        }
        
        # 分析前后两半部分的严重度
        mid_point = len(sorted_errors) // 2
        first_half = sorted_errors[:mid_point]
        second_half = sorted_errors[mid_point:]
        
        # 计算平均严重度
        first_avg_severity = sum(severity_levels.get(e.get("data", {}).get("severity", "warning"), 2) 
                               for e in first_half) / len(first_half)
        second_avg_severity = sum(severity_levels.get(e.get("data", {}).get("severity", "warning"), 2) 
                                for e in second_half) / len(second_half)
        
        # 计算错误频率
        first_timespan = first_half[-1]["timestamp"] - first_half[0]["timestamp"] if len(first_half) > 1 else 1
        second_timespan = second_half[-1]["timestamp"] - second_half[0]["timestamp"] if len(second_half) > 1 else 1
        
        first_freq = len(first_half) / first_timespan if first_timespan > 0 else 0
        second_freq = len(second_half) / second_timespan if second_timespan > 0 else 0
        
        # 频率增长率
        freq_growth = (second_freq / first_freq) - 1 if first_freq > 0 else 0
        
        # 严重度增长
        severity_growth = second_avg_severity - first_avg_severity
        
        # 综合判断渐进式故障可能性
        if severity_growth > 0.5 and freq_growth > 0.3:
            return 0.9  # 高度渐进式
        elif severity_growth > 0.3 or freq_growth > 0.5:
            return 0.7  # 中度渐进式
        elif severity_growth > 0.1 or freq_growth > 0.2:
            return 0.4  # 弱渐进式
        else:
            return 0.0  # 无渐进特征
    
    def _detect_burst_pattern(self, errors: List[Dict[str, Any]]) -> float:
        """检测突发故障模式"""
        if len(errors) < 3:
            return 0.0
        
        # 按时间排序
        sorted_errors = sorted(errors, key=lambda e: e["timestamp"])
        
        # 检查是否在短时间内有多个严重错误
        critical_errors = [e for e in sorted_errors 
                         if e.get("data", {}).get("severity") in ["error", "critical"]]
        
        if len(critical_errors) < 2:
            return 0.0
        
        # 计算严重错误的时间集中度
        time_range = sorted_errors[-1]["timestamp"] - sorted_errors[0]["timestamp"]
        if time_range < 1:
            time_range = 1  # 避免除以零
        
        critical_timespan = critical_errors[-1]["timestamp"] - critical_errors[0]["timestamp"]
        concentration = 1.0 - (critical_timespan / time_range) if time_range > 0 else 0
        
        # 根据集中度和严重错误比例判断突发可能性
        critical_ratio = len(critical_errors) / len(sorted_errors)
        
        if concentration > 0.8 and critical_ratio > 0.7:
            return 0.9  # 高度突发
        elif concentration > 0.6 and critical_ratio > 0.5:
            return 0.7  # 中度突发
        elif concentration > 0.4 or critical_ratio > 0.6:
            return 0.4  # 弱突发
        else:
            return 0.0  # 无突发特征
    
    def _detect_time_dependent_pattern(self, errors: List[Dict[str, Any]]) -> float:
        """检测时间相关故障模式"""
        if len(errors) < 5:
            return 0.0
        
        # 将错误按小时分组
        hour_groups = {}
        for error in errors:
            timestamp = error["timestamp"]
            dt = datetime.fromtimestamp(timestamp)
            hour = dt.hour
            
            if hour not in hour_groups:
                hour_groups[hour] = []
            
            hour_groups[hour].append(error)
        
        # 计算每小时错误数
        hourly_counts = {h: len(errors) for h, errors in hour_groups.items()}
        
        # 计算错误数标准差，高标准差表示分布不均
        hours = list(hourly_counts.keys())
        counts = list(hourly_counts.values())
        
        if not counts:
            return 0.0
            
        mean_count = sum(counts) / len(counts)
        std_dev = np.std(counts) if len(counts) > 1 else 0
        cv = std_dev / mean_count if mean_count > 0 else 0
        
        # 变异系数高表示错误在特定时间集中
        if cv > 1.0:
            return 0.9  # 高度时间相关
        elif cv > 0.7:
            return 0.7  # 中度时间相关
        elif cv > 0.5:
            return 0.4  # 弱时间相关
        else:
            return 0.0  # 无时间相关性
    
    def _detect_oscillating_pattern(self, errors: List[Dict[str, Any]]) -> float:
        """检测波动性故障模式"""
        if len(errors) < 5:
            return 0.0
        
        # 按时间排序
        sorted_errors = sorted(errors, key=lambda e: e["timestamp"])
        
        # 分析严重度的波动
        severity_levels = {
            "info": 1,
            "warning": 2,
            "error": 3,
            "critical": 4
        }
        
        # 提取严重度序列
        severities = [severity_levels.get(e.get("data", {}).get("severity", "warning"), 2) 
                    for e in sorted_errors]
        
        # 计算严重度变化
        changes = [abs(severities[i+1] - severities[i]) for i in range(len(severities)-1)]
        
        if not changes:
            return 0.0
        
        # 计算平均变化幅度
        avg_change = sum(changes) / len(changes)
        
        # 判断波动性
        if avg_change > 1.5:
            return 0.9  # 高度波动
        elif avg_change > 1.0:
            return 0.7  # 中度波动
        elif avg_change > 0.5:
            return 0.4  # 弱波动
        else:
            return 0.0  # 无波动特征
    
    def _save_pattern_analysis(self, service_id: str, patterns: Dict[str, float]):
        """保存模式分析结果"""
        analysis_file = os.path.join(self.data_dir, f"{service_id}_patterns.json")
        
        # 读取现有数据
        existing_data = []
        if os.path.exists(analysis_file):
            try:
                with open(analysis_file, 'r') as f:
                    existing_data = json.load(f)
            except (json.JSONDecodeError, IOError):
                pass
        
        # 添加新分析
        analysis = {
            "timestamp": time.time(),
            "patterns": patterns
        }
        
        existing_data.append(analysis)
        
        # 仅保留最近100条记录
        if len(existing_data) > 100:
            existing_data = existing_data[-100:]
        
        # 保存数据
        try:
            with open(analysis_file, 'w') as f:
                json.dump(existing_data, f, indent=2)
        except IOError as e:
            logger.error_status(f"保存模式分析失败: {e}")
    
    def _save_prediction(self, service_id: str, prediction: Dict[str, Any]):
        """保存预测结果"""
        # 更新内存缓存
        if service_id not in self.prediction_history:
            self.prediction_history[service_id] = []
        
        self.prediction_history[service_id].append(prediction)
        
        # 仅保留最近50条记录
        if len(self.prediction_history[service_id]) > 50:
            self.prediction_history[service_id] = self.prediction_history[service_id][-50:]
        
        # 保存到文件
        prediction_file = os.path.join(self.data_dir, f"{service_id}_predictions.json")
        
        try:
            with open(prediction_file, 'w') as f:
                json.dump(self.prediction_history[service_id], f, indent=2)
        except IOError as e:
            logger.error_status(f"保存预测结果失败: {e}")
    
    def get_pattern_analysis_history(self, service_id: str, limit: int = 10) -> List[Dict[str, Any]]:
        """
        获取服务的错误模式分析历史
        
        Args:
            service_id: 服务ID
            limit: 返回记录数量限制
            
        Returns:
            分析历史记录列表
        """
        analysis_file = os.path.join(self.data_dir, f"{service_id}_patterns.json")
        
        if not os.path.exists(analysis_file):
            return []
        
        try:
            with open(analysis_file, 'r') as f:
                data = json.load(f)
                
            # 返回最近的记录
            return data[-limit:] if len(data) > limit else data
            
        except (json.JSONDecodeError, IOError) as e:
            logger.error_status(f"读取模式分析历史失败: {e}")
            return []
    
    def get_prediction_history(self, service_id: str, limit: int = 10) -> List[Dict[str, Any]]:
        """
        获取服务的预测历史
        
        Args:
            service_id: 服务ID
            limit: 返回记录数量限制
            
        Returns:
            预测历史记录列表
        """
        if service_id not in self.prediction_history:
            return []
        
        # 返回最近的记录
        history = self.prediction_history[service_id]
        return history[-limit:] if len(history) > limit else history
    
    def analyze_prediction_accuracy(self, service_id: str) -> Dict[str, Any]:
        """
        分析预测准确率
        
        Args:
            service_id: 服务ID
            
        Returns:
            准确率分析结果
        """
        # 此功能需要实际故障数据才能计算准确率
        # 这里返回一个模拟的结果
        return {
            "service_id": service_id,
            "accuracy": random.uniform(0.7, 0.9),
            "precision": random.uniform(0.6, 0.85),
            "recall": random.uniform(0.7, 0.95),
            "f1_score": random.uniform(0.65, 0.9),
            "sample_size": random.randint(10, 50)
        }
    
    def get_system_prediction_summary(self) -> Dict[str, Any]:
        """获取系统级预测摘要"""
        # 收集所有服务的最新预测
        service_predictions = {}
        for service_id, predictions in self.prediction_history.items():
            if predictions:
                latest = sorted(predictions, key=lambda p: p["timestamp"], reverse=True)[0]
                service_predictions[service_id] = latest
        
        # 计算系统级指标
        if not service_predictions:
            return {
                "timestamp": time.time(),
                "overall_probability": 0.0,
                "service_count": 0,
                "high_risk_services": [],
                "has_multidimensional_patterns": False
            }
        
        # 计算系统级故障概率
        overall_probability = sum(p["probability"] for p in service_predictions.values()) / len(service_predictions)
        
        # 识别高风险服务
        high_risk_services = [
            {"service_id": svc_id, "probability": pred["probability"]}
            for svc_id, pred in service_predictions.items()
            if pred["probability"] >= 0.7
        ]
        
        # 检查是否存在多维度模式
        has_multidimensional_patterns = any(
            bool(pred.get("multidimensional_patterns", {}))
            for pred in service_predictions.values()
        )
        
        return {
            "timestamp": time.time(),
            "overall_probability": overall_probability,
            "service_count": len(service_predictions),
            "high_risk_services": high_risk_services,
            "has_multidimensional_patterns": has_multidimensional_patterns
        }

if __name__ == "__main__":
    # 演示用例
    predictor = get_instance()
    
    # 模拟错误数据
    errors = [
        {
            "timestamp": time.time() - 3600,
            "data": {
                "severity": "warning",
                "message": "Service response slow"
            }
        },
        {
            "timestamp": time.time() - 2400,
            "data": {
                "severity": "warning",
                "message": "Service response slow"
            }
        },
        {
            "timestamp": time.time() - 1200,
            "data": {
                "severity": "error",
                "message": "Connection timeout"
            }
        },
        {
            "timestamp": time.time() - 600,
            "data": {
                "severity": "error",
                "message": "Database error"
            }
        },
        {
            "timestamp": time.time() - 300,
            "data": {
                "severity": "critical",
                "message": "Service unavailable"
            }
        }
    ]
    
    # 分析错误模式
    patterns = predictor.analyze_error_patterns("demo_service", errors)
    print("检测到的错误模式:", patterns)
    
    # 预测故障概率
    prediction = predictor.predict_failure_probability("demo_service", errors)
    print("故障预测结果:", prediction) 