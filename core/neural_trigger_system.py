#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
神经网络触发系统
🔥 老王新增：实现自动触发机制，确保神经网络持续学习和优化
"""

import time
import threading
import asyncio
from typing import Dict, Any, List, Optional, Callable
from datetime import datetime, timedelta
from dataclasses import dataclass
from enum import Enum

import logging

# 尝试导入事件总线
try:
    from core.enhanced_event_bus import get_instance as get_event_bus
except ImportError:
    try:
        from core.integrated_event_bus import get_instance as get_event_bus
    except ImportError:
        try:
            from core.event_bus import get_instance as get_event_bus
        except ImportError:
            # 创建一个模拟的事件总线
            class DummyEventBus:
                def publish(self, *args, **kwargs): pass
                def subscribe(self, *args, **kwargs): pass

            def get_event_bus():
                return DummyEventBus()

logger = logging.getLogger("neural_trigger_system")


class TriggerType(Enum):
    """触发器类型"""
    TIMER = "timer"              # 定时触发
    EVENT = "event"              # 事件驱动触发
    THRESHOLD = "threshold"      # 阈值触发
    ADAPTIVE = "adaptive"        # 自适应触发


@dataclass
class TriggerCondition:
    """触发条件"""
    trigger_type: TriggerType
    condition_data: Dict[str, Any]
    priority: int = 5  # 1-10，数字越小优先级越高
    enabled: bool = True
    last_triggered: Optional[datetime] = None
    trigger_count: int = 0


class NeuralTriggerSystem:
    """神经网络触发系统"""
    
    def __init__(self, config: Dict[str, Any] = None):
        self.config = config or {}
        self.event_bus = get_event_bus()
        
        # 触发条件注册表
        self.trigger_conditions: Dict[str, TriggerCondition] = {}
        
        # 触发器状态
        self.running = False
        self.trigger_threads: List[threading.Thread] = []
        self.trigger_stats = {
            "total_triggers": 0,
            "successful_triggers": 0,
            "failed_triggers": 0,
            "last_trigger_time": None
        }
        
        # 神经网络系统引用
        self.neural_enhancer = None
        self.advanced_neural = None
        
        # 初始化默认触发条件
        self._initialize_default_triggers()
        
        logger.info("🔥 神经网络触发系统初始化完成")
    
    def _initialize_default_triggers(self):
        """初始化默认触发条件"""
        
        # 1. 定时触发 - 每30分钟进行一次神经网络学习
        self.register_trigger("neural_learning_timer", TriggerCondition(
            trigger_type=TriggerType.TIMER,
            condition_data={
                "interval": 1800,  # 30分钟
                "action": "neural_learning",
                "description": "定期神经网络学习触发"
            },
            priority=3
        ))
        
        # 2. 事件驱动触发 - 用户交互时触发
        self.register_trigger("user_interaction_event", TriggerCondition(
            trigger_type=TriggerType.EVENT,
            condition_data={
                "event_name": "user_message",
                "action": "consciousness_enhancement",
                "min_interval": 300,  # 最小间隔5分钟
                "description": "用户交互触发意识增强"
            },
            priority=2
        ))
        
        # 3. 阈值触发 - 系统负载低时触发
        self.register_trigger("low_load_threshold", TriggerCondition(
            trigger_type=TriggerType.THRESHOLD,
            condition_data={
                "metric": "system_load",
                "threshold": 0.3,
                "operator": "less_than",
                "action": "advanced_neural_learning",
                "description": "系统负载低时触发高级学习"
            },
            priority=4
        ))
        
        # 4. 自适应触发 - 基于学习效果调整
        self.register_trigger("adaptive_learning", TriggerCondition(
            trigger_type=TriggerType.ADAPTIVE,
            condition_data={
                "metric": "learning_effectiveness",
                "target_value": 0.8,
                "adaptation_rate": 0.1,
                "action": "adaptive_neural_optimization",
                "description": "基于学习效果的自适应触发"
            },
            priority=1
        ))
        
        logger.info(f"✅ 初始化了 {len(self.trigger_conditions)} 个默认触发条件")
    
    def register_trigger(self, trigger_id: str, condition: TriggerCondition):
        """注册触发条件"""
        self.trigger_conditions[trigger_id] = condition
        logger.debug(f"📝 注册触发条件: {trigger_id} - {condition.condition_data.get('description', 'N/A')}")
    
    def unregister_trigger(self, trigger_id: str):
        """注销触发条件"""
        if trigger_id in self.trigger_conditions:
            del self.trigger_conditions[trigger_id]
            logger.debug(f"🗑️ 注销触发条件: {trigger_id}")
    
    def start(self):
        """启动触发系统"""
        if self.running:
            logger.warning("触发系统已在运行")
            return
        
        self.running = True
        
        # 获取神经网络系统引用
        self._initialize_neural_systems()
        
        # 启动不同类型的触发器
        self._start_timer_triggers()
        self._start_event_triggers()
        self._start_threshold_triggers()
        self._start_adaptive_triggers()
        
        logger.info("🚀 神经网络触发系统已启动")
    
    def stop(self):
        """停止触发系统"""
        self.running = False
        
        # 等待所有触发线程结束
        for thread in self.trigger_threads:
            if thread.is_alive():
                thread.join(timeout=5)
        
        self.trigger_threads.clear()
        logger.info("⏹️ 神经网络触发系统已停止")
    
    def _initialize_neural_systems(self):
        """初始化神经网络系统引用"""
        try:
            from core.neural_consciousness_enhancer import get_instance as get_neural_enhancer
            from core.advanced_neural_consciousness import get_instance as get_advanced_neural
            
            self.neural_enhancer = get_neural_enhancer()
            self.advanced_neural = get_advanced_neural()
            
            logger.info("✅ 神经网络系统引用初始化完成")
        except Exception as e:
            logger.error(f"❌ 神经网络系统引用初始化失败: {e}")
    
    def _start_timer_triggers(self):
        """启动定时触发器"""
        timer_triggers = [
            (tid, condition) for tid, condition in self.trigger_conditions.items()
            if condition.trigger_type == TriggerType.TIMER and condition.enabled
        ]
        
        for trigger_id, condition in timer_triggers:
            thread = threading.Thread(
                target=self._timer_trigger_loop,
                args=(trigger_id, condition),
                daemon=True,
                name=f"TimerTrigger-{trigger_id}"
            )
            thread.start()
            self.trigger_threads.append(thread)
        
        logger.info(f"⏰ 启动了 {len(timer_triggers)} 个定时触发器")
    
    def _timer_trigger_loop(self, trigger_id: str, condition: TriggerCondition):
        """定时触发器循环"""
        interval = condition.condition_data.get("interval", 3600)
        
        while self.running:
            try:
                time.sleep(interval)
                if self.running and condition.enabled:
                    self._execute_trigger(trigger_id, condition, {"trigger_type": "timer"})
            except Exception as e:
                logger.error(f"定时触发器 {trigger_id} 异常: {e}")
                time.sleep(60)  # 异常时等待1分钟再重试
    
    def _start_event_triggers(self):
        """启动事件驱动触发器"""
        event_triggers = [
            (tid, condition) for tid, condition in self.trigger_conditions.items()
            if condition.trigger_type == TriggerType.EVENT and condition.enabled
        ]
        
        for trigger_id, condition in event_triggers:
            event_name = condition.condition_data.get("event_name")
            if event_name:
                self.event_bus.subscribe(event_name, 
                    lambda data, tid=trigger_id, cond=condition: self._handle_event_trigger(tid, cond, data))
        
        logger.info(f"📡 启动了 {len(event_triggers)} 个事件触发器")
    
    def _handle_event_trigger(self, trigger_id: str, condition: TriggerCondition, event_data: Dict[str, Any]):
        """处理事件触发"""
        try:
            # 检查最小间隔
            min_interval = condition.condition_data.get("min_interval", 0)
            if condition.last_triggered:
                elapsed = (datetime.now() - condition.last_triggered).total_seconds()
                if elapsed < min_interval:
                    logger.debug(f"事件触发器 {trigger_id} 间隔不足，跳过")
                    return
            
            self._execute_trigger(trigger_id, condition, {"trigger_type": "event", "event_data": event_data})
        except Exception as e:
            logger.error(f"事件触发器 {trigger_id} 处理异常: {e}")
    
    def _start_threshold_triggers(self):
        """启动阈值触发器"""
        threshold_triggers = [
            (tid, condition) for tid, condition in self.trigger_conditions.items()
            if condition.trigger_type == TriggerType.THRESHOLD and condition.enabled
        ]
        
        if threshold_triggers:
            thread = threading.Thread(
                target=self._threshold_trigger_loop,
                args=(threshold_triggers,),
                daemon=True,
                name="ThresholdTriggers"
            )
            thread.start()
            self.trigger_threads.append(thread)
        
        logger.info(f"📊 启动了 {len(threshold_triggers)} 个阈值触发器")
    
    def _threshold_trigger_loop(self, threshold_triggers: List[tuple]):
        """阈值触发器循环"""
        while self.running:
            try:
                for trigger_id, condition in threshold_triggers:
                    if not condition.enabled:
                        continue
                    
                    # 获取指标值
                    metric = condition.condition_data.get("metric")
                    current_value = self._get_metric_value(metric)
                    
                    if current_value is not None:
                        threshold = condition.condition_data.get("threshold")
                        operator = condition.condition_data.get("operator", "greater_than")
                        
                        triggered = False
                        if operator == "greater_than" and current_value > threshold:
                            triggered = True
                        elif operator == "less_than" and current_value < threshold:
                            triggered = True
                        elif operator == "equal" and abs(current_value - threshold) < 0.01:
                            triggered = True
                        
                        if triggered:
                            self._execute_trigger(trigger_id, condition, {
                                "trigger_type": "threshold",
                                "metric": metric,
                                "current_value": current_value,
                                "threshold": threshold
                            })
                
                time.sleep(60)  # 每分钟检查一次阈值
                
            except Exception as e:
                logger.error(f"阈值触发器循环异常: {e}")
                time.sleep(60)
    
    def _start_adaptive_triggers(self):
        """启动自适应触发器"""
        adaptive_triggers = [
            (tid, condition) for tid, condition in self.trigger_conditions.items()
            if condition.trigger_type == TriggerType.ADAPTIVE and condition.enabled
        ]
        
        if adaptive_triggers:
            thread = threading.Thread(
                target=self._adaptive_trigger_loop,
                args=(adaptive_triggers,),
                daemon=True,
                name="AdaptiveTriggers"
            )
            thread.start()
            self.trigger_threads.append(thread)
        
        logger.info(f"🧠 启动了 {len(adaptive_triggers)} 个自适应触发器")
    
    def _adaptive_trigger_loop(self, adaptive_triggers: List[tuple]):
        """自适应触发器循环"""
        while self.running:
            try:
                for trigger_id, condition in adaptive_triggers:
                    if not condition.enabled:
                        continue
                    
                    # 基于学习效果调整触发频率
                    effectiveness = self._get_learning_effectiveness()
                    target_value = condition.condition_data.get("target_value", 0.8)
                    adaptation_rate = condition.condition_data.get("adaptation_rate", 0.1)
                    
                    if effectiveness < target_value:
                        # 学习效果不佳，增加触发频率
                        self._execute_trigger(trigger_id, condition, {
                            "trigger_type": "adaptive",
                            "effectiveness": effectiveness,
                            "target_value": target_value,
                            "reason": "low_effectiveness"
                        })
                
                time.sleep(300)  # 每5分钟检查一次自适应条件
                
            except Exception as e:
                logger.error(f"自适应触发器循环异常: {e}")
                time.sleep(300)
    
    def _execute_trigger(self, trigger_id: str, condition: TriggerCondition, context: Dict[str, Any]):
        """执行触发动作"""
        try:
            action = condition.condition_data.get("action")
            
            # 更新触发统计
            condition.last_triggered = datetime.now()
            condition.trigger_count += 1
            self.trigger_stats["total_triggers"] += 1
            self.trigger_stats["last_trigger_time"] = datetime.now()
            
            logger.info(f"🔥 执行触发器: {trigger_id} - {action}")
            
            # 根据动作类型执行相应的神经网络操作
            success = False
            if action == "neural_learning":
                success = self._trigger_neural_learning(context)
            elif action == "consciousness_enhancement":
                success = self._trigger_consciousness_enhancement(context)
            elif action == "advanced_neural_learning":
                success = self._trigger_advanced_neural_learning(context)
            elif action == "adaptive_neural_optimization":
                success = self._trigger_adaptive_optimization(context)
            else:
                logger.warning(f"未知的触发动作: {action}")
            
            if success:
                self.trigger_stats["successful_triggers"] += 1
                logger.debug(f"✅ 触发器 {trigger_id} 执行成功")
            else:
                self.trigger_stats["failed_triggers"] += 1
                logger.warning(f"⚠️ 触发器 {trigger_id} 执行失败")
                
        except Exception as e:
            self.trigger_stats["failed_triggers"] += 1
            logger.error(f"❌ 触发器 {trigger_id} 执行异常: {e}")
    
    def _trigger_neural_learning(self, context: Dict[str, Any]) -> bool:
        """触发神经网络学习"""
        try:
            if not self.neural_enhancer:
                return False
            
            # 准备学习状态
            learning_state = {
                "trigger_context": context,
                "learning_type": "scheduled",
                "timestamp": time.time()
            }
            
            # 准备环境因子
            environmental_factors = {
                "learning_opportunity": 1.0,
                "cognitive_load": 0.3,
                "system_stability": 0.9,
                "trigger_source": context.get("trigger_type", "unknown")
            }
            
            # 执行神经网络增强
            enhanced_state = self.neural_enhancer.enhance_consciousness(learning_state, environmental_factors)
            
            logger.debug("🧠 神经网络学习触发完成")
            return True
            
        except Exception as e:
            logger.error(f"神经网络学习触发失败: {e}")
            return False

    def _trigger_consciousness_enhancement(self, context: Dict[str, Any]) -> bool:
        """触发意识增强"""
        try:
            if not self.neural_enhancer:
                return False

            # 准备意识状态
            consciousness_state = {
                "trigger_context": context,
                "enhancement_type": "event_driven",
                "user_interaction": context.get("event_data", {}),
                "timestamp": time.time()
            }

            # 准备环境因子
            environmental_factors = {
                "interaction_complexity": 0.7,
                "learning_opportunity": 0.8,
                "cognitive_load": 0.5,
                "social_context": 0.6,
                "trigger_source": context.get("trigger_type", "unknown")
            }

            # 执行意识增强
            enhanced_state = self.neural_enhancer.enhance_consciousness(consciousness_state, environmental_factors)

            logger.debug("🧠 意识增强触发完成")
            return True

        except Exception as e:
            logger.error(f"意识增强触发失败: {e}")
            return False

    def _trigger_advanced_neural_learning(self, context: Dict[str, Any]) -> bool:
        """触发高级神经网络学习"""
        try:
            if not self.advanced_neural:
                return False

            # 准备高级学习状态
            advanced_state = {
                "trigger_context": context,
                "learning_type": "advanced_scheduled",
                "system_metrics": context,
                "timestamp": time.time()
            }

            # 准备环境因子
            environmental_factors = {
                "learning_opportunity": 1.0,
                "cognitive_load": 0.2,  # 低负载时触发
                "system_stability": 0.95,
                "processing_complexity": 0.8,
                "trigger_source": context.get("trigger_type", "unknown")
            }

            # 执行高级神经网络增强
            ultimate_state = self.advanced_neural.ultimate_consciousness_enhancement(advanced_state, environmental_factors)

            logger.debug("🚀 高级神经网络学习触发完成")
            return True

        except Exception as e:
            logger.error(f"高级神经网络学习触发失败: {e}")
            return False

    def _trigger_adaptive_optimization(self, context: Dict[str, Any]) -> bool:
        """触发自适应优化"""
        try:
            # 同时使用基础和高级神经网络
            success_basic = False
            success_advanced = False

            if self.neural_enhancer:
                adaptive_state = {
                    "trigger_context": context,
                    "optimization_type": "adaptive",
                    "effectiveness_metrics": context,
                    "timestamp": time.time()
                }

                environmental_factors = {
                    "learning_opportunity": 0.9,
                    "cognitive_load": 0.4,
                    "adaptation_pressure": 0.8,
                    "optimization_target": context.get("target_value", 0.8),
                    "trigger_source": context.get("trigger_type", "unknown")
                }

                try:
                    enhanced_state = self.neural_enhancer.enhance_consciousness(adaptive_state, environmental_factors)
                    success_basic = True
                except Exception as e:
                    logger.warning(f"基础自适应优化失败: {e}")

            if self.advanced_neural:
                try:
                    ultimate_state = self.advanced_neural.ultimate_consciousness_enhancement(adaptive_state, environmental_factors)
                    success_advanced = True
                except Exception as e:
                    logger.warning(f"高级自适应优化失败: {e}")

            success = success_basic or success_advanced
            if success:
                logger.debug("🧠 自适应优化触发完成")

            return success

        except Exception as e:
            logger.error(f"自适应优化触发失败: {e}")
            return False

    def _get_metric_value(self, metric: str) -> Optional[float]:
        """获取系统指标值"""
        try:
            if metric == "system_load":
                # 简单的系统负载模拟
                import psutil
                return psutil.cpu_percent(interval=1) / 100.0
            elif metric == "memory_usage":
                import psutil
                return psutil.virtual_memory().percent / 100.0
            elif metric == "neural_activity":
                # 神经网络活动度（模拟）
                return 0.5  # 默认值
            else:
                logger.warning(f"未知的指标类型: {metric}")
                return None
        except Exception as e:
            logger.error(f"获取指标 {metric} 失败: {e}")
            return None

    def _get_learning_effectiveness(self) -> float:
        """获取学习效果评估"""
        try:
            # 基于神经网络统计计算学习效果
            effectiveness = 0.5  # 默认值

            if self.neural_enhancer:
                try:
                    stats = self.neural_enhancer.get_network_statistics()
                    if stats and "learning_stats" in stats:
                        learning_stats = stats["learning_stats"]
                        # 基于学习统计计算效果
                        if "average_loss" in learning_stats:
                            avg_loss = learning_stats["average_loss"]
                            effectiveness = max(0.0, min(1.0, 1.0 - avg_loss))
                except Exception as e:
                    logger.debug(f"获取基础神经网络统计失败: {e}")

            if self.advanced_neural:
                try:
                    stats = self.advanced_neural.get_ultimate_statistics()
                    if stats and "quantum_coherence" in stats:
                        coherence = stats["quantum_coherence"]
                        effectiveness = max(effectiveness, coherence)
                except Exception as e:
                    logger.debug(f"获取高级神经网络统计失败: {e}")

            return effectiveness

        except Exception as e:
            logger.error(f"获取学习效果失败: {e}")
            return 0.5

    def get_trigger_statistics(self) -> Dict[str, Any]:
        """获取触发器统计信息"""
        stats = self.trigger_stats.copy()
        stats["registered_triggers"] = len(self.trigger_conditions)
        stats["enabled_triggers"] = sum(1 for c in self.trigger_conditions.values() if c.enabled)
        stats["running"] = self.running

        # 按类型统计触发器
        type_stats = {}
        for condition in self.trigger_conditions.values():
            trigger_type = condition.trigger_type.value
            if trigger_type not in type_stats:
                type_stats[trigger_type] = {"count": 0, "enabled": 0, "total_triggers": 0}
            type_stats[trigger_type]["count"] += 1
            if condition.enabled:
                type_stats[trigger_type]["enabled"] += 1
            type_stats[trigger_type]["total_triggers"] += condition.trigger_count

        stats["trigger_types"] = type_stats
        return stats

    def enable_trigger(self, trigger_id: str):
        """启用触发器"""
        if trigger_id in self.trigger_conditions:
            self.trigger_conditions[trigger_id].enabled = True
            logger.info(f"✅ 启用触发器: {trigger_id}")

    def disable_trigger(self, trigger_id: str):
        """禁用触发器"""
        if trigger_id in self.trigger_conditions:
            self.trigger_conditions[trigger_id].enabled = False
            logger.info(f"❌ 禁用触发器: {trigger_id}")

    def manual_trigger(self, trigger_id: str, context: Dict[str, Any] = None):
        """手动触发"""
        if trigger_id in self.trigger_conditions:
            condition = self.trigger_conditions[trigger_id]
            manual_context = context or {}
            manual_context["trigger_type"] = "manual"
            self._execute_trigger(trigger_id, condition, manual_context)
            logger.info(f"🔧 手动触发: {trigger_id}")
        else:
            logger.warning(f"触发器不存在: {trigger_id}")


# 全局实例
_neural_trigger_system = None

def get_neural_trigger_system(config: Dict[str, Any] = None) -> NeuralTriggerSystem:
    """获取神经网络触发系统实例"""
    global _neural_trigger_system
    if _neural_trigger_system is None:
        _neural_trigger_system = NeuralTriggerSystem(config)
    return _neural_trigger_system
