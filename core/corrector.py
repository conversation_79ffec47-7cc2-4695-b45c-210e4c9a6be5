"""
自主纠错系统 - Corrector

该模块实现了数字生命体的自主纠错系统，包括响应质量评估、异常检测与修复、
自动重试机制和错误学习存储等功能，使数字生命体具有自我修复能力。

作者: Claude
创建日期: 2024-07-08
版本: 1.0
"""

from utilities.unified_logger import get_unified_logger, setup_unified_logging
import time
import threading
import traceback
import json
import os
from typing import Dict, Any, List, Optional, Tuple, Union, Callable

# 导入其他核心模块
from . import event_bus
from . import life_context

# 配置日志记录
setup_unified_logging()
logger = get_unified_logger(__name__)


class Corrector:
    """自主纠错系统类，负责错误检测、修复和学习"""
    
    def __init__(self, config: Dict[str, Any] = None):
        """
        初始化自主纠错系统
        
        Args:
            config (Dict[str, Any], optional): 配置参数
        """
        logger.success("初始化自主纠错系统...")
        
        self.config = config or {}
        self.max_retries = self.config.get("max_retries", 3)
        self.error_threshold = self.config.get("error_threshold", 0.7)
        self.learning_enabled = self.config.get("learning_enabled", True)
        
        # 创建错误存储目录
        self.error_dir = os.path.join(os.path.dirname(os.path.dirname(__file__)), "data", "errors")
        os.makedirs(self.error_dir, exist_ok=True)
        
        # 获取事件总线和生命上下文实例
        self.event_bus = event_bus.get_instance()
        self.life_context = life_context.get_instance()
        
        # 错误计数器
        self.error_counts: Dict[str, int] = {}
        
        # 已知错误和解决方案
        self.known_errors: Dict[str, Dict[str, Any]] = self._load_known_errors()

        # 🔥 老王修复：预加载基础解决方案
        self._preload_basic_solutions()

        # 最近错误历史
        self.recent_errors: List[Dict[str, Any]] = []
        self.max_recent_errors = 50

        # 订阅相关事件
        self._subscribe_events()

        logger.success("自主纠错系统初始化完成")
    
    def _load_known_errors(self) -> Dict[str, Dict[str, Any]]:
        """
        加载已知错误和解决方案
        
        Returns:
            Dict[str, Dict[str, Any]]: 已知错误字典
        """
        known_errors_path = os.path.join(self.error_dir, "known_errors.json")
        
        # 如果文件不存在，创建一个空的已知错误文件
        if not os.path.exists(known_errors_path):
            with open(known_errors_path, "w", encoding="utf-8") as f:
                json.dump({}, f, ensure_ascii=False, indent=2)
            return {}
        
        # 加载已知错误
        try:
            with open(known_errors_path, "r", encoding="utf-8") as f:
                return json.load(f)
        except Exception as e:
            logger.error_status(f"加载已知错误失败: {e}")
            return {}
    
    def _save_known_errors(self):
        """保存已知错误到文件"""
        known_errors_path = os.path.join(self.error_dir, "known_errors.json")
        try:
            with open(known_errors_path, "w", encoding="utf-8") as f:
                json.dump(self.known_errors, f, ensure_ascii=False, indent=2)
            logger.debug("已保存已知错误")
        except Exception as e:
            logger.error_status(f"保存已知错误失败: {e}")

    def _preload_basic_solutions(self):
        """🔥 老王修复：预加载基础解决方案到已知错误库"""
        try:
            # 定义需要预加载的错误类型
            error_types_to_preload = [
                "database_connection_pool_exhausted",
                "mysql_connection_pool_unavailable",
                "connection_pool_malformed_packet",
                "backup_file_not_found",
                "backup_file_permission_error",
                "backup_cleanup_general_error",
                "intelligence_manager_init_error",
                "intelligence_manager_module_error",
                "system_health_cpu_warning",
                "system_health_memory_warning",
                "system_health_disk_warning"
            ]

            # 为每个错误类型生成解决方案并添加到已知错误库
            current_time = time.time()
            preloaded_count = 0

            for error_type in error_types_to_preload:
                if error_type not in self.known_errors:
                    try:
                        # 🔥 修复：使用_generate_solution方法获取解决方案，添加类型检查
                        solution = self._generate_solution(error_type, {"message": f"预加载的{error_type}错误"})

                        # 🔥 修复：确保solution是字典类型
                        if not isinstance(solution, dict):
                            logger.warning_status(f"解决方案类型错误，期望dict，实际{type(solution)}: {error_type}")
                            solution = {
                                "action": "log",
                                "params": {},
                                "description": f"预加载的{error_type}解决方案"
                            }

                        self.known_errors[error_type] = {
                            "error_type": error_type,
                            "description": solution.get("description", f"预加载的{error_type}解决方案"),
                            "first_seen": current_time,
                            "last_seen": current_time,
                            "occurrence_count": 0,
                            "priority": "normal",
                            "solution": solution,
                            "solution_verified": True  # 预加载的解决方案标记为已验证
                        }
                        preloaded_count += 1

                    except Exception as e:
                        logger.error_status(f"预加载错误类型 {error_type} 失败: {e}")
                        # 使用默认解决方案
                        self.known_errors[error_type] = {
                            "error_type": error_type,
                            "description": f"预加载的{error_type}解决方案",
                            "first_seen": current_time,
                            "last_seen": current_time,
                            "occurrence_count": 0,
                            "priority": "normal",
                            "solution": {
                                "action": "log",
                                "params": {},
                                "description": f"预加载的{error_type}解决方案"
                            },
                            "solution_verified": True
                        }

            # 保存到文件
            if preloaded_count > 0:
                self._save_known_errors()
                logger.info(f"✅ 已预加载 {preloaded_count} 个基础解决方案")
            else:
                logger.debug("所有基础解决方案已存在，无需预加载")

        except Exception as e:
            import traceback
            logger.error_status(f"预加载基础解决方案失败: {e}")
            logger.debug(f"错误详情: {traceback.format_exc()}")

    def _subscribe_events(self):
        """订阅相关事件"""
        # 订阅系统错误事件
        self.event_bus.subscribe("system_error", self._on_system_error)
        
        # 订阅响应质量评估事件
        self.event_bus.subscribe("response_quality", self._on_response_quality)
        
        logger.debug("已订阅相关事件")
    
    def _on_system_error(self, data: Dict[str, Any]):
        """
        处理系统错误事件
        
        Args:
            data (Dict[str, Any]): 事件数据
        """
        error_type = data.get("error_type", "unknown")
        error_msg = data.get("message", "")
        
        # 记录错误
        self._record_error(error_type, error_msg, data)
        
        # 尝试自动修复
        if error_type in self.known_errors:
            solution = self.known_errors[error_type].get("solution")
            if solution:
                logger.info(f"尝试自动修复错误: {error_type}")
                # 应用解决方案
                self._apply_solution(error_type, solution, data)
    
    def _on_response_quality(self, data: Dict[str, Any]):
        """
        处理响应质量评估事件
        
        Args:
            data (Dict[str, Any]): 事件数据
        """
        quality_score = data.get("quality_score", 1.0)
        response_id = data.get("response_id", "")
        
        # 如果质量评分低于阈值，视为需要纠正的错误
        if quality_score < self.error_threshold:
            error_type = "low_quality_response"
            error_msg = f"响应质量低于阈值: {quality_score}"
            
            # 记录错误
            error_data = {
                "response_id": response_id,
                "quality_score": quality_score,
                "response_text": data.get("response_text", ""),
                "user_input": data.get("user_input", "")
            }
            
            self._record_error(error_type, error_msg, error_data)
            
            # 触发重新生成响应
            if data.get("can_retry", False):
                self.event_bus.publish("regenerate_response", {
                    "response_id": response_id,
                    "user_input": data.get("user_input", ""),
                    "reason": "quality_below_threshold"
                })
    
    def _record_error(self, error_type: str, error_msg: str, data: Dict[str, Any] = None):
        """
        记录错误
        
        Args:
            error_type (str): 错误类型
            error_msg (str): 错误消息
            data (Dict[str, Any], optional): 错误相关数据
        """
        # 更新错误计数
        if error_type in self.error_counts:
            self.error_counts[error_type] += 1
        else:
            self.error_counts[error_type] = 1
        
        # 创建错误记录
        error_record = {
            "error_type": error_type,
            "message": error_msg,
            "timestamp": time.time(),
            "count": self.error_counts[error_type],
            "data": data or {}
        }
        
        # 添加到最近错误历史
        self.recent_errors.append(error_record)
        if len(self.recent_errors) > self.max_recent_errors:
            self.recent_errors = self.recent_errors[-self.max_recent_errors:]
        
        # 更新生命上下文中的系统错误
        self.life_context.update_context("system.errors", self.recent_errors[-10:])
        
        # 发布错误记录事件
        self.event_bus.publish("error_recorded", error_record)
        
        logger.warning_status(f"记录错误: [{error_type}] {error_msg}")
        
        # 如果是高频错误，可能需要学习解决方案
        if self.learning_enabled and self.error_counts[error_type] >= 3:
            self._learn_from_error(error_type, error_record)
    
    def _learn_from_error(self, error_type: str, error_record: Dict[str, Any]):
        """
        从错误中学习
        
        Args:
            error_type (str): 错误类型
            error_record (Dict[str, Any]): 错误记录
        """
        # 如果已经是已知错误，更新它的统计信息
        if error_type in self.known_errors:
            known_error = self.known_errors[error_type]
            known_error["occurrence_count"] += 1
            known_error["last_seen"] = time.time()
            
            # 如果错误很频繁，可能需要提升优先级
            if known_error["occurrence_count"] > 10:
                known_error["priority"] = "high"
            
            logger.debug(f"更新已知错误: {error_type}, 计数: {known_error['occurrence_count']}")
        
        # 否则，创建新的已知错误
        else:
            # 尝试生成可能的解决方案
            possible_solution = self._generate_solution(error_type, error_record)
            
            self.known_errors[error_type] = {
                "error_type": error_type,
                "description": error_record["message"],
                "first_seen": time.time(),
                "last_seen": time.time(),
                "occurrence_count": 1,
                "priority": "normal",
                "solution": possible_solution,
                "solution_verified": False
            }
            
            logger.info(f"创建新的已知错误: {error_type}")
        
        # 保存已知错误
        self._save_known_errors()
    
    def _generate_solution(self, error_type: str, error_record: Dict[str, Any]) -> Dict[str, Any]:
        """
        生成可能的解决方案
        
        Args:
            error_type (str): 错误类型
            error_record (Dict[str, Any]): 错误记录
            
        Returns:
            Dict[str, Any]: 可能的解决方案
        """
        # TODO: 这里可以接入AI模型，根据错误类型和历史生成解决方案
        # 当前使用简单的预定义解决方案
        
        basic_solutions = {
            "database_connection_error": {
                "action": "retry",
                "params": {"max_retries": 3, "backoff_factor": 2},
                "description": "重试数据库连接，使用指数退避策略"
            },
            # 🔥 老王修复：添加数据库连接池相关的解决方案
            "database_connection_pool_exhausted": {
                "action": "cleanup_and_rebuild",
                "params": {"force_cleanup": True, "wait_time": 10},
                "description": "清理并重建数据库连接池"
            },
            "mysql_connection_pool_unavailable": {
                "action": "reinitialize_pool",
                "params": {"max_retries": 3, "health_check": True},
                "description": "重新初始化MySQL连接池并进行健康检查"
            },
            "connection_pool_malformed_packet": {
                "action": "force_rebuild",
                "params": {"cleanup_timeout": 15, "rebuild_delay": 5},
                "description": "强制重建连接池解决Malformed packet问题"
            },
            "api_timeout": {
                "action": "retry",
                "params": {"max_retries": 2, "timeout": 10},
                "description": "增加超时时间并重试API调用"
            },
            "low_quality_response": {
                "action": "regenerate",
                "params": {"temperature": 0.7, "max_retries": 1},
                "description": "使用不同参数重新生成响应"
            },
            "memory_access_error": {
                "action": "fallback",
                "params": {"use_cache": True},
                "description": "使用缓存的记忆数据"
            },
            # 🔥 老王修复：添加备份文件相关的解决方案
            "backup_file_not_found": {
                "action": "ignore_and_continue",
                "params": {"log_level": "debug"},
                "description": "备份文件不存在是正常情况，忽略并继续"
            },
            "backup_file_permission_error": {
                "action": "retry_with_fallback",
                "params": {"max_retries": 2, "fallback_action": "skip"},
                "description": "备份文件权限错误，重试后跳过"
            },
            "backup_file_deletion_error": {
                "action": "log_and_continue",
                "params": {"severity": "warning"},
                "description": "记录备份文件删除错误并继续"
            },
            "backup_cleanup_general_error": {
                "action": "disable_cleanup_temporarily",
                "params": {"disable_duration": 3600},
                "description": "暂时禁用备份清理功能1小时"
            },
            # 🔥 老王修复：添加智能整合管理器相关的解决方案
            "intelligence_manager_init_error": {
                "action": "auto_recover",
                "params": {"max_retries": 3, "retry_delay": 5},
                "description": "自动恢复智能整合管理器初始化"
            },
            "intelligence_manager_module_error": {
                "action": "restart_module",
                "params": {"isolation": True, "health_check": True},
                "description": "重启有问题的智能模块"
            },
            "intelligence_manager_dependency_error": {
                "action": "check_and_restore_dependencies",
                "params": {"recursive_check": True},
                "description": "检查并恢复模块依赖关系"
            },
            # 🔥 老王修复：添加系统监控相关的解决方案
            "system_monitor_error": {
                "action": "restart_monitor",
                "params": {"restart_delay": 5, "health_check": True},
                "description": "重启系统监控器"
            },
            "system_health_cpu_warning": {
                "action": "log_and_monitor",
                "params": {"monitor_interval": 30, "alert_threshold": 3},
                "description": "记录CPU警告并加强监控"
            },
            "system_health_cpu_critical": {
                "action": "emergency_alert",
                "params": {"immediate_action": True, "escalate": True},
                "description": "CPU严重警告，立即处理"
            },
            "system_health_memory_warning": {
                "action": "memory_cleanup",
                "params": {"force_gc": True, "clear_cache": True},
                "description": "内存警告，执行清理操作"
            },
            "system_health_memory_critical": {
                "action": "emergency_memory_cleanup",
                "params": {"aggressive_cleanup": True, "restart_services": True},
                "description": "内存严重警告，执行紧急清理"
            },
            "system_health_disk_warning": {
                "action": "disk_cleanup",
                "params": {"clean_logs": True, "clean_temp": True},
                "description": "磁盘警告，清理临时文件和日志"
            },
            "system_health_disk_critical": {
                "action": "emergency_disk_cleanup",
                "params": {"aggressive_cleanup": True, "archive_logs": True},
                "description": "磁盘严重警告，执行紧急清理"
            }
        }
        
        if error_type in basic_solutions:
            return basic_solutions[error_type]
        
        # 默认解决方案
        return {
            "action": "log",
            "params": {},
            "description": "记录错误并人工分析"
        }
    
    def _apply_solution(self, error_type: str, solution: Dict[str, Any], context: Dict[str, Any] = None):
        """
        应用解决方案
        
        Args:
            error_type (str): 错误类型
            solution (Dict[str, Any]): 解决方案
            context (Dict[str, Any], optional): 上下文数据
        
        Returns:
            bool: 是否成功应用解决方案
        """
        action = solution.get("action", "")
        params = solution.get("params", {})
        
        logger.info(f"应用解决方案: {error_type} -> {action}")
        
        # 根据不同的操作类型执行不同的动作
        if action == "retry":
            max_retries = params.get("max_retries", self.max_retries)
            backoff_factor = params.get("backoff_factor", 1)
            
            # 发布重试事件
            self.event_bus.publish("retry_operation", {
                "error_type": error_type,
                "max_retries": max_retries,
                "backoff_factor": backoff_factor,
                "context": context
            })
            
            return True
        
        elif action == "regenerate":
            # 发布重新生成事件
            self.event_bus.publish("regenerate_response", {
                "error_type": error_type,
                "params": params,
                "context": context
            })
            
            return True
        
        elif action == "fallback":
            # 发布使用备选方案事件
            self.event_bus.publish("use_fallback", {
                "error_type": error_type,
                "params": params,
                "context": context
            })
            
            return True
        
        elif action == "log":
            # 仅记录错误，不采取进一步行动
            logger.warning_status(f"错误需要人工干预: {error_type}")
            return True
        
        else:
            logger.warning_status(f"未知的解决方案动作: {action}")
            return False
    
    def handle_error(self, error: Exception, user_id: str = None, user_input: str = None) -> str:
        """
        处理异常，提供友好的错误响应
        
        Args:
            error (Exception): 异常对象
            user_id (str, optional): 用户ID
            user_input (str, optional): 用户输入
            
        Returns:
            str: 友好的错误响应
        """
        # 获取错误详情
        error_type = type(error).__name__
        error_msg = str(error)
        stack_trace = traceback.format_exc()
        
        # 创建错误数据
        error_data = {
            "user_id": user_id,
            "user_input": user_input,
            "stack_trace": stack_trace
        }
        
        # 记录错误
        self._record_error(error_type, error_msg, error_data)
        
        # 发布系统错误事件
        self.event_bus.publish("system_error", {
            "error_type": error_type,
            "message": error_msg,
            "user_id": user_id,
            "stack_trace": stack_trace
        })
        
        # 尝试应用已知解决方案
        if error_type in self.known_errors:
            solution = self.known_errors[error_type].get("solution")
            if solution:
                logger.info(f"尝试自动修复错误: {error_type}")
                self._apply_solution(error_type, solution, error_data)
        
        # 返回友好的错误消息
        return self._generate_friendly_error_message(error_type, error_msg)
    
    def _generate_friendly_error_message(self, error_type: str, error_msg: str) -> str:
        """
        生成友好的错误消息
        
        Args:
            error_type (str): 错误类型
            error_msg (str): 错误消息
            
        Returns:
            str: 友好的错误消息
        """
        # 获取当前上下文
        context = self.life_context.get_all_context()
        current_mood = context.get("current_state", {}).get("mood", "平静")
        
        # 根据当前情绪和错误类型生成友好消息
        if "database" in error_type.lower() or "sql" in error_type.lower():
            return "抱歉，我的记忆系统遇到了一点小问题，请稍后再试。"
        
        elif "timeout" in error_type.lower() or "connection" in error_type.lower():
            return "哎呀，好像网络有点问题，我们稍后再聊好吗？"
        
        elif "memory" in error_type.lower():
            return "我好像想不起来一些事情，让我稍微休息一下。"
        
        elif "value" in error_type.lower():
            return "嗯...我刚刚好像理解错了什么，能请你用不同的方式再说一次吗？"
        
        else:
            # 根据当前情绪调整回复
            if current_mood == "平静" or current_mood == "开心":
                return "哎呀，我遇到了一点小麻烦，让我们换个话题吧！"
            elif current_mood == "困惑" or current_mood == "焦虑":
                return "抱歉，我现在有点困惑，能给我一点时间整理一下思绪吗？"
            else:
                return "对不起，我暂时无法正确回应，请稍后再试。"
    
    def evaluate_response_quality(self, response: str, user_input: str, user_id: str = None) -> float:
        """
        评估响应质量
        
        Args:
            response (str): 响应文本
            user_input (str): 用户输入
            user_id (str, optional): 用户ID
            
        Returns:
            float: 质量评分(0-1)
        """
        # TODO: 实现更复杂的质量评估算法，可接入AI模型
        # 当前使用简单的启发式方法
        
        quality_score = 1.0
        
        # 空响应检查
        if not response or response.strip() == "":
            return 0.0
        
        # 过短响应检查
        if len(response) < 10:
            quality_score -= 0.3
        
        # 通用错误消息检查
        error_phrases = ["出错", "错误", "失败", "抱歉", "对不起", "无法", "不能"]
        if any(phrase in response for phrase in error_phrases):
            quality_score -= 0.2
        
        # 重复内容检查
        words = response.split()
        unique_words = set(words)
        if len(words) > 0:
            repetition_ratio = len(unique_words) / len(words)
            if repetition_ratio < 0.5:  # 高重复率
                quality_score -= 0.2
        
        # 确保分数在0-1范围内
        return max(0.0, min(1.0, quality_score))
    
    def get_error_statistics(self) -> Dict[str, Any]:
        """
        获取错误统计信息
        
        Returns:
            Dict[str, Any]: 错误统计信息
        """
        return {
            "total_errors": sum(self.error_counts.values()),
            "error_types": len(self.error_counts),
            "most_common": sorted(self.error_counts.items(), key=lambda x: x[1], reverse=True)[:5] if self.error_counts else [],
            "known_errors": len(self.known_errors),
            "recent_errors": len(self.recent_errors)
        }
    
    def get_recent_errors(self, limit: int = 10) -> List[Dict[str, Any]]:
        """
        获取最近的错误
        
        Args:
            limit (int, optional): 返回的错误数量
            
        Returns:
            List[Dict[str, Any]]: 最近的错误列表
        """
        return self.recent_errors[-limit:] if self.recent_errors else []
    
    def clear_error_count(self, error_type: str = None):
        """
        清除错误计数
        
        Args:
            error_type (str, optional): 错误类型，如果为None则清除所有
        """
        if error_type:
            if error_type in self.error_counts:
                del self.error_counts[error_type]
                logger.debug(f"已清除错误计数: {error_type}")
        else:
            self.error_counts.clear()
            logger.debug("已清除所有错误计数")
    
    def shutdown(self):
        """关闭自主纠错系统"""
        logger.info("关闭自主纠错系统...")
        
        # 保存最终状态
        self._save_known_errors()
        
        # 更新生命上下文中的系统状态
        self.life_context.update_context("system.error_statistics", self.get_error_statistics())
        
        logger.info("自主纠错系统已关闭")


# 单例模式
_instance = None

def get_instance(config: Dict[str, Any] = None) -> Corrector:
    """
    获取自主纠错系统实例（单例模式）
    
    Args:
        config (Dict[str, Any], optional): 配置参数
        
    Returns:
        Corrector: 自主纠错系统实例
    """
    global _instance
    if _instance is None:
        _instance = Corrector(config)
    return _instance 