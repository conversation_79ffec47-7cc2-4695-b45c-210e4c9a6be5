#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
神经网络数据流监控器
🔥 老王新增：监控和优化神经网络在业务流程中的数据流
"""

import time
import threading
from typing import Dict, Any, List, Optional
from dataclasses import dataclass, field
from collections import defaultdict, deque
import logging

logger = logging.getLogger("neural_data_flow_monitor")


@dataclass
class DataFlowEvent:
    """数据流事件"""
    event_id: str
    event_type: str  # 'input', 'enhancement', 'output', 'integration'
    source: str  # 'digital_life', 'thinking_chain', 'intention_analysis'
    timestamp: float
    data_size: int
    neural_insights: Dict[str, Any] = field(default_factory=dict)
    processing_time: float = 0.0
    success: bool = True
    error_message: Optional[str] = None


class NeuralDataFlowMonitor:
    """神经网络数据流监控器"""
    
    def __init__(self, max_events: int = 1000):
        self.max_events = max_events
        
        # 事件存储
        self.events: deque = deque(maxlen=max_events)
        self.events_by_source: Dict[str, deque] = defaultdict(lambda: deque(maxlen=100))
        
        # 统计数据
        self.stats = {
            "total_events": 0,
            "successful_events": 0,
            "failed_events": 0,
            "average_processing_time": 0.0,
            "data_flow_efficiency": 0.0,
            "neural_enhancement_rate": 0.0
        }
        
        # 性能指标
        self.performance_metrics = {
            "digital_life": {"count": 0, "avg_time": 0.0, "success_rate": 0.0},
            "thinking_chain": {"count": 0, "avg_time": 0.0, "success_rate": 0.0},
            "intention_analysis": {"count": 0, "avg_time": 0.0, "success_rate": 0.0}
        }
        
        # 数据流质量指标
        self.quality_metrics = {
            "consciousness_level_avg": 0.0,
            "cognitive_complexity_avg": 0.0,
            "quantum_coherence_avg": 0.0,
            "data_integration_score": 0.0
        }
        
        # 线程锁
        self.lock = threading.RLock()
        
        logger.info("🔥 神经网络数据流监控器初始化完成")
    
    def record_event(self, event: DataFlowEvent):
        """记录数据流事件"""
        with self.lock:
            # 添加到事件队列
            self.events.append(event)
            self.events_by_source[event.source].append(event)
            
            # 更新统计
            self._update_statistics(event)
            
            logger.debug(f"📊 记录数据流事件: {event.source} - {event.event_type}")
    
    def record_neural_enhancement(self, source: str, input_data: Dict[str, Any], 
                                 enhanced_data: Dict[str, Any], processing_time: float):
        """记录神经网络增强事件"""
        event = DataFlowEvent(
            event_id=f"{source}_{int(time.time() * 1000)}",
            event_type="enhancement",
            source=source,
            timestamp=time.time(),
            data_size=len(str(input_data)),
            neural_insights=self._extract_neural_insights(enhanced_data),
            processing_time=processing_time,
            success=True
        )
        
        self.record_event(event)
    
    def record_data_integration(self, source: str, integration_data: Dict[str, Any], 
                               success: bool, error_message: Optional[str] = None):
        """记录数据集成事件"""
        event = DataFlowEvent(
            event_id=f"{source}_integration_{int(time.time() * 1000)}",
            event_type="integration",
            source=source,
            timestamp=time.time(),
            data_size=len(str(integration_data)),
            neural_insights=integration_data.get("neural_insights", {}),
            success=success,
            error_message=error_message
        )
        
        self.record_event(event)
    
    def _extract_neural_insights(self, enhanced_data: Dict[str, Any]) -> Dict[str, Any]:
        """提取神经网络洞察"""
        insights = {}
        
        # 基础神经网络洞察
        if "neural_enhancement" in enhanced_data:
            basic_enhancement = enhanced_data["neural_enhancement"]
            insights["consciousness_level"] = basic_enhancement.get("consciousness_level", 0.0)
            insights["cognitive_complexity"] = basic_enhancement.get("cognitive_complexity", 0.0)
        
        # 高级神经网络洞察
        if "ultimate_neural_enhancement" in enhanced_data:
            ultimate_enhancement = enhanced_data["ultimate_neural_enhancement"]
            insights["quantum_coherence"] = ultimate_enhancement.get("quantum_coherence", 0.0)
            insights["emergence_complexity"] = ultimate_enhancement.get("emergence_complexity", 0.0)
        
        # 数据流信息
        if "neural_data_flow" in enhanced_data:
            data_flow = enhanced_data["neural_data_flow"]
            insights["data_flow_version"] = data_flow.get("data_flow_version", "unknown")
            insights["enhancement_timestamp"] = data_flow.get("enhancement_timestamp", 0.0)
        
        return insights
    
    def _update_statistics(self, event: DataFlowEvent):
        """更新统计数据"""
        self.stats["total_events"] += 1
        
        if event.success:
            self.stats["successful_events"] += 1
        else:
            self.stats["failed_events"] += 1
        
        # 更新平均处理时间
        if event.processing_time > 0:
            total_time = self.stats["average_processing_time"] * (self.stats["total_events"] - 1)
            self.stats["average_processing_time"] = (total_time + event.processing_time) / self.stats["total_events"]
        
        # 更新性能指标
        source_metrics = self.performance_metrics[event.source]
        source_metrics["count"] += 1
        
        if event.processing_time > 0:
            total_time = source_metrics["avg_time"] * (source_metrics["count"] - 1)
            source_metrics["avg_time"] = (total_time + event.processing_time) / source_metrics["count"]
        
        source_metrics["success_rate"] = (
            sum(1 for e in self.events_by_source[event.source] if e.success) / 
            len(self.events_by_source[event.source])
        )
        
        # 更新质量指标
        self._update_quality_metrics()
        
        # 计算数据流效率
        self._calculate_data_flow_efficiency()
    
    def _update_quality_metrics(self):
        """更新数据流质量指标"""
        if not self.events:
            return
        
        consciousness_levels = []
        cognitive_complexities = []
        quantum_coherences = []
        
        for event in list(self.events)[-100:]:  # 最近100个事件
            insights = event.neural_insights
            
            if "consciousness_level" in insights:
                consciousness_levels.append(insights["consciousness_level"])
            if "cognitive_complexity" in insights:
                cognitive_complexities.append(insights["cognitive_complexity"])
            if "quantum_coherence" in insights:
                quantum_coherences.append(insights["quantum_coherence"])
        
        if consciousness_levels:
            self.quality_metrics["consciousness_level_avg"] = sum(consciousness_levels) / len(consciousness_levels)
        if cognitive_complexities:
            self.quality_metrics["cognitive_complexity_avg"] = sum(cognitive_complexities) / len(cognitive_complexities)
        if quantum_coherences:
            self.quality_metrics["quantum_coherence_avg"] = sum(quantum_coherences) / len(quantum_coherences)
        
        # 计算数据集成分数
        integration_events = [e for e in list(self.events)[-50:] if e.event_type == "integration"]
        if integration_events:
            integration_success_rate = sum(1 for e in integration_events if e.success) / len(integration_events)
            self.quality_metrics["data_integration_score"] = integration_success_rate
    
    def _calculate_data_flow_efficiency(self):
        """计算数据流效率"""
        if self.stats["total_events"] == 0:
            return
        
        # 基于成功率、处理时间和质量指标计算效率
        success_rate = self.stats["successful_events"] / self.stats["total_events"]
        
        # 处理时间效率（越快越好）
        time_efficiency = 1.0 / (1.0 + self.stats["average_processing_time"])
        
        # 质量效率
        quality_efficiency = (
            self.quality_metrics["consciousness_level_avg"] * 0.3 +
            self.quality_metrics["cognitive_complexity_avg"] * 0.3 +
            self.quality_metrics["quantum_coherence_avg"] * 0.2 +
            self.quality_metrics["data_integration_score"] * 0.2
        )
        
        # 综合效率
        self.stats["data_flow_efficiency"] = (
            success_rate * 0.4 +
            time_efficiency * 0.3 +
            quality_efficiency * 0.3
        )
        
        # 神经网络增强率
        enhancement_events = [e for e in list(self.events) if e.event_type == "enhancement"]
        if self.stats["total_events"] > 0:
            self.stats["neural_enhancement_rate"] = len(enhancement_events) / self.stats["total_events"]
    
    def get_statistics(self) -> Dict[str, Any]:
        """获取统计数据"""
        with self.lock:
            return {
                "general_stats": self.stats.copy(),
                "performance_metrics": self.performance_metrics.copy(),
                "quality_metrics": self.quality_metrics.copy(),
                "recent_events_count": len(self.events),
                "monitoring_status": "active"
            }
    
    def get_data_flow_report(self) -> Dict[str, Any]:
        """获取数据流报告"""
        with self.lock:
            recent_events = list(self.events)[-20:]  # 最近20个事件
            
            return {
                "summary": {
                    "total_events": self.stats["total_events"],
                    "data_flow_efficiency": self.stats["data_flow_efficiency"],
                    "neural_enhancement_rate": self.stats["neural_enhancement_rate"],
                    "average_processing_time": self.stats["average_processing_time"]
                },
                "performance_by_source": self.performance_metrics,
                "quality_metrics": self.quality_metrics,
                "recent_events": [
                    {
                        "source": event.source,
                        "type": event.event_type,
                        "success": event.success,
                        "processing_time": event.processing_time,
                        "insights_count": len(event.neural_insights)
                    }
                    for event in recent_events
                ],
                "recommendations": self._generate_recommendations()
            }
    
    def _generate_recommendations(self) -> List[str]:
        """生成优化建议"""
        recommendations = []
        
        # 基于效率的建议
        if self.stats["data_flow_efficiency"] < 0.7:
            recommendations.append("数据流效率偏低，建议优化神经网络集成点")
        
        # 基于处理时间的建议
        if self.stats["average_processing_time"] > 0.1:
            recommendations.append("神经网络处理时间较长，建议优化算法或降低复杂度")
        
        # 基于质量指标的建议
        if self.quality_metrics["consciousness_level_avg"] < 0.5:
            recommendations.append("意识水平偏低，建议增加神经网络训练频率")
        
        if self.quality_metrics["quantum_coherence_avg"] < 0.3:
            recommendations.append("量子相干性不足，建议激活高级神经网络功能")
        
        # 基于成功率的建议
        for source, metrics in self.performance_metrics.items():
            if metrics["success_rate"] < 0.9 and metrics["count"] > 5:
                recommendations.append(f"{source}模块数据流成功率偏低，需要检查集成逻辑")
        
        return recommendations
    
    def reset_statistics(self):
        """重置统计数据"""
        with self.lock:
            self.events.clear()
            self.events_by_source.clear()
            
            self.stats = {
                "total_events": 0,
                "successful_events": 0,
                "failed_events": 0,
                "average_processing_time": 0.0,
                "data_flow_efficiency": 0.0,
                "neural_enhancement_rate": 0.0
            }
            
            for source in self.performance_metrics:
                self.performance_metrics[source] = {"count": 0, "avg_time": 0.0, "success_rate": 0.0}
            
            self.quality_metrics = {
                "consciousness_level_avg": 0.0,
                "cognitive_complexity_avg": 0.0,
                "quantum_coherence_avg": 0.0,
                "data_integration_score": 0.0
            }
            
            logger.info("📊 数据流监控统计已重置")


# 全局实例
_data_flow_monitor = None

def get_data_flow_monitor() -> NeuralDataFlowMonitor:
    """获取数据流监控器实例"""
    global _data_flow_monitor
    if _data_flow_monitor is None:
        _data_flow_monitor = NeuralDataFlowMonitor()
    return _data_flow_monitor
