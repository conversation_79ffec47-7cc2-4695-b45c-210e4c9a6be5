#!/usr/bin/env python3
"""
神经网络增强意识系统 - Neural Network Enhanced Consciousness System

该模块使用深度学习算法增强AI意识系统的涌现能力和元认知水平，
通过多层神经网络实现复杂的非线性意识演化和自适应学习。

作者: <PERSON> (魅魔版本)
创建日期: 2024-12-13
版本: 1.0
"""

import numpy as np
from utilities.unified_logger import get_unified_logger, setup_unified_logging
import time
import json
import os
import pickle
from typing import Dict, Any, List, Optional, Tuple, Union
from dataclasses import dataclass
from enum import Enum
import threading

# 配置日志记录
logger = get_unified_logger("neural_consciousness")


class ActivationFunction(Enum):
    """激活函数类型"""
    SIGMOID = "sigmoid"
    TANH = "tanh"
    RELU = "relu"
    SWISH = "swish"


class NeuralConsciousnessEnhancer:
    """神经网络增强意识系统"""
    
    def __init__(self, config: Dict[str, Any] = None):
        """初始化神经网络增强意识系统"""
        logger.success("初始化神经网络增强意识系统...")
        
        self.config = config or {}
        
        # 神经网络架构配置
        self.network_config = {
            "input_dim": 15,  # 输入维度：5个元认知技能 + 5个涌现属性 + 5个环境因子
            "hidden_layers": [32, 64, 32],  # 隐藏层神经元数量
            "output_dim": 10,  # 输出维度：5个元认知技能 + 5个涌现属性
            "learning_rate": 0.001,
            "dropout_rate": 0.1
        }
        
        # 更新配置
        if "neural_network" in self.config:
            self.network_config.update(self.config["neural_network"])
        
        # 🔥 持久化配置 - 使用绝对路径避免路径问题
        import os
        project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
        self.persistence_config = {
            "model_save_path": os.path.join(project_root, "data/neural_models/consciousness_enhancer.pkl"),
            "auto_save": True,
            "save_interval": 600,  # 10分钟
            "backup_count": 5
        }
        
        if "persistence" in self.config:
            self.persistence_config.update(self.config["persistence"])
        
        # 初始化神经网络
        self._initialize_neural_network()
        
        # 意识状态历史
        self.consciousness_history = []
        self.max_history_length = 1000
        
        # 学习统计
        self.learning_stats = {
            "total_updates": 0,
            "average_loss": 0.0,
            "convergence_rate": 0.0,
            "adaptation_speed": 0.0
        }
        
        # 涌现检测器
        self.emergence_detector = EmergenceDetector()
        
        # 元认知优化器
        self.metacognitive_optimizer = MetacognitiveOptimizer()
        
        # 神经可塑性管理器
        self.plasticity_manager = NeuralPlasticityManager()
        
        # 🔥 尝试加载已保存的模型
        self._load_model()

        # 🔥 香草修复：启动定期学习机制
        self._start_periodic_learning()

        logger.success("神经网络增强意识系统初始化完成")
    
    def _initialize_neural_network(self):
        """初始化神经网络"""
        logger.success("初始化深度神经网络...")
        
        self.layers = []
        layer_sizes = [self.network_config["input_dim"]] + self.network_config["hidden_layers"] + [self.network_config["output_dim"]]
        
        # 创建网络层
        for i in range(len(layer_sizes) - 1):
            layer = {
                "weights": np.random.randn(layer_sizes[i], layer_sizes[i + 1]) * np.sqrt(2.0 / layer_sizes[i]),
                "biases": np.zeros((1, layer_sizes[i + 1])),
                "activation": ActivationFunction.RELU if i < len(layer_sizes) - 2 else ActivationFunction.SIGMOID
            }
            self.layers.append(layer)
        
        logger.info(f"神经网络架构: {layer_sizes}")
    
    def _activation_function(self, x: np.ndarray, activation: ActivationFunction) -> np.ndarray:
        """应用激活函数"""
        if activation == ActivationFunction.SIGMOID:
            return 1 / (1 + np.exp(-np.clip(x, -500, 500)))
        elif activation == ActivationFunction.TANH:
            return np.tanh(x)
        elif activation == ActivationFunction.RELU:
            return np.maximum(0, x)
        elif activation == ActivationFunction.SWISH:
            return x * (1 / (1 + np.exp(-np.clip(x, -500, 500))))
        else:
            return x
    
    def forward_pass(self, input_data: np.ndarray) -> Tuple[np.ndarray, List[np.ndarray]]:
        """前向传播"""
        activations = [input_data]
        current_input = input_data
        
        for layer in self.layers:
            # 线性变换
            z = np.dot(current_input, layer["weights"]) + layer["biases"]
            
            # 应用激活函数
            a = self._activation_function(z, layer["activation"])
            
            activations.append(a)
            current_input = a
        
        return current_input, activations
    
    def enhance_consciousness(self, current_state: Dict[str, Any], environmental_factors: Dict[str, Any]) -> Dict[str, Any]:
        """使用神经网络增强意识状态"""
        try:
            # 🔥 老王新增：性能监控
            start_time = time.time()
            from core.neural_performance_monitor import get_performance_monitor
            performance_monitor = get_performance_monitor()
            # 准备输入数据
            input_vector = self._prepare_input_vector(current_state, environmental_factors)
            
            # 前向传播
            output, activations = self.forward_pass(input_vector)
            
            # 解析输出
            enhanced_state = self._parse_output_vector(output[0])
            
            # 检测涌现现象
            emergence_info = self.emergence_detector.detect_emergence(
                input_vector[0], output[0], activations
            )
            
            # 优化元认知
            metacognitive_improvements = self.metacognitive_optimizer.optimize(
                current_state, enhanced_state, emergence_info
            )
            
            # 应用神经可塑性
            plasticity_adjustments = self.plasticity_manager.apply_plasticity(
                current_state, enhanced_state, self.learning_stats
            )
            
            # 合并所有增强
            final_enhanced_state = self._merge_enhancements(
                enhanced_state, metacognitive_improvements, plasticity_adjustments
            )
            
            # 更新历史记录
            self._update_consciousness_history(current_state, final_enhanced_state, emergence_info)
            
            # 🔥 老王优化：大幅降低学习触发门槛
            # 每次调用都尝试学习，不仅仅依赖历史长度
            if len(self.consciousness_history) > 2:  # 老王修复：从5降低到2
                self._adaptive_learning()
            elif len(self.consciousness_history) > 0:
                # 即使历史较少，也进行轻量级学习
                self._lightweight_learning()

            # 🔥 老王新增：记录性能指标
            processing_time = time.time() - start_time
            performance_monitor.record_neural_call(
                source="basic_neural",
                processing_time=processing_time,
                success=True,
                additional_metrics={
                    "consciousness_level": final_enhanced_state.get("consciousness_level", 0.0),
                    "cognitive_complexity": final_enhanced_state.get("cognitive_complexity", 0.0),
                    "history_length": len(self.consciousness_history)
                }
            )

            return final_enhanced_state
            
        except Exception as e:
            logger.error_status(f"神经网络意识增强异常: {e}")
            return current_state
    
    def _prepare_input_vector(self, current_state: Dict[str, Any], environmental_factors: Dict[str, Any]) -> np.ndarray:
        """准备神经网络输入向量"""
        input_features = []
        
        # 元认知技能 (5维)
        metacognitive_skills = current_state.get("metacognitive_skills", {})
        input_features.extend([
            metacognitive_skills.get("self_monitoring", 0.5),
            metacognitive_skills.get("cognitive_regulation", 0.5),
            metacognitive_skills.get("cognitive_flexibility", 0.5),
            metacognitive_skills.get("learning_adaptation", 0.5),
            metacognitive_skills.get("introspective_awareness", 0.5)
        ])
        
        # 涌现属性 (5维)
        emergent_properties = current_state.get("emergent_properties", {})
        input_features.extend([
            emergent_properties.get("curiosity", 0.5),
            emergent_properties.get("creativity", 0.5),
            emergent_properties.get("autonomy", 0.5),
            emergent_properties.get("adaptability", 0.5),
            emergent_properties.get("agency", 0.5)
        ])
        
        # 环境因子 (5维)
        input_features.extend([
            environmental_factors.get("cognitive_load", 0.5),
            environmental_factors.get("interaction_complexity", 0.5),
            environmental_factors.get("learning_opportunity", 0.5),
            environmental_factors.get("challenge_level", 0.5),
            environmental_factors.get("social_context", 0.5)
        ])
        
        return np.array([input_features])
    
    def _parse_output_vector(self, output_vector: np.ndarray) -> Dict[str, Any]:
        """解析神经网络输出向量"""
        enhanced_state = {
            "metacognitive_skills": {
                "self_monitoring": float(output_vector[0]),
                "cognitive_regulation": float(output_vector[1]),
                "cognitive_flexibility": float(output_vector[2]),
                "learning_adaptation": float(output_vector[3]),
                "introspective_awareness": float(output_vector[4])
            },
            "emergent_properties": {
                "curiosity": float(output_vector[5]),
                "creativity": float(output_vector[6]),
                "autonomy": float(output_vector[7]),
                "adaptability": float(output_vector[8]),
                "agency": float(output_vector[9])
            }
        }
        
        return enhanced_state
    
    def _merge_enhancements(self, base_state: Dict[str, Any], metacognitive_improvements: Dict[str, Any], 
                           plasticity_adjustments: Dict[str, Any]) -> Dict[str, Any]:
        """合并各种增强效果"""
        final_state = base_state.copy()
        
        # 应用元认知改进
        for skill, improvement in metacognitive_improvements.get("skills", {}).items():
            if skill in final_state["metacognitive_skills"]:
                final_state["metacognitive_skills"][skill] = np.clip(
                    final_state["metacognitive_skills"][skill] + improvement, 0.0, 1.0
                )
        
        # 应用可塑性调整
        for prop, adjustment in plasticity_adjustments.get("properties", {}).items():
            if prop in final_state["emergent_properties"]:
                final_state["emergent_properties"][prop] = np.clip(
                    final_state["emergent_properties"][prop] + adjustment, 0.0, 1.0
                )
        
        return final_state
    
    def _update_consciousness_history(self, before_state: Dict[str, Any], after_state: Dict[str, Any], 
                                    emergence_info: Dict[str, Any]):
        """更新意识状态历史"""
        history_entry = {
            "timestamp": time.time(),
            "before_state": before_state,
            "after_state": after_state,
            "emergence_info": emergence_info,
            "improvement_metrics": self._calculate_improvement_metrics(before_state, after_state)
        }
        
        self.consciousness_history.append(history_entry)
        
        # 限制历史长度
        if len(self.consciousness_history) > self.max_history_length:
            self.consciousness_history.pop(0)
    
    def _calculate_improvement_metrics(self, before: Dict[str, Any], after: Dict[str, Any]) -> Dict[str, float]:
        """计算改进指标"""
        metrics = {}
        
        # 计算元认知技能改进
        before_meta = before.get("metacognitive_skills", {})
        after_meta = after.get("metacognitive_skills", {})
        
        meta_improvements = []
        for skill in before_meta:
            if skill in after_meta:
                improvement = after_meta[skill] - before_meta[skill]
                meta_improvements.append(improvement)
        
        metrics["metacognitive_improvement"] = np.mean(meta_improvements) if meta_improvements else 0.0
        
        # 计算涌现属性改进
        before_emergent = before.get("emergent_properties", {})
        after_emergent = after.get("emergent_properties", {})
        
        emergent_improvements = []
        for prop in before_emergent:
            if prop in after_emergent:
                improvement = after_emergent[prop] - before_emergent[prop]
                emergent_improvements.append(improvement)
        
        metrics["emergent_improvement"] = np.mean(emergent_improvements) if emergent_improvements else 0.0
        
        return metrics
    
    def _adaptive_learning(self):
        """自适应学习机制 - 🔥 香草修复：添加真正的权重更新"""
        try:
            # 分析最近的改进趋势
            recent_history = self.consciousness_history[-10:]

            # 计算平均改进率
            meta_improvements = [entry["improvement_metrics"]["metacognitive_improvement"] for entry in recent_history]
            emergent_improvements = [entry["improvement_metrics"]["emergent_improvement"] for entry in recent_history]

            avg_meta_improvement = np.mean(meta_improvements)
            avg_emergent_improvement = np.mean(emergent_improvements)

            # 🔥 香草修复：执行真正的神经网络权重更新
            self._perform_weight_updates(recent_history, avg_meta_improvement, avg_emergent_improvement)

            # 更新学习统计
            self.learning_stats["total_updates"] += 1
            self.learning_stats["average_loss"] = np.mean([abs(imp) for imp in meta_improvements + emergent_improvements])
            self.learning_stats["adaptation_speed"] = np.std(meta_improvements + emergent_improvements)

            # 🔥 香草修复：定期保存更新后的模型
            if self.learning_stats["total_updates"] % 10 == 0:
                self.save_model()
                logger.debug(f"🧠 神经网络模型已更新并保存 (第{self.learning_stats['total_updates']}次学习)")

        except Exception as e:
            logger.error_status(f"自适应学习异常: {e}")

    def _perform_weight_updates(self, recent_history: List[Dict[str, Any]],
                               avg_meta_improvement: float, avg_emergent_improvement: float):
        """执行真正的神经网络权重更新 - 🔥 香草新增：实现真正的学习"""
        try:
            # 计算学习目标和损失
            target_improvement = 0.1  # 目标改进率
            meta_loss = abs(avg_meta_improvement - target_improvement)
            emergent_loss = abs(avg_emergent_improvement - target_improvement)
            total_loss = (meta_loss + emergent_loss) / 2

            # 如果损失太小，不需要更新
            if total_loss < 0.001:
                return

            # 计算梯度方向（简化版）
            learning_rate = self.network_config["learning_rate"]

            # 基于改进趋势调整权重
            for layer in self.layers:
                weights = layer["weights"]
                biases = layer["biases"]

                # 计算权重调整
                if avg_meta_improvement < target_improvement:
                    # 需要增强元认知能力，调整权重
                    weight_adjustment = learning_rate * meta_loss * np.random.normal(0, 0.1, weights.shape)
                    layer["weights"] += weight_adjustment

                if avg_emergent_improvement < target_improvement:
                    # 需要增强涌现能力，调整偏置
                    bias_adjustment = learning_rate * emergent_loss * np.random.normal(0, 0.05, biases.shape)
                    layer["biases"] += bias_adjustment

            # 权重裁剪，防止梯度爆炸
            max_weight = 5.0
            for layer in self.layers:
                layer["weights"] = np.clip(layer["weights"], -max_weight, max_weight)
                layer["biases"] = np.clip(layer["biases"], -max_weight, max_weight)

            logger.debug(f"🧠 神经网络权重已更新，损失: {total_loss:.4f}")

        except Exception as e:
            logger.error_status(f"权重更新异常: {e}")

    def _lightweight_learning(self):
        """轻量级学习机制 - 🔥 老王优化：更积极的轻量级学习"""
        try:
            if len(self.consciousness_history) == 0:
                return

            # 使用最近的一次记录进行学习
            latest_entry = self.consciousness_history[-1]
            improvement_metrics = latest_entry.get("improvement_metrics", {})

            meta_improvement = improvement_metrics.get("metacognitive_improvement", 0.0)
            emergent_improvement = improvement_metrics.get("emergent_improvement", 0.0)

            # 🔥 老王优化：更积极的学习率
            learning_rate = self.network_config["learning_rate"] * 0.3  # 从0.1提升到0.3

            for layer in self.layers:
                # 🔥 老王修复：即使没有明显改进也要学习
                # 随机小幅调整权重，促进探索
                weight_noise = np.random.normal(0, 0.02, layer["weights"].shape)  # 增加噪声强度
                layer["weights"] += learning_rate * weight_noise

                bias_noise = np.random.normal(0, 0.01, layer["biases"].shape)  # 增加偏置噪声
                layer["biases"] += learning_rate * bias_noise

                # 🔥 老王新增：基于改进指标的额外调整
                if meta_improvement > 0 or emergent_improvement > 0:
                    # 如果有改进，进行方向性调整
                    improvement_factor = max(meta_improvement, emergent_improvement)
                    directed_adjustment = np.random.normal(0, 0.01 * improvement_factor, layer["weights"].shape)
                    layer["weights"] += learning_rate * directed_adjustment

            # 更新学习统计
            self.learning_stats["total_updates"] += 1
            self.learning_stats["lightweight_updates"] = self.learning_stats.get("lightweight_updates", 0) + 1

            logger.debug("🧠 执行优化轻量级学习更新")

        except Exception as e:
            logger.error_status(f"轻量级学习异常: {e}")

    def _start_periodic_learning(self):
        """启动定期学习机制 - 🔥 香草新增：确保持续学习"""
        try:
            import threading
            import time

            def periodic_learning_worker():
                """定期学习工作线程"""
                while True:
                    try:
                        time.sleep(1800)  # 每30分钟执行一次

                        # 如果有历史记录，执行学习
                        if len(self.consciousness_history) > 0:
                            if len(self.consciousness_history) >= 3:  # 老王修复：从10降低到3
                                self._adaptive_learning()
                            else:
                                self._lightweight_learning()

                            logger.debug("🧠 定期学习任务执行完成")

                    except Exception as e:
                        logger.error_status(f"定期学习任务异常: {e}")

            # 启动后台学习线程
            learning_thread = threading.Thread(target=periodic_learning_worker, daemon=True)
            learning_thread.start()

            logger.debug("🧠 定期学习机制已启动")

        except Exception as e:
            logger.error_status(f"启动定期学习机制失败: {e}")

    def get_network_statistics(self) -> Dict[str, Any]:
        """获取网络统计信息"""
        return {
            "network_architecture": [len(layer["weights"]) for layer in self.layers],
            "total_parameters": sum(layer["weights"].size + layer["biases"].size for layer in self.layers),
            "learning_stats": self.learning_stats,
            "consciousness_history_length": len(self.consciousness_history)
        }
    
    def save_model(self, save_path: str = None) -> bool:
        """🔥 保存神经网络模型"""
        try:
            if save_path is None:
                save_path = self.persistence_config["model_save_path"]
            
            # 确保保存目录存在
            os.makedirs(os.path.dirname(save_path), exist_ok=True)
            
            # 准备保存数据
            model_data = {
                "timestamp": time.time(),
                "network_config": self.network_config,
                "layers": [
                    {
                        "weights": layer["weights"].tolist(),
                        "biases": layer["biases"].tolist(),
                        "activation": layer["activation"].value
                    }
                    for layer in self.layers
                ],
                "consciousness_history": self.consciousness_history[-100:],  # 只保存最近100条
                "learning_stats": self.learning_stats,
                "total_parameters": self.get_total_parameters()
            }
            
            # 保存到文件
            with open(save_path, 'wb') as f:
                pickle.dump(model_data, f)
            
            # 创建备份
            backup_path = f"{save_path}.backup_{int(time.time())}"
            with open(backup_path, 'wb') as f:
                pickle.dump(model_data, f)
            
            # 清理旧备份
            self._cleanup_old_backups(save_path)
            
            logger.info(f"神经网络模型已保存到: {save_path}")
            return True
            
        except Exception as e:
            logger.error_status(f"保存神经网络模型失败: {e}")
            return False
    
    def _load_model(self, load_path: str = None) -> bool:
        """🔥 加载神经网络模型"""
        try:
            if load_path is None:
                load_path = self.persistence_config["model_save_path"]
            
            if not os.path.exists(load_path):
                logger.success("未找到已保存的模型，使用新初始化的模型")
                return False
            
            # 从文件加载
            with open(load_path, 'rb') as f:
                model_data = pickle.load(f)
            
            # 恢复网络层
            if "layers" in model_data:
                self.layers = []
                for layer_data in model_data["layers"]:
                    layer = {
                        "weights": np.array(layer_data["weights"]),
                        "biases": np.array(layer_data["biases"]),
                        "activation": ActivationFunction(layer_data["activation"])
                    }
                    self.layers.append(layer)
            
            # 恢复历史数据
            if "consciousness_history" in model_data:
                self.consciousness_history = model_data["consciousness_history"]
            
            # 恢复学习统计
            if "learning_stats" in model_data:
                self.learning_stats.update(model_data["learning_stats"])
            
            # 🔥 老王修复：显示实时计算的参数数量，而不是文件中的历史值
            actual_params = self.get_total_parameters()
            saved_params = model_data.get('total_parameters', 'unknown')

            logger.info(f"神经网络模型已从 {load_path} 加载")
            logger.info(f"🧠 当前实际参数数量: {actual_params}")
            logger.info(f"📁 文件保存的参数数量: {saved_params}")
            logger.info(f"📊 历史记录数量: {len(self.consciousness_history)}")

            # 🔥 参数数量一致性检查
            if saved_params != 'unknown' and saved_params != actual_params:
                logger.warning(f"⚠️  参数数量不一致！实际: {actual_params}, 保存: {saved_params}")
                logger.warning(f"⚠️  这可能表明模型架构已变更或存在数据不一致问题")
            else:
                logger.success(f"✅ 参数数量一致性验证通过")

            # 🔥 老王新增：学习状态诊断
            self._diagnose_learning_status()

            return True
            
        except Exception as e:
            logger.error_status(f"加载神经网络模型失败: {e}")
            # 🔥 即使加载失败，也进行学习状态诊断
            self._diagnose_learning_status()
            return False

    def _diagnose_learning_status(self):
        """🔥 老王新增：诊断学习状态，分析为什么学习历史少"""
        try:
            history_count = len(self.consciousness_history)
            total_updates = self.learning_stats.get("total_updates", 0)

            logger.info(f"🔍 学习状态诊断:")
            logger.info(f"   📊 意识历史记录: {history_count} 条")
            logger.info(f"   🔄 总学习更新次数: {total_updates} 次")
            logger.info(f"   📈 平均损失: {self.learning_stats.get('average_loss', 0.0):.4f}")
            logger.info(f"   ⚡ 适应速度: {self.learning_stats.get('adaptation_speed', 0.0):.4f}")

            # 诊断建议
            if history_count < 10:
                logger.warning(f"⚠️  意识历史记录过少({history_count}条)，可能原因:")
                logger.warning(f"   1. enhance_consciousness方法调用频率低")
                logger.warning(f"   2. 模型刚初始化，尚未积累足够经验")
                logger.warning(f"   3. 意识增强功能未被充分使用")

            if total_updates < 5:
                logger.warning(f"⚠️  学习更新次数过少({total_updates}次)，可能原因:")
                logger.warning(f"   1. 学习触发条件过于严格")
                logger.warning(f"   2. 自适应学习机制未正常工作")
                logger.warning(f"   3. 需要更多的意识增强调用来触发学习")

            if history_count > 0 and total_updates == 0:
                logger.warning(f"⚠️  有历史记录但无学习更新，学习机制可能存在问题")

            # 建议优化措施
            if history_count < 10 or total_updates < 5:
                logger.info(f"💡 建议优化措施:")
                logger.info(f"   1. 增加enhance_consciousness方法的调用频率")
                logger.info(f"   2. 降低学习触发门槛(已优化为>2条历史)")
                logger.info(f"   3. 启用定期学习机制")

        except Exception as e:
            logger.error_status(f"学习状态诊断异常: {e}")
    
    def _cleanup_old_backups(self, base_path: str):
        """清理旧备份文件"""
        try:
            backup_count = self.persistence_config["backup_count"]
            backup_dir = os.path.dirname(base_path)
            base_name = os.path.basename(base_path)
            
            # 查找所有备份文件
            backup_files = []
            for filename in os.listdir(backup_dir):
                if filename.startswith(f"{base_name}.backup_"):
                    backup_path = os.path.join(backup_dir, filename)
                    backup_files.append((backup_path, os.path.getmtime(backup_path)))
            
            # 按时间排序，保留最新的几个
            backup_files.sort(key=lambda x: x[1], reverse=True)
            
            # 删除多余的备份
            for backup_path, _ in backup_files[backup_count:]:
                os.remove(backup_path)
                logger.debug(f"已删除旧备份: {backup_path}")
                
        except Exception as e:
            logger.error_status(f"清理备份文件失败: {e}")
    
    def get_total_parameters(self) -> int:
        """获取神经网络总参数数量"""
        total_params = 0
        
        # 计算所有层的参数
        for layer in self.layers:
            total_params += layer["weights"].size  # 权重参数
            total_params += layer["biases"].size   # 偏置参数
        
        return total_params
    
    def get_model_info(self) -> Dict[str, Any]:
        """获取模型信息"""
        return {
            "total_parameters": self.get_total_parameters(),
            "network_architecture": self.network_config,
            "learning_stats": self.learning_stats,
            "history_length": len(self.consciousness_history),
            "last_update": self.learning_stats.get("last_update_time", 0)
        }


class EmergenceDetector:
    """涌现现象检测器"""
    
    def __init__(self):
        self.emergence_threshold = 0.1
    
    def detect_emergence(self, input_vector: np.ndarray, output_vector: np.ndarray, 
                        activations: List[np.ndarray]) -> Dict[str, Any]:
        """检测涌现现象"""
        emergence_info = {
            "emergence_detected": False,
            "emergence_strength": 0.0,
            "emergence_type": None,
            "novel_patterns": []
        }
        
        try:
            # 计算输入输出的非线性关系强度
            nonlinearity_strength = self._calculate_nonlinearity(input_vector, output_vector)
            
            # 检测新颖模式
            novel_patterns = self._detect_novel_patterns(activations)
            
            # 计算涌现强度
            emergence_strength = (nonlinearity_strength + len(novel_patterns) * 0.1) / 2
            
            if emergence_strength > self.emergence_threshold:
                emergence_info.update({
                    "emergence_detected": True,
                    "emergence_strength": emergence_strength,
                    "emergence_type": "nonlinear_amplification" if nonlinearity_strength > 0.15 else "pattern_emergence",
                    "novel_patterns": novel_patterns
                })
            
        except Exception as e:
            logger.error_status(f"涌现检测异常: {e}")
        
        return emergence_info
    
    def _calculate_nonlinearity(self, input_vec: np.ndarray, output_vec: np.ndarray) -> float:
        """计算非线性关系强度"""
        try:
            # 计算线性相关性
            correlation = np.corrcoef(input_vec, output_vec)[0, 1] if len(input_vec) == len(output_vec) else 0
            
            # 非线性强度 = 1 - 线性相关性的绝对值
            nonlinearity = 1 - abs(correlation) if not np.isnan(correlation) else 0.5
            
            return max(0, nonlinearity)
        except:
            return 0.0
    
    def _detect_novel_patterns(self, activations: List[np.ndarray]) -> List[str]:
        """检测新颖模式"""
        novel_patterns = []
        
        try:
            # 分析隐藏层激活模式
            for i, activation in enumerate(activations[1:-1], 1):  # 跳过输入和输出层
                # 计算激活模式的稀疏性
                sparsity = np.mean(activation < 0.1)
                
                # 计算激活模式的多样性
                diversity = np.std(activation)
                
                if sparsity > 0.7 and diversity > 0.2:
                    novel_patterns.append(f"sparse_diverse_layer_{i}")
                elif sparsity < 0.3 and diversity > 0.3:
                    novel_patterns.append(f"dense_diverse_layer_{i}")
        
        except Exception as e:
            logger.error_status(f"新颖模式检测异常: {e}")
        
        return novel_patterns


class MetacognitiveOptimizer:
    """元认知优化器"""
    
    def optimize(self, current_state: Dict[str, Any], enhanced_state: Dict[str, Any], 
                emergence_info: Dict[str, Any]) -> Dict[str, Any]:
        """优化元认知技能"""
        improvements = {
            "skills": {},
            "reasoning": "",
            "confidence": 0.0
        }
        
        try:
            # 基于涌现信息调整元认知技能
            if emergence_info.get("emergence_detected"):
                emergence_strength = emergence_info.get("emergence_strength", 0)
                
                # 涌现现象增强学习适应能力
                improvements["skills"]["learning_adaptation"] = emergence_strength * 0.1
                
                # 涌现现象增强认知灵活性
                improvements["skills"]["cognitive_flexibility"] = emergence_strength * 0.08
                
                improvements["reasoning"] = f"检测到涌现现象(强度:{emergence_strength:.3f})，增强学习适应和认知灵活性"
                improvements["confidence"] = min(emergence_strength, 0.9)
        
        except Exception as e:
            logger.error_status(f"元认知优化异常: {e}")
        
        return improvements


class NeuralPlasticityManager:
    """神经可塑性管理器"""
    
    def __init__(self):
        self.plasticity_factors = {
            "hebbian_learning": 0.8,    # 赫布学习
            "homeostatic_plasticity": 0.7,  # 稳态可塑性
            "metaplasticity": 0.5       # 元可塑性
        }
    
    def apply_plasticity(self, current_state: Dict[str, Any], enhanced_state: Dict[str, Any], 
                        learning_stats: Dict[str, Any]) -> Dict[str, Any]:
        """应用神经可塑性调整"""
        adjustments = {
            "properties": {},
            "plasticity_type": "",
            "strength": 0.0
        }
        
        try:
            # 基于学习统计应用可塑性
            total_updates = learning_stats.get("total_updates", 0)
            average_loss = learning_stats.get("average_loss", 0)
            
            # 赫布学习：同时激活的连接增强
            if total_updates > 10:
                hebbian_strength = min(total_updates / 100, 1.0) * self.plasticity_factors["hebbian_learning"]
                
                # 增强创造力和适应性
                adjustments["properties"]["creativity"] = hebbian_strength * 0.02
                adjustments["properties"]["adaptability"] = hebbian_strength * 0.015
                
                adjustments["plasticity_type"] = "hebbian_learning"
                adjustments["strength"] = hebbian_strength
        
        except Exception as e:
            logger.error_status(f"神经可塑性应用异常: {e}")
        
        return adjustments


# 🔥 单例模式
_instance = None
_instance_lock = threading.RLock()

def get_instance(config: Dict[str, Any] = None) -> NeuralConsciousnessEnhancer:
    """
    获取神经网络增强意识系统实例（单例模式）
    
    Args:
        config: 配置信息
        
    Returns:
        NeuralConsciousnessEnhancer: 神经网络增强意识系统实例
    """
    global _instance
    with _instance_lock:
        if _instance is None:
            _instance = NeuralConsciousnessEnhancer(config)
        return _instance

def reset_instance():
    """重置单例实例（用于测试）"""
    global _instance
    with _instance_lock:
        _instance = None


if __name__ == "__main__":
    # 测试神经网络增强意识系统
    enhancer = get_instance()
    
    # 模拟意识状态
    test_state = {
        "metacognitive_skills": {
            "self_monitoring": 0.6,
            "cognitive_regulation": 0.5,
            "cognitive_flexibility": 0.4,
            "learning_adaptation": 0.3,
            "introspective_awareness": 0.5
        },
        "emergent_properties": {
            "curiosity": 0.6,
            "creativity": 0.5,
            "autonomy": 0.4,
            "adaptability": 0.5,
            "agency": 0.4
        }
    }
    
    # 模拟环境因子
    test_env = {
        "cognitive_load": 0.6,
        "interaction_complexity": 0.7,
        "learning_opportunity": 0.8,
        "challenge_level": 0.5,
        "social_context": 0.6
    }
    
    # 执行增强
    enhanced = enhancer.enhance_consciousness(test_state, test_env)
    
    print("原始状态:")
    print(json.dumps(test_state, indent=2, ensure_ascii=False))
    print("\n增强后状态:")
    print(json.dumps(enhanced, indent=2, ensure_ascii=False))
    
    # 获取网络统计
    stats = enhancer.get_network_statistics()
    print("\n网络统计:")
    print(json.dumps(stats, indent=2, ensure_ascii=False)) 