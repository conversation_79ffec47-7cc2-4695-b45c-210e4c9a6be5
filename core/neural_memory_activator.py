#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
神经网络记忆系统激活器
🔥 老王新增：激活和优化神经网络记忆系统，提高记忆利用率
"""

import time
import threading
import json
import os
from typing import Dict, Any, List, Optional
from dataclasses import dataclass, field
import numpy as np
import logging

logger = logging.getLogger("neural_memory_activator")


@dataclass
class MemoryEntry:
    """记忆条目"""
    content: Dict[str, Any]
    timestamp: float
    importance: float = 1.0
    access_count: int = 0
    memory_type: str = "general"  # general, episodic, semantic, procedural
    tags: List[str] = field(default_factory=list)
    embedding: Optional[np.ndarray] = None


class NeuralMemoryActivator:
    """神经网络记忆系统激活器"""
    
    def __init__(self, memory_capacity: int = 2000):
        self.memory_capacity = memory_capacity
        
        # 记忆存储
        self.active_memories: List[MemoryEntry] = []
        self.memory_index: Dict[str, List[int]] = {}  # 标签到记忆索引的映射
        
        # 记忆统计
        self.memory_stats = {
            "total_stored": 0,
            "total_retrieved": 0,
            "average_importance": 0.0,
            "memory_utilization": 0.0,
            "last_cleanup": time.time()
        }
        
        # 记忆配置
        self.config = {
            "importance_threshold": 0.3,  # 重要性阈值
            "cleanup_interval": 3600,     # 清理间隔（秒）
            "max_retrievals": 10,         # 最大检索数量
            "similarity_threshold": 0.3,  # 🔥 老王优化：降低相似度阈值，提高检索率
            "auto_save_interval": 300     # 自动保存间隔（秒）
        }
        
        # 线程锁
        self.lock = threading.RLock()
        
        # 自动保存线程
        self.auto_save_active = False
        self.auto_save_thread = None
        
        logger.info("🔥 神经网络记忆系统激活器初始化完成")
    
    def activate_memory_system(self):
        """激活记忆系统"""
        try:
            # 加载现有记忆
            self._load_existing_memories()
            
            # 启动自动保存
            self._start_auto_save()
            
            # 初始化记忆索引
            self._rebuild_memory_index()
            
            logger.info("🚀 神经网络记忆系统已激活")
            return True
            
        except Exception as e:
            logger.error(f"记忆系统激活失败: {e}")
            return False
    
    def store_memory(self, content: Dict[str, Any], importance: float = 1.0, 
                    memory_type: str = "general", tags: List[str] = None) -> bool:
        """存储记忆"""
        try:
            with self.lock:
                # 创建记忆条目
                memory_entry = MemoryEntry(
                    content=content,
                    timestamp=time.time(),
                    importance=importance,
                    memory_type=memory_type,
                    tags=tags or [],
                    embedding=self._generate_embedding(content)
                )
                
                # 检查是否需要清理旧记忆
                if len(self.active_memories) >= self.memory_capacity:
                    self._cleanup_old_memories()
                
                # 添加到记忆库
                self.active_memories.append(memory_entry)
                
                # 更新索引
                self._update_memory_index(len(self.active_memories) - 1, memory_entry)
                
                # 更新统计
                self.memory_stats["total_stored"] += 1
                self._update_memory_statistics()
                
                logger.debug(f"📝 存储记忆: {memory_type}, 重要性: {importance:.2f}")
                return True
                
        except Exception as e:
            logger.error(f"存储记忆失败: {e}")
            return False
    
    def retrieve_memories(self, query: Dict[str, Any], limit: int = None, 
                         memory_type: str = None) -> List[MemoryEntry]:
        """检索记忆"""
        try:
            with self.lock:
                if limit is None:
                    limit = self.config["max_retrievals"]
                
                # 生成查询嵌入
                query_embedding = self._generate_embedding(query)
                
                # 计算相似度并排序
                scored_memories = []
                for i, memory in enumerate(self.active_memories):
                    # 类型过滤
                    if memory_type and memory.memory_type != memory_type:
                        continue

                    # 🔥 老王优化：多维度相似度计算
                    similarity = self._calculate_enhanced_similarity(query, memory, query_embedding)

                    if similarity >= self.config["similarity_threshold"]:
                        scored_memories.append((similarity, i, memory))
                
                # 按相似度排序
                scored_memories.sort(key=lambda x: x[0], reverse=True)
                
                # 返回前N个记忆
                retrieved_memories = []
                for similarity, index, memory in scored_memories[:limit]:
                    # 更新访问计数
                    memory.access_count += 1
                    retrieved_memories.append(memory)
                
                # 更新统计
                self.memory_stats["total_retrieved"] += len(retrieved_memories)
                
                logger.debug(f"🔍 检索到 {len(retrieved_memories)} 条记忆")
                return retrieved_memories
                
        except Exception as e:
            logger.error(f"检索记忆失败: {e}")
            return []
    
    def _generate_embedding(self, content: Dict[str, Any]) -> np.ndarray:
        """生成内容嵌入（简化版）"""
        try:
            # 简化的嵌入生成：基于内容的哈希和特征
            content_str = json.dumps(content, sort_keys=True, default=str)
            
            # 生成基础特征向量
            features = []
            
            # 长度特征
            features.append(len(content_str) / 1000.0)
            
            # 字符频率特征
            char_counts = {}
            for char in content_str.lower():
                char_counts[char] = char_counts.get(char, 0) + 1
            
            # 取前10个最常见字符的频率
            common_chars = sorted(char_counts.items(), key=lambda x: x[1], reverse=True)[:10]
            for char, count in common_chars:
                features.append(count / len(content_str))
            
            # 填充到固定长度
            while len(features) < 32:
                features.append(0.0)
            
            return np.array(features[:32])
            
        except Exception as e:
            logger.warning(f"生成嵌入失败: {e}")
            return np.random.random(32)
    
    def _calculate_similarity(self, embedding1: np.ndarray, embedding2: np.ndarray) -> float:
        """计算嵌入相似度"""
        try:
            if embedding1 is None or embedding2 is None:
                return 0.0

            # 余弦相似度
            dot_product = np.dot(embedding1, embedding2)
            norm1 = np.linalg.norm(embedding1)
            norm2 = np.linalg.norm(embedding2)

            if norm1 == 0 or norm2 == 0:
                return 0.0

            return dot_product / (norm1 * norm2)

        except Exception as e:
            logger.warning(f"计算相似度失败: {e}")
            return 0.0

    def _calculate_enhanced_similarity(self, query: Dict[str, Any], memory: MemoryEntry,
                                     query_embedding: np.ndarray) -> float:
        """🔥 老王新增：增强的多维度相似度计算"""
        try:
            total_similarity = 0.0
            weight_sum = 0.0

            # 1. 嵌入相似度 (权重: 0.4)
            embedding_sim = self._calculate_similarity(query_embedding, memory.embedding)
            total_similarity += embedding_sim * 0.4
            weight_sum += 0.4

            # 2. 内容关键词匹配 (权重: 0.3)
            content_sim = self._calculate_content_similarity(query, memory.content)
            total_similarity += content_sim * 0.3
            weight_sum += 0.3

            # 3. 标签匹配 (权重: 0.2)
            tag_sim = self._calculate_tag_similarity(query, memory.tags)
            total_similarity += tag_sim * 0.2
            weight_sum += 0.2

            # 4. 重要性加权 (权重: 0.1)
            importance_boost = memory.importance * 0.1
            total_similarity += importance_boost
            weight_sum += 0.1

            # 归一化
            if weight_sum > 0:
                return total_similarity / weight_sum
            else:
                return 0.0

        except Exception as e:
            logger.warning(f"计算增强相似度失败: {e}")
            return self._calculate_similarity(query_embedding, memory.embedding)

    def _calculate_content_similarity(self, query: Dict[str, Any], content: Dict[str, Any]) -> float:
        """计算内容相似度"""
        try:
            similarity = 0.0
            matches = 0

            # 检查共同的键
            for key in query.keys():
                if key in content:
                    matches += 1
                    query_val = str(query[key]).lower()
                    content_val = str(content[key]).lower()

                    # 字符串包含匹配
                    if query_val in content_val or content_val in query_val:
                        similarity += 1.0
                    # 部分匹配
                    elif any(word in content_val for word in query_val.split() if len(word) > 2):
                        similarity += 0.5

            return similarity / max(1, len(query)) if matches > 0 else 0.0

        except Exception as e:
            logger.warning(f"计算内容相似度失败: {e}")
            return 0.0

    def _calculate_tag_similarity(self, query: Dict[str, Any], tags: List[str]) -> float:
        """计算标签相似度"""
        try:
            if not tags:
                return 0.0

            query_text = " ".join(str(v) for v in query.values()).lower()
            tag_matches = 0

            for tag in tags:
                if tag.lower() in query_text:
                    tag_matches += 1

            return tag_matches / len(tags) if tags else 0.0

        except Exception as e:
            logger.warning(f"计算标签相似度失败: {e}")
            return 0.0
    
    def _cleanup_old_memories(self):
        """清理旧记忆"""
        try:
            # 按重要性和访问频率排序
            scored_memories = []
            for i, memory in enumerate(self.active_memories):
                # 计算综合分数：重要性 + 访问频率 + 时间衰减
                time_decay = max(0.1, 1.0 - (time.time() - memory.timestamp) / (30 * 24 * 3600))  # 30天衰减
                access_score = min(1.0, memory.access_count / 10.0)  # 访问分数
                
                total_score = memory.importance * 0.4 + access_score * 0.3 + time_decay * 0.3
                scored_memories.append((total_score, i, memory))
            
            # 排序并保留前80%
            scored_memories.sort(key=lambda x: x[0], reverse=True)
            keep_count = int(self.memory_capacity * 0.8)
            
            # 保留高分记忆
            new_memories = []
            new_indices = {}
            for i, (score, old_index, memory) in enumerate(scored_memories[:keep_count]):
                new_memories.append(memory)
                new_indices[old_index] = i
            
            self.active_memories = new_memories
            
            # 重建索引
            self._rebuild_memory_index()
            
            logger.info(f"🧹 清理记忆：保留 {len(new_memories)} 条，删除 {len(scored_memories) - len(new_memories)} 条")
            
        except Exception as e:
            logger.error(f"清理记忆失败: {e}")
    
    def _update_memory_index(self, memory_index: int, memory_entry: MemoryEntry):
        """更新记忆索引"""
        try:
            # 为标签建立索引
            for tag in memory_entry.tags:
                if tag not in self.memory_index:
                    self.memory_index[tag] = []
                self.memory_index[tag].append(memory_index)
            
            # 为记忆类型建立索引
            memory_type = memory_entry.memory_type
            if memory_type not in self.memory_index:
                self.memory_index[memory_type] = []
            self.memory_index[memory_type].append(memory_index)
            
        except Exception as e:
            logger.warning(f"更新记忆索引失败: {e}")
    
    def _rebuild_memory_index(self):
        """重建记忆索引"""
        try:
            self.memory_index.clear()
            
            for i, memory in enumerate(self.active_memories):
                self._update_memory_index(i, memory)
            
            logger.debug("🔄 记忆索引已重建")
            
        except Exception as e:
            logger.error(f"重建记忆索引失败: {e}")
    
    def _update_memory_statistics(self):
        """更新记忆统计"""
        try:
            if not self.active_memories:
                return
            
            # 计算平均重要性
            total_importance = sum(memory.importance for memory in self.active_memories)
            self.memory_stats["average_importance"] = total_importance / len(self.active_memories)
            
            # 计算记忆利用率
            self.memory_stats["memory_utilization"] = len(self.active_memories) / self.memory_capacity
            
        except Exception as e:
            logger.warning(f"更新记忆统计失败: {e}")
    
    def _load_existing_memories(self):
        """加载现有记忆"""
        try:
            memory_file = "data/neural_models/activated_memories.json"
            
            if not os.path.exists(memory_file):
                logger.info("未找到现有记忆文件，从空记忆开始")
                return
            
            with open(memory_file, 'r', encoding='utf-8') as f:
                memory_data = json.load(f)
            
            # 恢复记忆条目
            for item in memory_data.get("memories", []):
                memory_entry = MemoryEntry(
                    content=item["content"],
                    timestamp=item["timestamp"],
                    importance=item.get("importance", 1.0),
                    access_count=item.get("access_count", 0),
                    memory_type=item.get("memory_type", "general"),
                    tags=item.get("tags", []),
                    embedding=np.array(item["embedding"]) if "embedding" in item else None
                )
                self.active_memories.append(memory_entry)
            
            # 恢复统计
            if "stats" in memory_data:
                self.memory_stats.update(memory_data["stats"])
            
            logger.info(f"📚 加载了 {len(self.active_memories)} 条现有记忆")
            
        except Exception as e:
            logger.warning(f"加载现有记忆失败: {e}")
    
    def _start_auto_save(self):
        """启动自动保存"""
        if self.auto_save_active:
            return
        
        self.auto_save_active = True
        self.auto_save_thread = threading.Thread(
            target=self._auto_save_loop,
            daemon=True,
            name="MemoryAutoSave"
        )
        self.auto_save_thread.start()
        
        logger.info("💾 记忆自动保存已启动")
    
    def _auto_save_loop(self):
        """自动保存循环"""
        while self.auto_save_active:
            try:
                time.sleep(self.config["auto_save_interval"])
                self.save_memories()
                
            except Exception as e:
                logger.error(f"自动保存异常: {e}")
    
    def save_memories(self):
        """保存记忆"""
        try:
            with self.lock:
                memory_file = "data/neural_models/activated_memories.json"
                os.makedirs(os.path.dirname(memory_file), exist_ok=True)
                
                # 准备保存数据
                save_data = {
                    "timestamp": time.time(),
                    "stats": self.memory_stats,
                    "memories": []
                }
                
                for memory in self.active_memories:
                    try:
                        # 🔥 老王修复：安全处理memory.content，防止ThinkingContext序列化错误
                        content = memory.content
                        if hasattr(content, 'to_dict'):
                            # ThinkingContext等有to_dict方法的对象
                            content = content.to_dict()
                        elif not isinstance(content, (dict, list, str, int, float, bool, type(None))):
                            # 其他不可序列化的对象，转换为字符串
                            content = str(content)
                            logger.debug(f"记忆内容转换为字符串: {type(memory.content)}")

                        memory_item = {
                            "content": content,
                            "timestamp": memory.timestamp,
                            "importance": memory.importance,
                            "access_count": memory.access_count,
                            "memory_type": memory.memory_type,
                            "tags": memory.tags,
                            "embedding": memory.embedding.tolist() if memory.embedding is not None else None
                        }
                        save_data["memories"].append(memory_item)
                    except Exception as item_error:
                        logger.warning(f"跳过无效记忆项: {item_error}")
                        continue
                
                # 🔥 老王修复：使用原子文件写入，防止并发写入损坏
                from utilities.atomic_file_writer import safe_json_write

                success = safe_json_write(memory_file, save_data)
                if not success:
                    logger.error(f"💾 原子文件写入失败: {memory_file}")
                    return
                
                logger.debug(f"💾 保存了 {len(self.active_memories)} 条记忆")
                
        except Exception as e:
            logger.error(f"保存记忆失败: {e}")
    
    def get_memory_report(self) -> Dict[str, Any]:
        """获取记忆报告"""
        with self.lock:
            return {
                "memory_count": len(self.active_memories),
                "memory_capacity": self.memory_capacity,
                "memory_utilization": self.memory_stats["memory_utilization"],
                "average_importance": self.memory_stats["average_importance"],
                "total_stored": self.memory_stats["total_stored"],
                "total_retrieved": self.memory_stats["total_retrieved"],
                "memory_types": self._get_memory_type_distribution(),
                "index_size": len(self.memory_index),
                "recommendations": self._generate_memory_recommendations()
            }
    
    def _get_memory_type_distribution(self) -> Dict[str, int]:
        """获取记忆类型分布"""
        distribution = {}
        for memory in self.active_memories:
            memory_type = memory.memory_type
            distribution[memory_type] = distribution.get(memory_type, 0) + 1
        return distribution
    
    def _generate_memory_recommendations(self) -> List[str]:
        """生成记忆优化建议"""
        recommendations = []
        
        utilization = self.memory_stats["memory_utilization"]
        avg_importance = self.memory_stats["average_importance"]
        
        if utilization < 0.3:
            recommendations.append("记忆利用率较低，建议增加记忆存储频率")
        elif utilization > 0.9:
            recommendations.append("记忆接近容量上限，建议清理低重要性记忆")
        
        if avg_importance < 0.5:
            recommendations.append("平均记忆重要性偏低，建议提高重要性阈值")
        
        if self.memory_stats["total_retrieved"] == 0:
            recommendations.append("记忆检索次数为0，建议激活记忆检索功能")
        
        return recommendations
    
    def stop_auto_save(self):
        """停止自动保存"""
        self.auto_save_active = False
        if self.auto_save_thread and self.auto_save_thread.is_alive():
            self.auto_save_thread.join(timeout=5)
        
        # 最后保存一次
        self.save_memories()
        
        logger.info("⏹️ 记忆自动保存已停止")


# 全局实例
_memory_activator = None

def get_memory_activator() -> NeuralMemoryActivator:
    """获取记忆激活器实例"""
    global _memory_activator
    if _memory_activator is None:
        _memory_activator = NeuralMemoryActivator()
    return _memory_activator
