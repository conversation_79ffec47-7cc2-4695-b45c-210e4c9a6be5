#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
统一系统配置管理器 - Unified System Configuration Manager

为林嫣然数字生命系统提供全面的统一配置管理机制，
支持多级配置、热重载、配置验证、环境适配和动态更新。

作者: Claude
创建日期: 2025-01-08
版本: 3.0.0
"""

import os
import json
import yaml
import threading
import time
from enum import Enum
from typing import Any, Dict, List, Optional, Callable, Union, Tuple
from dataclasses import dataclass, field
from datetime import datetime
import copy
import hashlib
import glob
from pathlib import Path

from utilities.unified_logger import get_unified_logger

logger = get_unified_logger('unified_system_config_manager')

class ConfigScope(Enum):
    """配置作用域枚举"""
    SYSTEM = "system"           # 系统级配置
    SERVICE = "service"         # 服务级配置
    MODULE = "module"           # 模块级配置
    FEATURE = "feature"         # 功能级配置
    USER = "user"               # 用户级配置
    RUNTIME = "runtime"         # 运行时配置

class ConfigPriority(Enum):
    """配置优先级枚举"""
    DEFAULT = 0         # 默认配置
    ENVIRONMENT = 10    # 环境配置
    SYSTEM = 20         # 系统配置
    SERVICE = 30        # 服务配置
    MODULE = 40         # 模块配置
    USER = 50           # 用户配置
    RUNTIME = 60        # 运行时配置

class ConfigFormat(Enum):
    """配置文件格式枚举"""
    JSON = "json"
    YAML = "yaml"
    YML = "yml"
    ENV = "env"

@dataclass
class ConfigItem:
    """配置项数据类"""
    key: str
    value: Any
    scope: ConfigScope
    priority: ConfigPriority
    source_file: Optional[str] = None
    description: str = ""
    validator: Optional[Callable[[Any], bool]] = None
    is_sensitive: bool = False
    is_readonly: bool = False
    created_at: datetime = field(default_factory=datetime.now)
    updated_at: datetime = field(default_factory=datetime.now)
    version: int = 1
    
    def validate(self) -> Tuple[bool, Optional[str]]:
        """验证配置值"""
        try:
            if self.validator:
                result = self.validator(self.value)
                if isinstance(result, bool):
                    return result, None if result else "验证失败"
                elif isinstance(result, tuple):
                    return result
                else:
                    return bool(result), None if result else "验证失败"
            return True, None
        except Exception as e:
            return False, f"验证异常: {str(e)}"
    
    def update_value(self, new_value: Any) -> Tuple[bool, Optional[str]]:
        """更新配置值"""
        if self.is_readonly:
            return False, "配置项为只读，无法更新"
            
        old_value = self.value
        self.value = new_value
        self.updated_at = datetime.now()
        self.version += 1
        
        is_valid, error = self.validate()
        if not is_valid:
            # 回滚
            self.value = old_value
            self.version -= 1
            return False, error
        
        return True, None

class ConfigWatcher:
    """配置文件监视器"""
    
    def __init__(self, callback: Callable[[str, Dict[str, Any]], None]):
        self.callback = callback
        self.watched_files: Dict[str, float] = {}  # 文件路径 -> 最后修改时间
        self.is_running = False
        self.watch_thread = None
        self.check_interval = 1.0  # 检查间隔（秒）
    
    def start_watching(self):
        """开始监视文件变化"""
        if self.is_running:
            return
            
        self.is_running = True
        self.watch_thread = threading.Thread(target=self._watch_loop, daemon=True)
        self.watch_thread.start()
        logger.info("配置文件监视器已启动")
    
    def stop_watching(self):
        """停止监视文件变化"""
        self.is_running = False
        if self.watch_thread:
            self.watch_thread.join(timeout=2.0)
        logger.info("配置文件监视器已停止")
    
    def add_file(self, file_path: str):
        """添加要监视的文件"""
        if os.path.exists(file_path):
            self.watched_files[file_path] = os.path.getmtime(file_path)
            logger.debug(f"添加监视文件: {file_path}")
    
    def remove_file(self, file_path: str):
        """移除监视的文件"""
        if file_path in self.watched_files:
            del self.watched_files[file_path]
            logger.debug(f"移除监视文件: {file_path}")
    
    def _watch_loop(self):
        """监视循环"""
        while self.is_running:
            try:
                for file_path, last_mtime in list(self.watched_files.items()):
                    if os.path.exists(file_path):
                        current_mtime = os.path.getmtime(file_path)
                        if current_mtime > last_mtime:
                            self.watched_files[file_path] = current_mtime
                            logger.info(f"检测到配置文件变化: {file_path}")
                            
                            # 读取文件内容并回调
                            try:
                                config_data = self._load_file(file_path)
                                self.callback(file_path, config_data)
                            except Exception as e:
                                logger.error(f"处理配置文件变化失败: {e}")
                    else:
                        # 文件被删除
                        logger.warning(f"监视的配置文件已被删除: {file_path}")
                        del self.watched_files[file_path]
                
                time.sleep(self.check_interval)
            except Exception as e:
                logger.error(f"配置文件监视异常: {e}")
                time.sleep(self.check_interval)
    
    def _load_file(self, file_path: str) -> Dict[str, Any]:
        """加载配置文件"""
        ext = Path(file_path).suffix.lower()
        
        with open(file_path, 'r', encoding='utf-8') as f:
            if ext == '.json':
                return json.load(f)
            elif ext in ['.yaml', '.yml']:
                return yaml.safe_load(f) or {}
            else:
                return {}

class ConfigChangeListener:
    """配置变更监听器"""
    
    def __init__(self, callback: Callable[[str, Any, Any], None], key_pattern: Optional[str] = None):
        self.callback = callback
        self.key_pattern = key_pattern  # 支持通配符匹配
    
    def matches(self, key: str) -> bool:
        """检查键是否匹配监听模式"""
        if not self.key_pattern:
            return True
        
        import fnmatch
        return fnmatch.fnmatch(key, self.key_pattern)
    
    def on_config_changed(self, key: str, old_value: Any, new_value: Any):
        """配置变更回调"""
        if self.matches(key):
            try:
                self.callback(key, old_value, new_value)
            except Exception as e:
                logger.error(f"配置变更监听器执行失败: {e}")

class UnifiedSystemConfigManager:
    """统一系统配置管理器"""
    
    _instance = None
    _lock = threading.RLock()
    
    @classmethod
    def get_instance(cls, config_dir: Optional[str] = None) -> 'UnifiedSystemConfigManager':
        """获取单例实例"""
        with cls._lock:
            if cls._instance is None:
                cls._instance = UnifiedSystemConfigManager(config_dir)
        return cls._instance
    
    def __init__(self, config_dir: Optional[str] = None):
        """
        初始化统一系统配置管理器
        
        Args:
            config_dir: 配置文件目录
        """
        self.config_dir = config_dir or "config"
        self.config_items: Dict[str, ConfigItem] = {}
        self.listeners: List[Callable] = []
        self.config_history: List[Dict[str, Any]] = []
        self.lock = threading.RLock()
        
        # 配置文件缓存
        self.file_cache: Dict[str, Tuple[Dict[str, Any], float]] = {}  # 文件路径 -> (内容, 修改时间)
        
        # 配置监视器
        self.watcher = ConfigWatcher(self._on_file_changed)
        
        # 环境变量前缀
        self.env_prefix = "YANRAN_"
        
        # 配置验证器
        self.validators: Dict[str, Callable[[Any], bool]] = {}
        
        # 初始化
        self._initialize()
        
        logger.info("统一系统配置管理器初始化完成")
    
    def _initialize(self):
        """初始化配置管理器"""
        # 创建配置目录
        os.makedirs(self.config_dir, exist_ok=True)
        
        # 加载所有配置文件
        self._load_all_configurations()
        
        # 加载环境变量
        self._load_environment_variables()
        
        # 注册默认验证器
        self._register_default_validators()
        
        # 启动文件监视器
        self.watcher.start_watching()
    
    def _load_all_configurations(self):
        """加载所有配置文件"""
        # 定义配置文件加载顺序（按优先级）
        config_patterns = [
            ("system.json", ConfigScope.SYSTEM, ConfigPriority.SYSTEM),
            ("services/*.json", ConfigScope.SERVICE, ConfigPriority.SERVICE),
            ("modules/*.json", ConfigScope.MODULE, ConfigPriority.MODULE),
            ("features/*.json", ConfigScope.FEATURE, ConfigPriority.MODULE),
            ("ai_services.json", ConfigScope.SERVICE, ConfigPriority.SERVICE),
            ("database.json", ConfigScope.SERVICE, ConfigPriority.SERVICE),
            ("*.yaml", ConfigScope.SYSTEM, ConfigPriority.SYSTEM),
            ("*.yml", ConfigScope.SYSTEM, ConfigPriority.SYSTEM),
        ]
        
        for pattern, scope, priority in config_patterns:
            self._load_config_pattern(pattern, scope, priority)
    
    def _load_config_pattern(self, pattern: str, scope: ConfigScope, priority: ConfigPriority):
        """加载匹配模式的配置文件"""
        search_path = os.path.join(self.config_dir, pattern)
        
        for file_path in glob.glob(search_path):
            if os.path.isfile(file_path):
                self._load_config_file(file_path, scope, priority)
    
    def _load_config_file(self, file_path: str, scope: ConfigScope, priority: ConfigPriority):
        """加载单个配置文件"""
        try:
            # 检查文件缓存
            mtime = os.path.getmtime(file_path)
            if file_path in self.file_cache:
                cached_data, cached_mtime = self.file_cache[file_path]
                if mtime <= cached_mtime:
                    config_data = cached_data
                else:
                    config_data = self._read_config_file(file_path)
                    self.file_cache[file_path] = (config_data, mtime)
            else:
                config_data = self._read_config_file(file_path)
                self.file_cache[file_path] = (config_data, mtime)
            
            # 解析配置数据
            self._parse_config_data(config_data, scope, priority, file_path)
            
            # 添加到文件监视器
            self.watcher.add_file(file_path)
            
            logger.debug(f"已加载配置文件: {file_path}")
            
        except Exception as e:
            logger.error(f"加载配置文件失败 {file_path}: {e}")
    
    def _read_config_file(self, file_path: str) -> Dict[str, Any]:
        """读取配置文件"""
        ext = Path(file_path).suffix.lower()
        
        with open(file_path, 'r', encoding='utf-8') as f:
            if ext == '.json':
                return json.load(f)
            elif ext in ['.yaml', '.yml']:
                return yaml.safe_load(f) or {}
            else:
                return {}
    
    def _parse_config_data(self, config_data: Dict[str, Any], scope: ConfigScope, 
                          priority: ConfigPriority, source_file: str, prefix: str = ""):
        """解析配置数据"""
        for key, value in config_data.items():
            full_key = f"{prefix}.{key}" if prefix else key
            
            if isinstance(value, dict) and not self._is_leaf_config(value):
                # 递归处理嵌套配置
                self._parse_config_data(value, scope, priority, source_file, full_key)
            else:
                # 创建或更新配置项
                self._create_or_update_config_item(
                    full_key, value, scope, priority, source_file
                )
    
    def _is_leaf_config(self, value: Any) -> bool:
        """判断是否为叶子配置项"""
        if not isinstance(value, dict):
            return True
        
        # 如果字典包含特殊键，认为是配置值
        special_keys = {'_value', '_type', '_description', '_validator', '_sensitive'}
        return bool(special_keys.intersection(value.keys()))
    
    def _create_or_update_config_item(self, key: str, value: Any, scope: ConfigScope, 
                                    priority: ConfigPriority, source_file: str):
        """创建或更新配置项"""
        with self.lock:
            # 如果配置项已存在，检查优先级
            if key in self.config_items:
                existing_item = self.config_items[key]
                if existing_item.priority.value > priority.value:
                    # 现有配置优先级更高，不更新
                    return
                elif existing_item.priority.value == priority.value:
                    # 优先级相同，记录警告
                    logger.warning(f"配置项 {key} 存在优先级冲突，使用新值")
            
            # 处理特殊配置格式
            if isinstance(value, dict) and self._is_leaf_config(value):
                actual_value = value.get('_value', value)
                description = value.get('_description', "")
                is_sensitive = value.get('_sensitive', False)
                is_readonly = value.get('_readonly', False)
                validator_name = value.get('_validator')
                validator = self.validators.get(validator_name) if validator_name else None
            else:
                actual_value = value
                description = f"配置项: {key}"
                is_sensitive = self._is_sensitive_key(key)
                is_readonly = False
                validator = self.validators.get(key)
            
            # 创建配置项
            config_item = ConfigItem(
                key=key,
                value=actual_value,
                scope=scope,
                priority=priority,
                source_file=source_file,
                description=description,
                validator=validator,
                is_sensitive=is_sensitive,
                is_readonly=is_readonly
            )
            
            # 验证配置值
            is_valid, error = config_item.validate()
            if not is_valid:
                logger.warning(f"配置项 {key} 验证失败: {error}")
                return
            
            # 记录变更
            old_value = self.config_items.get(key)
            old_value = old_value.value if old_value else None
            
            # 存储配置项
            self.config_items[key] = config_item
            
            # 通知监听器
            if old_value != actual_value:
                self._notify_listeners(key, old_value, actual_value)
                self._record_config_change(key, old_value, actual_value)
    
    def _is_sensitive_key(self, key: str) -> bool:
        """判断是否为敏感配置键"""
        sensitive_patterns = [
            'password', 'passwd', 'pwd', 'secret', 'key', 'token', 
            'api_key', 'access_key', 'private_key', 'credential'
        ]
        key_lower = key.lower()
        return any(pattern in key_lower for pattern in sensitive_patterns)
    
    def _load_environment_variables(self):
        """加载环境变量配置"""
        for key, value in os.environ.items():
            if key.startswith(self.env_prefix):
                config_key = key[len(self.env_prefix):].lower().replace('_', '.')
                
                # 尝试解析值类型
                parsed_value = self._parse_env_value(value)
                
                # 创建配置项
                config_item = ConfigItem(
                    key=config_key,
                    value=parsed_value,
                    scope=ConfigScope.RUNTIME,
                    priority=ConfigPriority.ENVIRONMENT,
                    source_file="environment",
                    description=f"环境变量: {key}",
                    is_sensitive=self._is_sensitive_key(config_key)
                )
                
                self.config_items[config_key] = config_item
                logger.debug(f"加载环境变量配置: {config_key}")
    
    def _parse_env_value(self, value: str) -> Any:
        """解析环境变量值"""
        # 尝试解析为JSON
        try:
            return json.loads(value)
        except:
            pass
        
        # 尝试解析为布尔值
        if value.lower() in ('true', 'false'):
            return value.lower() == 'true'
        
        # 尝试解析为数字
        try:
            if '.' in value:
                return float(value)
            else:
                return int(value)
        except:
            pass
        
        # 返回字符串
        return value
    
    def _register_default_validators(self):
        """注册默认验证器"""
        # 端口号验证器
        def validate_port(value):
            try:
                port = int(value)
                return 1 <= port <= 65535
            except:
                return False
        
        # URL验证器
        def validate_url(value):
            if not isinstance(value, str):
                return False
            return value.startswith(('http://', 'https://'))
        
        # 邮箱验证器
        def validate_email(value):
            if not isinstance(value, str):
                return False
            import re
            pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
            return re.match(pattern, value) is not None
        
        # 注册验证器
        self.validators.update({
            'port': validate_port,
            'url': validate_url,
            'email': validate_email,
        })
    
    def _on_file_changed(self, file_path: str, config_data: Dict[str, Any]):
        """配置文件变化回调"""
        logger.info(f"重新加载配置文件: {file_path}")
        
        # 确定配置作用域和优先级
        rel_path = os.path.relpath(file_path, self.config_dir)
        scope, priority = self._determine_scope_and_priority(rel_path)
        
        # 重新解析配置
        self._parse_config_data(config_data, scope, priority, file_path)
    
    def _determine_scope_and_priority(self, rel_path: str) -> Tuple[ConfigScope, ConfigPriority]:
        """根据文件路径确定作用域和优先级"""
        if rel_path.startswith('services/'):
            return ConfigScope.SERVICE, ConfigPriority.SERVICE
        elif rel_path.startswith('modules/'):
            return ConfigScope.MODULE, ConfigPriority.MODULE
        elif rel_path.startswith('features/'):
            return ConfigScope.FEATURE, ConfigPriority.MODULE
        elif 'system' in rel_path:
            return ConfigScope.SYSTEM, ConfigPriority.SYSTEM
        else:
            return ConfigScope.SYSTEM, ConfigPriority.SYSTEM
    
    def get(self, key: str, default: Any = None, scope: Optional[ConfigScope] = None) -> Any:
        """
        获取配置值
        
        Args:
            key: 配置键
            default: 默认值
            scope: 配置作用域过滤
            
        Returns:
            配置值
        """
        with self.lock:
            if key in self.config_items:
                config_item = self.config_items[key]
                if scope is None or config_item.scope == scope:
                    return config_item.value
            
            return default
    
    def set(self, key: str, value: Any, scope: ConfigScope = ConfigScope.RUNTIME,
            priority: ConfigPriority = ConfigPriority.RUNTIME,
            description: str = "", persist: bool = False) -> Tuple[bool, Optional[str]]:
        """
        设置配置值
        
        Args:
            key: 配置键
            value: 配置值
            scope: 配置作用域
            priority: 配置优先级
            description: 配置描述
            persist: 是否持久化到文件
            
        Returns:
            (是否成功, 错误信息)
        """
        with self.lock:
            # 检查是否存在且为只读
            if key in self.config_items and self.config_items[key].is_readonly:
                return False, "配置项为只读，无法修改"
            
            # 记录旧值
            old_value = self.config_items.get(key)
            old_value = old_value.value if old_value else None
            
            # 创建配置项
            config_item = ConfigItem(
                key=key,
                value=value,
                scope=scope,
                priority=priority,
                description=description or f"运行时配置: {key}",
                validator=self.validators.get(key),
                is_sensitive=self._is_sensitive_key(key)
            )
            
            # 验证配置值
            is_valid, error = config_item.validate()
            if not is_valid:
                return False, error
            
            # 存储配置项
            self.config_items[key] = config_item
            
            # 持久化
            if persist:
                success = self._persist_config(key, value, scope)
                if not success:
                    logger.warning(f"配置项 {key} 持久化失败")
            
            # 通知监听器
            if old_value != value:
                self._notify_listeners(key, old_value, value)
                self._record_config_change(key, old_value, value)
            
            return True, None
    
    def _persist_config(self, key: str, value: Any, scope: ConfigScope) -> bool:
        """持久化配置到文件"""
        try:
            # 根据作用域确定文件路径
            if scope == ConfigScope.SYSTEM:
                config_file = os.path.join(self.config_dir, "system.json")
            elif scope == ConfigScope.SERVICE:
                config_file = os.path.join(self.config_dir, "services", "runtime.json")
            elif scope == ConfigScope.MODULE:
                config_file = os.path.join(self.config_dir, "modules", "runtime.json")
            else:
                config_file = os.path.join(self.config_dir, "runtime.json")
            
            # 确保目录存在
            os.makedirs(os.path.dirname(config_file), exist_ok=True)
            
            # 读取现有配置
            if os.path.exists(config_file):
                with open(config_file, 'r', encoding='utf-8') as f:
                    config_data = json.load(f)
            else:
                config_data = {}
            
            # 设置嵌套键值
            self._set_nested_value(config_data, key, value)
            
            # 写入文件
            with open(config_file, 'w', encoding='utf-8') as f:
                json.dump(config_data, f, indent=2, ensure_ascii=False, default=str)
            
            return True
            
        except Exception as e:
            logger.error(f"持久化配置失败: {e}")
            return False
    
    def _set_nested_value(self, data: Dict[str, Any], key: str, value: Any):
        """设置嵌套键值"""
        keys = key.split('.')
        current = data
        
        for k in keys[:-1]:
            if k not in current:
                current[k] = {}
            current = current[k]
        
        current[keys[-1]] = value
    
    def remove(self, key: str) -> bool:
        """
        移除配置项
        
        Args:
            key: 配置键
            
        Returns:
            是否成功移除
        """
        with self.lock:
            if key in self.config_items:
                config_item = self.config_items[key]
                if config_item.is_readonly:
                    logger.warning(f"配置项 {key} 为只读，无法移除")
                    return False
                
                old_value = config_item.value
                del self.config_items[key]
                
                # 通知监听器
                self._notify_listeners(key, old_value, None)
                self._record_config_change(key, old_value, None)
                
                return True
            
            return False
    
    def get_by_scope(self, scope: ConfigScope) -> Dict[str, Any]:
        """
        按作用域获取配置
        
        Args:
            scope: 配置作用域
            
        Returns:
            配置字典
        """
        with self.lock:
            result = {}
            for key, item in self.config_items.items():
                if item.scope == scope:
                    result[key] = item.value
            return result
    
    def get_by_prefix(self, prefix: str) -> Dict[str, Any]:
        """
        按前缀获取配置
        
        Args:
            prefix: 配置键前缀
            
        Returns:
            配置字典
        """
        with self.lock:
            result = {}
            for key, item in self.config_items.items():
                if key.startswith(prefix):
                    result[key] = item.value
            return result
    
    def add_validator(self, key: str, validator: Callable[[Any], bool]) -> bool:
        """
        添加配置验证器
        
        Args:
            key: 配置键
            validator: 验证函数
            
        Returns:
            是否成功添加
        """
        self.validators[key] = validator
        
        # 验证现有配置
        if key in self.config_items:
            config_item = self.config_items[key]
            config_item.validator = validator
            is_valid, error = config_item.validate()
            if not is_valid:
                logger.warning(f"现有配置项 {key} 验证失败: {error}")
        
        return True
    
    def add_listener(self, listener: Callable):
        """添加配置变更监听器"""
        self.listeners.append(listener)
    
    def remove_listener(self, listener: Callable):
        """移除配置变更监听器"""
        if listener in self.listeners:
            self.listeners.remove(listener)
    
    def _notify_listeners(self, key: str, old_value: Any, new_value: Any):
        """通知配置变更监听器"""
        for listener in self.listeners:
            try:
                listener(key, old_value, new_value)
            except Exception as e:
                logger.error(f"配置变更监听器通知失败: {e}")
    
    def _record_config_change(self, key: str, old_value: Any, new_value: Any):
        """记录配置变更历史"""
        change_record = {
            "timestamp": datetime.now().isoformat(),
            "key": key,
            "old_value": old_value,
            "new_value": new_value,
            "change_type": "update" if old_value is not None else "create" if new_value is not None else "delete"
        }
        
        self.config_history.append(change_record)
        
        # 限制历史记录数量
        if len(self.config_history) > 1000:
            self.config_history = self.config_history[-500:]
    
    def get_config_history(self, key: Optional[str] = None, limit: int = 100) -> List[Dict[str, Any]]:
        """
        获取配置变更历史
        
        Args:
            key: 配置键过滤
            limit: 返回记录数限制
            
        Returns:
            历史记录列表
        """
        history = self.config_history
        
        if key:
            history = [record for record in history if record["key"] == key]
        
        return history[-limit:] if limit > 0 else history
    
    def reload_configuration(self) -> bool:
        """
        重新加载所有配置
        
        Returns:
            是否成功重新加载
        """
        try:
            with self.lock:
                # 清空当前配置（保留运行时配置）
                runtime_configs = {
                    key: item for key, item in self.config_items.items()
                    if item.priority == ConfigPriority.RUNTIME
                }
                
                self.config_items.clear()
                self.config_items.update(runtime_configs)
                
                # 清空文件缓存
                self.file_cache.clear()
                
                # 重新加载所有配置
                self._load_all_configurations()
                self._load_environment_variables()
                
                logger.info("配置重新加载完成")
                return True
                
        except Exception as e:
            logger.error(f"配置重新加载失败: {e}")
            return False
    
    def export_configuration(self, file_path: str, scope: Optional[ConfigScope] = None, 
                           include_sensitive: bool = False) -> bool:
        """
        导出配置到文件
        
        Args:
            file_path: 导出文件路径
            scope: 作用域过滤
            include_sensitive: 是否包含敏感配置
            
        Returns:
            是否成功导出
        """
        try:
            export_data = {}
            
            with self.lock:
                for key, item in self.config_items.items():
                    # 作用域过滤
                    if scope and item.scope != scope:
                        continue
                    
                    # 敏感信息过滤
                    if item.is_sensitive and not include_sensitive:
                        continue
                    
                    # 构建嵌套结构
                    self._set_nested_value(export_data, key, item.value)
            
            # 写入文件
            ext = Path(file_path).suffix.lower()
            os.makedirs(os.path.dirname(file_path), exist_ok=True)
            
            # 🔥 老王修复：使用原子写入避免并发冲突
            from utilities.atomic_file_writer import safe_json_write
            
            if ext == '.json' or ext not in ['.yaml', '.yml']:
                success = safe_json_write(file_path, export_data, default=str)
                if not success:
                    logger.error(f"JSON配置导出失败: {file_path}")
                    return False
            elif ext in ['.yaml', '.yml']:
                with open(file_path, 'w', encoding='utf-8') as f:
                    yaml.dump(export_data, f, default_flow_style=False, allow_unicode=True)
            
            logger.info(f"配置已导出到: {file_path}")
            return True
            
        except Exception as e:
            logger.error(f"配置导出失败: {e}")
            return False
    
    def get_stats(self) -> Dict[str, Any]:
        """获取配置管理器统计信息"""
        with self.lock:
            scope_counts = {}
            priority_counts = {}
            sensitive_count = 0
            readonly_count = 0
            
            for item in self.config_items.values():
                # 按作用域统计
                scope_counts[item.scope.value] = scope_counts.get(item.scope.value, 0) + 1
                
                # 按优先级统计
                priority_counts[item.priority.value] = priority_counts.get(item.priority.value, 0) + 1
                
                # 敏感配置统计
                if item.is_sensitive:
                    sensitive_count += 1
                
                # 只读配置统计
                if item.is_readonly:
                    readonly_count += 1
            
            return {
                "total_configs": len(self.config_items),
                "scope_distribution": scope_counts,
                "priority_distribution": priority_counts,
                "sensitive_configs": sensitive_count,
                "readonly_configs": readonly_count,
                "listeners_count": len(self.listeners),
                "history_records": len(self.config_history),
                "watched_files": len(self.watcher.watched_files),
                "cached_files": len(self.file_cache)
            }
    
    def shutdown(self):
        """关闭配置管理器"""
        logger.info("正在关闭统一系统配置管理器...")
        
        # 停止文件监视器
        self.watcher.stop_watching()
        
        # 清理资源
        with self.lock:
            self.config_items.clear()
            self.listeners.clear()
            self.config_history.clear()
            self.file_cache.clear()
            self.validators.clear()
        
        logger.success("统一系统配置管理器已关闭")

# 全局实例
_config_manager = None

def get_unified_system_config_manager(config_dir: Optional[str] = None) -> UnifiedSystemConfigManager:
    """获取统一系统配置管理器实例"""
    global _config_manager
    if _config_manager is None:
        _config_manager = UnifiedSystemConfigManager.get_instance(config_dir)
    return _config_manager

def get_config(key: str, default: Any = None, scope: Optional[ConfigScope] = None) -> Any:
    """获取配置值的便捷函数"""
    manager = get_unified_system_config_manager()
    return manager.get(key, default, scope)

def set_config(key: str, value: Any, scope: ConfigScope = ConfigScope.RUNTIME,
               priority: ConfigPriority = ConfigPriority.RUNTIME, persist: bool = False) -> bool:
    """设置配置值的便捷函数"""
    manager = get_unified_system_config_manager()
    success, error = manager.set(key, value, scope, priority, persist=persist)
    if not success:
        logger.error(f"设置配置失败: {error}")
    return success 