#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
统一用户身份管理器
==================

解决用户ID和session管理在不同层级重复处理的问题，提供统一的用户身份管理接口。

核心功能:
1. 统一用户身份验证和管理
2. 会话生命周期管理
3. 用户状态同步
4. 多数据源用户信息整合
"""

import asyncio
import json
import threading
import time
import uuid
from dataclasses import dataclass, field
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Union
from enum import Enum

from utilities.singleton_manager import get as get_singleton_manager
from utilities.unified_logger import get_unified_logger


class UserStatus(Enum):
    """用户状态枚举"""
    ACTIVE = "active"
    INACTIVE = "inactive"
    SUSPENDED = "suspended"
    OFFLINE = "offline"


class SessionStatus(Enum):
    """会话状态枚举"""
    ACTIVE = "active"
    EXPIRED = "expired"
    TERMINATED = "terminated"


@dataclass
class UnifiedUserProfile:
    """统一用户档案"""
    user_id: str
    name: str
    status: UserStatus = UserStatus.ACTIVE
    created_at: datetime = field(default_factory=datetime.now)
    last_active: datetime = field(default_factory=datetime.now)
    
    # 基本信息
    nickname: Optional[str] = None
    sex: str = "0"  # 0=未知, 1=男, 2=女
    avatar: Optional[str] = None
    
    # 联系人信息
    wxid: Optional[str] = None
    contact_info: Dict[str, Any] = field(default_factory=dict)
    
    # 用户偏好和设置
    preferences: Dict[str, Any] = field(default_factory=dict)
    settings: Dict[str, Any] = field(default_factory=dict)
    
    # 统计信息
    interaction_count: int = 0
    total_messages: int = 0
    last_message_time: Optional[datetime] = None
    
    # 扩展数据
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            'user_id': self.user_id,
            'name': self.name,
            'status': self.status.value,
            'created_at': self.created_at.isoformat(),
            'last_active': self.last_active.isoformat(),
            'nickname': self.nickname,
            'sex': self.sex,
            'avatar': self.avatar,
            'wxid': self.wxid,
            'contact_info': self.contact_info,
            'preferences': self.preferences,
            'settings': self.settings,
            'interaction_count': self.interaction_count,
            'total_messages': self.total_messages,
            'last_message_time': self.last_message_time.isoformat() if self.last_message_time else None,
            'metadata': self.metadata
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'UnifiedUserProfile':
        """从字典创建"""
        # 处理日期字段
        created_at = datetime.fromisoformat(data.get('created_at', datetime.now().isoformat()))
        last_active = datetime.fromisoformat(data.get('last_active', datetime.now().isoformat()))
        last_message_time = None
        if data.get('last_message_time'):
            last_message_time = datetime.fromisoformat(data['last_message_time'])
        
        return cls(
            user_id=data['user_id'],
            name=data['name'],
            status=UserStatus(data.get('status', 'active')),
            created_at=created_at,
            last_active=last_active,
            nickname=data.get('nickname'),
            sex=data.get('sex', '0'),
            avatar=data.get('avatar'),
            wxid=data.get('wxid'),
            contact_info=data.get('contact_info', {}),
            preferences=data.get('preferences', {}),
            settings=data.get('settings', {}),
            interaction_count=data.get('interaction_count', 0),
            total_messages=data.get('total_messages', 0),
            last_message_time=last_message_time,
            metadata=data.get('metadata', {})
        )


@dataclass
class UnifiedSession:
    """统一会话"""
    session_id: str
    user_id: str
    status: SessionStatus = SessionStatus.ACTIVE
    created_at: datetime = field(default_factory=datetime.now)
    last_activity: datetime = field(default_factory=datetime.now)
    expires_at: Optional[datetime] = None
    
    # 会话数据
    context: Dict[str, Any] = field(default_factory=dict)
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    # 安全信息
    csrf_token: Optional[str] = None
    ip_address: Optional[str] = None
    user_agent: Optional[str] = None
    
    def is_expired(self) -> bool:
        """检查会话是否过期"""
        if self.expires_at and datetime.now() > self.expires_at:
            return True
        return False
    
    def is_active(self) -> bool:
        """检查会话是否活跃"""
        return self.status == SessionStatus.ACTIVE and not self.is_expired()
    
    def update_activity(self):
        """更新活动时间"""
        self.last_activity = datetime.now()
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            'session_id': self.session_id,
            'user_id': self.user_id,
            'status': self.status.value,
            'created_at': self.created_at.isoformat(),
            'last_activity': self.last_activity.isoformat(),
            'expires_at': self.expires_at.isoformat() if self.expires_at else None,
            'context': self.context,
            'metadata': self.metadata,
            'csrf_token': self.csrf_token,
            'ip_address': self.ip_address,
            'user_agent': self.user_agent
        }


class UnifiedUserManager:
    """统一用户身份管理器"""
    
    def __init__(self):
        """初始化统一用户管理器"""
        # self.logger = logger
        self.logger = get_unified_logger("unified_user_manager")
        # 用户和会话数据
        self.users: Dict[str, UnifiedUserProfile] = {}
        self.sessions: Dict[str, UnifiedSession] = {}
        
        # 用户ID映射 (支持多种ID格式)
        self.user_id_mapping: Dict[str, str] = {}  # {alternative_id: primary_user_id}
        
        # 线程安全锁
        self.users_lock = threading.RLock()
        self.sessions_lock = threading.RLock()
        
        # 配置
        self.config = {
            'session_timeout': 86400,  # 24小时
            'cleanup_interval': 3600,  # 1小时清理一次
            'max_sessions_per_user': 10,
            'enable_auto_cleanup': True
        }
        
        # 后台任务
        self.cleanup_task = None
        self.running = False
        
        # 集成的管理器
        self.user_manager = None
        self.contacts_manager = None
        self.storage_manager = None
        self.security_middleware = None
        
        self.logger.info("统一用户身份管理器初始化完成")
    
    def start(self):
        """启动管理器"""
        if self.running:
            return
        
        self.running = True
        
        # 注册到单例管理器
        from utilities.singleton_manager import register
        register('unified_user_manager', self)
        
        # 集成现有管理器
        self._integrate_existing_managers()
        
        self.logger.info("统一用户身份管理器启动成功")
    
    def stop(self):
        """停止管理器"""
        if not self.running:
            return
        
        self.running = False
        
        # 停止后台任务
        if self.cleanup_task:
            self.cleanup_task.cancel()
        
        self.logger.info("统一用户身份管理器已停止")
    
    def _integrate_existing_managers(self):
        """集成现有的管理器"""
        try:
            # 使用get_silent避免警告
            from utilities.singleton_manager import get_silent
            
            # 集成用户管理器
            self.user_manager = get_silent('user_manager')
            
            # 集成联系人管理器
            try:
                from core.contacts_manager import get_contacts_manager
                self.contacts_manager = get_contacts_manager()  # 🔥 老王修复：使用单例工厂函数
            except Exception as e:
                self.logger.warning(f"获取联系人管理器失败: {e}")
                self.contacts_manager = None
            
            # 集成存储管理器
            self.storage_manager = get_silent('storage_manager')
            
            # 集成安全中间件
            self.security_middleware = get_silent('security_middleware')
            
            self.logger.info("已集成现有管理器")
            
        except Exception as e:
            self.logger.warning(f"集成现有管理器时出现问题: {e}")
    
    # ===== 用户管理方法 =====
    
    def get_or_create_user(self, user_id: str, **kwargs) -> UnifiedUserProfile:
        """获取或创建用户"""
        # 解析用户ID
        primary_user_id = self._resolve_user_id(user_id)
        
        with self.users_lock:
            if primary_user_id in self.users:
                user = self.users[primary_user_id]
                # 更新最后活跃时间
                user.last_active = datetime.now()
                return user
            
            # 创建新用户
            user = self._create_user(primary_user_id, **kwargs)
            self.users[primary_user_id] = user
            
            # 建立ID映射
            if user_id != primary_user_id:
                self.user_id_mapping[user_id] = primary_user_id
            
            self.logger.info(f"创建新用户: {primary_user_id}")
            return user
    
    def _create_user(self, user_id: str, **kwargs) -> UnifiedUserProfile:
        """创建新用户"""
        # 从现有管理器获取用户信息
        user_info = self._gather_user_info(user_id)
        
        # 合并传入的参数
        user_info.update(kwargs)
        
        # 🔥 修复：优先使用API传入的用户名，避免硬编码"命令行用户"
        # 优先级：API传入的user_name > 数据库中的name > 联系人的nickname > 生成的默认名
        api_user_name = kwargs.get('user_name')
        
        # 🔥 老王修复：严格验证API传入的用户名
        if api_user_name and api_user_name.strip():
            # 🔥 关键修复：拒绝明显的假数据
            if api_user_name in ["神秘嘉宾", "未知用户", "命令行用户"]:
                self.logger.error(f"❌ API传入的用户名 '{api_user_name}' 不是有效数据")
                raise ValueError(f"API传入的用户名 '{api_user_name}' 不是有效数据")

            user_name = api_user_name
            self.logger.info(f"✅ 使用API传入的有效用户名: {user_name}")
        else:
            # 🔥 关键修复：如果没有API数据，不能创建用户
            self.logger.error(f"❌ 没有API传入的用户名，无法创建用户 {user_id}")
            raise ValueError(f"没有API传入的用户名，无法创建用户 {user_id}")

            # 注释掉降级方案，严格遵循API唯一数据源原则
            # user_name = (
            #     user_info.get('name') or    # 数据库中的name
            #     user_info.get('nickname') or  # 联系人的nickname
            #     self._generate_fallback_name(user_id)  # 生成的默认名
            # )
        
        # 同样处理nickname
        nickname = (
            api_user_name or  # API传入的用户名也可以作为nickname
            user_info.get('nickname') or
            user_name  # 使用确定的user_name作为nickname
        )
        
        # 创建统一用户档案
        user = UnifiedUserProfile(
            user_id=user_id,
            name=user_name,
            nickname=nickname,
            sex=user_info.get('sex', kwargs.get('user_sex', '0')),
            wxid=user_info.get('wxid', user_id if user_id.startswith('wx') else None),
            contact_info=user_info.get('contact_info', {}),
            preferences=user_info.get('preferences', {}),
            metadata=user_info.get('metadata', {})
        )
        
        # 同步到其他管理器
        self._sync_user_to_managers(user)
        
        return user
    
    def _generate_fallback_name(self, user_id: str) -> str:
        """生成降级用户名"""
        if user_id.endswith("@chatroom"):
            # 群聊ID处理
            chatroom_id = user_id.split("@")[0]
            if len(chatroom_id) > 6:
                return f"群聊{chatroom_id[-6:]}"
            else:
                return "群聊用户"
        else:
            # 普通用户ID处理
            if len(user_id) > 6:
                return f"用户{user_id[-6:]}"
            else:
                return "朋友"
    
    def _gather_user_info(self, user_id: str) -> Dict[str, Any]:
        """从各个管理器收集用户信息 - 🔥 修复：统一数据源优先级"""
        user_info = {}
        
        # 🔥 P0级别修复：按优先级顺序获取用户信息
        # 优先级：数据库 > 联系人管理器 > 用户管理器 > 存储管理器
        
        # 1. 从数据库获取（最高优先级）
        try:
            from adapters.legacy_adapter import get_instance as get_legacy_adapter
            legacy_adapter = get_legacy_adapter()
            
            if legacy_adapter and legacy_adapter.mysql and legacy_adapter.mysql.is_available:
                user_query = "SELECT id, name, created_at, first_chat_time, last_chat_time FROM users WHERE id = %s"
                success, result, error = legacy_adapter.mysql.query_one(user_query, (user_id,))
                
                if success and result:
                    user_info.update({
                        "id": result.get("id"),
                        "name": result.get("name"),
                        "created_at": result.get("created_at") or result.get("first_chat_time"),
                        "last_active": result.get("last_chat_time"),
                        "source": "database"
                    })
                    self.logger.debug(f"✅ 从数据库获取用户信息: {result.get('name')}")
        except Exception as e:
            self.logger.debug(f"从数据库获取用户信息失败: {e}")
        
        # 2. 从联系人管理器获取（高优先级）
        if self.contacts_manager:
            try:
                info = self.contacts_manager.get_user_info(user_id)
                if info:
                    # 只更新数据库中没有的字段
                    contact_info = {
                        "nickname": info.get("nickname"),
                        "sex": info.get("sex"),
                        "avatar": info.get("avatar"),
                        "contact_info": info,
                        "source": "contacts"
                    }
                    # 如果数据库中没有name，使用联系人的nickname
                    if not user_info.get("name") and info.get("nickname"):
                        contact_info["name"] = info.get("nickname")
                    
                    user_info.update({k: v for k, v in contact_info.items() if v is not None})
                    self.logger.debug(f"✅ 从联系人管理器获取用户信息: {info.get('nickname')}")
            except Exception as e:
                self.logger.debug(f"从联系人管理器获取信息失败: {e}")
        
        # 3. 从用户管理器获取（中等优先级）
        if self.user_manager:
            try:
                info = self.user_manager.get_user_info(user_id)
                if info:
                    # 只更新前面没有的字段
                    user_manager_info = {
                        "metadata": info.get("metadata"),
                        "preferences": info.get("preferences"),
                        "source": "user_manager"
                    }
                    if not user_info.get("name") and info.get("name"):
                        user_manager_info["name"] = info.get("name")
                    
                    user_info.update({k: v for k, v in user_manager_info.items() if v is not None})
                    self.logger.debug(f"✅ 从用户管理器获取用户信息")
            except Exception as e:
                self.logger.debug(f"从用户管理器获取信息失败: {e}")
        
        # 4. 从存储管理器获取（最低优先级）
        if self.storage_manager:
            try:
                info = self.storage_manager.get_user(user_id)
                if info:
                    # 只更新前面没有的字段
                    storage_info = {
                        "preferences": info.get("preferences"),
                        "data": info.get("data"),
                        "source": "storage"
                    }
                    if not user_info.get("name") and info.get("username"):
                        storage_info["name"] = info.get("username")
                    
                    user_info.update({k: v for k, v in storage_info.items() if v is not None})
                    self.logger.debug(f"✅ 从存储管理器获取用户信息")
            except Exception as e:
                self.logger.debug(f"从存储管理器获取信息失败: {e}")
        
        return user_info
    
    def _sync_user_to_managers(self, user: UnifiedUserProfile):
        """同步用户信息到其他管理器"""
        # 同步到用户管理器
        if self.user_manager:
            try:
                if not self.user_manager.user_exists(user.user_id):
                    self.user_manager.add_user(user.user_id, user.name, user.metadata)
            except Exception as e:
                self.logger.debug(f"同步到用户管理器失败: {e}")
        
        # 同步到联系人管理器
        if self.contacts_manager:
            try:
                self.contacts_manager.ensure_user_exists(
                    user.user_id, 
                    user.nickname or user.name, 
                    user.sex
                )
            except Exception as e:
                self.logger.debug(f"同步到联系人管理器失败: {e}")
    
    def _resolve_user_id(self, user_id: str) -> str:
        """解析用户ID，返回主要用户ID"""
        # 检查ID映射
        if user_id in self.user_id_mapping:
            return self.user_id_mapping[user_id]
        
        # 🔥 修复：保持用户ID原样，不自动添加前缀
        # 只有在用户ID为空或无效时才生成标准ID
        if not user_id or user_id.strip() == "":
            return f"user_anonymous_{int(time.time())}"
        
        # 保持原始用户ID不变
        return user_id
    
    def get_user(self, user_id: str) -> Optional[UnifiedUserProfile]:
        """获取用户"""
        primary_user_id = self._resolve_user_id(user_id)
        
        with self.users_lock:
            return self.users.get(primary_user_id)
    
    def update_user(self, user_id: str, **kwargs) -> bool:
        """更新用户信息 - 🔥 修复：确保数据同步到所有管理器"""
        primary_user_id = self._resolve_user_id(user_id)
        
        with self.users_lock:
            if primary_user_id not in self.users:
                return False
            
            user = self.users[primary_user_id]
            
            # 更新字段
            for key, value in kwargs.items():
                if hasattr(user, key):
                    setattr(user, key, value)
            
            # 更新活跃时间
            user.last_active = datetime.now()
            
            # 🔥 P0级别修复：确保数据同步到所有管理器
            self._sync_user_to_all_managers(user, kwargs)
            
            return True
    
    def _sync_user_to_all_managers(self, user: UnifiedUserProfile, updated_fields: Dict[str, Any]):
        """同步用户信息到所有管理器 - 🔥 新增：确保数据一致性"""
        # 1. 同步到数据库（最高优先级）
        try:
            from adapters.legacy_adapter import get_instance as get_legacy_adapter
            legacy_adapter = get_legacy_adapter()
            
            if legacy_adapter and legacy_adapter.mysql and legacy_adapter.mysql.is_available:
                # 更新users表
                if "name" in updated_fields:
                    update_query = "UPDATE users SET name = %s WHERE id = %s"
                    success, affected_rows, error = legacy_adapter.mysql.execute_update(
                        update_query, (user.name, user.user_id)
                    )
                    if success:
                        self.logger.debug(f"✅ 同步用户名到数据库: {user.name}")
                    else:
                        self.logger.warning(f"⚠️ 同步用户名到数据库失败: {error}")
        except Exception as e:
            self.logger.debug(f"同步到数据库失败: {e}")
        
        # 2. 同步到联系人管理器
        if self.contacts_manager:
            try:
                # 🔥 老王修复：强制更新联系人信息，而不是只确保存在
                if "name" in updated_fields or "nickname" in updated_fields:
                    # 用户名或昵称发生变更，强制更新
                    self.contacts_manager.ensure_user_exists(
                        user.user_id,
                        user.nickname or user.name,
                        user.sex
                    )
                    self.logger.debug(f"✅ 强制同步到联系人管理器: {user.nickname or user.name}")
                else:
                    # 其他字段更新，只确保存在
                    self.contacts_manager.ensure_user_exists(
                        user.user_id,
                        user.nickname or user.name,
                        user.sex
                    )
                    self.logger.debug(f"✅ 同步到联系人管理器: {user.nickname or user.name}")
            except Exception as e:
                self.logger.debug(f"同步到联系人管理器失败: {e}")
        
        # 3. 同步到用户管理器
        if self.user_manager:
            try:
                if not self.user_manager.user_exists(user.user_id):
                    self.user_manager.add_user(user.user_id, user.name, user.metadata)
                else:
                    # 更新用户信息
                    if "name" in updated_fields:
                        self.user_manager.update_user_name(user.user_id, user.name)
                self.logger.debug(f"✅ 同步到用户管理器: {user.name}")
            except Exception as e:
                self.logger.debug(f"同步到用户管理器失败: {e}")
        
        # 4. 同步到存储管理器
        if self.storage_manager:
            try:
                self.storage_manager.update_user(user.user_id, {
                    "username": user.name,
                    "preferences": user.preferences,
                    "data": user.metadata
                })
                self.logger.debug(f"✅ 同步到存储管理器: {user.name}")
            except Exception as e:
                self.logger.debug(f"同步到存储管理器失败: {e}")
    
    def record_user_interaction(self, user_id: str, message_length: int = 0):
        """记录用户交互"""
        user = self.get_or_create_user(user_id)
        
        with self.users_lock:
            user.interaction_count += 1
            user.total_messages += 1
            user.last_message_time = datetime.now()
            user.last_active = datetime.now()
    
    # ===== 会话管理方法 =====
    
    def _create_session_with_id(self, session_id: str, user_id: str, expires_in: int = None, **kwargs) -> UnifiedSession:
        """使用指定的session_id创建会话"""
        user = self.get_or_create_user(user_id)
        
        # 计算过期时间
        expires_at = None
        if expires_in:
            expires_at = datetime.now() + timedelta(seconds=expires_in)
        else:
            expires_at = datetime.now() + timedelta(seconds=self.config['session_timeout'])
        
        # 创建会话 - 🔥 修复：过滤掉UnifiedSession不接受的参数
        session_kwargs = {}
        valid_session_fields = ['context', 'metadata', 'ip_address', 'user_agent']
        
        for field in valid_session_fields:
            if field in kwargs:
                session_kwargs[field] = kwargs[field]
        
        session = UnifiedSession(
            session_id=session_id,  # 🔥 使用传入的session_id
            user_id=user.user_id,
            expires_at=expires_at,
            csrf_token=f"csrf_{uuid.uuid4().hex[:16]}",
            **session_kwargs
        )
        
        with self.sessions_lock:
            self.sessions[session_id] = session
        
        self.logger.info(f"创建指定ID会话: {session_id} (用户: {user.user_id})")
        return session

    def create_session(self, user_id: str, expires_in: int = None, **kwargs) -> UnifiedSession:
        """创建会话"""
        user = self.get_or_create_user(user_id)
        
        # 生成会话ID
        session_id = f"session_{uuid.uuid4().hex[:16]}"
        
        # 计算过期时间
        expires_at = None
        if expires_in:
            expires_at = datetime.now() + timedelta(seconds=expires_in)
        else:
            expires_at = datetime.now() + timedelta(seconds=self.config['session_timeout'])
        
        # 创建会话 - 🔥 修复：过滤掉UnifiedSession不接受的参数
        session_kwargs = {}
        valid_session_fields = ['context', 'metadata', 'ip_address', 'user_agent']
        
        for field in valid_session_fields:
            if field in kwargs:
                session_kwargs[field] = kwargs[field]
        
        session = UnifiedSession(
            session_id=session_id,
            user_id=user.user_id,
            expires_at=expires_at,
            csrf_token=f"csrf_{uuid.uuid4().hex[:16]}",
            **session_kwargs
        )
        
        with self.sessions_lock:
            self.sessions[session_id] = session
        
        self.logger.info(f"创建会话: {session_id} (用户: {user.user_id})")
        return session
    
    def get_session(self, session_id: str) -> Optional[UnifiedSession]:
        """获取会话"""
        with self.sessions_lock:
            session = self.sessions.get(session_id)
            if session and session.is_active():
                session.update_activity()
                return session
            elif session and not session.is_active():
                # 清理无效会话
                del self.sessions[session_id]
        
        return None
    
    # ===== 统一接口方法 =====
    
    def process_user_request(self, user_id: str = None, session_id: str = None, session_id_locked: bool = False, request_context: Dict = None, **kwargs) -> Dict[str, Any]:
        """
        处理用户请求 - 支持完整的用户身份管理和请求隔离

        Args:
            user_id: 用户ID
            session_id: 会话ID
            session_id_locked: 是否锁定session_id（禁止重新生成）
            request_context: 请求上下文（用于隔离）
            **kwargs: 其他用户信息

        Returns:
            包含用户和会话信息的字典
        """
        # 🔥 增强版：请求级别的线程安全处理
        import threading
        import uuid
        import contextvars

        request_id = f"umgr_{uuid.uuid4().hex[:8]}"
        thread_id = threading.current_thread().ident
        original_user_id = user_id

        # 🔥 新增：从请求上下文获取隔离信息
        if request_context:
            parent_request_id = request_context.get("request_id", "unknown")
            self.logger.debug(f"🔗 [{request_id}] 处理来自 {parent_request_id} 的用户请求")
        else:
            parent_request_id = "unknown"
        
        # 🔥 关键修复：每个请求创建独立的处理上下文
        with self.users_lock:  # 确保整个处理过程的原子性
            try:
                self.logger.info(f"🔄 [{request_id}] Thread-{thread_id} 开始处理用户请求: user_id={user_id}")
                
                # 参数验证和清理
                if not user_id or user_id.strip() == "":
                    user_id = f"anonymous_{int(time.time())}_{thread_id}"
                    self.logger.warning(f"⚠️ [{request_id}] 用户ID为空，生成临时ID: {user_id}")
                
                # 🔥 关键修复：用户创建/获取的线程安全处理
                user = self.get_or_create_user(user_id, **kwargs)
                
                # 🔥 确保用户名正确设置（优先使用API传入的参数）
                api_user_name = kwargs.get('user_name')
                if api_user_name and api_user_name.strip() and api_user_name != "神秘嘉宾":
                    if user.name != api_user_name:
                        self.logger.info(f"🔄 [{request_id}] 更新用户名: {user.name} -> {api_user_name}")
                        user.name = api_user_name
                        user.nickname = api_user_name
                
                # 🔥 会话管理的线程安全处理
                is_new_session = False
                if session_id:
                    # 使用传入的session_id
                    session = self.get_session(session_id)
                    if not session:
                        # 会话不存在，创建新会话
                        session = self._create_session_with_id(session_id, user.user_id, **kwargs)
                        is_new_session = True
                        self.logger.info(f"🔗 [{request_id}] 创建指定ID会话: {session_id}")
                    else:
                        self.logger.info(f"🔗 [{request_id}] 使用现有会话: {session_id}")
                else:
                    # 创建新会话
                    if not session_id_locked:
                        session = self.create_session(user.user_id, **kwargs)
                        is_new_session = True
                        self.logger.info(f"🔗 [{request_id}] 创建新会话: {session.session_id}")
                    else:
                        # 会话ID被锁定但为空，这是错误状态
                        self.logger.error(f"❌ [{request_id}] 会话ID被锁定但为空")
                        session = self.create_session(user.user_id, **kwargs)
                        is_new_session = True
                
                # 🔥 记录用户交互（线程安全）
                self.record_user_interaction(user.user_id, kwargs.get('message_length', 0))
                
                self.logger.success(f"✅ [{request_id}] 用户请求处理完成: {user.user_id} ({user.name})")
                
                return {
                    'user': user,
                    'session': session,
                    'is_new_user': user_id not in self.users,
                    'is_new_session': is_new_session,
                    'user_id_fixed': original_user_id != user_id,
                    'original_user_id': original_user_id,
                    'request_id': request_id,  # 🔥 新增：返回请求ID用于追踪
                    'thread_id': thread_id     # 🔥 新增：返回线程ID用于调试
                }
                
            except Exception as e:
                self.logger.error(f"❌ [{request_id}] 处理用户请求失败: {e}")
                # 🔥 容错处理：即使出错也要返回可用的用户信息
                fallback_user_id = user_id or f"fallback_user_{int(time.time())}_{thread_id}"
                fallback_user = self.get_or_create_user(fallback_user_id, **kwargs)
                fallback_session = self.create_session(fallback_user_id, **kwargs)
                
                return {
                    'user': fallback_user,
                    'session': fallback_session,
                    'is_new_user': True,
                    'is_new_session': True,
                    'user_id_fixed': True,
                    'original_user_id': original_user_id,
                    'error': str(e),
                    'request_id': request_id,
                    'thread_id': thread_id
                }
    
    def _validate_and_fix_user_id(self, user_id: str, kwargs: Dict[str, Any]) -> str:
        """
        验证和修复用户ID
        
        Args:
            user_id: 原始用户ID
            kwargs: 其他参数
            
        Returns:
            修复后的用户ID
        """
        # 🔥 修复问题6：用户ID验证和修复逻辑
        
        # 1. 如果用户ID有效，直接返回
        if user_id and user_id.strip() and user_id != "None" and user_id != "null":
            return user_id.strip()
        
        # 2. 尝试从其他字段获取用户ID
        fallback_sources = [
            kwargs.get('from_user_ID'),
            kwargs.get('from_user_id'), 
            kwargs.get('wxid'),
            kwargs.get('user_id'),
            kwargs.get('sender_id')
        ]
        
        for source_id in fallback_sources:
            if source_id and source_id.strip() and source_id != "None" and source_id != "null":
                self.logger.info(f"🔧 从备用字段恢复用户ID: {source_id}")
                return source_id.strip()
        
        # 3. 尝试从token或appid生成用户ID
        token = kwargs.get('_token')
        appid = kwargs.get('_appid')
        
        if token and token.strip():
            generated_id = f"token_user_{hash(token) % 1000000}"
            self.logger.info(f"🔧 从token生成用户ID: {generated_id}")
            return generated_id
        
        if appid and appid.strip():
            generated_id = f"app_user_{hash(appid) % 1000000}"
            self.logger.info(f"🔧 从appid生成用户ID: {generated_id}")
            return generated_id
        
        # 4. 最后手段：生成时间戳用户ID
        timestamp_id = f"unknown_user_{int(time.time())}"
        self.logger.warning(f"⚠️ 无法恢复用户ID，生成时间戳ID: {timestamp_id}")
        return timestamp_id
    
    def _record_user_id_loss_event(self, original_user_id: str, kwargs: Dict[str, Any], emergency_user_id: str):
        """记录用户ID丢失事件"""
        try:
            loss_event = {
                "timestamp": time.time(),
                "original_user_id": original_user_id,
                "emergency_user_id": emergency_user_id,
                "available_fields": {k: v for k, v in kwargs.items() if k.startswith(('user', 'from_', 'wxid', '_token', '_appid'))},
                "severity": "P0_CRITICAL"
            }
            
            # 记录到文件（简单实现）
            import json
            import os
            
            log_dir = "data/errors"
            os.makedirs(log_dir, exist_ok=True)
            
            log_file = os.path.join(log_dir, "user_id_loss_events.json")
            
            if os.path.exists(log_file):
                with open(log_file, 'r', encoding='utf-8') as f:
                    events = json.load(f)
            else:
                events = []
            
            events.append(loss_event)
            
            # 只保留最近100条记录
            if len(events) > 100:
                events = events[-100:]
            
            with open(log_file, 'w', encoding='utf-8') as f:
                json.dump(events, f, ensure_ascii=False, indent=2)
                
            self.logger.error(f"💾 用户ID丢失事件已记录到: {log_file}")
            
        except Exception as e:
            self.logger.error(f"记录用户ID丢失事件失败: {e}")
    
    # ===== 状态和统计方法 =====
    
    def get_stats(self) -> Dict[str, Any]:
        """获取统计信息"""
        with self.users_lock, self.sessions_lock:
            active_sessions = sum(1 for s in self.sessions.values() if s.is_active())
            
            return {
                'total_users': len(self.users),
                'total_sessions': len(self.sessions),
                'active_sessions': active_sessions,
                'user_id_mappings': len(self.user_id_mapping),
                'config': self.config.copy()
            }


# 单例获取函数
_unified_user_manager_instance = None
_unified_user_manager_lock = threading.Lock()


def get_unified_user_manager() -> UnifiedUserManager:
    """获取统一用户管理器实例"""
    global _unified_user_manager_instance
    
    if _unified_user_manager_instance is None:
        with _unified_user_manager_lock:
            if _unified_user_manager_instance is None:
                _unified_user_manager_instance = UnifiedUserManager()
                _unified_user_manager_instance.start()
    
    return _unified_user_manager_instance


if __name__ == "__main__":
    # 测试代码
    manager = get_unified_user_manager()
    
    # 测试用户管理
    user_data = manager.process_user_request("test_user", name="测试用户")
    print(f"用户数据: {user_data}")
    
    # 测试会话管理
    session = manager.create_session("test_user")
    print(f"会话: {session}")
    
    # 测试统计
    stats = manager.get_stats()
    print(f"统计: {stats}") 