#!/usr/bin/env python3
"""
思维链路模块 - Thinking Chain

该模块实现了数字生命体的思维链路系统，负责协调认知模块的执行流程，
形成从感知到行为的完整思维链条。思维链路系统通过事件总线与各认知模块交互，
确保认知过程的有序进行和数据的正确流转。

作者: Claude
创建日期: 2024-06-12
版本: 1.0
"""

import os
import sys
import time
import json
import uuid
from utilities.unified_logger import get_unified_logger, setup_unified_logging
import threading
import asyncio
from typing import Dict, Any, List, Callable, Optional, Union, Tuple
from dataclasses import dataclass, field
from enum import Enum
import importlib
import traceback

# 配置日志记录
setup_unified_logging()
logger = get_unified_logger("thinking_chain")

# 尝试导入依赖模块
try:
    from core.enhanced_event_bus import get_instance as get_event_bus, EnhancedEvent, EventPriority
    from core.life_context import get_instance as get_life_context
except ImportError as e:
    logger.error_status(f"导入依赖模块失败: {e}")
    raise

# 思维步骤状态
class ThinkingStepStatus(Enum):
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    SKIPPED = "skipped"
    TIMEOUT = "timeout"

# 思维链路状态
class ThinkingStatus(Enum):
    IDLE = "idle"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    TIMEOUT = "timeout"
    ABORTED = "aborted"

@dataclass
class ThinkingStep:
    """思维步骤类，表示思维链路中的一个步骤"""
    id: str
    name: str
    description: str
    modules: List[str] = field(default_factory=list)  # 旧的多模块字段，保留向后兼容
    status: ThinkingStepStatus = ThinkingStepStatus.PENDING
    start_time: Optional[float] = None
    end_time: Optional[float] = None
    result: Optional[Dict[str, Any]] = None
    error: Optional[Any] = None
    timeout: float = 5.0  # 超时时间(秒)
    required: bool = True  # 是否必须执行
    enabled: bool = True  # 是否启用
    module: str = ""  # 新的模块路径
    function: str = "process"  # 处理函数名
    duration: float = 0.0  # 执行时长
    skip_conditions: Optional[Dict[str, Any]] = None  # 跳过条件
    
    def reset(self):
        """重置步骤状态"""
        self.status = ThinkingStepStatus.PENDING
        self.start_time = None
        self.end_time = None
        self.result = None
        self.error = None
        self.duration = 0.0
    
    def mark_running(self):
        """标记为正在运行"""
        self.status = ThinkingStepStatus.RUNNING
        self.start_time = time.time()
    
    def mark_completed(self, result: Dict[str, Any] = None):
        """标记为已完成"""
        self.status = ThinkingStepStatus.COMPLETED
        self.end_time = time.time()
        self.result = result
        if self.start_time:
            self.duration = self.end_time - self.start_time
    
    def mark_failed(self, error: str):
        """标记为失败"""
        self.status = ThinkingStepStatus.FAILED
        self.end_time = time.time()
        self.error = error
        if self.start_time:
            self.duration = self.end_time - self.start_time
    
    def mark_skipped(self):
        """标记为已跳过"""
        self.status = ThinkingStepStatus.SKIPPED
        self.end_time = time.time()
        if self.start_time:
            self.duration = self.end_time - self.start_time
    
    def mark_timeout(self):
        """标记为超时"""
        self.status = ThinkingStepStatus.TIMEOUT
        self.end_time = time.time()
        self.error = "步骤执行超时"
        if self.start_time:
            self.duration = self.end_time - self.start_time
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            "id": self.id,
            "name": self.name,
            "description": self.description,
            "module": self.module,
            "function": self.function,
            "status": self.status.value,
            "start_time": self.start_time,
            "end_time": self.end_time,
            "duration": self.duration,
            "result": self.result,
            "error": str(self.error) if self.error else None,
            "timeout": self.timeout,
            "required": self.required,
            "enabled": self.enabled
        }

class ThinkingContext:
    """思维上下文类，保存思维链路执行过程中的上下文信息"""
    
    def __init__(self, session_id: str, input_data: Dict[str, Any], metadata: Dict[str, Any] = None):
        """
        初始化思维上下文
        
        Args:
            session_id: 会话ID
            input_data: 输入数据
            metadata: 元数据
        """
        self.session_id = session_id
        
        # 确保输入数据包含必要的字段
        if not isinstance(input_data, dict):
            logger.warning_status(f"输入数据不是字典类型，转换为字典: {input_data}")
            if isinstance(input_data, str):
                self.input_data = {"text": input_data}
            else:
                self.input_data = {"text": str(input_data)}
        else:
            self.input_data = input_data
            
        # 确保有text字段
        if "text" not in self.input_data or not self.input_data["text"]:
            if "input_text" in self.input_data:
                logger.debug("使用input_text作为text字段")
                self.input_data["text"] = self.input_data["input_text"]
            elif "message" in self.input_data:
                logger.debug("使用message作为text字段")
                self.input_data["text"] = self.input_data["message"]
            elif "content" in self.input_data:
                logger.debug("使用content作为text字段")
                self.input_data["text"] = self.input_data["content"]
        
        self.metadata = metadata or {}
        self.shared_data = {}  # 步骤之间共享的数据
        self.step_results = {}  # 各步骤执行结果
        self.start_time = time.time()
        self.end_time = None
        self.status = ThinkingStepStatus.PENDING
        self.error = None
        
        # 🔥 P0级别修复：确保用户ID和用户名在各个位置都正确设置
        user_id = None
        user_name = None
        
        # 1. 从input_data中获取用户ID和用户名
        if isinstance(self.input_data, dict):
            user_id = (self.input_data.get("user_id") or 
                      self.input_data.get("from_user_id") or 
                      self.input_data.get("from_user_ID"))
            user_name = (self.input_data.get("user_name") or 
                        self.input_data.get("name") or
                        self.input_data.get("user_nickname") or
                        self.input_data.get("nickname"))
        
        # 2. 如果input_data没有，从metadata中获取
        if not user_id and isinstance(self.metadata, dict):
            user_id = (self.metadata.get("user_id") or
                      self.metadata.get("from_user_id") or
                      self.metadata.get("from_user_ID"))
        
        if not user_name and isinstance(self.metadata, dict):
            user_name = (self.metadata.get("user_name") or
                        self.metadata.get("name") or
                        self.metadata.get("unified_user_name") or
                        self.metadata.get("user_nickname") or
                        self.metadata.get("nickname"))
        
        # 3. 确保用户ID和用户名在所有位置都正确设置
        if user_id:
            # 设置到shared_data中，供所有步骤访问
            self.shared_data["user_id"] = user_id
            self.shared_data["from_user_id"] = user_id  # 兼容性
            self.shared_data["from_user_ID"] = user_id  # 兼容性
            
            # 确保input_data中有用户ID
            if isinstance(self.input_data, dict):
                self.input_data["user_id"] = user_id
                if "from_user_id" not in self.input_data:
                    self.input_data["from_user_id"] = user_id
                if "from_user_ID" not in self.input_data:
                    self.input_data["from_user_ID"] = user_id
            
            # 确保metadata中有用户ID
            if isinstance(self.metadata, dict):
                self.metadata["user_id"] = user_id
                if "from_user_id" not in self.metadata:
                    self.metadata["from_user_id"] = user_id
                if "from_user_ID" not in self.metadata:
                    self.metadata["from_user_ID"] = user_id
            
            logger.debug(f"🔧 思维上下文用户ID设置完成: {user_id}")
        else:
            logger.warning(f"⚠️ 思维上下文创建时未找到用户ID，这可能导致后续处理问题")
        
        # 4. 设置用户名到各个位置
        if user_name and user_name.strip():
            # 🔥 P0级修复：过滤掉无效的用户名
            if user_name not in ["神秘嘉宾", "朋友", "用户", "命令行用户", "unknown"]:
                # 设置到shared_data中，供所有步骤访问
                self.shared_data["user_name"] = user_name
                self.user_name = user_name  # 🔥 关键修复：设置为实例属性
                
                # 确保input_data中有用户名
                if isinstance(self.input_data, dict):
                    self.input_data["user_name"] = user_name
                    if "name" not in self.input_data:
                        self.input_data["name"] = user_name
                
                # 确保metadata中有用户名
                if isinstance(self.metadata, dict):
                    self.metadata["user_name"] = user_name
                    if "name" not in self.metadata:
                        self.metadata["name"] = user_name
                
                logger.debug(f"🔧 思维上下文用户名设置完成: {user_name}")
                user_name_set = True
            else:
                logger.debug(f"🔧 过滤掉无效用户名: {user_name}")
                user_name_set = False
        else:
            user_name_set = False
        
        # 5. 如果没有有效用户名，尝试获取
        if not user_name_set:
            # 🔥 P0级别修复：如果没有用户名，尝试从用户ID生成一个合理的默认名称
            if user_id and user_id.strip():
                # 尝试从联系人管理器获取用户名
                try:
                    from core.contacts_manager import get_contacts_manager
                    contacts_manager = get_contacts_manager()
                    user_info = contacts_manager.get_user_info(user_id)
                    if user_info and user_info.get('nickname'):
                        fallback_name = user_info['nickname']
                        logger.debug(f"🔧 从联系人管理器获取用户名: {fallback_name}")
                    else:
                        # 🔥 P0级修复：尝试从统一用户管理器获取
                        try:
                            from core.unified_user_manager import get_unified_user_manager
                            user_manager = get_unified_user_manager()
                            user = user_manager.get_user(user_id)
                            if user and user.name and user.name not in ["神秘嘉宾", "朋友", "用户", "命令行用户"]:
                                fallback_name = user.name
                                logger.debug(f"🔧 从统一用户管理器获取用户名: {fallback_name}")
                            else:
                                # 生成默认用户名
                                if user_id.endswith("@chatroom"):
                                    fallback_name = "群聊朋友"
                                elif len(user_id) > 6:
                                    fallback_name = "神秘嘉宾"
                                else:
                                    fallback_name = "朋友"
                                logger.debug(f"🔧 生成默认用户名: {fallback_name}")
                        except Exception as e:
                            # 生成默认用户名
                            if user_id.endswith("@chatroom"):
                                fallback_name = "群聊朋友"
                            elif len(user_id) > 6:
                                fallback_name = "神秘嘉宾"
                            else:
                                fallback_name = "朋友"
                            logger.debug(f"🔧 生成默认用户名: {fallback_name}")
                except Exception as e:
                    # 联系人管理器获取失败，使用用户ID生成
                    if user_id.endswith("@chatroom"):
                        fallback_name = "群聊朋友"
                    elif len(user_id) > 6:
                        fallback_name = "神秘嘉宾"
                    else:
                        fallback_name = "朋友"
                    logger.debug(f"🔧 联系人获取失败，使用默认用户名: {fallback_name}")
                
                # 设置兜底用户名
                self.shared_data["user_name"] = fallback_name
                self.user_name = fallback_name
                
                # 更新到input_data和metadata
                if isinstance(self.input_data, dict):
                    self.input_data["user_name"] = fallback_name
                    self.input_data["name"] = fallback_name
                
                if isinstance(self.metadata, dict):
                    self.metadata["user_name"] = fallback_name
                    self.metadata["name"] = fallback_name
                
                logger.info(f"🔧 思维上下文用户名兜底设置完成: {fallback_name}")
            else:
                # 完全没有用户信息的情况
                self.user_name = "朋友"
                self.shared_data["user_name"] = "朋友"
                logger.warning(f"⚠️ 思维上下文创建时未找到用户ID和用户名，使用默认值: 朋友")
        
        # 将输入数据的text复制到shared_data中，确保模块可以访问
        if "text" in self.input_data and self.input_data["text"]:
            self.shared_data["input_text"] = self.input_data["text"]
            logger.debug(f"设置共享数据input_text: {self.shared_data['input_text'][:50]}...")
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        # 🔥 老王修复：安全序列化，防止循环引用和不可序列化对象
        def safe_serialize(obj):
            """安全序列化对象"""
            if obj is None:
                return None
            elif isinstance(obj, (str, int, float, bool)):
                return obj
            elif isinstance(obj, dict):
                return {k: safe_serialize(v) for k, v in obj.items()}
            elif isinstance(obj, (list, tuple)):
                return [safe_serialize(item) for item in obj]
            elif hasattr(obj, 'to_dict'):
                return obj.to_dict()
            elif hasattr(obj, '__dict__'):
                return {k: safe_serialize(v) for k, v in obj.__dict__.items() if not k.startswith('_')}
            else:
                return str(obj)

        return {
            "session_id": self.session_id,
            "input_data": safe_serialize(self.input_data),
            "metadata": safe_serialize(self.metadata),
            "shared_data": safe_serialize(self.shared_data),
            "step_results": safe_serialize(self.step_results),
            "start_time": self.start_time,
            "end_time": self.end_time,
            "status": self.status.value if hasattr(self.status, 'value') else str(self.status),
            "error": str(self.error) if self.error else None
        }
    
    def set_shared_data(self, key: str, value: Any) -> None:
        """
        设置共享数据
        
        Args:
            key: 数据键
            value: 数据值
        """
        self.shared_data[key] = value
    
    def get_shared_data(self, key: str, default: Any = None) -> Any:
        """
        获取共享数据
        
        Args:
            key: 数据键
            default: 默认值
            
        Returns:
            数据值
        """
        return self.shared_data.get(key, default)
    
    def set_step_result(self, step_id: str, result: Any) -> None:
        """
        设置步骤结果
        
        Args:
            step_id: 步骤ID
            result: 步骤结果
        """
        self.step_results[step_id] = result
    
    def get_step_result(self, step_id: str, default: Any = None) -> Any:
        """
        获取步骤结果
        
        Args:
            step_id: 步骤ID
            default: 默认值
            
        Returns:
            步骤结果
        """
        return self.step_results.get(step_id, default)
    
    def get(self, key: str, default: Any = None) -> Any:
        """
        获取上下文数据，按以下顺序查找：
        1. 步骤结果
        2. 共享数据
        3. 输入数据
        4. 元数据
        
        Args:
            key: 数据键
            default: 默认值
            
        Returns:
            数据值
        """
        # 查找步骤结果
        for step_id, step_result in self.step_results.items():
            if isinstance(step_result, dict) and key in step_result:
                return step_result[key]
        
        # 查找共享数据
        if key in self.shared_data:
            return self.shared_data[key]
        
        # 查找输入数据
        if key in self.input_data:
            return self.input_data[key]
        
        # 查找元数据
        if key in self.metadata:
            return self.metadata[key]
        
        return default

class ThinkingChain:
    """思维链路类，用于执行思维步骤序列"""
    
    def __init__(self, config: Dict[str, Any] = None):
        """
        初始化思维链路
        
        Args:
            config: 配置信息
        """
        logger.success("初始化思维链路...")
        
        # 从配置文件加载配置
        if config is None:
            config_path = os.path.join("config", "thinking_chain.json")
            if os.path.exists(config_path):
                try:
                    with open(config_path, 'r', encoding='utf-8') as f:
                        config = json.load(f)
                    logger.info(f"已加载思维链路配置: {config_path}")
                except Exception as e:
                    logger.error_status(f"加载思维链路配置失败: {str(e)}")
                    config = {}
            else:
                # 尝试旧的配置路径
                old_config_path = os.path.join("configs", "thinking_chain.json")
                if os.path.exists(old_config_path):
                    try:
                        with open(old_config_path, 'r', encoding='utf-8') as f:
                            config = json.load(f)
                        logger.info(f"已加载思维链路配置(旧路径): {old_config_path}")
                    except Exception as e:
                        logger.error_status(f"加载思维链路配置(旧路径)失败: {str(e)}")
                        config = {}
                else:
                    logger.warning_status("未找到思维链路配置文件，使用默认配置")
                    config = {}
        
        self.config = config
        
        # 获取核心组件实例
        self.event_bus = get_event_bus()
        self.life_context = get_life_context()
        
        # 初始化步骤字典
        self.steps = {}
        self._init_steps()
        
        # 执行状态
        self.status = ThinkingStatus.IDLE
        self.status_history = [ThinkingStatus.IDLE]
        self.current_step_id = None
        self.error = None
        self.current_context = None
        self.running = False
        self.execution_lock = threading.Lock()
        
        # 超时设置
        self.total_timeout = config.get("total_timeout", config.get("default_timeout", 30.0))
        self.timeout_checker = None

        logger.success("思维链路初始化完成")
    
    def shutdown(self):
        """
        关闭思维链路，清理资源
        """
        try:
            logger.info("开始关闭思维链路...")
            
            # 停止正在运行的任务
            self.stop()
            
            # 停止超时检查器
            self._stop_timeout_checker()
            
            # 清理步骤
            if hasattr(self, 'steps'):
                for step in self.steps.values():
                    if step.status == ThinkingStepStatus.RUNNING:
                        step.mark_failed("系统关闭")
                self.steps.clear()
            
            # 清理上下文
            self.current_context = None
            
            # 标记为已完成
            self.running = False
            self.status = ThinkingStatus.ABORTED
            
            logger.success("✅ 思维链路已成功关闭")
            
        except Exception as e:
            logger.error_status(f"❌ 思维链路关闭失败: {e}")
    
    def _init_steps(self):
        """初始化思维步骤"""
        steps_config = self.config.get("steps", [])
        
        for step_config in steps_config:
            step_id = step_config.get("id")
            if not step_id:
                logger.warning_status(f"步骤配置缺少ID，跳过: {step_config}")
                continue
            
            self.steps[step_id] = ThinkingStep(
                id=step_id,
                name=step_config.get("name", step_id),
                description=step_config.get("description", ""),
                module=step_config.get("module", ""),
                function=step_config.get("function", "process"),
                timeout=step_config.get("timeout", self.config.get("default_timeout", 5.0)),
                enabled=step_config.get("enabled", True),
                required=step_config.get("required", False),
                skip_conditions=step_config.get("skip_conditions", None)
            )
        
        logger.success(f"已初始化 {len(self.steps)} 个思维步骤")
    
    def _init_timeout_checker(self):
        """初始化超时检查器"""
        # 停止现有的超时检查器
        self._stop_timeout_checker()
        
        # 创建新的超时检查器
        def _check_timeout():
            if self.running and self.status == ThinkingStatus.RUNNING:
                logger.warning_status(f"思维链路执行超时: {self.total_timeout}秒")
                self.status = ThinkingStatus.TIMEOUT
        
        self.timeout_checker = threading.Timer(self.total_timeout, _check_timeout)
        self.timeout_checker.daemon = True
        self.timeout_checker.start()
    
    def _stop_timeout_checker(self):
        """停止超时检查"""
        if hasattr(self, '_timeout_thread') and self._timeout_thread:
            self._timeout_thread.cancel()
            self._timeout_thread = None
    
    def _should_skip_step(self, step: ThinkingStep) -> bool:
        """检查步骤是否应该跳过"""
        if not step.skip_conditions:
            return False
        
        # 遍历所有跳过条件
        for condition_key, condition_value in step.skip_conditions.items():
            if condition_key == "description":
                # 跳过描述字段
                continue
            
            # 检查共享数据中的条件
            shared_value = self.current_context.get_shared_data(condition_key)
            
            # 如果条件值为True，检查共享数据是否也为True
            if condition_value is True and shared_value is True:
                logger.debug(f"跳过条件满足: {condition_key} = {shared_value}")
                return True
            
            # 如果条件值为具体值，检查是否匹配
            elif shared_value == condition_value:
                logger.debug(f"跳过条件满足: {condition_key} = {shared_value}")
                return True
        
        return False
    
    def _execute_steps(self):
        """执行所有思维步骤"""
        # 记录开始时间
        start_time = time.time()
        
        # 遍历执行所有步骤
        for step_id, step in self.steps.items():
            # 如果链路已中止，停止执行
            if self.status == ThinkingStatus.ABORTED:
                logger.warning_status("思维链路已中止，停止执行")
                break
            
            # 如果链路超时，停止执行
            if time.time() - start_time > self.total_timeout:
                logger.warning_status("思维链路执行超时，停止执行")
                self.status = ThinkingStatus.TIMEOUT
                break
            
            # 检查步骤是否需要执行
            if not step.enabled:
                logger.debug(f"步骤[{step_id}]已禁用，跳过执行")
                continue
            
            # 检查跳过条件
            if self._should_skip_step(step):
                logger.info(f"步骤[{step_id}]满足跳过条件，跳过执行")
                step.status = ThinkingStepStatus.SKIPPED
                step.start_time = time.time()
                step.end_time = time.time()
                step.duration = 0.0
                continue
            
            # 执行步骤
            logger.info(f"开始执行步骤[{step_id}]: {step.name}")
            
            # 标记步骤开始
            step.status = ThinkingStepStatus.RUNNING
            step.start_time = time.time()
            
            try:
                # 执行步骤处理逻辑
                step_module = importlib.import_module(step.module)
                step_func = getattr(step_module, step.function)
                step_result = step_func(self.current_context)
                
                # 记录步骤结果
                if step_result is not None:
                    self.current_context.set_step_result(step_id, step_result)
                    
                    # 应用数据流映射
                    self._apply_data_flow(step_id, step_result)
                
                # 标记步骤完成
                step.status = ThinkingStepStatus.COMPLETED
                logger.success(f"步骤[{step_id}]执行完成")
            except Exception as e:
                # 记录步骤异常
                step.status = ThinkingStepStatus.FAILED
                step.error = e
                logger.error_status(f"步骤[{step_id}]执行异常: {e}")
                traceback.print_exc()
                
                # 如果是必需步骤，整个链路失败
                if step.required:
                    logger.error_status(f"必需步骤[{step_id}]执行失败，中止思维链路")
                    self.status = ThinkingStatus.FAILED
                    self.error = e
                    if self.config.get("stop_on_error", False):
                        break
            finally:
                # 记录步骤结束时间
                step.end_time = time.time()
                
                # 计算步骤执行时间
                step.duration = step.end_time - step.start_time
    
    def _apply_data_flow(self, step_id, step_result):
        """应用数据流映射"""
        if not self.config.get("data_flow", {}) or not step_result:
            return
        
        # 检查step_result的类型，确保可以进行键值检查
        if isinstance(step_result, dict):
            # 🔥 特殊处理：perception步骤的意图识别结果
            if step_id == "perception":
                # 将意图识别结果设置到共享数据
                self.current_context.set_shared_data("intent", step_result)
                self.current_context.set_shared_data("intent_recognized", True)
                logger.debug(f"设置意图识别结果到共享数据: {step_result.get('type', 'unknown')}")
                
                # 同时设置到input_data中供其他步骤使用
                if hasattr(self.current_context, 'input_data'):
                    self.current_context.input_data["intent"] = step_result
                    self.current_context.input_data["intent_data"] = step_result
                    logger.debug("同时设置意图数据到input_data中")
            
            # 特殊处理：自动设置skill_executed标志
            if step_id == "skill_execution" and step_result.get("skill_executed"):
                self.current_context.set_shared_data("skill_executed", True)
                logger.debug("设置skill_executed标志为True")
            
            # 如果是字典，直接应用映射
            data_flow = self.config.get("data_flow", {})
            step_mappings = data_flow.get(step_id, {})
            for source_key, target_key in step_mappings.items():
                if source_key in step_result:
                    self.current_context.set_shared_data(target_key, step_result[source_key])
                    logger.debug(f"数据流映射: {step_id}.{source_key} -> shared.{target_key}")
        else:
            # 如果不是字典，记录日志但不处理
            logger.debug(f"步骤 {step_id} 返回非字典结果: {type(step_result)}")
        
        # 🔥 额外处理：确保关键数据的传递
        if step_id == "perception" and isinstance(step_result, dict):
            # 提取关键字段并设置到共享数据
            for key in ["type", "main_intent", "confidence", "requires_realtime_data"]:
                if key in step_result:
                    self.current_context.set_shared_data(f"intent_{key}", step_result[key])
        
        if step_id == "skill_execution" and isinstance(step_result, dict):
            # 提取技能执行的关键信息
            for key in ["skill_name", "intent_type", "skill_result"]:
                if key in step_result:
                    self.current_context.set_shared_data(key, step_result[key])
    
    def _finalize_execution(self):
        """完成执行，更新状态"""
        # 更新执行状态
        if self.status == ThinkingStatus.RUNNING:
            # 如果所有必需步骤都完成，标记为成功
            all_required_completed = all(
                step.status == ThinkingStepStatus.COMPLETED
                for step in self.steps.values()
                if step.required and step.enabled
            )
            
            if all_required_completed:
                self.status = ThinkingStatus.COMPLETED
                logger.success("思维链路执行成功完成")
            else:
                self.status = ThinkingStatus.FAILED
                logger.warning_status("思维链路执行失败: 部分必需步骤未完成")
        
        # 更新上下文状态
        if self.current_context:
            self.current_context.end_time = time.time()
            self.current_context.status = self.status
            
            # 计算执行时间
            duration = self.current_context.end_time - self.current_context.start_time
            logger.info(f"思维链路执行耗时: {duration:.2f}秒")
        
        # 更新状态历史
        self.status_history.append(self.status)
    
    def execute(self, context: Optional[ThinkingContext] = None) -> ThinkingContext:
        """
        执行思维链路
        
        Args:
            context: 思维上下文
            
        Returns:
            执行结果
        """
        try:
            # 记录执行状态
            self.running = True
            self.status = ThinkingStatus.RUNNING
            
            # 设置上下文
            self.current_context = context or ThinkingContext(str(uuid.uuid4()), {}, {})
            
            # 更新状态
            self.status_history.append(ThinkingStatus.RUNNING)
            
            # 初始化步骤超时检查
            self._init_timeout_checker()
            
            # 执行所有步骤
            self._execute_steps()
            
            # 处理结果
            self._finalize_execution()
            
            return self.current_context
        except Exception as e:
            logger.error_status(f"思维链路执行异常: {e}")
            traceback.print_exc()
            self.error = e
            self.status = ThinkingStatus.FAILED
            self.status_history.append(ThinkingStatus.FAILED)
            if self.current_context:
                self.current_context.error = e
            raise
        finally:
            # 停止超时检查
            self._stop_timeout_checker()
            self.running = False
    
    async def process(self, context: ThinkingContext) -> ThinkingContext:
        """
        异步执行思维链路处理
        
        Args:
            context: 思维上下文
            
        Returns:
            处理后的上下文
        """
        logger.info(f"开始思维链路处理: {context.session_id}")
        
        try:
            # 记录执行状态
            self.running = True
            self.status = ThinkingStatus.RUNNING
            
            # 设置上下文
            self.current_context = context
            
            # 更新状态
            self.status_history.append(ThinkingStatus.RUNNING)
            
            # 初始化步骤超时检查
            self._init_timeout_checker()
            
            # 🔥 老王新增：神经网络意识增强集成
            await self._enhance_thinking_with_neural_consciousness()

            # 执行所有步骤
            await self._execute_steps_async()

            # 处理结果
            self._finalize_execution()
            
            logger.success(f"思维链路处理完成: {context.session_id}")
            return self.current_context
        except Exception as e:
            logger.error_status(f"思维链路执行异常: {e}")
            traceback.print_exc()
            self.error = e
            self.status = ThinkingStatus.FAILED
            self.status_history.append(ThinkingStatus.FAILED)
            if self.current_context:
                self.current_context.error = e
            raise
        finally:
            # 停止超时检查
            self._stop_timeout_checker()
            self.running = False

    async def _enhance_thinking_with_neural_consciousness(self):
        """🔥 老王新增：使用神经网络增强思维过程"""
        try:
            if not self.current_context:
                return

            # 获取神经网络增强系统
            try:
                from core.neural_consciousness_enhancer import get_instance as get_neural_enhancer
                from core.advanced_neural_consciousness import get_instance as get_advanced_neural
                neural_enhancer = get_neural_enhancer()
                advanced_neural = get_advanced_neural()
            except Exception as e:
                logger.debug(f"神经网络系统不可用: {e}")
                return

            # 准备思维状态数据
            # 🔥 老王修复：使用正确的属性名
            context_data = self.current_context.shared_data or self.current_context.input_data or {}
            thinking_state = {
                "session_id": self.current_context.session_id,
                "thinking_steps": len(self.steps),
                "context_data": context_data,
                "metadata": self.current_context.metadata,
                "cognitive_complexity": len(str(context_data)) / 1000.0,
                "timestamp": time.time()
            }

            # 准备环境因子
            environmental_factors = {
                "thinking_depth": min(len(self.steps) / 10.0, 1.0),
                "cognitive_load": thinking_state["cognitive_complexity"],
                "learning_opportunity": 0.9,  # 思维过程是重要的学习机会
                "challenge_level": 0.8,
                "processing_complexity": min(len(self.steps) / 5.0, 1.0)
            }

            # 🧠 基础神经网络增强
            neural_insights = {}
            if neural_enhancer:
                try:
                    enhanced_thinking = neural_enhancer.enhance_consciousness(thinking_state, environmental_factors)
                    self.current_context.metadata["neural_enhancement"] = enhanced_thinking

                    # 🔥 老王优化：提取关键思维洞察
                    neural_insights["thinking_depth"] = enhanced_thinking.get("consciousness_level", 0.5)
                    neural_insights["cognitive_load"] = enhanced_thinking.get("cognitive_complexity", 0.5)

                    logger.debug("🧠 思维链路神经网络增强完成")
                except Exception as e:
                    logger.warning(f"思维链路神经网络增强失败: {e}")

            # 🚀 高级神经网络增强
            if advanced_neural:
                try:
                    ultimate_thinking = advanced_neural.ultimate_consciousness_enhancement(thinking_state, environmental_factors)
                    self.current_context.metadata["ultimate_neural_enhancement"] = ultimate_thinking

                    # 🔥 老王优化：提取高级思维洞察
                    neural_insights["quantum_thinking"] = ultimate_thinking.get("quantum_coherence", 0.0)
                    neural_insights["emergent_insights"] = ultimate_thinking.get("emergence_complexity", 0.0)

                    logger.debug("🚀 思维链路高级神经网络增强完成")
                except Exception as e:
                    logger.warning(f"思维链路高级神经网络增强失败: {e}")

            # 🔥 老王新增：将神经网络洞察集成到思维数据流
            if neural_insights:
                self.current_context.metadata["neural_insights"] = neural_insights
                self.current_context.metadata["neural_data_flow"] = {
                    "thinking_enhanced": True,
                    "enhancement_timestamp": time.time(),
                    "insights_count": len(neural_insights)
                }
                logger.debug(f"🔗 思维神经网络数据流集成完成: {neural_insights}")

        except Exception as e:
            logger.error(f"思维链路神经网络增强失败: {e}")
    
    def process_thought(self, thinking_context: Dict[str, Any]) -> Dict[str, Any]:
        """
        处理思维（同步版本，兼容性方法）
        
        Args:
            thinking_context: 思维上下文字典
            
        Returns:
            Dict[str, Any]: 处理结果
        """
        try:
            # 从字典创建ThinkingContext对象
            session_id = thinking_context.get("session_id", f"session_{int(time.time())}")
            input_data = {
                "text": thinking_context.get("input", ""),
                "user_id": thinking_context.get("user_id", "default_user"),
                "session_id": session_id
            }
            metadata = thinking_context.get("metadata", {})
            
            # 创建思维上下文对象
            context = ThinkingContext(session_id, input_data, metadata)
            
            # 同步执行思维链路
            result_context = self.execute(context)
            
            # 构建返回结果
            if result_context and self.status == ThinkingStatus.COMPLETED:
                # 🔥 修复：改进默认响应机制，提供更有意义的默认响应
                default_responses = [
                    "我现在思绪有点乱，让我整理一下再回复你好吗？",
                    "抱歉，刚才走神了，你能再说一遍吗？",
                    "我需要静下心来想想，稍等一下哦～",
                    "让我重新组织一下语言，这样能更好地回答你"
                ]
                import random
                response = random.choice(default_responses)
                
                # 检查各个步骤的结果
                step_results = result_context.step_results
                
                # 优先从决策制定步骤获取响应
                if "decision_making" in step_results:
                    decision_result = step_results["decision_making"]
                    if isinstance(decision_result, dict):
                        if "response" in decision_result:
                            # 处理嵌套的响应结构
                            response_data = decision_result["response"]
                            if isinstance(response_data, dict) and "result" in response_data:
                                response = response_data["result"]
                            elif isinstance(response_data, str):
                                response = response_data
                            else:
                                response = str(response_data)
                
                # 如果没有决策结果，尝试从技能执行步骤获取
                if response == "我正在思考中..." and "skill_execution" in step_results:
                    skill_result = step_results["skill_execution"]
                    if isinstance(skill_result, dict) and "skill_result" in skill_result:
                        skill_data = skill_result["skill_result"]
                        if isinstance(skill_data, dict) and "result" in skill_data:
                            response = skill_data["result"]
                
                # 如果仍然没有找到，尝试从其他步骤获取
                if response in default_responses:  # 🔥 修复：检查是否仍是默认响应
                    for step_id, step_result in step_results.items():
                        if isinstance(step_result, dict):
                            if "response" in step_result:
                                response_data = step_result["response"]
                                if isinstance(response_data, dict) and "result" in response_data:
                                    response = response_data["result"]
                                elif isinstance(response_data, str) and response_data.strip():
                                    response = response_data
                                else:
                                    response = str(response_data)
                                break
                            elif "result" in step_result:
                                result_data = step_result["result"]
                                if isinstance(result_data, str) and result_data.strip():
                                    response = result_data
                                    break
                                elif isinstance(result_data, dict) and result_data:
                                    response = str(result_data)
                                    break
                
                # 确保响应是字符串类型
                if not isinstance(response, str):
                    response = str(response)
                
                return {
                    "success": True,
                    "response": response,
                    "context": result_context.to_dict(),
                    "status": self.status.value
                }
            else:
                return {
                    "success": False,
                    "response": "思维链路处理失败",
                    "error": str(self.error) if self.error else "未知错误",
                    "status": self.status.value if self.status else "failed"
                }
                
        except Exception as e:
            logger.error_status(f"process_thought异常: {e}")
            return {
                "success": False,
                "response": "思维处理异常",
                "error": str(e),
                "status": "failed"
            }

    async def _execute_steps_async(self):
        """异步执行所有思维步骤"""
        # 记录开始时间
        start_time = time.time()
        
        # 遍历执行所有步骤
        for step_id, step in self.steps.items():
            # 如果链路已中止，停止执行
            if self.status == ThinkingStatus.ABORTED:
                logger.warning_status("思维链路已中止，停止执行")
                break
            
            # 如果链路超时，停止执行
            if time.time() - start_time > self.total_timeout:
                logger.warning_status("思维链路执行超时，停止执行")
                self.status = ThinkingStatus.TIMEOUT
                break
            
            # 检查步骤是否需要执行
            if not step.enabled:
                logger.debug(f"步骤[{step_id}]已禁用，跳过执行")
                continue
            
            # 检查跳过条件
            if self._should_skip_step(step):
                logger.info(f"步骤[{step_id}]满足跳过条件，跳过执行")
                step.status = ThinkingStepStatus.SKIPPED
                step.start_time = time.time()
                step.end_time = time.time()
                step.duration = 0.0
                continue
            
            # 执行步骤
            logger.info(f"开始执行步骤[{step_id}]: {step.name}")
            
            # 标记步骤开始
            step.status = ThinkingStepStatus.RUNNING
            step.start_time = time.time()
            
            try:
                # 执行步骤处理逻辑
                step_module = importlib.import_module(step.module)
                step_func = getattr(step_module, step.function)
                
                # 判断函数是否为异步函数
                if asyncio.iscoroutinefunction(step_func):
                    step_result = await step_func(self.current_context)
                else:
                    # 同步函数在线程池中执行
                    step_result = await asyncio.to_thread(step_func, self.current_context)
                
                # 记录步骤结果
                if step_result is not None:
                    self.current_context.set_step_result(step_id, step_result)
                    
                    # 应用数据流映射
                    self._apply_data_flow(step_id, step_result)
                
                # 标记步骤完成
                step.status = ThinkingStepStatus.COMPLETED
                logger.success(f"步骤[{step_id}]执行完成")
            except Exception as e:
                # 记录步骤异常
                step.status = ThinkingStepStatus.FAILED
                step.error = e
                logger.error_status(f"步骤[{step_id}]执行异常: {e}")
                traceback.print_exc()
                
                # 如果是必需步骤，整个链路失败
                if step.required:
                    logger.error_status(f"必需步骤[{step_id}]执行失败，中止思维链路")
                    self.status = ThinkingStatus.FAILED
                    self.error = e
                    if self.config.get("stop_on_error", False):
                        break
            finally:
                # 记录步骤结束时间
                step.end_time = time.time()
                
                # 计算步骤执行时间
                step.duration = step.end_time - step.start_time

    async def activate(self):
        """
        激活思维链路
        
        启动思维链路的处理能力和事件监听
        """
        try:
            logger.info("激活思维链路...")
            
            # 重置状态
            self.status = ThinkingStatus.IDLE
            self.running = False
            self.error = None
            
            # 重置所有步骤状态
            for step in self.steps.values():
                step.reset()
            
            # 发布激活事件
            self.event_bus.publish("thinking.activated", {
                "timestamp": time.time(),
                "status": "active"
            })
            
            logger.info("思维链路已激活")
            
        except Exception as e:
            logger.error_status(f"激活思维链路失败: {e}")
            raise

    async def deactivate(self):
        """
        停用思维链路
        
        停止思维链路的处理能力和事件监听
        """
        try:
            logger.info("停用思维链路...")
            
            # 如果正在执行，先中止
            if self.running:
                self.status = ThinkingStatus.ABORTED
                logger.warning_status("中止正在执行的思维链路")
            
            # 停止超时检查器
            self._stop_timeout_checker()
            
            # 重置状态
            self.status = ThinkingStatus.IDLE
            self.running = False
            self.current_context = None
            self.current_step_id = None
            
            # 发布停用事件
            self.event_bus.publish("thinking.deactivated", {
                "timestamp": time.time(),
                "status": "inactive"
            })
            
            logger.info("思维链路已停用")
            
        except Exception as e:
            logger.error_status(f"停用思维链路失败: {e}")
            raise

    def stop(self):
        """
        停止思维链路（同步版本）
        
        停止思维链路并清理资源
        """
        try:
            logger.info("停止思维链路...")
            
            # 如果正在执行，先中止
            if self.running:
                self.status = ThinkingStatus.ABORTED
                logger.warning_status("中止正在执行的思维链路")
            
            # 停止超时检查器
            self._stop_timeout_checker()
            
            # 重置状态
            self.status = ThinkingStatus.IDLE
            self.running = False
            self.current_context = None
            self.current_step_id = None
            
            logger.info("思维链路已停止")
            
        except Exception as e:
            logger.error_status(f"停止思维链路失败: {e}")
            raise

    def __del__(self):
        """
        析构函数，确保资源正确释放
        """
        try:
            if hasattr(self, 'running') and self.running:
                self.stop()
        except Exception as e:
            logger.error_status(f"思维链路析构失败: {e}")


# 单例模式
_instance = None

def get_instance() -> ThinkingChain:
    """
    获取思维链路实例（单例模式）
    
    Returns:
        ThinkingChain: 思维链路实例
    """
    global _instance
    if _instance is None:
        _instance = ThinkingChain()
    return _instance 