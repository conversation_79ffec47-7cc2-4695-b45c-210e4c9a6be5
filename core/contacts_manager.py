#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
联系人管理器 - 用户ID规范化和联系人信息管理
负责：
1. 用户ID验证和规范化 (user_id = wxid)
2. 联系人信息缓存和持久化
3. 新用户自动注册
4. Redis缓存优化
"""

import json
import os
import time
import threading
from typing import Dict, List, Optional, Any
from pathlib import Path
from utilities.unified_logger import get_unified_logger, setup_unified_logging

# 配置日志
setup_unified_logging()

class ContactsManager:
    """联系人管理器"""
    
    def __init__(self, config: Dict[str, Any] = None):
        """
        初始化联系人管理器
        
        Args:
            config: 配置参数
        """
        self.config = config or {}
        self.logger = get_unified_logger("ContactsManager")
        
        # 配置参数
        self.contacts_file = self.config.get("contacts_file", "config/contacts/contacts.json")
        self.cache_prefix = self.config.get("cache_prefix", "contacts:")
        self.cache_ttl = self.config.get("cache_ttl", 3600 * 24)  # 24小时
        self.sync_interval = self.config.get("sync_interval", 60)  # 🔥 老王修复：1分钟同步一次，确保及时保存
        
        # 内存缓存
        self._contacts_cache: Dict[str, Dict[str, Any]] = {}
        self._cache_dirty = False
        self._last_sync_time = 0
        
        # 线程锁
        self._lock = threading.RLock()
        
        # Redis缓存客户端
        self._redis_client = None
        self._init_redis()
        
        # 🔥 老王修复：初始化数据库同步管理器
        self._db_sync_manager = None
        self._init_database_sync()
        
        # 🔥 老王修复：初始化文件监听器
        self._sync_trigger = None
        self._init_file_watcher()
        
        # 初始化
        self._load_contacts()
        
        # 启动同步线程
        self._start_sync_thread()
    
    def _init_redis(self):
        """初始化Redis客户端"""
        try:
            # 复用现有的Redis缓存中间件
            from middleware.caching_middleware import RedisCacheBackend
            
            redis_config = self.config.get("redis", {
                "host": "localhost",
                "port": 6379,
                "db": 0,
                "key_prefix": self.cache_prefix
            })
            
            self._redis_client = RedisCacheBackend({"redis": redis_config})
            
            if self._redis_client.client:
                self.logger.info("联系人管理器Redis缓存初始化成功")
            else:
                self.logger.warning("Redis不可用，将使用内存缓存")
                
        except Exception as e:
            self.logger.error(f"初始化Redis缓存失败: {e}")
            self._redis_client = None
    
    def _init_database_sync(self):
        """🔥 老王修复：初始化数据库同步管理器"""
        try:
            from adapters.database_sync_manager import get_database_sync_manager
            self._db_sync_manager = get_database_sync_manager()
            
            # 🔥 老王修复：不要启动数据库同步服务，避免重复定时同步
            # 只获取实例用于立即同步，定时同步由数据库同步管理器自己管理
            # self._db_sync_manager.start_sync_service()
            
            self.logger.info("联系人管理器数据库同步初始化成功")
            
        except Exception as e:
            self.logger.error(f"初始化数据库同步失败: {e}")
            self._db_sync_manager = None
    
    def _init_file_watcher(self):
        """🔥 老王修复：初始化文件监听器"""
        try:
            from core.contacts_file_watcher import get_contacts_sync_trigger
            self._sync_trigger = get_contacts_sync_trigger(
                contacts_manager=self,
                db_sync_manager=self._db_sync_manager
            )
            
            # 启动文件监听
            self._sync_trigger.start_file_watching(self.contacts_file)
            
            self.logger.info("联系人文件监听器初始化成功")
            
        except Exception as e:
            self.logger.error(f"初始化文件监听器失败: {e}")
            self._sync_trigger = None
    
    def _load_contacts(self):
        """加载联系人数据"""
        try:
            contacts_path = Path(self.contacts_file)
            
            # 确保目录存在
            contacts_path.parent.mkdir(parents=True, exist_ok=True)
            
            # 加载文件
            if contacts_path.exists():
                with open(contacts_path, 'r', encoding='utf-8') as f:
                    contacts_list = json.load(f)
                    
                # 转换为字典格式 (wxid -> contact_info)
                with self._lock:
                    self._contacts_cache = {
                        contact['wxid']: contact 
                        for contact in contacts_list 
                        if 'wxid' in contact
                    }
                    
                self.logger.info(f"加载联系人数据: {len(self._contacts_cache)}个用户")
                
            else:
                # 创建默认文件
                user_name = self.config.get("default_user_name")
                if not user_name:
                    user_name = f"用户_{self.config.get('default_user_id', 'unknown')[-8:] if self.config.get('default_user_id') else 'unknown'}"
                
                default_contacts = [
                    {
                        "nickname": user_name,
                        "sex": "1",
                        "wxid": "default_user"
                    }
                ]
                
                # 🔥 老王修复：使用原子写入避免并发冲突
                from utilities.atomic_file_writer import safe_json_write
                success = safe_json_write(str(contacts_path), default_contacts)
                if not success:
                    self.logger.error(f"保存默认联系人失败: {contacts_path}")
                    raise Exception("保存默认联系人失败")
                
                with self._lock:
                    self._contacts_cache = {
                        contact['wxid']: contact 
                        for contact in default_contacts
                    }
                    
                self.logger.info("创建默认联系人文件")
                
        except Exception as e:
            self.logger.error(f"加载联系人数据失败: {e}")
            with self._lock:
                self._contacts_cache = {}
    
    def _save_contacts(self):
        """保存联系人数据到文件"""
        try:
            contacts_path = Path(self.contacts_file)
            
            with self._lock:
                contacts_list = list(self._contacts_cache.values())
            
            # 原子写入
            temp_file = contacts_path.with_suffix('.tmp')
            with open(temp_file, 'w', encoding='utf-8') as f:
                json.dump(contacts_list, f, ensure_ascii=False, indent=4)
            
            # 原子替换
            temp_file.replace(contacts_path)
            
            self.logger.debug(f"保存联系人数据: {len(contacts_list)}个用户")
            
        except Exception as e:
            self.logger.error(f"保存联系人数据失败: {e}")
    
    def _start_sync_thread(self):
        """启动同步线程"""
        def sync_worker():
            while True:
                try:
                    time.sleep(self.sync_interval)
                    
                    with self._lock:
                        if self._cache_dirty:
                            self._save_contacts()
                            self._cache_dirty = False
                            self._last_sync_time = time.time()
                            
                except Exception as e:
                    self.logger.error(f"同步线程异常: {e}")
        
        sync_thread = threading.Thread(target=sync_worker, daemon=True)
        sync_thread.start()
        self.logger.debug("联系人同步线程已启动")
    
    def validate_user_id(self, user_id: str) -> bool:
        """
        验证用户ID是否存在
        
        Args:
            user_id: 用户ID (wxid)
            
        Returns:
            是否存在
        """
        if not user_id:
            return False
        
        # 先检查缓存
        with self._lock:
            if user_id in self._contacts_cache:
                return True
        
        # 检查Redis缓存
        if self._redis_client:
            cache_key = f"user:{user_id}"
            cached = self._redis_client.get(cache_key)
            if cached:
                return True
        
        return False
    
    def get_user_info(self, user_id: str) -> Optional[Dict[str, Any]]:
        """
        获取用户信息
        
        Args:
            user_id: 用户ID (wxid)
            
        Returns:
            用户信息或None
        """
        if not user_id:
            return None
        
        # 先检查内存缓存
        with self._lock:
            if user_id in self._contacts_cache:
                return self._contacts_cache[user_id].copy()
        
        # 检查Redis缓存
        if self._redis_client:
            cache_key = f"user:{user_id}"
            cached = self._redis_client.get(cache_key)
            if cached:
                # 更新内存缓存
                with self._lock:
                    self._contacts_cache[user_id] = cached
                return cached.copy()
        
        return None
    
    def register_user(self, user_id: str, nickname: str = None, sex: str = "0", **kwargs) -> Dict[str, Any]:
        """
        注册新用户

        Args:
            user_id: 用户ID (wxid)
            nickname: 用户昵称
            sex: 用户性别 (0=未知, 1=男, 2=女)
            **kwargs: 其他用户信息

        Returns:
            用户信息
        """
        if not user_id:
            raise ValueError("用户ID不能为空")

        # 🔥 老王修复：严格遵循API唯一数据源原则
        if not nickname or nickname.strip() == "":
            raise ValueError(f"拒绝注册用户 {user_id}：没有提供用户昵称")

        # 🔥 关键修复：拒绝注册明显的假数据
        if nickname in ["未知用户", "神秘嘉宾", "命令行用户"]:
            raise ValueError(f"拒绝注册用户 {user_id}：昵称 '{nickname}' 不是有效的API数据")

        # 构建用户信息
        user_info = {
            "wxid": user_id,
            "nickname": nickname,  # 🔥 修复：直接使用传入的nickname，不生成默认值
            "sex": str(sex),
            "created_time": time.time(),
            "last_seen": time.time(),
            **kwargs
        }
        
        # 更新缓存
        with self._lock:
            self._contacts_cache[user_id] = user_info
            self._cache_dirty = True

        # 🔥 老王修复：增强保存机制，支持超时重试
        for retry_count in range(3):
            try:
                self._save_contacts()
                self._cache_dirty = False
                self.logger.debug(f"新用户数据已立即保存: {user_id}")
                break
            except Exception as save_error:
                if retry_count < 2:
                    wait_time = 1 * (retry_count + 1)  # 1秒、2秒等
                    self.logger.warning(f"保存新用户数据失败，{wait_time}秒后重试 {retry_count + 1}/3: {save_error}")
                    time.sleep(wait_time)
                else:
                    self.logger.error(f"立即保存新用户数据失败，已重试3次: {save_error}")

        # 更新Redis缓存
        if self._redis_client:
            cache_key = f"user:{user_id}"
            self._redis_client.set(cache_key, user_info, self.cache_ttl)

        # 🔥 老王修复：立即同步到MySQL数据库
        if self._db_sync_manager:
            try:
                sync_success = self._db_sync_manager.sync_user_immediately(user_id, nickname, is_new_user=True)
                if sync_success:
                    self.logger.debug(f"新用户已立即同步到MySQL: {user_id}")
                else:
                    self.logger.warning(f"新用户MySQL同步失败: {user_id}")
            except Exception as db_error:
                self.logger.error(f"新用户MySQL同步异常: {user_id}, 错误: {db_error}")

        self.logger.info(f"注册新用户: {user_id} ({nickname})")
        return user_info.copy()
    
    def update_user(self, user_id: str, **updates) -> Optional[Dict[str, Any]]:
        """
        更新用户信息
        
        Args:
            user_id: 用户ID
            **updates: 更新的字段
            
        Returns:
            更新后的用户信息
        """
        if not user_id:
            return None
        
        with self._lock:
            if user_id not in self._contacts_cache:
                return None

            # 更新信息
            self._contacts_cache[user_id].update(updates)
            self._contacts_cache[user_id]["last_updated"] = time.time()
            self._cache_dirty = True

            user_info = self._contacts_cache[user_id].copy()

                # 🔥 老王修复：增强保存机制，支持超时重试
        for retry_count in range(3):
            try:
                self._save_contacts()
                self._cache_dirty = False
                self.logger.debug(f"用户数据更新已立即保存: {user_id}")
                break
            except Exception as save_error:
                if retry_count < 2:
                    wait_time = 1 * (retry_count + 1)  # 1秒、2秒等
                    self.logger.warning(f"保存用户数据失败，{wait_time}秒后重试 {retry_count + 1}/3: {save_error}")
                    time.sleep(wait_time)
                else:
                    self.logger.error(f"立即保存用户数据更新失败，已重试3次: {save_error}")

        # 更新Redis缓存
        if self._redis_client:
            cache_key = f"user:{user_id}"
            self._redis_client.set(cache_key, user_info, self.cache_ttl)

        # 🔥 老王修复：立即同步到MySQL数据库
        if self._db_sync_manager:
            try:
                sync_success = self._db_sync_manager.sync_user_immediately(user_id, user_info.get('nickname'), is_new_user=False)
                if sync_success:
                    self.logger.debug(f"用户更新已立即同步到MySQL: {user_id}")
                else:
                    self.logger.warning(f"用户更新MySQL同步失败: {user_id}")
            except Exception as db_error:
                self.logger.error(f"用户更新MySQL同步异常: {user_id}, 错误: {db_error}")

        return user_info
    
    def ensure_user_exists(self, user_id: str, nickname: str = None, sex: str = "0", **kwargs) -> Dict[str, Any]:
        """
        确保用户存在，不存在则自动注册

        Args:
            user_id: 用户ID (wxid)
            nickname: 用户昵称
            sex: 用户性别
            **kwargs: 其他用户信息

        Returns:
            用户信息
        """
        # 检查是否已存在
        user_info = self.get_user_info(user_id)
        if user_info:
            # 🔥 老王修复：检查用户信息是否需要更新
            current_nickname = user_info.get("nickname")
            current_sex = user_info.get("sex")

            # 检测信息变更
            nickname_changed = nickname and current_nickname != nickname
            sex_changed = sex and current_sex != str(sex)

            if nickname_changed or sex_changed:
                self.logger.info(f"🔄 检测到用户信息变更，更新contacts.json: {user_id}")
                if nickname_changed:
                    self.logger.info(f"   昵称: {current_nickname} -> {nickname}")
                if sex_changed:
                    self.logger.info(f"   性别: {current_sex} -> {sex}")

                # 更新用户信息
                update_data = {"last_seen": time.time()}
                if nickname:
                    update_data["nickname"] = nickname
                if sex:
                    update_data["sex"] = str(sex)

                self.update_user(user_id, **update_data)

                # 返回更新后的信息
                return self.get_user_info(user_id)
            else:
                # 只更新最后见面时间
                self.update_user(user_id, last_seen=time.time())
                return user_info

        # 🔥 老王修复：严格遵循API唯一数据源原则
        # 如果用户不存在，不能自动注册假数据
        if not nickname or nickname.strip() == "":
            self.logger.error(f"❌ 拒绝自动注册用户 {user_id}：没有提供有效的用户昵称")
            self.logger.error(f"❌ 用户数据的唯一来源只能是API chat，contacts_manager不能自己生成用户数据")
            raise ValueError(f"无法自动注册用户 {user_id}：缺少有效的API数据")

        # 🔥 关键修复：拒绝注册明显的假数据
        if nickname in ["未知用户", "神秘嘉宾", "命令行用户"]:
            self.logger.error(f"❌ 拒绝自动注册用户 {user_id}：昵称 '{nickname}' 不是有效的API数据")
            raise ValueError(f"无法自动注册用户 {user_id}：昵称不是有效的API数据")

        # 自动注册新用户（只有在有有效API数据时）
        return self.register_user(user_id, nickname, sex, **kwargs)
    
    def get_all_users(self) -> List[Dict[str, Any]]:
        """
        获取所有用户信息
        
        Returns:
            用户信息列表
        """
        with self._lock:
            return list(self._contacts_cache.values())
    
    def get_user_count(self) -> int:
        """
        获取用户数量
        
        Returns:
            用户数量
        """
        with self._lock:
            return len(self._contacts_cache)
    
    def force_sync(self):
        """强制同步到文件"""
        with self._lock:
            if self._cache_dirty:
                self._save_contacts()
                self._cache_dirty = False
                self._last_sync_time = time.time()
                self.logger.info("强制同步联系人数据完成")
    
    def get_stats(self) -> Dict[str, Any]:
        """
        获取统计信息
        
        Returns:
            统计信息
        """
        with self._lock:
            stats = {
                "total_users": len(self._contacts_cache),
                "cache_dirty": self._cache_dirty,
                "last_sync_time": self._last_sync_time,
                "redis_available": self._redis_client is not None and self._redis_client.client is not None
            }
        
        return stats


# 全局单例
_contacts_manager_instance = None
_instance_lock = threading.Lock()


def get_contacts_manager(config: Dict[str, Any] = None) -> ContactsManager:
    """
    获取联系人管理器单例

    Args:
        config: 配置参数

    Returns:
        ContactsManager实例
    """
    global _contacts_manager_instance

    with _instance_lock:
        if _contacts_manager_instance is None:
            _contacts_manager_instance = ContactsManager(config)

            # 🔥 老王修复：注册到单例管理器，确保其他模块可以通过get_silent获取
            try:
                from utilities.singleton_manager import register
                register("contacts_manager", _contacts_manager_instance)
            except Exception as e:
                # 如果注册失败，记录警告但不影响正常使用
                _contacts_manager_instance.logger.warning(f"注册到单例管理器失败: {e}")

        return _contacts_manager_instance


def validate_user_id(user_id: str) -> bool:
    """
    验证用户ID是否存在
    
    Args:
        user_id: 用户ID
        
    Returns:
        是否存在
    """
    manager = get_contacts_manager()
    return manager.validate_user_id(user_id)


def ensure_user_exists(user_id: str, nickname: str = None, sex: str = "0", **kwargs) -> Dict[str, Any]:
    """
    确保用户存在，不存在则自动注册
    
    Args:
        user_id: 用户ID (wxid)
        nickname: 用户昵称
        sex: 用户性别
        **kwargs: 其他用户信息
        
    Returns:
        用户信息
    """
    manager = get_contacts_manager()
    return manager.ensure_user_exists(user_id, nickname, sex, **kwargs)


def get_user_info(user_id: str) -> Optional[Dict[str, Any]]:
    """
    获取用户信息
    
    Args:
        user_id: 用户ID
        
    Returns:
        用户信息或None
    """
    manager = get_contacts_manager()
    return manager.get_user_info(user_id)


if __name__ == "__main__":
    # 测试代码
    import asyncio
    
    async def test_contacts_manager():
        print("=== 联系人管理器测试 ===")
        
        # 初始化
        manager = get_contacts_manager()
        print(f"初始用户数: {manager.get_user_count()}")
        
        # 测试用户注册
        user_info = manager.ensure_user_exists("test_user_001", "测试用户1", "1")
        print(f"注册用户: {user_info}")
        
        # 测试用户验证
        exists = manager.validate_user_id("test_user_001")
        print(f"用户存在: {exists}")
        
        # 测试获取用户信息
        info = manager.get_user_info("test_user_001")
        print(f"用户信息: {info}")
        
        # 测试统计信息
        stats = manager.get_stats()
        print(f"统计信息: {stats}")
        
        # 强制同步
        manager.force_sync()
        print("同步完成")
    
    asyncio.run(test_contacts_manager()) 