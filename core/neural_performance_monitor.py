#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
神经网络性能监控系统
🔥 老王新增：监控神经网络的运行性能、资源使用和性能瓶颈
"""

import time
import threading
import psutil
import gc
from typing import Dict, Any, List, Optional
from dataclasses import dataclass, field
from collections import deque
import logging

logger = logging.getLogger("neural_performance_monitor")


@dataclass
class PerformanceMetric:
    """性能指标"""
    timestamp: float
    metric_name: str
    metric_value: float
    metric_type: str  # 'cpu', 'memory', 'processing_time', 'accuracy', 'throughput'
    source: str  # 'basic_neural', 'advanced_neural', 'trigger_system'
    additional_data: Dict[str, Any] = field(default_factory=dict)


class NeuralPerformanceMonitor:
    """神经网络性能监控系统"""
    
    def __init__(self, max_metrics: int = 1000, monitoring_interval: float = 5.0):
        self.max_metrics = max_metrics
        self.monitoring_interval = monitoring_interval
        
        # 性能指标存储
        self.metrics: deque = deque(maxlen=max_metrics)
        self.metrics_by_type: Dict[str, deque] = {
            'cpu': deque(maxlen=200),
            'memory': deque(maxlen=200),
            'processing_time': deque(maxlen=300),
            'accuracy': deque(maxlen=100),
            'throughput': deque(maxlen=100)
        }
        
        # 实时性能状态
        self.current_performance = {
            "cpu_usage": 0.0,
            "memory_usage": 0.0,
            "neural_load": 0.0,
            "processing_efficiency": 0.0,
            "system_health": "good"
        }
        
        # 性能统计
        self.performance_stats = {
            "total_neural_calls": 0,
            "average_processing_time": 0.0,
            "peak_memory_usage": 0.0,
            "peak_cpu_usage": 0.0,
            "efficiency_score": 0.0,
            "uptime": 0.0
        }
        
        # 性能阈值
        self.thresholds = {
            "cpu_warning": 70.0,
            "cpu_critical": 90.0,
            "memory_warning": 80.0,
            "memory_critical": 95.0,
            "processing_time_warning": 0.5,
            "processing_time_critical": 2.0
        }
        
        # 监控状态
        self.monitoring_active = False
        self.monitoring_thread = None
        self.start_time = time.time()
        
        # 线程锁
        self.lock = threading.RLock()
        
        logger.info("🔥 神经网络性能监控系统初始化完成")
    
    def start_monitoring(self):
        """启动性能监控"""
        if self.monitoring_active:
            logger.warning("性能监控已在运行")
            return
        
        self.monitoring_active = True
        self.monitoring_thread = threading.Thread(
            target=self._monitoring_loop,
            daemon=True,
            name="NeuralPerformanceMonitor"
        )
        self.monitoring_thread.start()
        
        logger.info("🚀 神经网络性能监控已启动")
    
    def stop_monitoring(self):
        """停止性能监控"""
        self.monitoring_active = False
        if self.monitoring_thread and self.monitoring_thread.is_alive():
            self.monitoring_thread.join(timeout=5)
        
        logger.info("⏹️ 神经网络性能监控已停止")
    
    def _monitoring_loop(self):
        """监控循环"""
        while self.monitoring_active:
            try:
                self._collect_system_metrics()
                self._update_performance_status()
                self._check_performance_thresholds()
                
                time.sleep(self.monitoring_interval)
                
            except Exception as e:
                logger.error(f"性能监控循环异常: {e}")
                time.sleep(self.monitoring_interval)
    
    def _collect_system_metrics(self):
        """收集系统性能指标"""
        try:
            # CPU使用率
            cpu_percent = psutil.cpu_percent(interval=1)
            self.record_metric("cpu_usage", cpu_percent, "cpu", "system")
            
            # 内存使用率
            memory = psutil.virtual_memory()
            memory_percent = memory.percent
            self.record_metric("memory_usage", memory_percent, "memory", "system")
            
            # 进程内存使用
            process = psutil.Process()
            process_memory = process.memory_info().rss / 1024 / 1024  # MB
            self.record_metric("process_memory", process_memory, "memory", "process")
            
            # 更新当前性能状态
            with self.lock:
                self.current_performance["cpu_usage"] = cpu_percent
                self.current_performance["memory_usage"] = memory_percent
                
        except Exception as e:
            logger.error(f"收集系统指标失败: {e}")
    
    def record_metric(self, metric_name: str, metric_value: float, 
                     metric_type: str, source: str, additional_data: Dict[str, Any] = None):
        """记录性能指标"""
        with self.lock:
            metric = PerformanceMetric(
                timestamp=time.time(),
                metric_name=metric_name,
                metric_value=metric_value,
                metric_type=metric_type,
                source=source,
                additional_data=additional_data or {}
            )
            
            self.metrics.append(metric)
            if metric_type in self.metrics_by_type:
                self.metrics_by_type[metric_type].append(metric)
            
            # 更新统计
            self._update_statistics(metric)
            
            logger.debug(f"📊 记录性能指标: {metric_name}={metric_value:.3f} ({source})")
    
    def record_neural_call(self, source: str, processing_time: float, 
                          success: bool, additional_metrics: Dict[str, Any] = None):
        """记录神经网络调用性能"""
        with self.lock:
            self.performance_stats["total_neural_calls"] += 1
            
            # 记录处理时间
            self.record_metric("processing_time", processing_time, "processing_time", source)
            
            # 记录成功率
            success_value = 1.0 if success else 0.0
            self.record_metric("call_success", success_value, "accuracy", source)
            
            # 记录额外指标
            if additional_metrics:
                for metric_name, metric_value in additional_metrics.items():
                    if isinstance(metric_value, (int, float)):
                        self.record_metric(metric_name, float(metric_value), "custom", source)
            
            # 更新平均处理时间
            total_time = self.performance_stats["average_processing_time"] * (self.performance_stats["total_neural_calls"] - 1)
            self.performance_stats["average_processing_time"] = (total_time + processing_time) / self.performance_stats["total_neural_calls"]
    
    def _update_statistics(self, metric: PerformanceMetric):
        """更新性能统计"""
        if metric.metric_type == "memory" and metric.metric_value > self.performance_stats["peak_memory_usage"]:
            self.performance_stats["peak_memory_usage"] = metric.metric_value
        
        if metric.metric_type == "cpu" and metric.metric_value > self.performance_stats["peak_cpu_usage"]:
            self.performance_stats["peak_cpu_usage"] = metric.metric_value
        
        # 更新运行时间
        self.performance_stats["uptime"] = time.time() - self.start_time
    
    def _update_performance_status(self):
        """更新性能状态"""
        with self.lock:
            # 计算神经网络负载
            recent_calls = [m for m in list(self.metrics)[-50:] if m.metric_type == "processing_time"]
            if recent_calls:
                avg_processing_time = sum(m.metric_value for m in recent_calls) / len(recent_calls)
                self.current_performance["neural_load"] = min(avg_processing_time * 100, 100.0)
            
            # 计算处理效率
            if self.performance_stats["total_neural_calls"] > 0:
                success_metrics = [m for m in list(self.metrics)[-100:] if m.metric_name == "call_success"]
                if success_metrics:
                    success_rate = sum(m.metric_value for m in success_metrics) / len(success_metrics)
                    time_efficiency = 1.0 / (1.0 + self.performance_stats["average_processing_time"])
                    self.current_performance["processing_efficiency"] = (success_rate + time_efficiency) / 2.0 * 100
            
            # 评估系统健康状态
            self.current_performance["system_health"] = self._evaluate_system_health()
    
    def _evaluate_system_health(self) -> str:
        """评估系统健康状态"""
        cpu_usage = self.current_performance["cpu_usage"]
        memory_usage = self.current_performance["memory_usage"]
        neural_load = self.current_performance["neural_load"]
        
        if (cpu_usage > self.thresholds["cpu_critical"] or 
            memory_usage > self.thresholds["memory_critical"] or
            neural_load > 90):
            return "critical"
        elif (cpu_usage > self.thresholds["cpu_warning"] or 
              memory_usage > self.thresholds["memory_warning"] or
              neural_load > 70):
            return "warning"
        else:
            return "good"
    
    def _check_performance_thresholds(self):
        """检查性能阈值"""
        cpu_usage = self.current_performance["cpu_usage"]
        memory_usage = self.current_performance["memory_usage"]
        
        if cpu_usage > self.thresholds["cpu_critical"]:
            logger.warning(f"⚠️ CPU使用率过高: {cpu_usage:.1f}%")
        elif cpu_usage > self.thresholds["cpu_warning"]:
            logger.info(f"💡 CPU使用率较高: {cpu_usage:.1f}%")
        
        if memory_usage > self.thresholds["memory_critical"]:
            logger.warning(f"⚠️ 内存使用率过高: {memory_usage:.1f}%")
            # 触发垃圾回收
            gc.collect()
        elif memory_usage > self.thresholds["memory_warning"]:
            logger.info(f"💡 内存使用率较高: {memory_usage:.1f}%")
    
    def get_performance_report(self) -> Dict[str, Any]:
        """获取性能报告"""
        with self.lock:
            # 计算效率分数
            efficiency_factors = []
            
            if self.current_performance["processing_efficiency"] > 0:
                efficiency_factors.append(self.current_performance["processing_efficiency"] / 100.0)
            
            if self.current_performance["cpu_usage"] < 80:
                efficiency_factors.append(1.0 - self.current_performance["cpu_usage"] / 100.0)
            
            if self.current_performance["memory_usage"] < 80:
                efficiency_factors.append(1.0 - self.current_performance["memory_usage"] / 100.0)
            
            if efficiency_factors:
                self.performance_stats["efficiency_score"] = sum(efficiency_factors) / len(efficiency_factors)
            
            return {
                "current_performance": self.current_performance.copy(),
                "performance_stats": self.performance_stats.copy(),
                "thresholds": self.thresholds.copy(),
                "monitoring_status": {
                    "active": self.monitoring_active,
                    "uptime": time.time() - self.start_time,
                    "total_metrics": len(self.metrics)
                },
                "recommendations": self._generate_performance_recommendations()
            }
    
    def _generate_performance_recommendations(self) -> List[str]:
        """生成性能优化建议"""
        recommendations = []
        
        cpu_usage = self.current_performance["cpu_usage"]
        memory_usage = self.current_performance["memory_usage"]
        neural_load = self.current_performance["neural_load"]
        avg_processing_time = self.performance_stats["average_processing_time"]
        
        if cpu_usage > 80:
            recommendations.append("CPU使用率过高，建议优化神经网络算法或减少并发调用")
        
        if memory_usage > 80:
            recommendations.append("内存使用率过高，建议优化数据结构或增加内存清理")
        
        if neural_load > 80:
            recommendations.append("神经网络负载过高，建议优化网络架构或分批处理")
        
        if avg_processing_time > 0.1:
            recommendations.append("神经网络处理时间较长，建议优化前向传播算法")
        
        if self.performance_stats["efficiency_score"] < 0.7:
            recommendations.append("整体效率偏低，建议全面优化神经网络系统")
        
        if len(recommendations) == 0:
            recommendations.append("性能状态良好，继续保持当前配置")
        
        return recommendations
    
    def get_metrics_by_type(self, metric_type: str, limit: int = 50) -> List[PerformanceMetric]:
        """按类型获取性能指标"""
        with self.lock:
            if metric_type in self.metrics_by_type:
                return list(self.metrics_by_type[metric_type])[-limit:]
            return []
    
    def reset_statistics(self):
        """重置性能统计"""
        with self.lock:
            self.metrics.clear()
            for metric_type in self.metrics_by_type:
                self.metrics_by_type[metric_type].clear()
            
            self.performance_stats = {
                "total_neural_calls": 0,
                "average_processing_time": 0.0,
                "peak_memory_usage": 0.0,
                "peak_cpu_usage": 0.0,
                "efficiency_score": 0.0,
                "uptime": 0.0
            }
            
            self.start_time = time.time()
            logger.info("📊 性能监控统计已重置")


# 全局实例
_performance_monitor = None

def get_performance_monitor() -> NeuralPerformanceMonitor:
    """获取性能监控器实例"""
    global _performance_monitor
    if _performance_monitor is None:
        _performance_monitor = NeuralPerformanceMonitor()
    return _performance_monitor
