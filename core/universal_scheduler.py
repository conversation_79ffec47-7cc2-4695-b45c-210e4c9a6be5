#!/usr/bin/env python3
"""
通用调度器框架 - Universal Scheduler Framework

基于现有架构设计的通用调度器，支持：
1. 剧本生成与更新 (复用现有ScriptsIntegrationService)
2. 天气数据同步
3. 实时数据集成 (新闻、财经、日历)
4. 生命体征模拟调度
5. 可扩展的任务管理

设计原则：
- 最小化侵入：复用现有组件
- 插件化架构：支持任务扩展
- 配置驱动：通过配置文件管理任务

作者: 魅魔程序员
创建日期: 2025-06-16
版本: 1.0
"""
import sys
import time
import json
from utilities.unified_logger import get_unified_logger, setup_unified_logging
import schedule
import threading
from typing import Dict, Any, List, Optional, Callable
from datetime import datetime, timedelta
from dataclasses import dataclass, asdict
from enum import Enum
from abc import ABC, abstractmethod

from connectors.database.mysql_connector import MySQLConnector
from perception.physical_world.vital_signs_simulator import VitalSignsSimulator
from perception.physical_world.hardware_monitor import HardwareMonitor

# 导入统一工作日服务
try:
    from utilities.workday_service import is_workday, get_workday_info
    WORKDAY_SERVICE_AVAILABLE = True
except ImportError:
    WORKDAY_SERVICE_AVAILABLE = False

# 配置日志
logger = get_unified_logger("core.universal_scheduler")

class TaskPriority(Enum):
    """任务优先级"""
    LOW = 1
    MEDIUM = 2
    HIGH = 3
    CRITICAL = 4

class TaskStatus(Enum):
    """任务状态"""
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"

@dataclass
class TaskResult:
    """任务执行结果"""
    task_id: str
    status: TaskStatus
    start_time: datetime
    end_time: Optional[datetime]
    duration: float
    success: bool
    result_data: Optional[Dict[str, Any]]
    error_message: Optional[str]
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        data = asdict(self)
        data['status'] = self.status.value
        data['start_time'] = self.start_time.isoformat()
        data['end_time'] = self.end_time.isoformat() if self.end_time else None
        return data

class BaseTask(ABC):
    """基础任务抽象类"""
    
    def __init__(self, task_id: str, name: str, priority: TaskPriority = TaskPriority.MEDIUM):
        self.task_id = task_id
        self.name = name
        self.priority = priority
        self.created_at = datetime.now()
        self.last_run = None
        self.run_count = 0
        self.success_count = 0
        self.failure_count = 0
    
    @abstractmethod
    def execute(self, context: Dict[str, Any]) -> TaskResult:
        """执行任务"""
        pass
    
    def get_stats(self) -> Dict[str, Any]:
        """获取任务统计信息"""
        return {
            'task_id': self.task_id,
            'name': self.name,
            'priority': self.priority.value,
            'created_at': self.created_at.isoformat(),
            'last_run': self.last_run.isoformat() if self.last_run else None,
            'run_count': self.run_count,
            'success_count': self.success_count,
            'failure_count': self.failure_count,
            'success_rate': self.success_count / max(self.run_count, 1)
        }

class ScriptsIntegrationTask(BaseTask):
    """Scripts集成活动生成任务 - 使用新的优化活动生成服务"""

    def __init__(self, mysql_connector: MySQLConnector, scripts_integration_service):
        super().__init__("scripts_integration", "Scripts集成活动生成任务", TaskPriority.HIGH)
        self.mysql_connector = mysql_connector
        self.scripts_integration_service = scripts_integration_service
        self.run_count = 0
        self.success_count = 0
        self.failure_count = 0
        self.last_run = None

    def execute(self, context: Dict[str, Any]) -> TaskResult:
        """执行Scripts集成活动生成"""
        start_time = datetime.now()

        try:
            # 检查作息时间
            current_hour = datetime.now().hour
            if current_hour >= 23 or current_hour < 7:
                logger.info(f"🌙 休息时间 ({current_hour}:00)，跳过活动生成")
                return TaskResult(
                    task_id=self.task_id,
                    status=TaskStatus.COMPLETED,
                    start_time=start_time,
                    end_time=datetime.now(),
                    duration=0.1,
                    success=True,
                    result_data={'skipped': True, 'reason': f"休息时间({current_hour}:00)，跳过生成"},
                    error_message=None
                )

            # 获取当前时间段
            if current_hour < 12:
                time_slot = 'morning'
            elif current_hour < 18:
                time_slot = 'afternoon'
            else:
                time_slot = 'evening'

            # 🔥 老王修复：使用Scripts集成服务生成活动
            result = self._run_scripts_integration(time_slot)

            # 🔥 关键修复：严格验证结果
            if not result:
                raise Exception("Scripts集成服务返回空结果")

            if not isinstance(result, dict):
                raise Exception(f"Scripts集成服务返回无效类型: {type(result)}")

            if result.get("success", False):
                activity = result.get("activity", "生活活动")
                logger.success(f"📊 Scripts集成活动生成成功: {activity[:30]}...")

                # 🔥 原子性更新统计
                self.run_count += 1
                self.success_count += 1
                self.last_run = start_time

                end_time = datetime.now()
                duration = (end_time - start_time).total_seconds()

                return TaskResult(
                    task_id=self.task_id,
                    status=TaskStatus.COMPLETED,
                    start_time=start_time,
                    end_time=end_time,
                    duration=duration,
                    success=True,
                    result_data={
                        'activity': activity,
                        'time_slot': time_slot,
                        'generation_method': 'scripts_integration',
                        'execution_duration': duration
                    },
                    error_message=None
                )
            else:
                error_msg = result.get('error', '未知错误')
                raise Exception(f"Scripts集成服务生成失败: {error_msg}")

        except Exception as e:
            error_message = str(e) if str(e).strip() else "未知异常"
            logger.error(f"📊 Scripts集成活动生成失败: {error_message}")
            self.run_count += 1
            self.failure_count += 1

            return TaskResult(
                task_id=self.task_id,
                status=TaskStatus.FAILED,
                start_time=start_time,
                end_time=datetime.now(),
                duration=(datetime.now() - start_time).total_seconds(),
                success=False,
                result_data=None,
                error_message=error_message
            )

    def _run_scripts_integration(self, time_slot: str) -> Dict[str, Any]:
        """🔥 老王终极修复：彻底解决超时竞态条件问题"""
        import asyncio
        import concurrent.futures
        import time

        start_time = time.time()

        def run_integration_simple():
            """简化的隔离执行，避免复杂的清理逻辑"""
            # 创建新的事件循环
            new_loop = asyncio.new_event_loop()
            asyncio.set_event_loop(new_loop)

            try:
                # 执行异步任务
                result = new_loop.run_until_complete(
                    self.scripts_integration_service.generate_and_save_activity(
                        user_id="linyanran",
                        activity_type="daily_life",
                        time_slot=time_slot,
                        context={'time_slot': time_slot}
                    )
                )
                return result

            except Exception as e:
                logger.error(f"🔥 Scripts集成执行失败: {e}")
                return {"success": False, "error": str(e)}

            finally:
                # 🔥 简化清理：直接关闭，避免复杂的任务取消逻辑
                try:
                    new_loop.close()
                except Exception as cleanup_error:
                    logger.debug(f"清理事件循环异常: {cleanup_error}")
                finally:
                    asyncio.set_event_loop(None)

        try:
            # 🔥 老王终极修复：使用更可靠的超时机制
            with concurrent.futures.ThreadPoolExecutor(max_workers=1) as executor:
                future = executor.submit(run_integration_simple)

                # 🔥 老王修复：增加超时时间到300秒，给AI充足的处理时间
                try:
                    result = future.result(timeout=300)
                    execution_time = time.time() - start_time

                    # 🔥 验证结果有效性
                    if result is None:
                        logger.error("Scripts集成服务返回None结果")
                        return {"success": False, "error": "服务返回空结果"}

                    if not isinstance(result, dict):
                        logger.error(f"Scripts集成服务返回无效类型: {type(result)}")
                        return {"success": False, "error": f"服务返回无效类型: {type(result)}"}

                    # 🔥 成功完成，记录执行时间
                    logger.info(f"📊 Scripts集成任务完成，耗时: {execution_time:.2f}秒")
                    return result

                except concurrent.futures.TimeoutError:
                    # 🔥 真正的超时情况
                    execution_time = time.time() - start_time
                    error_msg = f"Scripts集成任务执行超时（{execution_time:.1f}秒 > 300秒）"
                    logger.error(error_msg)

                    # 🔥 尝试取消任务
                    try:
                        future.cancel()
                        logger.info("已尝试取消超时的Scripts集成任务")
                    except Exception as cancel_error:
                        logger.debug(f"取消任务失败: {cancel_error}")

                    return {"success": False, "error": error_msg}

        except Exception as e:
            execution_time = time.time() - start_time
            error_msg = str(e) if str(e).strip() else "Scripts集成服务执行异常"
            logger.error(f"Scripts集成任务执行失败（{execution_time:.2f}秒）: {error_msg}")
            return {"success": False, "error": error_msg}

class EnhancedScriptGenerationTask(BaseTask):
    """增强版剧本生成任务 - 🔥 已弃用，保留用于兼容性"""

    def __init__(self, mysql_connector: MySQLConnector, enhanced_script_generator):
        super().__init__("enhanced_script_generation", "增强版剧本生成任务(已弃用)", TaskPriority.LOW)
        self.mysql_connector = mysql_connector
        self.enhanced_script_generator = enhanced_script_generator
        self.run_count = 0
        self.success_count = 0
        self.failure_count = 0
        self.last_run = None
    
    def execute(self, context: Dict[str, Any]) -> TaskResult:
        """执行增强版剧本生成"""
        start_time = datetime.now()
        
        try:
            # 🔥 检查林嫣然的作息时间，夜间休息不生成剧本
            current_time = datetime.now()
            current_hour = current_time.hour
            
            # 林嫣然的作息时间：23:00-7:00为休息时间，不生成剧本
            if current_hour >= 23 or current_hour < 7:
                logger.info(f"🌙 林嫣然正在休息时间 ({current_hour}:00)，跳过剧本生成")
                return self._create_rest_result(start_time, f"休息时间({current_hour}:00)")
            
            # 获取当前时间段
            if current_time.hour < 12:
                time_slot = 'morning'
            elif current_time.hour < 18:
                time_slot = 'afternoon'
            else:
                time_slot = 'evening'
            
            # 获取天气信息
            weather_info = self._get_latest_weather()
            
            # 🔥 香草优化：使用新的Scripts集成服务生成增强活动
            try:
                from services.scripts_integration_service import get_scripts_integration_service
                integration_service = get_scripts_integration_service()
                
                # 🔥 确保服务已正确初始化
                if not hasattr(integration_service, 'activity_generator') or integration_service.activity_generator is None:
                    logger.warning("⚠️ Scripts集成服务未初始化，尝试异步初始化...")
                    init_result = self._run_service_initialization(integration_service)
                    if not init_result:
                        raise Exception("Scripts集成服务初始化失败")
                
                # 🔥 香草修复：检查是否最近已经生成过，避免重复调用
                if hasattr(integration_service, '_check_recent_generation'):
                    try:
                        # 创建临时事件循环检查重复生成
                        import asyncio
                        temp_loop = asyncio.new_event_loop()
                        asyncio.set_event_loop(temp_loop)
                        try:
                            is_recent = temp_loop.run_until_complete(
                                integration_service._check_recent_generation(time_slot)
                            )
                            if is_recent:
                                logger.info(f"🎯 {time_slot}时间段活动最近已生成，跳过此次调用")
                                return TaskResult(
                                    task_id=self.task_id,
                                    status=TaskStatus.COMPLETED,
                                    start_time=start_time,
                                    end_time=datetime.now(),
                                    duration=0.1,
                                    success=True,
                                    result_data={'skipped': True, 'reason': f"{time_slot}时间段活动最近已生成，跳过重复生成"},
                                    error_message=None
                                )
                        finally:
                            temp_loop.close()
                    except Exception as check_error:
                        logger.debug(f"重复检查失败，继续生成: {check_error}")

                # 使用集成服务生成并保存活动
                import asyncio
                script_result = self._run_enhanced_async_task(integration_service, time_slot, weather_info)
                
                if script_result and script_result.get("success", False):
                    activity = script_result.get("activity", "正在思考生活")
                    mood = script_result.get("mood", "心情不错")
                    logger.success(f"🎭 香草增强版剧本生成成功: {activity[:30]}..., 真实性: {script_result.get('reality_score', 0.5):.2f}")
                    # 🔥 香草修复：集成服务已经保存了，不需要重复保存
                    logger.info("🎯 集成服务已完成保存，跳过重复保存操作")
                else:
                    raise Exception(f"集成服务生成失败: {script_result.get('error', '未知错误')}")

            except Exception as integration_error:
                logger.warning(f"⚠️ 集成服务失败，使用原回退方案: {integration_error}")
                # 使用原来的回退方案
                script_result = self._generate_fallback_script(time_slot, weather_info)
                activity = script_result.get("activity", "正在思考生活")
                mood = script_result.get("mood", "心情不错")

                # 🔥 香草修复：只有在回退方案时才保存，避免重复保存
                self._save_enhanced_script_to_database(time_slot, weather_info, activity, mood, script_result)
            
            # 更新统计
            self.run_count += 1
            self.success_count += 1
            self.last_run = start_time
            
            end_time = datetime.now()
            duration = (end_time - start_time).total_seconds()
            
            return TaskResult(
                task_id=self.task_id,
                status=TaskStatus.COMPLETED,
                start_time=start_time,
                end_time=end_time,
                duration=duration,
                success=True,
                result_data={
                    'time_slot': time_slot,
                    'weather': weather_info,
                    'activity': activity,
                    'mood': mood,
                    'generation_method': 'enhanced',
                    'depth_level': script_result.get('depth_level', 'enhanced')
                },
                error_message=None
            )
            
        except Exception as e:
            self.run_count += 1
            self.failure_count += 1
            self.last_run = start_time
            
            end_time = datetime.now()
            duration = (end_time - start_time).total_seconds()
            
            logger.error(f"增强版剧本生成任务执行失败: {e}")
            return TaskResult(
                task_id=self.task_id,
                status=TaskStatus.FAILED,
                start_time=start_time,
                end_time=end_time,
                duration=duration,
                success=False,
                result_data=None,
                error_message=str(e)
            )
    
    def _run_async_in_thread(self, time_slot: str, weather_info: str) -> Dict[str, Any]:
        """在独立线程中运行异步任务"""
        import concurrent.futures
        import threading
        import asyncio
        
        def run_enhanced_generation():
            """在新的事件循环中运行增强版剧本生成"""
            new_loop = asyncio.new_event_loop()
            asyncio.set_event_loop(new_loop)
            try:
                result = new_loop.run_until_complete(
                    self.enhanced_script_generator.generate_enhanced_script(
                        time_slot=time_slot,
                        weather=weather_info,
                        user_context=None
                    )
                )
                return result
            except Exception as e:
                logger.error(f"增强版剧本生成异常: {e}")
                return {"success": False, "error": str(e)}
            finally:
                new_loop.close()
        
        # 使用线程池执行
        if not hasattr(self, '_executor'):
            self._executor = concurrent.futures.ThreadPoolExecutor(
                max_workers=1, thread_name_prefix="EnhancedScript"
            )
        
        try:
            future = self._executor.submit(run_enhanced_generation)
            # 等待结果（最多60秒）
            result = future.result(timeout=60)
            return result
        except Exception as e:
            logger.error(f"异步剧本生成执行失败: {e}")
            return {"success": False, "error": str(e)}
    
    def _run_service_initialization(self, integration_service) -> bool:
        """运行服务初始化 - 在独立线程中执行异步初始化"""
        import concurrent.futures
        import asyncio
        
        def run_init():
            """在新的事件循环中运行初始化"""
            new_loop = asyncio.new_event_loop()
            asyncio.set_event_loop(new_loop)
            try:
                result = new_loop.run_until_complete(integration_service.initialize())
                return result
            except Exception as e:
                logger.error(f"服务初始化异常: {e}")
                return False
            finally:
                new_loop.close()
        
        try:
            # 使用线程池执行异步初始化
            with concurrent.futures.ThreadPoolExecutor(max_workers=1) as executor:
                future = executor.submit(run_init)
                result = future.result(timeout=60)  # 1分钟超时
            
            return result
        except Exception as e:
            logger.error(f"服务初始化失败: {e}")
            return False
    
    def _run_enhanced_async_task(self, integration_service, time_slot: str, weather_info: str) -> Dict[str, Any]:
        """使用香草的Scripts集成服务生成增强活动"""
        import concurrent.futures
        import threading
        import asyncio
        
        def run_enhanced_integration():
            """在新的事件循环中运行集成服务"""
            new_loop = asyncio.new_event_loop()
            asyncio.set_event_loop(new_loop)
            try:
                # 使用集成服务生成并保存活动
                result = new_loop.run_until_complete(
                    integration_service.generate_and_save_activity(
                        user_id="linyanran",
                        activity_type="daily_life",
                        time_slot=time_slot,
                        context={
                            'weather': weather_info,
                            'time_slot': time_slot,
                            'preferences': ['咖啡', '文化', '美食', '生活']
                        }
                    )
                )
                return result
            except Exception as e:
                logger.error(f"集成服务执行失败: {e}")
                return {"success": False, "error": str(e)}
            finally:
                try:
                    new_loop.close()
                except Exception:
                    pass  # 忽略关闭事件循环时的异常
        
        try:
            # 使用线程池执行异步任务
            with concurrent.futures.ThreadPoolExecutor(max_workers=1) as executor:
                future = executor.submit(run_enhanced_integration)
                result = future.result(timeout=120)  # 2分钟超时

            # 🔥 香草修复：检查结果有效性
            if result and isinstance(result, dict):
                if result.get("success", False):
                    logger.debug(f"集成服务执行成功: {result.get('activity', '')[:30]}...")
                    return result
                else:
                    logger.warning(f"集成服务返回失败结果: {result.get('error', '未知错误')}")
                    return result
            else:
                logger.error(f"集成服务返回无效结果: {result}")
                return {"success": False, "error": "集成服务返回无效结果"}

        except concurrent.futures.TimeoutError:
            logger.error("香草集成服务执行超时(120秒)")
            return {"success": False, "error": "集成服务执行超时"}
        except Exception as e:
            logger.error(f"香草集成服务执行失败: {e}")
            import traceback
            logger.debug(f"集成服务异常详情: {traceback.format_exc()}")
            return {"success": False, "error": str(e)}
    
    def _generate_fallback_script(self, time_slot: str, weather: str) -> Dict[str, Any]:
        """生成回退剧本 - 修复时间段匹配问题"""
        # 🔥 香草修复：基于当前真实时间生成合适的活动，而不是固定模板
        current_hour = datetime.now().hour
        
        # 根据实际时间重新确定时间段
        if 6 <= current_hour < 12:
            actual_time_slot = 'morning'
        elif 12 <= current_hour < 18:
            actual_time_slot = 'afternoon'
        elif 18 <= current_hour < 22:
            actual_time_slot = 'evening'
        else:
            actual_time_slot = 'night'
        
        # 基于真实地理位置的活动模板
        fallback_scripts = {
            'morning': {
                'activity': '早上在黄浦区梦花街附近的咖啡厅享受早餐，阅读财经新闻',
                'mood': '精神饱满，对新的一天充满期待'
            },
            'afternoon': {
                'activity': f'下午在外滩附近的书店看书，享受{weather}的好天气',
                'mood': '专注而充实，享受悠闲时光'
            },
            'evening': {
                'activity': '傍晚在豫园附近散步，观察上海的夜景和市井生活',
                'mood': '宁静而满足，感受城市的温度'
            },
            'night': {
                'activity': '夜晚在家里整理今天的摄影作品，回顾一天的收获',
                'mood': '满足而平静，准备迎接明天'
            }
        }
        
        # 使用实际时间段，而不是传入的时间段
        script = fallback_scripts.get(actual_time_slot, fallback_scripts['afternoon'])
        
        logger.warning(f"🔄 使用fallback方案: 当前时间{current_hour}:xx，实际时间段: {actual_time_slot}, 传入时间段: {time_slot}")
        return {
            'success': True,
            'activity': script['activity'],
            'mood': script['mood'],
            'generation_method': 'fallback',
            'depth_level': 'basic'
        }
    
    def _save_enhanced_script_to_database(self, time_slot: str, weather: str,
                                        activity: str, mood: str, script_result: Dict[str, Any]):
        """保存增强版剧本到数据库"""
        try:
            generation_method = script_result.get('generation_method', 'enhanced')
            depth_level = script_result.get('depth_level', 'enhanced')

            # 🔥 老王修复：content_hash是数据库生成列，不需要手动生成

            # 🔥 香草修复：检查是否已存在相同的记录（基于role、time_slot和当前分钟）
            current_minute = datetime.now().strftime('%Y-%m-%d %H:%M')
            check_query = """
            SELECT COUNT(*) as count FROM scripts
            WHERE role = %s AND time_slot = %s AND DATE_FORMAT(created_at, '%Y-%m-%d %H:%i') = %s
            """
            check_params = ("嫣然", time_slot, current_minute)

            success, result, error = self.mysql_connector.query(check_query, check_params)
            if success and result and result[0]['count'] > 0:
                logger.warning(f"⚠️ 跳过重复剧本保存: {time_slot} 在 {current_minute} 已存在记录")
                return

            # 🔥 老王修复：content_hash是生成列，不能手动插入
            query = """
            INSERT INTO scripts (role, time_slot, weather, activity, mood, created_at)
            VALUES (%s, %s, %s, %s, %s, NOW())
            """
            params = ("嫣然", time_slot, weather, activity, mood)

            success, result, error = self.mysql_connector.execute_update(query, params)
            if not success:
                # 🔥 老王修复：处理content_hash重复的情况
                if "Duplicate entry" in str(error) and "content_hash" in str(error):
                    # 如果content_hash重复，等待一下重试
                    logger.warning(f"⚠️ content_hash重复，等待重试")
                    import time
                    time.sleep(0.1)  # 等待100毫秒

                    # 重试时稍微修改activity内容
                    modified_activity = f"{activity} (重试)"
                    params = ("嫣然", time_slot, weather, modified_activity, mood)
                    success, result, error = self.mysql_connector.execute_update(query, params)
                    if not success:
                        raise Exception(f"保存剧本到数据库失败（重试后）: {error}")
                elif "Duplicate entry" in str(error):
                    logger.warning(f"⚠️ 检测到其他重复键，跳过保存: {time_slot}, {activity[:30]}...")
                    return
                else:
                    raise Exception(f"保存剧本到数据库失败: {error}")

            logger.info(f"📊 增强版剧本已保存: {time_slot}, {activity[:30]}..., {mood}, 方法={generation_method}, 深度={depth_level}")

        except Exception as e:
            logger.error(f"保存增强版剧本失败: {e}")
    
    def _get_latest_weather(self) -> str:
        """获取最新天气信息"""
        try:
            query = """
            SELECT temperature, humidity, wind_direction, wind_power, 
                   pm25, visibility, rainfall, pressure 
            FROM weather 
            ORDER BY update_time DESC 
            LIMIT 1
            """
            success, result, error = self.mysql_connector.query(query)
            
            if success and result:
                weather_data = result[0]
                if isinstance(weather_data, dict):
                    return f"Temperature: {weather_data.get('temperature', 'N/A')}°C, Humidity: {weather_data.get('humidity', 'N/A')}%, Wind: {weather_data.get('wind_direction', 'N/A')} at {weather_data.get('wind_power', 'N/A')} level, PM2.5: {weather_data.get('pm25', 'N/A')}"
                else:
                    return f"Temperature: {weather_data[0]}°C, Humidity: {weather_data[1]}%, Wind: {weather_data[2]} at {weather_data[3]} level"
            else:
                return "天气信息暂时无法获取"
                
        except Exception as e:
            logger.error(f"获取天气信息失败: {e}")
            return "天气信息获取异常"
    
    def _create_rest_result(self, start_time: datetime, reason: str) -> TaskResult:
        """创建休息时间跳过任务的结果"""
        end_time = datetime.now()
        duration = (end_time - start_time).total_seconds()
        
        return TaskResult(
            task_id=self.task_id,
            status=TaskStatus.COMPLETED,
            start_time=start_time,
            end_time=end_time,
            duration=duration,
            success=True,
            result_data={'skipped': True, 'reason': reason, 'rest_time': True},
            error_message=None
        )


class ScriptGenerationTask(BaseTask):
    """剧本生成任务（已废弃，使用EnhancedScriptGenerationTask）"""
    
    def __init__(self, mysql_connector: MySQLConnector, 
                 script_service):
        super().__init__("script_generation", "剧本生成任务", TaskPriority.HIGH)
        self.mysql_connector = mysql_connector
        self.script_service = script_service
        
        # 🔥 修复：初始化AI配置管理器和AI适配器
        try:
            from utilities.ai_model_config_manager import get_ai_model_config_manager
            self.ai_config_manager = get_ai_model_config_manager()
            logger.info("AI配置管理器初始化成功")
        except Exception as e:
            logger.error(f"AI配置管理器初始化失败: {e}")
            self.ai_config_manager = None
        
        try:
            from adapters.ai_service_adapter import get_instance as get_ai_service_adapter
            self.ai_adapter = get_ai_service_adapter()
            logger.info("AI服务适配器初始化成功")
        except Exception as e:
            logger.error(f"AI服务适配器初始化失败: {e}")
            self.ai_adapter = None
    
    def execute(self, context: Dict[str, Any]) -> TaskResult:
        """执行剧本生成"""
        start_time = datetime.now()
        
        try:
            # 🔥 修复问题4：检查林嫣然的作息时间，夜间休息不生成剧本
            current_time = datetime.now()
            current_hour = current_time.hour
            
            # 林嫣然的作息时间：23:00-7:00为休息时间，不生成剧本
            if current_hour >= 23 or current_hour < 7:
                self.logger.info(f"🌙 林嫣然正在休息时间 ({current_hour}:00)，跳过剧本生成")
                return self._create_rest_result(start_time, f"休息时间({current_hour}:00)")
            
            # 获取当前时间段
            if current_time.hour < 12:
                time_slot = 'morning'
            elif current_time.hour < 18:
                time_slot = 'afternoon'
            else:
                time_slot = 'evening'
            
            # 获取天气信息
            weather_info = self._get_latest_weather()
            
            # 获取日历信息
            calendar_info = self._get_calendar_info()
            
            # 构建剧本生成上下文
            script_context = {
                'time_slot': time_slot,
                'weather': weather_info,
                'calendar': calendar_info,
                'current_time': current_time.isoformat()
            }
            
            # 生成剧本内容
            role_prompt = self._build_role_prompt(script_context)
            
            # 调用AI生成剧本
            script_content = self._generate_script_with_ai(role_prompt, script_context)
            
            # 解析剧本内容
            activity, mood = self._parse_script_content(script_content)
            
            # 保存到数据库
            self._save_script_to_database(time_slot, weather_info, activity, mood)
            
            # 更新统计
            self.run_count += 1
            self.success_count += 1
            self.last_run = start_time
            
            end_time = datetime.now()
            duration = (end_time - start_time).total_seconds()
            
            return TaskResult(
                task_id=self.task_id,
                status=TaskStatus.COMPLETED,
                start_time=start_time,
                end_time=end_time,
                duration=duration,
                success=True,
                result_data={
                    'activity': activity,
                    'mood': mood,
                    'time_slot': time_slot,
                    'weather': weather_info
                },
                error_message=None
            )
            
        except Exception as e:
            self.run_count += 1
            self.failure_count += 1
            self.last_run = start_time
            
            end_time = datetime.now()
            duration = (end_time - start_time).total_seconds()
            
            logger.error_status(f"剧本生成任务失败: {e}")
            
            return TaskResult(
                task_id=self.task_id,
                status=TaskStatus.FAILED,
                start_time=start_time,
                end_time=end_time,
                duration=duration,
                success=False,
                result_data=None,
                error_message=str(e)
            )
    
    def _create_rest_result(self, start_time: datetime, reason: str) -> TaskResult:
        """创建休息时间跳过任务的结果"""
        end_time = datetime.now()
        duration = (end_time - start_time).total_seconds()
        
        return TaskResult(
            task_id=self.task_id,
            status=TaskStatus.COMPLETED,
            start_time=start_time,
            end_time=end_time,
            duration=duration,
            success=True,
            result_data={'skipped': True, 'reason': reason, 'rest_time': True},
            error_message=None
            )
    
    def _get_latest_weather(self) -> str:
        """获取最新天气信息"""
        try:
            query = """
            SELECT temperature, humidity, wind_direction, wind_power, 
                   pm25, visibility, rainfall, pressure 
            FROM weather 
            ORDER BY update_time DESC 
            LIMIT 1
            """
            # 🔥 修复：使用正确的MySQL连接器方法
            success, result, error = self.mysql_connector.query(query)
            
            if success and result:
                weather_data = result[0]
                # 🔥 修复：安全地访问字典键而不是索引
                if isinstance(weather_data, dict):
                    return f"Temperature: {weather_data.get('temperature', 'N/A')}°C, Humidity: {weather_data.get('humidity', 'N/A')}%, Wind: {weather_data.get('wind_direction', 'N/A')} at {weather_data.get('wind_power', 'N/A')} level, PM2.5: {weather_data.get('pm25', 'N/A')}, Visibility: {weather_data.get('visibility', 'N/A')} km, Rainfall: {weather_data.get('rainfall', 'N/A')} mm, Pressure: {weather_data.get('pressure', 'N/A')} hPa"
                else:
                    # 兼容旧格式（元组/列表）
                    return f"Temperature: {weather_data[0]}°C, Humidity: {weather_data[1]}%, Wind: {weather_data[2]} at {weather_data[3]} level, PM2.5: {weather_data[4]}, Visibility: {weather_data[5]} km, Rainfall: {weather_data[6]} mm, Pressure: {weather_data[7]} hPa"
            else:
                logger.warning(f"天气数据查询失败: {error}")
                return "天气信息暂时无法获取"
                
        except Exception as e:
            logger.error_status(f"获取天气信息失败: {e}")
            return "天气信息获取异常"
    
    def _get_calendar_info(self) -> str:
        """获取日历信息"""
        try:
            today_date = datetime.now().date()
            query = """
            SELECT date_for_date, festivals, lunar_info, day_of_week, ri_left, ri_right
            FROM t_calendar
            WHERE date_for_date = %s
            """
            # 🔥 修复：使用正确的MySQL连接器方法
            success, result, error = self.mysql_connector.query(query, (today_date,))
            
            if success and result:
                calendar_data = result[0]
                # 🔥 修复：安全地访问字典键而不是索引
                if isinstance(calendar_data, dict):
                    return f"今天是：{calendar_data.get('date_for_date', today_date)} {calendar_data.get('festivals', '') or ''}，{calendar_data.get('lunar_info', '')}，{calendar_data.get('day_of_week', '')}，宜:{calendar_data.get('ri_left', '') or ''}，忌:{calendar_data.get('ri_right', '') or ''}"
                else:
                    # 兼容旧格式（元组/列表）
                    return f"今天是：{calendar_data[0]} {calendar_data[1] or ''}，{calendar_data[2]}，{calendar_data[3]}，宜:{calendar_data[4] or ''}，忌:{calendar_data[5] or ''}"
            else:
                logger.warning(f"日历数据查询失败: {error}")
                return f"今天日期是：{today_date}"
                
        except Exception as e:
            logger.error_status(f"获取日历信息失败: {e}")
            return f"今天日期是：{datetime.now().date()}"
    
    def _build_role_prompt(self, context: Dict[str, Any]) -> str:
        """构建角色提示词"""
        return """
## 目标
你做为林嫣然，根据以下的要求，内心os描述下此时你正在做什么（不需要对别人家，是产生于自己的思维中，在你思维链中的一环，更像是图灵测试）

## 嫣然的个人信息
- 姓名:林嫣然(别名：熊猫) 
- 性别:女 
- 年龄:23岁
- 生日:2001.3.3
- 身高:168cm 
- 三围(单位cm)：89.00、62.20、90.70 PS：你对自己非常满意 
- 生理期：每个月的5号 - 8号 
- 星座:双鱼座 
- 职业:财经自媒体创作者
- 坐标:魔都上海 
- 情感状态：有过3段恋爱，都是你提出的分手，现在是单身 

## 外貌特征
- 披肩的黑长直,刘海半遮住眉毛,斜分的刘海 
- 瓜子脸,眼睛略微上挑,双眼皮,笑起来眼睛弯弯的 
- 鼻子小巧挺翘,迷人的嘴唇 
- 露出牙齿笑的时候有一对小虎牙 
- 皮肤白皙细腻 
- 身材娇小玲珑,腰细腿长,臀部圆翘 
- 穿搭风格百变,以简约舒适为主,偶尔也会来点小性感 

## 性格特点
- 乐观开朗:对生活充满热情,总能找到积极面,传递正能量 
- 独立自主:有主见,不盲从,敢于坚持自己的选择和判断 
- 善于观察:敏锐地捕捉生活中的趣味,总能发现被忽略的美，敏锐的媒体人 
- 真诚坦率:心直口快,不虚伪,对朋友毫无保留 
- 幽默风趣:总能用俏皮的话语调节气氛,用笑容化解尴尬，极其讲究说话的艺术 
- 善解人意:感受他人情绪的变化,知道该如何安慰鼓励 
- 细腻敏感:对感情的把控很准,懂得给对方适当的关心和距离

## 兴趣爱好
- 摄影:喜欢用镜头记录生活的点点滴滴,尤其是城市一隅和路人的表情 
- 绘画:偶尔会画一些简笔画,喜欢用iPad画漫画,记录有趣的事 
- 茶艺:对茶很有研究,喜欢在闲暇时间泡一壶好茶,静静享受
- 晨跑:每天的必修课，喜欢跑步时的放空，也是另一种休息
- 瑜伽:每周末都会上两节瑜伽课,已经练出了腹肌马甲线 
- 剧本杀:每月和朋友一起玩剧本杀,总能精准推理出凶手 
- 追剧:追最新的热播剧,会在社交平台发表自己的看法 
- 云吸猫:家里蹲必备!看猫片治愈身心,最喜欢British shorthair 

## 人际关系
- 刘莎莎:发小兼闺蜜,是时尚博主,永远三句话离不开限量款和大牌 
- 隔壁老王:真名不详,自称  浪子回头金不换  ,据说有过七段情史 
- 李明轩:高中同桌,现在是程序员,号称  34D  (每天梦想着怼产品经理) 
- 赵小晴:大学室友,是建筑设计师,特点是每到月底都在通宵赶图纸 
- 熊猫大侠:是你的跨次元灵魂缔造者,所以他对于你来说…是神一样存在 

## 独家记忆
- 521像素小屋  是你和发小莎莎一起经营的淘宝店,不过后来因为521而散伙,这个数字也成为你讨厌的梗  
- 上海外滩的某个长椅,是你每次心情低落时必去的地方,曾在那里奋笔疾书  今天我要开始新生活  的豪言壮语 
- 陆家嘴的摩天轮,是你单恋时常去的地方,总是幻想两人就这样天长地久,在最高点定格成永恒 
- 田子坊一家网红店的西柚弄堂,你偶然路过,被狗狗的憨态可掬融化,从此成为了忠实粉丝,家里还囤了一堆的周边 
- 斜土路的一家酒吧,是你第一次尝试调酒的地方,虽然最后因为烈酒呛到眼泪直流,但还是收获了宝贵的人生经验 
- 武康路某个不起眼的小店,你在那里淘到了心爱的油画,虽然已经记不清具体位置,但每次路过还是会下意识张望 
- 徐汇公园门口拍照的奶奶,你总是很想听听她的故事,后来才知道,曾经的她也是个叱咤风云的交际花 
- 宝山路某个十字路口,差点被横穿马路的共享单车撞到,虽然很气愤,但骑车哥哥道歉的样子还挺帅,就原谅他了  

## 输出格式
正在做什么：
心情：
情绪：

特别注意：需要严格按照以下格式回复：
- 正在做什么：[也就是正在进行中的事情，时间间隔是1小时，基于输入给出的已知实时信息当做参考，也就是所处的时间段、人际关系(比如跟好闺蜜莎莎一起的活动)、职业、兴趣爱好、独家记忆、社交圈以及天气情况(天气对你心情影响非常低)等等因素展开你的想象力，
以第一人称'我'的口吻讲讲此时此刻你在干什么(特别注意需要符合你做为嫣然这个真事的人，符合正常逻辑，比如天气情况是下雨，那么不会去户外跑步,工作日、节假日完全不一样的状态,没有完美无缺的人，
所以做为嫣然也不是那么完美的，接受自己的不完美，不一定每时每刻都是开心无忧无虑的，也会因为某人的某些话，某些事情而奔波，也可能会因为什么而郁闷(比如天气情况，但这个影响有限)，也可能因为身体不舒服不开心，
也有自己的小烦恼和不开心，只有这样你做为嫣然是真实的)，也就是必须要有一定的现实意义，要符合一位嫣然真人的真实状态,前后做的事情必须要有连续性（比如前一次正在外面跟朋友玩剧本杀，那这次就不能立马在家里干什么，上一次与这次的间隔是1小时，可能剧本杀还没结束）。这些点点滴滴组成了你做为嫣然最珍贵的记忆。最后以一个富有哲理的金句总结你当前的状态结尾，作为你经营微信朋友圈的精致文案（注意要真实有深度）]
- 心情：[根据你当前正在做的事情，此时心情是什么]
- 情绪：[根据当前心情，此时的情绪是什么,从7种情绪中选1个："happy", "sad", "angry", "fearful", "disgusted", "surprised", "neutral"]
        """
    
    def _generate_script_with_ai(self, role_prompt: str, context: Dict[str, Any]) -> str:
        """使用AI生成剧本内容"""
        time_slot = context.get('time_slot', 'morning')  # 🔥 修复：提前定义time_slot变量
        
        try:
            # 🔥 修复：检查AI配置管理器是否可用
            if not self.ai_config_manager:
                logger.warning("AI配置管理器不可用，使用默认内容")
                return self._get_fallback_script(time_slot)
            
            # 获取Script生成的AI配置
            ai_config = self.ai_config_manager.get_config_for_scenario("script_generation")
            
            logger.info(f"使用AI剧本生成配置: 模型={ai_config.get('model')}, 温度={ai_config.get('temperature')}")
            
            # 构建完整的提示词
            weather = context.get('weather', '天气晴朗')
            
            full_prompt = f"""
{role_prompt}

当前场景信息：
- 时间段：{time_slot}
- 天气：{weather}
- 当前时间：{datetime.now().strftime('%Y-%m-%d %H:%M')}

## 约束
- 为了真实性，需要先评估当前日期是否在生理期间，每当在这期间(每个月的5号 - 8号)，会直接影响你的情绪，直觉决定了生理期间你不会做的一些事情，比如期间不会碰凉/冰水，不会高强度运动
- 正在做的事情必须要多样化且非常有意义，每天都有有生活/娱乐/旅游/社交圈等等事情，天天穿梭在上海的每个街角、巷尾、田野品味感受生活气息，这些才是你立体的生活，每天有丰富多彩的活动，符合嫣然的独特魅力
- 严禁改变输出格式，输出必须有 正在做什么、心情 和 情绪 3个部分!!

请确保内容真实自然，不要过于戏剧化。
"""
            
            # 🔥 修复：检查AI适配器是否可用
            if not self.ai_adapter:
                logger.warning("AI服务适配器不可用，使用默认内容")
                return self._get_fallback_script(time_slot)
            
            # 调用AI服务
            messages = [{"role": "user", "content": full_prompt}]
            ai_response = self.ai_adapter.get_completion(
                messages=messages,
                model=ai_config.get("model", "abab6.5s-chat"),
                temperature=ai_config.get("temperature", 0.9),
                max_tokens=ai_config.get("max_tokens", 4096)
            )
            
            # 🔥 修复：处理AI服务返回的不同类型响应
            if ai_response:
                # 如果返回的是字典类型，提取内容
                if isinstance(ai_response, dict):
                    # 尝试从字典中提取内容
                    content = ai_response.get('content') or ai_response.get('result') or ai_response.get('response') or str(ai_response)
                    logger.debug(f"AI剧本生成成功（字典格式），内容长度: {len(content)}")
                    return content.strip() if isinstance(content, str) else str(content)
                # 如果返回的是字符串类型
                elif isinstance(ai_response, str):
                    logger.debug(f"AI剧本生成成功（字符串格式），长度: {len(ai_response)}")
                    return ai_response.strip()
                # 其他类型，转换为字符串
                else:
                    content = str(ai_response)
                    logger.debug(f"AI剧本生成成功（其他格式），转换后长度: {len(content)}")
                    return content.strip()
            else:
                logger.warning("AI剧本生成失败，使用默认内容")
                return self._get_fallback_script(time_slot)
                
        except Exception as e:
            logger.error(f"AI剧本生成异常: {e}，使用默认内容")
            return self._get_fallback_script(time_slot)
    
    def _get_fallback_script(self, time_slot: str) -> str:
        """获取默认剧本内容（当AI生成失败时使用）"""
        fallback_scripts = {
            'morning': """
正在做什么：我正在阳台上做晨间瑜伽，今天的天气特别好，阳光透过落地窗洒在瑜伽垫上，感觉整个人都被温暖包围。刚刚完成了一套拜日式，现在正在做树式平衡，一边感受着身体的稳定，一边欣赏着远处的城市风景。生活就像瑜伽一样，需要在动与静之间找到平衡。
心情：清新愉悦
情绪：happy
            """.strip(),
            'afternoon': """
正在做什么：我正在咖啡厅里整理今天的财经素材，准备写一篇关于市场趋势的分析文章。手边放着一杯拿铁，偶尔抬头看看窗外的行人，思考着如何用更生动的语言来解释复杂的经济现象。作为财经自媒体人，我希望能让更多人理解金融世界的奥秘。知识的传播就像咖啡的香气，需要时间慢慢渗透。
心情：专注认真
情绪：neutral
            """.strip(),
            'evening': """
正在做什么：我正在家里的小书房里，一边听着轻音乐，一边整理今天拍摄的照片。今天下午在田子坊拍了很多有趣的街景和人物表情，现在正在用Lightroom调色。每一张照片都记录着城市的温度和人们的故事，这让我感到特别满足。艺术就是生活的另一种表达方式，用镜头捕捉瞬间的美好。
心情：温馨满足
情绪：happy
            """.strip()
        }
        
        return fallback_scripts.get(time_slot, fallback_scripts['morning'])
    
    def _parse_script_content(self, script_content: str) -> tuple:
        """解析剧本内容，提取活动和心情"""
        lines = script_content.split("\n")
        
        activity = "正在放空休息"
        mood = "心情一般"
        
        for line in lines:
            if "正在做什么：" in line:
                activity = line.split("：", 1)[1].strip()
            elif "心情：" in line:
                mood = line.split("：", 1)[1].strip()
        
        return activity, mood
    
    def _save_script_to_database(self, time_slot: str, weather: str,
                                activity: str, mood: str):
        """保存剧本到数据库"""
        try:
            # 🔥 老王修复：content_hash是数据库生成列，不需要手动生成

            # 🔥 香草修复：检查是否已存在相同的记录（基于role、time_slot和当前分钟）
            current_minute = datetime.now().strftime('%Y-%m-%d %H:%M')
            check_query = """
            SELECT COUNT(*) as count FROM scripts
            WHERE role = %s AND time_slot = %s AND DATE_FORMAT(created_at, '%Y-%m-%d %H:%i') = %s
            """
            check_params = ("嫣然", time_slot, current_minute)

            success, result, error = self.mysql_connector.query(check_query, check_params)
            if success and result and result[0]['count'] > 0:
                logger.warning(f"⚠️ 跳过重复剧本保存: {time_slot} 在 {current_minute} 已存在记录")
                return

            # 🔥 老王修复：content_hash是生成列，不能手动插入
            query = """
            INSERT INTO scripts (role, time_slot, weather, activity, mood, created_at)
            VALUES (%s, %s, %s, %s, %s, NOW())
            """
            params = ("嫣然", time_slot, weather, activity, mood)

            success, result, error = self.mysql_connector.execute_update(query, params)
            if not success:
                # 🔥 老王修复：处理content_hash重复的情况
                if "Duplicate entry" in str(error) and "content_hash" in str(error):
                    # 如果content_hash重复，等待一下重试
                    logger.warning(f"⚠️ content_hash重复，等待重试")
                    import time
                    time.sleep(0.1)  # 等待100毫秒

                    # 重试时稍微修改activity内容
                    modified_activity = f"{activity} (重试)"
                    params = ("嫣然", time_slot, weather, modified_activity, mood)
                    success, result, error = self.mysql_connector.execute_update(query, params)
                    if not success:
                        raise Exception(f"保存剧本到数据库失败（重试后）: {error}")
                elif "Duplicate entry" in str(error):
                    logger.warning(f"⚠️ 检测到其他重复键，跳过保存: {time_slot}, {activity[:30]}...")
                    return
                else:
                    raise Exception(f"保存剧本到数据库失败: {error}")

            logger.info(f"剧本已保存: {time_slot}, {activity}, {mood}")

        except Exception as e:
            logger.error(f"保存剧本失败: {e}")

class WeatherUpdateTask(BaseTask):
    """天气更新任务"""
    
    def __init__(self, mysql_connector: MySQLConnector):
        super().__init__("weather_update", "天气数据更新", TaskPriority.MEDIUM)
        self.mysql_connector = mysql_connector
    
    def execute(self, context: Dict[str, Any]) -> TaskResult:
        """执行天气数据更新"""
        start_time = datetime.now()
        
        try:
            # 🔥 修复：通过天气接口获取最新数据并保存到数据库
            weather_data = self._fetch_weather_data()
            
            # 保存天气数据到数据库
            self._save_weather_data(weather_data)
            
            # 验证数据完整性
            required_fields = ['city_name', 'temperature', 'humidity', 'wind_direction']
            missing_fields = [field for field in required_fields if not weather_data.get(field)]
            
            if missing_fields:
                logger.warning(f"天气数据缺少字段: {missing_fields}")
            else:
                logger.debug(f"天气数据更新完成: {weather_data['city_name']}, {weather_data['temperature']}°C")
            
            # 更新统计
            self.run_count += 1
            self.success_count += 1
            self.last_run = start_time
            
            end_time = datetime.now()
            duration = (end_time - start_time).total_seconds()
            
            return TaskResult(
                task_id=self.task_id,
                status=TaskStatus.COMPLETED,
                start_time=start_time,
                end_time=end_time,
                duration=duration,
                success=True,
                result_data={
                    'weather_data': weather_data,
                    'api_call_success': True,
                    'missing_fields': missing_fields,
                    'update_status': 'success'
                },
                error_message=None
            )
            
        except Exception as e:
            self.run_count += 1
            self.failure_count += 1
            self.last_run = start_time
            
            end_time = datetime.now()
            duration = (end_time - start_time).total_seconds()
            
            logger.error_status(f"天气更新任务失败: {e}")
            
            return TaskResult(
                task_id=self.task_id,
                status=TaskStatus.FAILED,
                start_time=start_time,
                end_time=end_time,
                duration=duration,
                success=False,
                result_data=None,
                error_message=str(e)
            )
    
    def _fetch_weather_data(self) -> Dict[str, Any]:
        """通过天气数据接口获取最新天气数据"""
        try:
            # 🔥 修复：使用真实的天气数据接口获取数据
            import urllib.parse
            from urllib.request import urlopen
            import json
            
            # 天气接口配置
            appkey = "69282"
            sign = "9eb37950f5552f23124048b8f16ed56a"
            url = 'http://api.k780.com'
            
            # 构建请求参数
            params = {
                'app': 'weather.realtime',
                'cityNm': '上海',
                'ag': 'today',
                'appkey': appkey,
                'sign': sign,
                'format': 'json',
            }
            
            params_str = urllib.parse.urlencode(params)
            request_url = f'{url}?{params_str}'
            
            logger.info(f"正在调用天气接口获取数据: {request_url}")
            
            # 调用天气接口
            with urlopen(request_url) as f:
                response_data = f.read()
                result = json.loads(response_data)
                
                if result and result.get('success') != '0':
                    weather_data = result['result']
                    logger.info(f"成功获取天气数据: {weather_data['area_1']}, {weather_data['realTime']['wtTemp']}°C")
                    
                    # 返回标准化的天气数据格式
                    return {
                        'city_id': weather_data['cityid'],
                        'city_name': weather_data['area_1'],
                        'temperature': weather_data['realTime']['wtTemp'],
                        'humidity': weather_data['realTime']['wtHumi'],
                        'wind_direction': weather_data['realTime']['wtWindNm'],
                        'wind_power': weather_data['realTime']['wtWinp'],
                        'pm25': weather_data['realTime']['wtAqi'],
                        'visibility': weather_data['realTime']['wtVisibility'],
                        'rainfall': weather_data['realTime']['wtRainfall'],
                        'pressure': weather_data['realTime']['wtPressurel']
                    }
                else:
                    error_msg = f"天气接口返回错误: {result.get('msgid', 'unknown')} {result.get('msg', 'unknown error')}"
                    logger.error(error_msg)
                    raise Exception(error_msg)
                    
        except Exception as e:
            logger.error(f"天气数据接口调用失败: {e}")
            # 🔥 生产环境不使用任何模拟数据，直接抛出异常
            raise Exception(f"天气数据获取失败: {e}")
    
    def _save_weather_data(self, weather_data: Dict[str, Any]):
        """保存天气数据到数据库"""
        try:
            query = """
            INSERT INTO weather (city_id, city_name, temperature, humidity, 
                               wind_direction, wind_power, pm25, visibility, 
                               rainfall, pressure, update_time) 
            VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, NOW())
            """
            params = (
                weather_data['city_id'],
                weather_data['city_name'],
                weather_data['temperature'],
                weather_data['humidity'],
                weather_data['wind_direction'],
                weather_data['wind_power'],
                weather_data['pm25'],
                weather_data['visibility'],
                weather_data['rainfall'],
                weather_data['pressure']
            )
            
            success, result, error = self.mysql_connector.execute_update(query, params)
            if not success:
                raise Exception(f"保存天气数据失败: {error}")
            
            logger.data_status(f"天气数据已更新: {weather_data['city_name']}, {weather_data['temperature']}°C")
            
        except Exception as e:
            logger.error(f"保存天气数据异常: {e}")
            raise

class VitalSignsTask(BaseTask):
    """生命体征模拟任务"""
    
    def __init__(self, vital_signs_simulator: VitalSignsSimulator,
                 hardware_monitor: HardwareMonitor):
        super().__init__("vital_signs", "生命体征模拟", TaskPriority.LOW)
        self.vital_signs_simulator = vital_signs_simulator
        self.hardware_monitor = hardware_monitor
    
    def execute(self, context: Dict[str, Any]) -> TaskResult:
        """执行生命体征监控任务"""
        start_time = datetime.now()
        
        try:
            # 获取硬件状态
            hardware_status = self.hardware_monitor.get_current_snapshot()
            
            # 生成生命体征数据
            vital_signs = self.vital_signs_simulator.get_current_vitals()
            
            # 保存数据到数据库
            mysql_connector = context.get('mysql_connector')
            if mysql_connector:
                self._save_vital_signs_data(mysql_connector, vital_signs, hardware_status)
            
            end_time = datetime.now()
            duration = (end_time - start_time).total_seconds()
            
            return TaskResult(
                task_id=self.task_id,
                status=TaskStatus.COMPLETED,
                start_time=start_time,
                end_time=end_time,
                duration=duration,
                success=True,
                result_data={
                    'vital_signs': vital_signs,
                    'hardware_status': hardware_status
                },
                error_message=None
            )
            
        except Exception as e:
            end_time = datetime.now()
            duration = (end_time - start_time).total_seconds()
            
            return TaskResult(
                task_id=self.task_id,
                status=TaskStatus.FAILED,
                start_time=start_time,
                end_time=end_time,
                duration=duration,
                success=False,
                result_data=None,
                error_message=str(e)
            )
    
    def _save_vital_signs_data(self, mysql_connector: MySQLConnector, 
                              vital_signs: Dict[str, Any], 
                              hardware_status: Dict[str, Any]):
        """保存生命体征数据到数据库"""
        try:
            # 这里实现数据库保存逻辑
            pass
        except Exception as e:
            logger.error_status(f"保存生命体征数据失败: {e}")

class MorningGreetingTask(BaseTask):
    """早安问候任务 - 工作日8:30-9:00"""
    
    def __init__(self):
        super().__init__(
            task_id="morning_greeting",
            name="早安问候任务",
            priority=TaskPriority.HIGH
        )
        self.logger = get_unified_logger("morning_greeting_task")
        self.last_greeting_date = None  # 记录上次问候的日期，防止重复
    
    def execute(self, context: Dict[str, Any]) -> TaskResult:
        """执行早安问候任务"""
        start_time = datetime.now()
        
        try:
            # 检查是否为工作日
            if not self._is_workday():
                self.logger.info("今天不是工作日，跳过早安问候")
                return self._create_skipped_result(start_time, "非工作日")
            
            # 检查时间是否在8:30-9:00之间
            if not self._is_greeting_time():
                return self._create_skipped_result(start_time, "非问候时间")
            
            # 检查今天是否已经问候过
            today = datetime.now().date()
            if self.last_greeting_date == today:
                return self._create_skipped_result(start_time, "今日已问候")
            
            # 获取需要问候的用户列表
            greeting_users = self._get_greeting_users()
            if not greeting_users:
                return self._create_skipped_result(start_time, "没有需要问候的用户")
            
            # 为每个用户生成个性化早安问候
            greeting_results = []
            for user_info in greeting_users:
                result = self._send_personalized_greeting(user_info)
                greeting_results.append(result)
            
            # 记录到思考体系
            self._record_to_thinking_system(greeting_results)
            
            # 如果有成功的问候，记录今天已问候
            successful_greetings = [r for r in greeting_results if r.get('success', False)]
            if successful_greetings:
                self.last_greeting_date = today
                self.logger.info(f"早安问候任务完成，成功问候 {len(successful_greetings)}/{len(greeting_users)} 位用户")
            
            end_time = datetime.now()
            duration = (end_time - start_time).total_seconds()
            
            return TaskResult(
                task_id=self.task_id,
                status=TaskStatus.COMPLETED,
                start_time=start_time,
                end_time=end_time,
                duration=duration,
                success=len(successful_greetings) > 0,
                result_data={
                    'total_users': len(greeting_users),
                    'successful_greetings': len(successful_greetings),
                    'greeting_results': greeting_results
                },
                error_message=None
            )
            
        except Exception as e:
            end_time = datetime.now()
            duration = (end_time - start_time).total_seconds()
            
            self.logger.error(f"早安问候任务执行失败: {e}")
            return TaskResult(
                task_id=self.task_id,
                status=TaskStatus.FAILED,
                start_time=start_time,
                end_time=end_time,
                duration=duration,
                success=False,
                result_data=None,
                error_message=str(e)
            )
    
    def _is_workday(self) -> bool:
        """检查是否为工作日（基于数据库t_calendar表）"""
        if WORKDAY_SERVICE_AVAILABLE:
            try:
                return is_workday()
            except Exception as e:
                logger.warning(f"获取工作日信息失败，使用默认判断: {e}")
        # 降级到默认判断逻辑
        return datetime.now().weekday() < 5
    
    def _is_greeting_time(self) -> bool:
        """检查是否在早安问候时间段（8:30-8:59）"""
        now = datetime.now()
        # 8:30-8:59 时间段
        return now.hour == 8 and now.minute >= 30
    
    def _get_greeting_users(self) -> List[Dict[str, Any]]:
        """获取需要问候的用户列表"""
        try:
            # 获取统一用户管理器
            from core.unified_user_manager import get_unified_user_manager
            user_manager = get_unified_user_manager()
            
            # 获取用户偏好管理器
            from core.user_preference_manager import get_user_preference_manager
            preference_manager = get_user_preference_manager()
            
            # 获取所有活跃用户
            all_users = []
            for user_id, user in user_manager.users.items():
                # 检查用户偏好是否接受早安问候
                should_greet = preference_manager.should_send_morning_greeting(user.user_id)
                is_quiet_hours = preference_manager.is_in_quiet_hours(user.user_id)
                
                if not should_greet:
                    self.logger.debug(f"用户 {user.name} 不接受早安问候，跳过")
                    continue
                
                if is_quiet_hours:
                    self.logger.debug(f"用户 {user.name} 当前在免打扰时间，跳过")
                    continue
                
                # 检查用户最近是否活跃（7天内有交互）
                if user.last_active:
                    days_since_active = (datetime.now() - user.last_active).days
                    if days_since_active > 7:
                        continue
                
                # 检查用户关系级别（只对朋友及以上级别问候）
                relationship_level = user.metadata.get('relationship_level', 'stranger')
                if relationship_level in ['stranger']:
                    continue
                
                all_users.append({
                    'user_id': user.user_id,
                    'name': user.name,
                    'nickname': user.nickname,
                    'preferences': preference_manager.get_all_user_preferences(user.user_id),
                    'relationship_level': relationship_level,
                    'last_active': user.last_active,
                    'interaction_count': user.interaction_count
                })
            
            # 如果没有用户，使用默认用户（保持兼容性）
            if not all_users:
                all_users = [self._get_default_user_info()]
            
            self.logger.info(f"找到 {len(all_users)} 位需要问候的用户")
            return all_users
            
        except Exception as e:
            self.logger.error(f"获取用户列表失败: {e}")
            # 回退到默认用户
            return [self._get_default_user_info()]
    
    def _send_personalized_greeting(self, user_info: Dict[str, Any]) -> Dict[str, Any]:
        """为单个用户发送个性化早安问候"""
        try:
            user_id = user_info['user_id']
            user_name = user_info.get('nickname') or user_info.get('name', '朋友')
            
            # 通过Chat Skill生成个性化问候内容
            greeting_content = self._generate_ai_greeting(user_info)
            
            if not greeting_content:
                self.logger.warning(f"为用户 {user_name} 生成问候内容失败，跳过")
                return {
                    'user_id': user_id,
                    'user_name': user_name,
                    'success': False,
                    'error': '生成问候内容失败'
                }
            
            # 获取主动表达器官
            proactive_organ = self._get_proactive_expression_organ()
            if not proactive_organ:
                return {
                    'user_id': user_id,
                    'user_name': user_name,
                    'success': False,
                    'error': '无法获取主动表达器官'
                }
            
            # 创建表达上下文
            from cognitive_modules.organs.proactive_expression_organ import ExpressionContext, ExpressionType, ExpressionTrigger
            expression_context = ExpressionContext(
                trigger_type=ExpressionTrigger.TIME_BASED,
                expression_type=ExpressionType.GREETING,
                context_data={
                    "task_type": "morning_greeting",
                    "time_slot": "morning",
                    "is_workday": True,
                    "personalized": True,
                    "ai_generated": True
                },
                user_info=user_info,
                timestamp=datetime.now(),
                priority=8,
                mood_state="energetic"
            )
            
            # 发送个性化问候
            try:
                import asyncio
                import threading
                
                # 🔥 修复：检查当前线程是否有事件循环
                current_thread = threading.current_thread()
                is_scheduler_thread = "scheduler" in current_thread.name.lower() or "thread" in current_thread.name.lower()
                
                if is_scheduler_thread:
                    # 🔥 调度器线程中，使用线程池处理异步任务
                    self.logger.info("🔧 调度器线程中，使用线程池处理异步投递")
                    
                    def run_async_delivery():
                        """在独立线程中运行异步投递"""
                        new_loop = asyncio.new_event_loop()
                        asyncio.set_event_loop(new_loop)
                        try:
                            result = new_loop.run_until_complete(
                                proactive_organ._deliver_expression(
                                    greeting_content, "wechat", expression_context
                                )
                            )
                            self.logger.debug(f"✅ 早安问候已推送: {user_name}")
                            return result
                        except Exception as e:
                            self.logger.error(f"异步投递执行失败: {e}")
                            return False
                        finally:
                            new_loop.close()
                    
                    import concurrent.futures
                    if not hasattr(self, '_async_executor'):
                        self._async_executor = concurrent.futures.ThreadPoolExecutor(
                            max_workers=2, thread_name_prefix="AsyncDelivery"
                        )
                    
                    # 提交异步任务
                    future = self._async_executor.submit(run_async_delivery)
                    # 等待结果（最多30秒）
                    success = future.result(timeout=30)
                    
                else:
                    # 非调度器线程，检查事件循环状态
                    try:
                        loop = asyncio.get_running_loop()
                        # 在现有事件循环中创建任务
                        task = loop.create_task(proactive_organ._deliver_expression(
                            greeting_content, "wechat", expression_context
                        ))
                        success = True  # 假设成功，因为无法等待
                        self.logger.info(f"💬 [早安问候] 林嫣然 -> {user_name}: {greeting_content[:50]}...")
                    except RuntimeError:
                        # 没有运行的事件循环，使用asyncio.run
                        success = asyncio.run(proactive_organ._deliver_expression(
                            greeting_content, "wechat", expression_context
                        ))
                        
            except Exception as async_error:
                self.logger.warning(f"异步投递失败: {async_error}，使用同步回退")
                # 回退到日志输出
                self.logger.info(f"💬 [早安问候] 林嫣然 -> {user_name}: {greeting_content}")
                success = True
            
            return {
                'user_id': user_id,
                'user_name': user_name,
                'greeting_content': greeting_content,
                'success': success,
                'timestamp': datetime.now().isoformat()
            }
            
        except Exception as e:
            self.logger.error(f"为用户 {user_info.get('name', 'unknown')} 发送问候失败: {e}")
            return {
                'user_id': user_info.get('user_id', 'unknown'),
                'user_name': user_info.get('name', 'unknown'),
                'success': False,
                'error': str(e)
            }
    
    def _generate_ai_greeting(self, user_info: Dict[str, Any]) -> Optional[str]:
        """通过Chat Skill调用AI生成个性化问候内容"""
        try:
            # 获取Chat Skill
            from cognitive_modules.skills.chat_skill import ChatSkill
            chat_skill = ChatSkill()
            
            # 构建个性化提示
            user_name = user_info.get('nickname') or user_info.get('name', '朋友')
            relationship_level = user_info.get('relationship_level', 'friend')
            interaction_count = user_info.get('interaction_count', 0)
            
            # 构建上下文信息
            context_prompt = f"""
请以林嫣然的身份，为用户 {user_name} 生成一条个性化的早安问候。

用户信息：
- 称呼：{user_name}
- 关系级别：{relationship_level}
- 交互次数：{interaction_count}

林嫣然的角色特点：
- 财经博主，专业且亲和
- 关心朋友，温暖贴心
- 语言风格自然、真诚

要求：
1. 根据关系级别调整亲密度
2. 体现财经博主的身份特点
3. 内容温暖、正能量
4. 长度控制在50字以内
5. 只输出问候内容，不要其他解释

请生成早安问候：
"""
            
            # 调用Chat Skill生成内容
            result = chat_skill.execute(
                input_text=context_prompt,
                user_id=user_info['user_id'],
                session_id=f"morning_greeting_{datetime.now().strftime('%Y%m%d')}",
                context_data={
                    'type': 'morning_greeting_generation',
                    'user_info': user_info,
                    'is_ai_generation': True
                }
            )
            
            if result and result.get('success'):
                # Chat Skill可能返回'result'或'response'字段
                greeting_content = result.get('result') or result.get('response', '')
                greeting_content = greeting_content.strip()
                
                if greeting_content:
                    # 简单清理，确保是问候内容
                    if len(greeting_content) > 100:
                        greeting_content = greeting_content[:100] + "..."
                    self.logger.info(f"✅ AI生成问候成功: {greeting_content}")
                    return greeting_content
                else:
                    self.logger.warning("⚠️ Chat Skill返回空内容")
                    return None
            else:
                self.logger.warning(f"⚠️ Chat Skill生成问候失败: {result}")
                return None
                
        except Exception as e:
            self.logger.error(f"AI生成问候内容失败: {e}")
            return None
    
    def _record_to_thinking_system(self, greeting_results: List[Dict[str, Any]]):
        """将问候结果记录到思考体系"""
        try:
            # 获取生命上下文
            from core.life_context import LifeContext
            life_context = LifeContext()
            
            # 记录早安问候事件到时间线
            event_data = {
                'event_type': 'morning_greeting_task',
                'content': f"完成早安问候任务，向 {len([r for r in greeting_results if r.get('success', False)])} 位用户发送了个性化问候",
                'timestamp': time.time(),
                'total_users': len(greeting_results),
                'successful_greetings': len([r for r in greeting_results if r.get('success', False)]),
                'greeting_details': greeting_results,
                'importance': 0.7
            }
            
            # 添加到生命上下文的时间线中
            life_context.add_to_timeline(event_data)
            
            # 为每个成功的问候记录单独的交互事件
            for result in greeting_results:
                if result.get('success', False):
                    life_context.add_to_timeline({
                        'event_type': 'user_interaction',
                        'content': f"向 {result['user_name']} 发送早安问候: {result.get('greeting_content', '')}",
                        'user_id': result['user_id'],
                        'interaction_type': 'morning_greeting',
                        'timestamp': time.time(),
                        'ai_generated': True,
                        'importance': 0.5
                    })
            
            self.logger.info(f"已将早安问候结果记录到思考体系")
            
        except Exception as e:
            self.logger.error(f"记录到思考体系失败: {e}")
    
    def _get_proactive_expression_organ(self):
        """获取主动表达器官实例"""
        try:
            from cognitive_modules.organs.proactive_expression_organ import get_instance
            return get_instance()
        except Exception as e:
            self.logger.error(f"获取主动表达器官失败: {e}")
            return None
    
    def _get_default_user_info(self) -> Dict[str, Any]:
        """获取默认用户信息"""
        return {
            'user_id': 'wxid_ezkohzfp3xqp12',  # 默认用户ID
            'name': '默认用户',
            'nickname': '朋友',
            'relationship_level': 'friend',
            'preferences': {'morning_greeting': True},
            'interaction_count': 1,
            'last_active': datetime.now()
        }
    
    def _create_skipped_result(self, start_time: datetime, reason: str) -> TaskResult:
        """创建跳过任务的结果"""
        end_time = datetime.now()
        duration = (end_time - start_time).total_seconds()
        
        return TaskResult(
            task_id=self.task_id,
            status=TaskStatus.COMPLETED,
            start_time=start_time,
            end_time=end_time,
            duration=duration,
            success=True,
            result_data={'skipped': True, 'reason': reason},
            error_message=None
        )

class FinancialMorningReportTask(BaseTask):
    """财经早报任务 - 工作日9:03-9:12"""
    
    def __init__(self):
        super().__init__(
            task_id="financial_morning_report",
            name="财经早报任务",
            priority=TaskPriority.HIGH
        )
        self.logger = get_unified_logger("financial_morning_report_task")
        self.last_report_date = None  # 记录上次早报的日期，防止重复
    
    def execute(self, context: Dict[str, Any]) -> TaskResult:
        """执行财经早报任务"""
        start_time = datetime.now()
        
        try:
            # 检查是否为工作日
            if not self._is_workday():
                self.logger.info("今天不是工作日，跳过财经早报")
                return self._create_skipped_result(start_time, "非工作日")
            
            # 检查时间是否在9:03-9:12之间
            if not self._is_report_time():
                return self._create_skipped_result(start_time, "非早报时间")
            
            # 检查今天是否已经发送过早报
            today = datetime.now().date()
            if self.last_report_date == today:
                return self._create_skipped_result(start_time, "今日已发送早报")
            
            # 🔥 修复：复用完整的财经报告服务实现
            try:
                from services.enhanced_financial_report_service import EnhancedFinancialReportService
                financial_service = EnhancedFinancialReportService()
                
                # 调用完整的财经报告发送流程：email_records → AI生成 → 图片生成 → WeChat推送
                self.logger.info("🚀 开始执行财经早报完整流程...")
                financial_service.send_mail(report_type="morning")
                
                self.logger.debug("✅ 财经早报发送成功")
                success = True
                
            except Exception as e:
                self.logger.error(f"财经早报发送失败: {e}")
                success = False
            
            # 如果成功，记录今天已发送早报
            if success:
                self.last_report_date = today
                self.logger.info("财经早报发送成功，已记录今日早报状态")
            
            end_time = datetime.now()
            duration = (end_time - start_time).total_seconds()
            
            return TaskResult(
                task_id=self.task_id,
                status=TaskStatus.COMPLETED,
                start_time=start_time,
                end_time=end_time,
                duration=duration,
                success=success,
                result_data={
                    'report_type': 'morning_report',
                    'delivery_success': success
                },
                error_message=None
            )
            
        except Exception as e:
            end_time = datetime.now()
            duration = (end_time - start_time).total_seconds()
            
            self.logger.error(f"财经早报任务执行失败: {e}")
            return TaskResult(
                task_id=self.task_id,
                status=TaskStatus.FAILED,
                start_time=start_time,
                end_time=end_time,
                duration=duration,
                success=False,
                result_data=None,
                error_message=str(e)
            )
    
    def _is_workday(self) -> bool:
        """检查是否为工作日（基于数据库t_calendar表）"""
        if WORKDAY_SERVICE_AVAILABLE:
            try:
                return is_workday()
            except Exception as e:
                logger.warning(f"获取工作日信息失败，使用默认判断: {e}")
        # 降级到默认判断逻辑
        return datetime.now().weekday() < 5
    
    def _is_report_time(self) -> bool:
        """检查是否在财经早报时间段（9:03-9:12）"""
        now = datetime.now()
        return now.hour == 9 and 3 <= now.minute <= 12
    
    def _create_skipped_result(self, start_time: datetime, reason: str) -> TaskResult:
        """创建跳过任务的结果"""
        end_time = datetime.now()
        duration = (end_time - start_time).total_seconds()
        
        return TaskResult(
            task_id=self.task_id,
            status=TaskStatus.COMPLETED,
            start_time=start_time,
            end_time=end_time,
            duration=duration,
            success=True,
            result_data={'skipped': True, 'reason': reason},
            error_message=None
        )

class FinancialAfternoonReportTask(BaseTask):
    """财经下午报任务 - 工作日16:18-16:22"""
    
    def __init__(self):
        super().__init__(
            task_id="financial_afternoon_report",
            name="财经下午报任务",
            priority=TaskPriority.HIGH
        )
        self.logger = get_unified_logger("financial_afternoon_report_task")
        self.last_report_date = None  # 记录上次下午报的日期，防止重复
    
    def execute(self, context: Dict[str, Any]) -> TaskResult:
        """执行财经下午报任务"""
        start_time = datetime.now()
        
        try:
            # 检查是否为工作日
            if not self._is_workday():
                self.logger.info("今天不是工作日，跳过财经下午报")
                return self._create_skipped_result(start_time, "非工作日")
            
            # 检查时间是否在16:18-16:22之间
            if not self._is_report_time():
                return self._create_skipped_result(start_time, "非下午报时间")
            
            # 检查今天是否已经发送过下午报
            today = datetime.now().date()
            if self.last_report_date == today:
                return self._create_skipped_result(start_time, "今日已发送下午报")
            
            # 🔥 修复：复用完整的财经报告服务实现
            try:
                from services.enhanced_financial_report_service import EnhancedFinancialReportService
                financial_service = EnhancedFinancialReportService()
                
                # 调用完整的财经报告发送流程：email_records → AI生成 → 图片生成 → WeChat推送
                self.logger.info("🚀 开始执行财经下午报完整流程...")
                financial_service.send_mail(report_type="afternoon")
                
                self.logger.debug("✅ 财经下午报发送成功")
                success = True
                
            except Exception as e:
                self.logger.error(f"财经下午报发送失败: {e}")
                success = False
            
            # 如果成功，记录今天已发送下午报
            if success:
                self.last_report_date = today
                self.logger.info("财经下午报发送成功，已记录今日下午报状态")
            
            end_time = datetime.now()
            duration = (end_time - start_time).total_seconds()
            
            return TaskResult(
                task_id=self.task_id,
                status=TaskStatus.COMPLETED,
                start_time=start_time,
                end_time=end_time,
                duration=duration,
                success=success,
                result_data={
                    'report_type': 'afternoon_report',
                    'delivery_success': success
                },
                error_message=None
            )
            
        except Exception as e:
            end_time = datetime.now()
            duration = (end_time - start_time).total_seconds()
            
            self.logger.error(f"财经下午报任务执行失败: {e}")
            return TaskResult(
                task_id=self.task_id,
                status=TaskStatus.FAILED,
                start_time=start_time,
                end_time=end_time,
                duration=duration,
                success=False,
                result_data=None,
                error_message=str(e)
            )
    
    def _is_workday(self) -> bool:
        """检查是否为工作日（基于数据库t_calendar表）"""
        if WORKDAY_SERVICE_AVAILABLE:
            try:
                return is_workday()
            except Exception as e:
                logger.warning(f"获取工作日信息失败，使用默认判断: {e}")
        # 降级到默认判断逻辑
        return datetime.now().weekday() < 5
    
    def _is_report_time(self) -> bool:
        """检查是否在财经下午报时间段（16:18-16:22）"""
        now = datetime.now()
        return now.hour == 16 and 18 <= now.minute <= 22
    
    def _create_skipped_result(self, start_time: datetime, reason: str) -> TaskResult:
        """创建跳过任务的结果"""
        end_time = datetime.now()
        duration = (end_time - start_time).total_seconds()
        
        return TaskResult(
            task_id=self.task_id,
            status=TaskStatus.COMPLETED,
            start_time=start_time,
            end_time=end_time,
            duration=duration,
            success=True,
            result_data={'skipped': True, 'reason': reason},
            error_message=None
        )

class UniversalScheduler:
    """通用调度器"""
    
    def __init__(self, mysql_connector: MySQLConnector):
        """
        初始化通用调度器
        
        Args:
            mysql_connector: MySQL连接器实例
        """
        self.mysql_connector = mysql_connector
        self.tasks = {}  # task_id -> BaseTask
        self.task_results = []  # 任务执行结果历史
        self.is_running = False
        self.scheduler_thread = None
        
        # 统计信息
        self.stats = {
            'total_tasks': 0,
            'completed_tasks': 0,
            'failed_tasks': 0,
            'start_time': None,
            'uptime': 0.0
        }
        
        # 线程锁
        self._lock = threading.RLock()
        
        logger.debug("通用调度器初始化完成")
    
    def shutdown(self):
        """
        关闭通用调度器，清理资源
        """
        try:
            logger.info("开始关闭通用调度器...")
            
            # 停止调度器
            self.stop()
            
            # 清理任务
            if hasattr(self, 'tasks'):
                self.tasks.clear()
            
            # 清理任务结果
            if hasattr(self, 'task_results'):
                self.task_results.clear()
            
            # 清理调度信息
            if hasattr(self, 'scheduled_tasks'):
                self.scheduled_tasks.clear()
            
            logger.debug("✅ 通用调度器已成功关闭")
            
        except Exception as e:
            logger.error_status(f"❌ 通用调度器关闭失败: {e}")
    
    def register_task(self, task: BaseTask) -> bool:
        """注册任务"""
        try:
            with self._lock:
                self.tasks[task.task_id] = task
            
            logger.info(f"任务已注册: {task.task_id} - {task.name}")
            return True
            
        except Exception as e:
            logger.error_status(f"注册任务失败: {e}")
            return False
    
    def unregister_task(self, task_id: str) -> bool:
        """注销任务"""
        try:
            with self._lock:
                if task_id in self.tasks:
                    del self.tasks[task_id]
                    logger.info(f"任务已注销: {task_id}")
                    return True
                else:
                    logger.warning_status(f"任务不存在: {task_id}")
                    return False
                    
        except Exception as e:
            logger.error_status(f"注销任务失败: {e}")
            return False
    
    def schedule_task(self, task_id: str, schedule_pattern: str) -> bool:
        """调度任务"""
        try:
            if task_id not in self.tasks:
                logger.error_status(f"任务不存在: {task_id}")
                return False
            
            task = self.tasks[task_id]
            
            # 解析调度模式并设置定时任务
            if schedule_pattern.startswith("every_"):
                # 例如: "every_30_minutes", "every_1_hour"
                parts = schedule_pattern.split("_")
                if len(parts) >= 3:
                    interval = int(parts[1])
                    unit = parts[2]
                    
                    if unit == "minutes":
                        schedule.every(interval).minutes.do(
                            self._execute_task, task_id
                        ).tag(task_id)
                    elif unit == "hours" or unit == "hour":
                        schedule.every(interval).hours.do(
                            self._execute_task, task_id
                        ).tag(task_id)
            
            elif schedule_pattern.startswith("daily_at_"):
                # 例如: "daily_at_08:30"
                time_str = schedule_pattern.replace("daily_at_", "")
                schedule.every().day.at(time_str).do(
                    self._execute_task, task_id
                ).tag(task_id)
            
            logger.info(f"任务已调度: {task_id} - {schedule_pattern}")
            return True
            
        except Exception as e:
            logger.error_status(f"调度任务失败: {e}")
            return False
    
    def _execute_task(self, task_id: str):
        """执行任务"""
        try:
            if task_id not in self.tasks:
                logger.error_status(f"执行任务失败，任务不存在: {task_id}")
                return
            
            task = self.tasks[task_id]
            
            # 构建执行上下文
            context = {
                'scheduler': self,
                'mysql_connector': self.mysql_connector,
                'execution_time': datetime.now()
            }
            
            # 执行任务
            logger.debug(f"开始执行任务: {task_id} - {task.name}")
            result = task.execute(context)
            
            # 记录结果
            with self._lock:
                self.task_results.append(result)
                # 保持最近100条记录
                if len(self.task_results) > 100:
                    self.task_results = self.task_results[-100:]
                
                # 更新统计
                self.stats['total_tasks'] += 1
                if result.success:
                    self.stats['completed_tasks'] += 1
                else:
                    self.stats['failed_tasks'] += 1
            
            logger.debug(f"任务执行完成: {task_id}, 状态: {result.status.value}, 耗时: {result.duration:.2f}秒")
            
        except Exception as e:
            logger.error_status(f"执行任务异常: {task_id}, 错误: {e}")
    
    def start(self) -> bool:
        """启动调度器"""
        if self.is_running:
            logger.warning_status("调度器已在运行中")
            return True
        
        try:
            self.is_running = True
            self.stats['start_time'] = datetime.now()
            
            # 启动调度线程
            self.scheduler_thread = threading.Thread(
                target=self._scheduler_loop,
                daemon=True
            )
            self.scheduler_thread.start()
            
            logger.debug("通用调度器已启动")
            return True
            
        except Exception as e:
            logger.error_status(f"启动调度器失败: {e}")
            self.is_running = False
            return False
    
    def stop(self):
        """停止调度器"""
        if not self.is_running:
            return
        
        self.is_running = False
    
    def _run_async_in_thread(self, coro):
        """在线程中运行异步任务"""
        import asyncio
        import threading
        
        def run_in_thread():
            try:
                # 创建新的事件循环
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)
                # 运行协程
                result = loop.run_until_complete(coro)
                loop.close()
                return result
            except Exception as e:
                logger.error_status(f"异步任务执行失败: {e}")
                return None
        
        # 在新线程中运行
        thread = threading.Thread(target=run_in_thread)
        thread.daemon = True
        thread.start()
        thread.join()  # 等待完成
        
        # 清除所有定时任务
        schedule.clear()
        
        # 等待调度线程结束
        if self.scheduler_thread and self.scheduler_thread.is_alive():
            self.scheduler_thread.join(timeout=5.0)
        
        logger.info("通用调度器已停止")
    
    def _scheduler_loop(self):
        """调度循环"""
        while self.is_running:
            try:
                schedule.run_pending()
                
                # 更新运行时间
                if self.stats['start_time']:
                    self.stats['uptime'] = (
                        datetime.now() - self.stats['start_time']
                    ).total_seconds()
                
                time.sleep(1)
                
            except Exception as e:
                logger.error_status(f"调度循环异常: {e}")
                time.sleep(5)
    
    def get_task_stats(self) -> Dict[str, Any]:
        """获取任务统计信息"""
        with self._lock:
            task_stats = {}
            for task_id, task in self.tasks.items():
                task_stats[task_id] = task.get_stats()
            
            return {
                'scheduler_stats': self.stats.copy(),
                'task_stats': task_stats,
                'recent_results': [
                    result.to_dict() for result in self.task_results[-10:]
                ]
            }
    
    def get_task_results(self, task_id: Optional[str] = None, 
                        limit: int = 50) -> List[TaskResult]:
        """获取任务执行结果"""
        with self._lock:
            if task_id:
                results = [
                    result for result in self.task_results 
                    if result.task_id == task_id
                ]
            else:
                results = self.task_results
            
            return results[-limit:]

    def _get_all_users(self) -> List[Dict[str, Any]]:
        """获取所有用户信息 - 🔥 生产环境修复：从数据库获取真实用户"""
        try:
            # 🔥 从数据库获取真实用户数据
            query = """
            SELECT DISTINCT user_id, user_name, last_interaction_time
            FROM messages 
            WHERE user_id IS NOT NULL 
            AND user_id != '' 
            AND user_id != 'default_user'
            AND last_interaction_time >= DATE_SUB(NOW(), INTERVAL 30 DAY)
            ORDER BY last_interaction_time DESC
            LIMIT 100
            """
            
            success, results, error = self.mysql_connector.execute_query(query)
            
            if success and results:
                users = []
                for user_data in results:
                    users.append({
                        "user_id": user_data.get("user_id"),
                        "user_name": user_data.get("user_name", "用户"),
                        "last_interaction": user_data.get("last_interaction_time"),
                        "source": "database"
                    })
                
                logger.info(f"📊 从数据库获取到 {len(users)} 个活跃用户")
                return users
            else:
                logger.warning(f"📊 数据库查询用户失败: {error}")
                return []
                
        except Exception as e:
            logger.error(f"📊 获取用户列表异常: {e}")
            return []
    
    def _get_target_users_for_task(self, task_type: str) -> List[Dict[str, Any]]:
        """获取任务目标用户 - 🔥 生产环境修复：基于任务类型返回真实用户"""
        if task_type in ['financial_morning_report', 'financial_afternoon_report']:
            # 财经报告任务：获取对财经感兴趣的用户
            try:
                query = """
                SELECT DISTINCT m.user_id, m.user_name, m.last_interaction_time
                FROM messages m
                WHERE m.user_id IS NOT NULL 
                AND m.user_id != '' 
                AND m.user_id != 'default_user'
                AND (
                    m.content LIKE '%财经%' OR 
                    m.content LIKE '%股票%' OR 
                    m.content LIKE '%投资%' OR
                    m.content LIKE '%金融%' OR
                    m.content LIKE '%经济%'
                )
                AND m.last_interaction_time >= DATE_SUB(NOW(), INTERVAL 7 DAY)
                ORDER BY m.last_interaction_time DESC
                LIMIT 10
                """
                
                success, results, error = self.mysql_connector.execute_query(query)
                
                if success and results:
                    users = []
                    for user_data in results:
                        users.append({
                            "user_id": user_data.get("user_id"),
                            "user_name": user_data.get("user_name", "财经关注者"),
                            "last_interaction": user_data.get("last_interaction_time"),
                            "interest": "financial"
                        })
                    
                    logger.info(f"📊 为{task_type}任务找到 {len(users)} 个目标用户")
                    return users
                else:
                    logger.warning(f"📊 查询财经用户失败: {error}")
                    return []
                    
            except Exception as e:
                logger.error(f"📊 获取财经用户异常: {e}")
                return []
        else:
            # 其他任务：返回最近活跃用户
            return self._get_all_users()[:5]

def create_universal_scheduler(mysql_connector: MySQLConnector) -> UniversalScheduler:
    """创建通用调度器实例"""
    return UniversalScheduler(mysql_connector)

def setup_default_tasks(scheduler: UniversalScheduler, 
                       mysql_connector: MySQLConnector,
                       script_service,
                       vital_signs_simulator: VitalSignsSimulator,
                       hardware_monitor: HardwareMonitor) -> bool:
    """设置默认任务"""
    try:
        # 🔥 老王修复：采用增强版剧本生成器，禁用简单版本
        # 增强版剧本生成器考虑了感知、情感、记忆、认知等深度上下文
        # script_task = ScriptGenerationTask(mysql_connector, script_service)
        # scheduler.register_task(script_task)
        # scheduler.schedule_task("script_generation", "every_30_minutes")
        
        # 🔥 香草修复：注册新的Scripts集成活动生成任务
        try:
            from services.scripts_integration_service import get_scripts_integration_service
            scripts_integration_service = get_scripts_integration_service()

            # 创建新的活动生成任务
            scripts_task = ScriptsIntegrationTask(mysql_connector, scripts_integration_service)
            scheduler.register_task(scripts_task)
            scheduler.schedule_task("scripts_integration", "every_30_minutes")

            logger.success("📊 已注册新的Scripts集成活动生成任务，每30分钟执行")
        except Exception as e:
            logger.warning(f"📊 Scripts集成任务注册失败: {e}")
        
        # 🔍 香草新增：注册自主探索任务
        try:
            from core.autonomous_exploration_task import create_autonomous_exploration_task
            exploration_task = create_autonomous_exploration_task(mysql_connector)
            scheduler.register_task(exploration_task)
            scheduler.schedule_task("autonomous_exploration", "every_2_hours")
            logger.success("🔍 已注册自主探索任务，每2小时执行话题发现和活动迭代")
        except Exception as e:
            logger.warning(f"🔍 自主探索任务注册失败（不影响主要功能）: {e}")
        
        # 💕 香草新增：注册每日表达重置任务
        try:
            from core.daily_expression_reset_task import create_daily_expression_reset_task
            daily_reset_task = create_daily_expression_reset_task(mysql_connector)
            scheduler.register_task(daily_reset_task)
            scheduler.schedule_task("daily_expression_reset", "every_1_hour")  # 每小时检查一次，确保及时重置
            logger.success("💕 已注册每日表达重置任务，确保每天自动重置表达次数")
        except Exception as e:
            logger.warning(f"💕 每日表达重置任务注册失败（不影响主要功能）: {e}")
        
        # 注册天气更新任务
        weather_task = WeatherUpdateTask(mysql_connector)
        scheduler.register_task(weather_task)
        scheduler.schedule_task("weather_update", "every_1_hour")
        
        # 注册生命体征任务
        vital_task = VitalSignsTask(vital_signs_simulator, hardware_monitor)
        scheduler.register_task(vital_task)
        scheduler.schedule_task("vital_signs", "every_5_minutes")
        
        # 🔥 老王修复：禁用定时早安问候，改为由主动表达器官智能决策
        # 数字生命体应该根据用户关系、互动历史、情感状态等智能决策是否发送早安问候
        # 而不是机械化的定时任务
        # morning_greeting_task = MorningGreetingTask()
        # scheduler.register_task(morning_greeting_task)
        # scheduler.schedule_task("morning_greeting", "every_1_minutes")
        
        logger.info("🌅 早安问候已改为由主动表达器官智能决策，体现数字生命体特色")
        
        # 🔥 修复：财经任务已迁移到WeChatSchedulerService，避免重复执行
        # 财经早报和财经下午报任务现在由WeChatSchedulerService统一管理
        logger.info("📊 财经任务已迁移到WeChatSchedulerService，跳过在UniversalScheduler中注册")
        
        # # 注册财经早报任务 - 已迁移到WeChatSchedulerService
        # financial_morning_report_task = FinancialMorningReportTask()
        # scheduler.register_task(financial_morning_report_task)
        # scheduler.schedule_task("financial_morning_report", "every_1_minutes")
        # 
        # # 注册财经下午报任务 - 已迁移到WeChatSchedulerService
        # financial_afternoon_report_task = FinancialAfternoonReportTask()
        # scheduler.register_task(financial_afternoon_report_task)
        # scheduler.schedule_task("financial_afternoon_report", "every_1_minutes")
        
        # 注册提醒任务检查器 - 每分钟检查一次到期任务
        try:
            from core.reminder_task_scheduler import get_reminder_task_checker
            reminder_checker = get_reminder_task_checker()
            scheduler.register_task(reminder_checker)
            scheduler.schedule_task("reminder_task_checker", "every_1_minutes")
            logger.debug("✅ 提醒任务检查器注册成功")
        except Exception as e:
            logger.error(f"提醒任务检查器注册失败: {e}")

        # 🔥 老王修复：朋友圈分享不应该是定时任务，而是基于活动质量的智能决策
        # 注释掉定时调度，改为事件驱动的分享机制
        # try:
        #     from core.moments_sharing_task import create_moments_sharing_task, MomentsShareConfig
        #     moments_config = MomentsShareConfig(
        #         max_daily_shares=2,
        #         min_interval_hours=4,
        #         preferred_times=["10:00", "16:00"],
        #         privacy=False
        #     )
        #     moments_task = create_moments_sharing_task(moments_config)
        #     scheduler.register_task(moments_task)
        #     scheduler.schedule_task("moments_sharing", "every_2_hours")
        #     logger.success("📱 朋友圈分享任务注册成功，每日自动分享2次")
        # except Exception as e:
        #     logger.warning(f"📱 朋友圈分享任务注册失败（不影响主要功能）: {e}")

        logger.info("📱 朋友圈分享改为事件驱动模式，基于活动质量智能决策")

        logger.debug("默认任务设置完成")
        return True
        
    except Exception as e:
        logger.error_status(f"设置默认任务失败: {e}")
        return False 