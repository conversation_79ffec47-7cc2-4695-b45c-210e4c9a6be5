# 记忆管理器模块
from utilities.unified_logger import get_unified_logger, setup_unified_logging
import asyncio
from typing import Dict, Any, List, Optional

# 单例模式
_instance = None

class MemoryManager:
    """记忆管理器类，管理各种记忆类型和记忆操作"""
    
    def __init__(self):
        """初始化记忆管理器"""
        self.logger = get_unified_logger("memory_manager")
        self.logger.success("初始化记忆管理器...")
        
        # 记忆存储
        self.episodic_memory = []  # 情景记忆
        self.semantic_memory = {}  # 语义记忆
        self.procedural_memory = []  # 程序性记忆
        
        # 记忆模块
        self.modules = {}
        
        # 事件总线
        try:
            from core.enhanced_event_bus import get_instance as get_event_bus
            self.event_bus = get_event_bus()
            self.logger.success("已连接事件总线")
            
            # 注册事件处理器
            self.event_bus.subscribe("perception.processed", self._on_perception)
        except Exception as e:
            self.logger.warning_status(f"连接事件总线失败: {e}，部分功能可能受限")
            self.event_bus = None
        
        # 初始化完成标志
        self.initialized = True
        self.logger.success("记忆管理器初始化完成")
    
    def _on_perception(self, event_data):
        """处理感知事件，存储记忆"""
        # 在实际实现中，这里会将感知结果存储到适当的记忆中
        pass
    
    async def store_memory(self, memory_type: str, content: Any) -> bool:
        """存储记忆"""
        try:
            if memory_type == "episodic":
                self.episodic_memory.append(content)
            elif memory_type == "semantic":
                if "key" in content and "value" in content:
                    self.semantic_memory[content["key"]] = content["value"]
            elif memory_type == "procedural":
                self.procedural_memory.append(content)
            else:
                self.logger.warning_status(f"未知的记忆类型: {memory_type}")
                return False
                
            self.logger.debug(f"已存储{memory_type}记忆: {content}")
            return True
        except Exception as e:
            self.logger.error_status(f"存储记忆失败: {e}")
            return False
    
    async def retrieve_memory(self, memory_type: str, query: Any) -> Optional[Any]:
        """检索记忆"""
        try:
            if memory_type == "episodic":
                # 简单实现：返回与查询相关的所有情景记忆
                return [m for m in self.episodic_memory if self._match_query(m, query)]
            elif memory_type == "semantic":
                # 简单实现：根据键检索语义记忆
                if isinstance(query, str) and query in self.semantic_memory:
                    return self.semantic_memory[query]
            elif memory_type == "procedural":
                # 简单实现：返回与查询相关的程序性记忆
                return [m for m in self.procedural_memory if self._match_query(m, query)]
            else:
                self.logger.warning_status(f"未知的记忆类型: {memory_type}")
                return None
                
            return None
        except Exception as e:
            self.logger.error_status(f"检索记忆失败: {e}")
            return None
    
    def _match_query(self, memory: Any, query: Any) -> bool:
        """检查记忆是否匹配查询"""
        # 简单实现：字符串匹配
        if isinstance(query, str) and isinstance(memory, Dict):
            return any(query.lower() in str(v).lower() for v in memory.values())
        return False

    def search_memory(self, query: str, memory_type: str = "all", limit: int = 10) -> List[Dict[str, Any]]:
        """
        🔥 香草修复：搜索记忆

        Args:
            query: 搜索查询
            memory_type: 记忆类型 ("episodic", "semantic", "all")
            limit: 返回结果数量限制

        Returns:
            List[Dict[str, Any]]: 搜索结果
        """
        try:
            results = []

            # 搜索情景记忆
            if memory_type in ["episodic", "all"]:
                for memory in self.episodic_memory:
                    if self._match_query(memory, query):
                        results.append({
                            "type": "episodic",
                            "content": memory,
                            "relevance": self._calculate_relevance(memory, query)
                        })

            # 搜索语义记忆
            if memory_type in ["semantic", "all"]:
                for memory in self.semantic_memory:
                    if self._match_query(memory, query):
                        results.append({
                            "type": "semantic",
                            "content": memory,
                            "relevance": self._calculate_relevance(memory, query)
                        })

            # 按相关性排序
            results.sort(key=lambda x: x["relevance"], reverse=True)

            # 限制返回数量
            return results[:limit]

        except Exception as e:
            self.logger.error_status(f"搜索记忆失败: {e}")
            return []

    def _calculate_relevance(self, memory: Dict[str, Any], query: str) -> float:
        """计算记忆与查询的相关性"""
        try:
            if not isinstance(memory, dict) or not isinstance(query, str):
                return 0.0

            query_lower = query.lower()
            relevance = 0.0

            # 检查各个字段的匹配度
            for key, value in memory.items():
                if isinstance(value, str):
                    value_lower = value.lower()
                    if query_lower in value_lower:
                        # 完全匹配得分更高
                        if query_lower == value_lower:
                            relevance += 1.0
                        else:
                            # 部分匹配根据匹配长度计算得分
                            relevance += len(query_lower) / len(value_lower)

            return min(relevance, 1.0)  # 限制最大值为1.0

        except Exception as e:
            self.logger.debug(f"计算相关性失败: {e}")
            return 0.0

def get_instance():
    """获取记忆管理器实例（同步版本）"""
    global _instance
    if _instance is None:
        _instance = MemoryManager()
    return _instance
