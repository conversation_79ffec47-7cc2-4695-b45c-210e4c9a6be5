#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
记忆检索模块 (Memory Retrieval)

该模块负责优化记忆检索策略，根据上下文和查询意图从各类记忆中高效检索信息，
提供结果排序、过滤和组合功能，支持多种检索模式。

作者: Claude
创建日期: 2024-07-17
版本: 1.0
"""

import os
import sys
import json
import time
from utilities.unified_logger import get_unified_logger, setup_unified_logging
import threading
from typing import Dict, List, Any, Optional, Tuple, Union, Set
from datetime import datetime, timedelta
import re

# 添加项目根目录到模块搜索路径
current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(current_dir)
root_dir = os.path.dirname(parent_dir)
if root_dir not in sys.path:
    sys.path.append(root_dir)

# 导入项目模块
from cognitive_modules.base.module_base import CognitiveModuleBase
from cognitive_modules.memory.episodic_memory import EpisodicMemory
from cognitive_modules.memory.semantic_memory import get_instance as get_semantic_memory
from cognitive_modules.memory.procedural_memory import ProceduralMemory
from cognitive_modules.memory.memory_integration import get_instance as get_memory_integration

# 设置日志
setup_unified_logging()
logger = get_unified_logger("memory.retrieval")


class MemoryRetrieval(CognitiveModuleBase):
    """记忆检索模块，负责优化记忆检索策略"""
    
    def __init__(self, module_id: str = "memory_retrieval", config: Dict[str, Any] = None):
        """
        初始化记忆检索模块
        
        参数:
            module_id: 模块ID
            config: 配置参数
        """
        super().__init__(module_id, "memory", config)
        
        # 设置模块描述
        self.description = "记忆检索模块 - 提供高效的记忆检索功能"
        
        # 设置依赖模块
        self.dependencies = ["episodic_memory", "semantic_memory", "procedural_memory", "memory_integration"]
        
        # 记忆模块引用
        self.episodic_memory = None
        self.semantic_memory = None
        self.procedural_memory = None
        self.memory_integration = None
        
        # 缓存最近的检索结果
        self.retrieval_cache = {}
        self.cache_expiry = 300  # 缓存过期时间（秒）
        
        # 检索统计信息
        self.retrieval_stats = {
            "total_queries": 0,
            "cache_hits": 0,
            "episodic_retrievals": 0,
            "semantic_retrievals": 0,
            "procedural_retrievals": 0,
            "combined_retrievals": 0,
            "average_response_time": 0,
            "total_response_time": 0
        }
        
        # 检索锁
        self.retrieval_lock = threading.Lock()
        
        logger.success(f"记忆检索模块创建成功 (ID: {self.module_id})")
    
    def _do_initialize(self) -> None:
        """
        执行初始化操作
        """
        logger.success(f"正在初始化记忆检索模块 (ID: {self.module_id})...")
        
        # 获取各记忆模块实例
        self.episodic_memory = EpisodicMemory.get_instance()
        self.semantic_memory = get_semantic_memory()
        self.procedural_memory = ProceduralMemory.get_instance()
        self.memory_integration = get_memory_integration()
        
        # 确保记忆模块已初始化
        if not self.episodic_memory.is_initialized:
            self.episodic_memory.initialize()
        
        if not self.semantic_memory.is_initialized:
            self.semantic_memory.initialize()
        
        if not self.procedural_memory.is_initialized:
            self.procedural_memory.initialize()
            
        if not self.memory_integration.is_initialized:
            self.memory_integration.initialize()
        
        # 从配置中获取缓存过期时间
        if self.config and "cache_expiry" in self.config:
            self.cache_expiry = self.config["cache_expiry"]
        
        logger.success(f"记忆检索模块初始化成功 (ID: {self.module_id})")
    
    def _do_process(self, input_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        处理输入数据
        
        参数:
            input_data: 输入数据
            
        返回:
            Dict[str, Any]: 处理结果
        """
        operation = input_data.get("operation", "retrieve")
        
        if operation == "retrieve":
            # 执行记忆检索
            query = input_data.get("query", "")
            memory_types = input_data.get("memory_types", ["episodic", "semantic", "procedural"])
            filters = input_data.get("filters", {})
            limit = input_data.get("limit", 10)
            strategy = input_data.get("strategy", "relevance")
            context = input_data.get("context", {})
            
            result = self.retrieve_memories(
                query=query,
                memory_types=memory_types,
                filters=filters,
                limit=limit,
                strategy=strategy,
                context=context
            )
            
            return {
                "success": True,
                "memories": result,
                "query": query,
                "strategy": strategy
            }
            
        elif operation == "get_stats":
            # 获取检索统计信息
            return {
                "success": True,
                "stats": self.retrieval_stats
            }
            
        elif operation == "clear_cache":
            # 清除检索缓存
            self.retrieval_cache = {}
            return {
                "success": True,
                "message": "检索缓存已清除"
            }
            
        else:
            return {
                "success": False,
                "message": f"未知的操作: {operation}"
            }
    
    def retrieve_memories(self, 
                         query: str, 
                         memory_types: List[str] = None,
                         filters: Dict[str, Any] = None, 
                         limit: int = 10,
                         strategy: str = "relevance",
                         context: Dict[str, Any] = None) -> Dict[str, List[Dict[str, Any]]]:
        """
        检索记忆
        
        参数:
            query: 查询字符串
            memory_types: 要检索的记忆类型列表，可选项: ["episodic", "semantic", "procedural"]
            filters: 过滤条件
            limit: 每种类型返回的最大记忆数量
            strategy: 检索策略，可选值: "relevance", "recency", "emotional", "combined"
            context: 上下文信息，用于提供检索的额外信息
            
        返回:
            Dict[str, List[Dict[str, Any]]]: 检索结果，按记忆类型分组
        """
        # 更新统计信息
        self.retrieval_stats["total_queries"] += 1
        start_time = time.time()
        
        # 检查缓存
        cache_key = self._generate_cache_key(query, memory_types, filters, limit, strategy)
        if cache_key in self.retrieval_cache:
            cache_entry = self.retrieval_cache[cache_key]
            if cache_entry["expiry"] > time.time():
                self.retrieval_stats["cache_hits"] += 1
                return cache_entry["result"]
        
        # 默认检索所有类型的记忆
        if memory_types is None:
            memory_types = ["episodic", "semantic", "procedural"]
        
        # 初始化结果
        results = {
            "episodic": [],
            "semantic": [],
            "procedural": []
        }
        
        # 检索不同类型的记忆
        if "episodic" in memory_types:
            self.retrieval_stats["episodic_retrievals"] += 1
            episodic_memories = self._retrieve_episodic_memories(query, filters, limit, strategy, context)
            results["episodic"] = episodic_memories
        
        if "semantic" in memory_types:
            self.retrieval_stats["semantic_retrievals"] += 1
            semantic_memories = self._retrieve_semantic_memories(query, filters, limit, strategy, context)
            results["semantic"] = semantic_memories
        
        if "procedural" in memory_types:
            self.retrieval_stats["procedural_retrievals"] += 1
            procedural_memories = self._retrieve_procedural_memories(query, filters, limit, strategy, context)
            results["procedural"] = procedural_memories
        
        # 应用后处理策略
        if strategy == "combined":
            self.retrieval_stats["combined_retrievals"] += 1
            results = self._apply_combined_strategy(results, query, limit, context)
        
        # 更新响应时间统计
        response_time = time.time() - start_time
        self.retrieval_stats["total_response_time"] += response_time
        query_count = self.retrieval_stats["total_queries"]
        self.retrieval_stats["average_response_time"] = (
            self.retrieval_stats["total_response_time"] / query_count
        )
        
        # 缓存结果
        self.retrieval_cache[cache_key] = {
            "result": results,
            "expiry": time.time() + self.cache_expiry
        }
        
        # 清理过期缓存
        self._clean_expired_cache()
        
        return results
    
    def _retrieve_episodic_memories(self, 
                                   query: str, 
                                   filters: Dict[str, Any], 
                                   limit: int,
                                   strategy: str,
                                   context: Dict[str, Any]) -> List[Dict[str, Any]]:
        """
        检索情境记忆
        
        参数:
            query: 查询字符串
            filters: 过滤条件
            limit: 返回的最大记忆数量
            strategy: 检索策略
            context: 上下文信息
            
        返回:
            List[Dict[str, Any]]: 情境记忆列表
        """
        # 准备搜索参数
        search_params = {"limit": limit}
        
        # 添加过滤条件
        if filters:
            search_params.update(filters)
        
        # 根据策略调整搜索参数
        if strategy == "recency":
            search_params["sort_by"] = "timestamp"
            search_params["sort_order"] = "desc"
        elif strategy == "emotional":
            search_params["sort_by"] = "emotion.intensity"
            search_params["sort_order"] = "desc"
        
        # 执行搜索
        memories = self.episodic_memory.search_memories(query, search_params)
        
        # 根据上下文进行后处理
        if context:
            memories = self._post_process_memories(memories, context, "episodic")
        
        return memories
    
    def _retrieve_semantic_memories(self, 
                                   query: str, 
                                   filters: Dict[str, Any], 
                                   limit: int,
                                   strategy: str,
                                   context: Dict[str, Any]) -> List[Dict[str, Any]]:
        """
        检索语义记忆
        
        参数:
            query: 查询字符串
            filters: 过滤条件
            limit: 返回的最大记忆数量
            strategy: 检索策略
            context: 上下文信息
            
        返回:
            List[Dict[str, Any]]: 语义记忆列表
        """
        # 准备搜索参数
        search_params = {"limit": limit}
        
        # 添加过滤条件
        if filters:
            search_params.update(filters)
        
        # 根据策略调整搜索参数
        if strategy == "recency":
            search_params["sort_by"] = "created_at"
            search_params["sort_order"] = "desc"
        
        # 执行搜索
        memories = self.semantic_memory.search_memories(query, search_params)
        
        # 根据上下文进行后处理
        if context:
            memories = self._post_process_memories(memories, context, "semantic")
        
        return memories
    
    def _retrieve_procedural_memories(self, 
                                     query: str, 
                                     filters: Dict[str, Any], 
                                     limit: int,
                                     strategy: str,
                                     context: Dict[str, Any]) -> List[Dict[str, Any]]:
        """
        检索程序记忆
        
        参数:
            query: 查询字符串
            filters: 过滤条件
            limit: 返回的最大记忆数量
            strategy: 检索策略
            context: 上下文信息
            
        返回:
            List[Dict[str, Any]]: 程序记忆列表
        """
        # 准备搜索参数
        search_params = {"limit": limit}
        
        # 添加过滤条件
        if filters:
            search_params.update(filters)
        
        # 根据策略调整搜索参数
        if strategy == "recency":
            search_params["sort_by"] = "last_used"
            search_params["sort_order"] = "desc"
        
        # 执行搜索
        memories = self.procedural_memory.search_memories(query, search_params)
        
        # 根据上下文进行后处理
        if context:
            memories = self._post_process_memories(memories, context, "procedural")
        
        return memories
    
    def _post_process_memories(self, 
                              memories: List[Dict[str, Any]], 
                              context: Dict[str, Any],
                              memory_type: str) -> List[Dict[str, Any]]:
        """
        根据上下文对检索结果进行后处理
        
        参数:
            memories: 记忆列表
            context: 上下文信息
            memory_type: 记忆类型
            
        返回:
            List[Dict[str, Any]]: 处理后的记忆列表
        """
        # 提取上下文中的相关信息
        user_id = context.get("user_id")
        current_topic = context.get("current_topic")
        emotional_state = context.get("emotional_state")
        
        # 根据用户ID过滤
        if user_id and memory_type == "episodic":
            memories = [m for m in memories if m.get("user_id") == user_id]
        
        # 根据当前话题增强相关性
        if current_topic:
            # 根据话题相关性重新排序
            def topic_relevance(memory):
                content = memory.get("content", "")
                tags = memory.get("tags", [])
                
                # 检查话题是否在内容或标签中
                topic_in_content = current_topic.lower() in content.lower()
                topic_in_tags = current_topic.lower() in [t.lower() for t in tags]
                
                # 计算相关性得分
                relevance = 0
                if topic_in_content:
                    relevance += 0.6
                if topic_in_tags:
                    relevance += 0.4
                
                return relevance
            
            # 添加话题相关性得分
            for memory in memories:
                memory["topic_relevance"] = topic_relevance(memory)
            
            # 根据话题相关性排序
            memories.sort(key=lambda m: m.get("topic_relevance", 0), reverse=True)
        
        # 根据情感状态调整
        if emotional_state and memory_type == "episodic":
            # 定义情感匹配函数
            def emotional_match(memory):
                memory_emotion = memory.get("emotion", {}).get("type")
                if not memory_emotion:
                    return 0
                
                # 计算情感匹配度
                if memory_emotion == emotional_state:
                    return 1
                
                # 情感对比度（正面情绪vs负面情绪）
                positive_emotions = ["happiness", "joy", "excitement", "love"]
                negative_emotions = ["sadness", "anger", "fear", "disgust"]
                
                if (memory_emotion in positive_emotions and emotional_state in negative_emotions) or \
                   (memory_emotion in negative_emotions and emotional_state in positive_emotions):
                    return -0.5
                
                return 0
            
            # 添加情感匹配度得分
            for memory in memories:
                memory["emotional_match"] = emotional_match(memory)
            
            # 情感相关的记忆排在前面
            memories.sort(key=lambda m: m.get("emotional_match", 0), reverse=True)
        
        return memories
    
    def _apply_combined_strategy(self, 
                                results: Dict[str, List[Dict[str, Any]]],
                                query: str,
                                limit: int,
                                context: Dict[str, Any]) -> Dict[str, List[Dict[str, Any]]]:
        """
        应用组合策略，整合不同类型的记忆结果
        
        参数:
            results: 各类型的记忆结果
            query: 查询字符串
            limit: 返回的最大记忆数量
            context: 上下文信息
            
        返回:
            Dict[str, List[Dict[str, Any]]]: 应用组合策略后的结果
        """
        # 此处可以实现更复杂的结果融合策略
        # 例如，根据查询意图动态调整不同类型记忆的权重
        
        # 简单实现：根据记忆类型相关性动态分配限额
        query_lower = query.lower()
        type_weights = {
            "episodic": 0.33,
            "semantic": 0.33,
            "procedural": 0.33
        }
        
        # 根据查询关键词调整权重
        episodic_keywords = ["remember", "happened", "experience", "felt", "when", "day"]
        semantic_keywords = ["what", "definition", "explain", "concept", "understand", "mean"]
        procedural_keywords = ["how", "steps", "procedure", "method", "technique", "do"]
        
        for keyword in episodic_keywords:
            if keyword in query_lower:
                type_weights["episodic"] += 0.1
                type_weights["semantic"] -= 0.05
                type_weights["procedural"] -= 0.05
                break
                
        for keyword in semantic_keywords:
            if keyword in query_lower:
                type_weights["semantic"] += 0.1
                type_weights["episodic"] -= 0.05
                type_weights["procedural"] -= 0.05
                break
                
        for keyword in procedural_keywords:
            if keyword in query_lower:
                type_weights["procedural"] += 0.1
                type_weights["episodic"] -= 0.05
                type_weights["semantic"] -= 0.05
                break
        
        # 正规化权重
        total_weight = sum(type_weights.values())
        for memory_type in type_weights:
            type_weights[memory_type] /= total_weight
        
        # 根据权重分配每种类型的限额
        type_limits = {}
        for memory_type, weight in type_weights.items():
            type_limits[memory_type] = max(1, int(limit * weight))
        
        # 应用限额
        for memory_type, memories in results.items():
            results[memory_type] = memories[:type_limits[memory_type]]
        
        return results
    
    def _generate_cache_key(self, 
                           query: str, 
                           memory_types: List[str],
                           filters: Dict[str, Any], 
                           limit: int,
                           strategy: str) -> str:
        """
        生成缓存键
        
        参数:
            query: 查询字符串
            memory_types: 记忆类型列表
            filters: 过滤条件
            limit: 返回的最大记忆数量
            strategy: 检索策略
            
        返回:
            str: 缓存键
        """
        # 对参数进行序列化
        memory_types_str = ",".join(sorted(memory_types)) if memory_types else ""
        filters_str = json.dumps(filters, sort_keys=True) if filters else ""
        
        # 生成缓存键
        cache_key = f"{query}|{memory_types_str}|{filters_str}|{limit}|{strategy}"
        
        return cache_key
    
    def _clean_expired_cache(self) -> None:
        """
        清理过期的缓存项
        """
        current_time = time.time()
        expired_keys = [
            key for key, entry in self.retrieval_cache.items()
            if entry["expiry"] < current_time
        ]
        
        for key in expired_keys:
            del self.retrieval_cache[key]
    
    def get_retrieval_stats(self) -> Dict[str, Any]:
        """
        获取检索统计信息
        
        返回:
            Dict[str, Any]: 检索统计信息
        """
        return self.retrieval_stats.copy()
    
    def clear_cache(self) -> None:
        """
        清除检索缓存
        """
        self.retrieval_cache = {}
        logger.info("检索缓存已清除")
    
    def _do_shutdown(self) -> None:
        """
        关闭模块
        """
        logger.info(f"正在关闭记忆检索模块 (ID: {self.module_id})...")
        
        # 清除缓存
        self.retrieval_cache = {}
        
        logger.success(f"记忆检索模块关闭成功 (ID: {self.module_id})")


# 单例模式
_instance = None

def get_instance(module_id: str = "memory_retrieval", config: Dict[str, Any] = None) -> MemoryRetrieval:
    """
    获取记忆检索模块实例
    
    参数:
        module_id: 模块ID
        config: 配置参数
        
    返回:
        MemoryRetrieval: 记忆检索模块实例
    """
    global _instance
    
    if _instance is None:
        _instance = MemoryRetrieval(module_id, config)
    
    return _instance 