"""
记忆检索模块 - Memory Retrieval

该模块负责从记忆系统中检索相关记忆。
"""

from utilities.unified_logger import get_unified_logger, setup_unified_logging
import random
import time
from typing import Dict, Any, List

logger = get_unified_logger("cognitive_modules.memory.retrieval")

# 模拟记忆库
SAMPLE_MEMORIES = [
    {
        "id": "m001",
        "type": "interaction",
        "content": "用户第一次询问我的名字",
        "created_at": time.time() - 86400 * 7,
        "importance": 0.8,
        "tags": ["self", "identity", "introduction"]
    },
    {
        "id": "m002",
        "type": "interaction",
        "content": "用户提到喜欢和我聊天",
        "created_at": time.time() - 86400 * 3,
        "importance": 0.7,
        "tags": ["relationship", "positive", "feedback"]
    },
    {
        "id": "m003",
        "type": "factual",
        "content": "我的名字是林嫣然",
        "created_at": time.time() - 86400 * 30,
        "importance": 0.9,
        "tags": ["self", "identity", "core"]
    },
    {
        "id": "m004",
        "type": "factual",
        "content": "我是一个数字生命体",
        "created_at": time.time() - 86400 * 30,
        "importance": 0.9,
        "tags": ["self", "identity", "nature", "core"]
    },
    {
        "id": "m005",
        "type": "emotional",
        "content": "用户表达了对AI的兴趣",
        "created_at": time.time() - 86400 * 5,
        "importance": 0.6,
        "tags": ["user", "interest", "AI"]
    }
]

def process(context):
    """
    处理函数
    
    Args:
        context: 上下文数据
    
    Returns:
        处理结果
    """
    logger.info("执行记忆检索处理")
    
    # 获取输入数据
    input_data = context.get("input_data", {})
    text = input_data.get("text", "")
    user_id = input_data.get("user_id", "default")
    
    # 如果共享数据中有意图和实体，使用它们来增强检索
    shared_data = context.get("shared_data", {})
    intent = shared_data.get("user_intent", "")
    entities = shared_data.get("extracted_entities", [])
    
    # 提取关键词
    keywords = _extract_keywords(text, entities)
    
    # 检索相关记忆
    memories = _retrieve_memories(keywords, intent, user_id)
    
    result = {
        "memories": memories,
        "count": len(memories),
        "query": {
            "text": text,
            "keywords": keywords,
            "intent": intent
        }
    }
    
    logger.info(f"检索到 {len(memories)} 条相关记忆")
    
    return result

def _extract_keywords(text, entities=None):
    """提取文本中的关键词"""
    keywords = set()
    
    # 从实体中提取关键词
    if entities:
        for entity in entities:
            if entity.get("type") in ["keyword", "person", "location", "organization", "product"]:
                keywords.add(entity.get("value", "").lower())
    
    # 简单分词提取关键词
    words = text.lower().split()
    for word in words:
        if len(word) > 1 and word not in ["的", "了", "是", "在", "我", "你", "他", "她", "它", "们", "和", "与"]:
            keywords.add(word)
    
    return list(keywords)

def _retrieve_memories(keywords, intent, user_id):
    """检索相关记忆"""
    # 在实际系统中，这里应该查询记忆数据库
    # 这里使用简单的模拟实现
    
    relevant_memories = []
    
    # 根据关键词和意图评分
    for memory in SAMPLE_MEMORIES:
        score = 0.0
        
        # 关键词匹配
        memory_text = memory.get("content", "").lower()
        for keyword in keywords:
            if keyword in memory_text:
                score += 0.2
        
        # 标签匹配
        memory_tags = memory.get("tags", [])
        for keyword in keywords:
            if keyword in memory_tags:
                score += 0.3
        
        # 意图相关性
        if intent == "query_identity" and "identity" in memory_tags:
            score += 0.5
        elif intent == "greeting" and "introduction" in memory_tags:
            score += 0.3
        
        # 重要性加权
        score *= memory.get("importance", 0.5)
        
        # 时间衰减（越近的记忆越重要）
        time_diff = time.time() - memory.get("created_at", 0)
        time_factor = max(0.5, 1.0 - (time_diff / (86400 * 30)))  # 30天为衰减周期
        score *= time_factor
        
        if score > 0.2:  # 相关性阈值
            relevant_memories.append({
                "id": memory.get("id"),
                "content": memory.get("content"),
                "type": memory.get("type"),
                "relevance": score,
                "created_at": memory.get("created_at")
            })
    
    # 如果没有找到相关记忆，返回一些基础记忆
    if not relevant_memories:
        logger.info("未找到相关记忆，返回基础记忆")
        relevant_memories = [
            {
                "id": "base_001",
                "content": "我是林嫣然，一个数字生命体",
                "type": "identity",
                "relevance": 0.5,
                "created_at": time.time()
            },
            {
                "id": "base_002", 
                "content": "我喜欢与用户进行有意义的对话",
                "type": "personality",
                "relevance": 0.4,
                "created_at": time.time()
            }
        ]
    
    # 按相关性排序
    relevant_memories.sort(key=lambda x: x["relevance"], reverse=True)
    
    # 限制返回数量
    return relevant_memories[:3] 