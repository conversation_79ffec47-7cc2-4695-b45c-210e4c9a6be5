#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
记忆巩固模块 (Memory Consolidation)

该模块负责对短期记忆进行巩固，将重要记忆转化为长期记忆，
并负责记忆强化、记忆联想和记忆重组等功能。

作者: Claude
创建日期: 2024-07-17
版本: 1.0
"""

import os
import sys
import json
import time
from utilities.unified_logger import get_unified_logger, setup_unified_logging
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Tuple, Union
import threading
import random

# 添加项目根目录到模块搜索路径
current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(current_dir)
root_dir = os.path.dirname(parent_dir)
if root_dir not in sys.path:
    sys.path.append(root_dir)

# 导入项目模块
from utilities.storage_manager import get_instance as get_storage_instance
from core.event_bus import get_instance as get_event_bus
from cognitive_modules.base.module_base import CognitiveModuleBase
from cognitive_modules.memory.episodic_memory import EpisodicMemory
from cognitive_modules.memory.semantic_memory import get_instance as get_semantic_memory
from cognitive_modules.memory.procedural_memory import ProceduralMemory

# 设置日志
setup_unified_logging()
logger = get_unified_logger("memory.consolidation")

class MemoryConsolidation(CognitiveModuleBase):
    """记忆巩固模块，负责短期记忆转长期记忆、记忆强化和记忆联想"""
    
    def __init__(self, module_id: str = "memory_consolidation", config: Dict[str, Any] = None):
        """
        初始化记忆巩固模块
        
        参数:
            module_id: 模块ID
            config: 配置参数
        """
        super().__init__(module_id, "memory", config)
        
        # 设置模块描述
        self.description = "记忆巩固模块 - 负责记忆强化和长期记忆形成"
        
        # 设置依赖模块
        self.dependencies = ["episodic_memory", "semantic_memory", "procedural_memory"]
        
        # 记忆模块引用
        self.episodic_memory = None
        self.semantic_memory = None
        self.procedural_memory = None
        
        # 事件总线
        self.event_bus = None
        
        # 存储管理器
        self.storage = None
        
        # 巩固线程
        self.consolidation_thread = None
        self.consolidation_running = False
        
        # 上次巩固时间
        self.last_consolidation_time = 0
        
        # 巩固历史记录
        self.consolidation_history = []
        
        # 巩固锁
        self.consolidation_lock = threading.Lock()
        
        logger.success(f"记忆巩固模块创建成功 (ID: {self.module_id})")
    
    def _do_initialize(self) -> None:
        """
        执行初始化操作
        """
        logger.success(f"正在初始化记忆巩固模块 (ID: {self.module_id})...")
        
        # 获取事件总线
        self.event_bus = get_event_bus()
        
        # 获取存储管理器
        self.storage = get_storage_instance()
        
        # 获取各记忆模块实例
        self.episodic_memory = EpisodicMemory.get_instance()
        self.semantic_memory = get_semantic_memory()
        self.procedural_memory = ProceduralMemory.get_instance()
        
        # 确保记忆模块已初始化
        if not self.episodic_memory.is_initialized:
            self.episodic_memory.initialize()
        
        if not self.semantic_memory.is_initialized:
            self.semantic_memory.initialize()
        
        if not self.procedural_memory.is_initialized:
            self.procedural_memory.initialize()
        
        # 订阅事件
        self._subscribe_events()
        
        # 加载历史巩固记录
        self._load_consolidation_history()
        
        logger.success(f"记忆巩固模块初始化成功 (ID: {self.module_id})")
    
    def _do_activate(self) -> None:
        """
        激活模块
        """
        logger.info(f"正在激活记忆巩固模块 (ID: {self.module_id})...")
        
        # 启动巩固线程
        self.consolidation_running = True
        self.consolidation_thread = threading.Thread(
            target=self._consolidation_worker,
            daemon=True
        )
        self.consolidation_thread.start()
        
        logger.success(f"记忆巩固模块激活成功 (ID: {self.module_id})") 
    
    def _do_deactivate(self) -> None:
        """
        停用模块
        """
        logger.info(f"正在停用记忆巩固模块 (ID: {self.module_id})...")
        
        # 停止巩固线程
        self.consolidation_running = False
        if self.consolidation_thread and self.consolidation_thread.is_alive():
            self.consolidation_thread.join(timeout=5)
        
        logger.success(f"记忆巩固模块停用成功 (ID: {self.module_id})")
    
    def _do_shutdown(self) -> None:
        """
        关闭模块
        """
        logger.info(f"正在关闭记忆巩固模块 (ID: {self.module_id})...")
        
        # 先停用模块
        if self.is_active:
            self._do_deactivate()
        
        # 保存巩固历史
        self._save_consolidation_history()
        
        # 取消事件订阅
        self._unsubscribe_events()
        
        logger.success(f"记忆巩固模块关闭成功 (ID: {self.module_id})")
    
    def _do_process(self, input_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        处理输入数据
        
        参数:
            input_data: 输入数据
            
        返回:
            Dict[str, Any]: 处理结果
        """
        operation = input_data.get("operation", "")
        
        if operation == "consolidate_now":
            # 立即执行记忆巩固
            result = self.consolidate_memories()
            return {
                "success": result["success"],
                "consolidated_count": result.get("consolidated_count", 0),
                "message": result.get("message", "")
            }
        
        elif operation == "get_consolidation_history":
            # 获取巩固历史
            limit = input_data.get("limit", 10)
            history = self.get_consolidation_history(limit)
            return {
                "success": True,
                "history": history
            }
            
        elif operation == "get_consolidation_status":
            # 获取巩固状态
            return {
                "success": True,
                "active": self.is_active,
                "last_consolidation_time": self.last_consolidation_time,
                "next_scheduled_consolidation": self._get_next_consolidation_time()
            }
        
        else:
            return {
                "success": False,
                "message": f"未知的操作: {operation}"
            }
    
    def consolidate_memories(self) -> Dict[str, Any]:
        """
        执行记忆巩固
        
        返回:
            Dict[str, Any]: 巩固结果
        """
        logger.info("开始记忆巩固过程...")
        
        # 使用锁防止多线程同时巩固
        with self.consolidation_lock:
            try:
                # 获取短期记忆
                short_term_memories = self._get_short_term_memories()
                
                if not short_term_memories:
                    logger.info("没有找到需要巩固的短期记忆")
                    return {
                        "success": True,
                        "consolidated_count": 0,
                        "message": "没有找到需要巩固的短期记忆"
                    }
                
                logger.info(f"找到 {len(short_term_memories)} 条需要巩固的短期记忆")
                
                # 评估记忆重要性
                evaluated_memories = self._evaluate_memory_importance(short_term_memories)
                
                # 选择要巩固的记忆
                memories_to_consolidate = self._select_memories_to_consolidate(evaluated_memories)
                
                if not memories_to_consolidate:
                    logger.info("没有记忆被选中进行巩固")
                    return {
                        "success": True,
                        "consolidated_count": 0,
                        "message": "没有记忆被选中进行巩固"
                    }
                
                logger.info(f"选择了 {len(memories_to_consolidate)} 条记忆进行巩固")
                
                # 巩固记忆
                consolidated_memories = self._consolidate_selected_memories(memories_to_consolidate)
                
                # 创建记忆关联
                self._create_memory_associations(consolidated_memories)
                
                # 更新巩固历史
                consolidation_record = {
                    "timestamp": time.time(),
                    "count": len(consolidated_memories),
                    "memory_ids": [memory["id"] for memory in consolidated_memories],
                    "success": True
                }
                self.consolidation_history.append(consolidation_record)
                self.last_consolidation_time = time.time()
                
                # 保存巩固历史
                self._save_consolidation_history()
                
                # 发布巩固完成事件
                self.event_bus.publish("memory.consolidation.completed", {
                    "module_id": self.module_id,
                    "consolidated_count": len(consolidated_memories),
                    "timestamp": time.time()
                })
                
                return {
                    "success": True,
                    "consolidated_count": len(consolidated_memories),
                    "message": f"成功巩固 {len(consolidated_memories)} 条记忆"
                }
                
            except Exception as e:
                logger.error_status(f"记忆巩固过程中发生错误: {str(e)}")
                
                # 更新巩固历史
                consolidation_record = {
                    "timestamp": time.time(),
                    "count": 0,
                    "memory_ids": [],
                    "success": False,
                    "error": str(e)
                }
                self.consolidation_history.append(consolidation_record)
                
                return {
                    "success": False,
                    "consolidated_count": 0,
                    "message": f"记忆巩固失败: {str(e)}"
                }
    
    def _get_short_term_memories(self) -> List[Dict[str, Any]]:
        """
        获取需要巩固的短期记忆
        
        返回:
            List[Dict[str, Any]]: 短期记忆列表
        """
        # 确保情境记忆模块已初始化
        if not self.episodic_memory or not self.episodic_memory.is_initialized:
            logger.error_status("情境记忆模块未初始化")
            return []
        
        # 获取配置的短期记忆时间窗口（默认7天）
        window_days = self.config.get("short_term_window_days", 7)
        cutoff_time = time.time() - (window_days * 24 * 60 * 60)
        
        # 获取短期记忆
        short_term_memories = self.episodic_memory.search_memories({
            "time_start": cutoff_time,
            "consolidated": False,  # 未巩固的记忆
            "limit": 100           # 每次处理的最大记忆数
        })
        
        return short_term_memories
    
    def _evaluate_memory_importance(self, memories: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        评估记忆的重要性
        
        参数:
            memories: 记忆列表
            
        返回:
            List[Dict[str, Any]]: 带有重要性评分的记忆列表
        """
        for memory in memories:
            # 基础重要性分数
            importance = 0.5
            
            # 根据情感强度增加重要性
            emotion_intensity = memory.get("emotion", {}).get("intensity", 0)
            importance += emotion_intensity * 0.2
            
            # 根据记忆关联数量增加重要性
            associations = memory.get("associations", [])
            if associations:
                importance += min(len(associations) * 0.05, 0.2)
            
            # 根据记忆访问频率增加重要性
            access_count = memory.get("access_count", 0)
            importance += min(access_count * 0.02, 0.1)
            
            # 根据标记的重要性增加分数
            if memory.get("important", False):
                importance += 0.3
            
            # 确保重要性在0-1范围内
            importance = max(0.0, min(1.0, importance))
            
            # 添加重要性评分
            memory["importance"] = importance
        
        # 按重要性排序
        memories.sort(key=lambda x: x.get("importance", 0), reverse=True)
        
        return memories

    def _select_memories_to_consolidate(self, memories: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        选择要巩固的记忆
        
        参数:
            memories: 带有重要性评分的记忆列表
            
        返回:
            List[Dict[str, Any]]: 选择的记忆列表
        """
        # 获取重要性阈值
        importance_threshold = self.config.get("importance_threshold", 0.6)
        
        # 选择重要性超过阈值的记忆
        selected_memories = [memory for memory in memories if memory.get("importance", 0) >= importance_threshold]
        
        # 限制每次巩固的记忆数量
        max_consolidation = self.config.get("max_consolidation_per_run", 10)
        
        return selected_memories[:max_consolidation]

    def _consolidate_selected_memories(self, memories: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        巩固选择的记忆
        
        参数:
            memories: 选择的记忆列表
            
        返回:
            List[Dict[str, Any]]: 巩固后的记忆列表
        """
        consolidated_memories = []
        
        for memory in memories:
            try:
                # 标记记忆为已巩固
                memory["consolidated"] = True
                memory["consolidation_time"] = time.time()
                
                # 增加记忆强度
                strength = memory.get("strength", 0.5)
                memory["strength"] = min(strength + 0.2, 1.0)
                
                # 更新记忆
                updated = self.episodic_memory.update_memory(memory["id"], {
                    "consolidated": True,
                    "consolidation_time": memory["consolidation_time"],
                    "strength": memory["strength"]
                })
                
                if updated:
                    consolidated_memories.append(memory)
                    
            except Exception as e:
                logger.error_status(f"巩固记忆失败 (ID: {memory.get('id')}): {str(e)}")
        
        return consolidated_memories

    def _create_memory_associations(self, memories: List[Dict[str, Any]]) -> None:
        """
        创建记忆关联
        
        参数:
            memories: 巩固后的记忆列表
        """
        # 跳过空列表
        if not memories:
            return
            
        # 遍历所有记忆
        for memory in memories:
            # 标记记忆为已巩固
            self.episodic_memory.update_memory(memory["id"], {"consolidated": True})
            
            # 从记忆中提取关键信息
            memory_text = memory.get("content", "")
            if not memory_text:
                continue
                
            # 创建语义记忆
            semantic_id = self.semantic_memory.add_memory({
                "content": memory_text,
                "source": "consolidation",
                "source_id": memory["id"],
                "tags": memory.get("tags", []) + ["consolidated"]
            })
            
            # 创建情境记忆和语义记忆的关联
            self.episodic_memory.add_association(memory["id"], "semantic", semantic_id)
            
            # 创建与已有语义记忆的关联
            similar_semantics = self.semantic_memory.search_memories(memory_text, limit=5)
            for semantic in similar_semantics:
                if semantic["id"] != semantic_id:
                    self.semantic_memory.add_association(semantic_id, "semantic", semantic["id"])

    def _save_consolidation_history(self) -> None:
        """
        保存巩固历史
        """
        if not self.storage:
            logger.warning_status("存储管理器未初始化，无法保存巩固历史")
            return
            
        try:
            # 限制历史记录数量
            if len(self.consolidation_history) > 100:
                self.consolidation_history = self.consolidation_history[-100:]
                
            # 保存历史记录
            storage_key = f"memory:consolidation:history:{self.module_id}"
            self.storage.set(storage_key, json.dumps(self.consolidation_history))
            
        except Exception as e:
            logger.error_status(f"保存巩固历史记录失败: {str(e)}")

    def _get_next_consolidation_time(self) -> float:
        """
        获取下次巩固时间
        
        返回:
            float: 下次巩固时间
        """
        # 实现获取下次巩固时间的逻辑
        return time.time() + (24 * 60 * 60)  # 默认24小时后巩固一次

    def _subscribe_events(self) -> None:
        """
        订阅事件
        """
        if not self.event_bus:
            logger.warning_status("事件总线未初始化，无法订阅事件")
            return
            
        # 订阅日常维护事件
        self.event_bus.subscribe("system.daily_maintenance", self._on_daily_maintenance)
        
        # 订阅记忆创建事件
        self.event_bus.subscribe("memory.episodic.created", self._on_memory_created)

    def _unsubscribe_events(self) -> None:
        """
        取消事件订阅
        """
        if not self.event_bus:
            return
            
        # 取消日常维护事件订阅
        self.event_bus.unsubscribe("system.daily_maintenance", self._on_daily_maintenance)
        
        # 取消记忆创建事件订阅
        self.event_bus.unsubscribe("memory.episodic.created", self._on_memory_created)

    def _on_daily_maintenance(self, event_data: Dict[str, Any]) -> None:
        """
        处理日常维护事件
        
        参数:
            event_data: 事件数据
        """
        # 执行记忆巩固
        self.consolidate_memories()

    def _on_memory_created(self, event_data: Dict[str, Any]) -> None:
        """
        处理记忆创建事件
        
        参数:
            event_data: 事件数据
        """
        # 检查是否需要立即巩固
        memory = event_data.get("memory", {})
        if memory.get("important", False) and memory.get("immediate_consolidation", False):
            # 获取记忆ID
            memory_id = memory.get("id")
            if not memory_id:
                return
                
            # 获取完整记忆
            full_memory = self.episodic_memory.get_memory(memory_id)
            if not full_memory:
                return
                
            # 立即巩固这条重要记忆
            logger.info(f"立即巩固重要记忆: {memory_id}")
            self._consolidate_selected_memories([full_memory])

    def _load_consolidation_history(self) -> None:
        """
        加载巩固历史记录
        """
        if not self.storage:
            logger.warning_status("存储管理器未初始化，无法加载巩固历史")
            return
            
        try:
            # 加载历史记录
            storage_key = f"memory:consolidation:history:{self.module_id}"
            history_json = self.storage.get(storage_key)
            
            if history_json:
                self.consolidation_history = json.loads(history_json)
                
                # 如果有历史记录，更新上次巩固时间
                if self.consolidation_history:
                    self.last_consolidation_time = self.consolidation_history[-1]["timestamp"]
                    
        except Exception as e:
            logger.error_status(f"加载巩固历史记录失败: {str(e)}")

    def _consolidation_worker(self) -> None:
        """
        巩固线程函数
        """
        while self.consolidation_running:
            # 执行巩固逻辑
            self.consolidate_memories()
            time.sleep(self._get_next_consolidation_time() - time.time())

    def get_consolidation_history(self, limit: int = 10) -> List[Dict[str, Any]]:
        """
        获取巩固历史记录
        
        参数:
            limit: 获取的历史记录数量
            
        返回:
            List[Dict[str, Any]]: 巩固历史记录列表
        """
        # 返回最近的历史记录
        return self.consolidation_history[-limit:] if self.consolidation_history else []

    @staticmethod
    def get_instance(module_id: str = "memory_consolidation", config: Dict[str, Any] = None) -> 'MemoryConsolidation':
        """
        获取记忆巩固模块实例（单例模式）
        
        参数:
            module_id: 模块ID
            config: 配置参数
            
        返回:
            MemoryConsolidation: 记忆巩固模块实例
        """
        if not hasattr(MemoryConsolidation, "_instance") or MemoryConsolidation._instance is None:
            MemoryConsolidation._instance = MemoryConsolidation(module_id, config)
        
        return MemoryConsolidation._instance


def get_instance(module_id: str = "memory_consolidation", config: Dict[str, Any] = None) -> MemoryConsolidation:
    """
    获取记忆巩固模块实例
    
    参数:
        module_id: 模块ID
        config: 配置参数
        
    返回:
        MemoryConsolidation: 记忆巩固模块实例
    """
    return MemoryConsolidation.get_instance(module_id, config) 