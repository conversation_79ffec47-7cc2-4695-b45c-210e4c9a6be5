"""
记忆集成管理器
负责统一管理Redis记忆、MySQL关系数据和思维链路增强
优先使用Redis本地存储的中短期记忆，因为它们在AI调用的动态上下文中更实时有效
"""

import json
import time
from typing import Dict, List, Optional, Any, Tuple
from datetime import datetime, timedelta
import logging

from utilities.unified_logger import get_unified_logger, setup_unified_logging
from connectors.database.redis_cluster_connector import get_instance as get_redis_connector
from connectors.database.mysql_connector import get_instance as get_mysql_connector
from cognitive_modules.memory.episodic_memory import EpisodicMemory
from cognitive_modules.memory.memory_consolidation import MemoryConsolidation
from cognitive_modules.memory.semantic_memory import SemanticMemory

logger = get_unified_logger(__name__)

class MemoryIntegrationManager:
    """记忆集成管理器"""
    
    def __init__(self):
        self.redis_connector = None
        self.mysql_connector = None
        self.episodic_memory = None
        self.memory_consolidation = None
        self.semantic_memory = None
        self.is_initialized = False
        
        # 记忆权重配置
        self.memory_weights = {
            "redis_recent": 0.4,      # Redis最近记忆权重最高
            "redis_episodic": 0.3,    # Redis情景记忆
            "mysql_relationship": 0.2, # MySQL关系数据
            "vector_historical": 0.1   # 远程向量库历史记忆权重最低
        }
        
    async def initialize(self):
        """初始化记忆集成管理器"""
        try:
            # 初始化连接器
            self.redis_connector = get_redis_connector()
            self.mysql_connector = get_mysql_connector()
            
            # 初始化记忆模块
            self.episodic_memory = EpisodicMemory()
            self.memory_consolidation = MemoryConsolidation()
            self.semantic_memory = SemanticMemory()
            
            # 初始化记忆模块
            await self.episodic_memory.initialize()
            await self.memory_consolidation.initialize()
            await self.semantic_memory.initialize()
            
            self.is_initialized = True
            logger.info("✅ 记忆集成管理器初始化成功")
            
        except Exception as e:
            logger.error(f"❌ 记忆集成管理器初始化失败: {e}")
            raise
    
    async def get_integrated_user_memory(self, user_id: str) -> Dict:
        """
        获取用户的集成记忆
        按照权重优先级：Redis最近记忆 > Redis情景记忆 > MySQL关系数据 > 远程向量库
        """
        try:
            integrated_memory = {
                "user_id": user_id,
                "timestamp": time.time(),
                "redis_recent_memory": {},
                "redis_episodic_memory": {},
                "mysql_relationship_data": {},
                "vector_historical_memory": {},
                "consolidated_insights": {},
                "proactive_context": {}
            }
            
            # 1. 获取Redis最近记忆（最高权重）
            redis_recent = await self._get_redis_recent_memory(user_id)
            integrated_memory["redis_recent_memory"] = redis_recent
            
            # 2. 获取Redis情景记忆
            redis_episodic = await self._get_redis_episodic_memory(user_id)
            integrated_memory["redis_episodic_memory"] = redis_episodic
            
            # 3. 获取MySQL关系数据
            mysql_relationship = await self._get_mysql_relationship_data(user_id)
            integrated_memory["mysql_relationship_data"] = mysql_relationship
            
            # 4. 获取远程向量库历史记忆（最低权重）
            vector_historical = await self._get_vector_historical_memory(user_id)
            integrated_memory["vector_historical_memory"] = vector_historical
            
            # 5. 生成整合洞察
            consolidated_insights = await self._generate_consolidated_insights(integrated_memory)
            integrated_memory["consolidated_insights"] = consolidated_insights
            
            # 6. 生成主动表达上下文
            proactive_context = await self._generate_proactive_context(integrated_memory)
            integrated_memory["proactive_context"] = proactive_context
            
            logger.info(f"✅ 成功获取用户 {user_id} 的集成记忆")
            return integrated_memory
            
        except Exception as e:
            logger.error(f"❌ 获取用户集成记忆失败 (用户: {user_id}): {e}")
            return self._create_empty_memory(user_id)
    
    async def update_proactive_memory(self, user_id: str, proactive_data: Dict) -> bool:
        """
        更新主动表达相关的记忆
        """
        try:
            # 1. 更新Redis中的主动行为记录
            await self._update_redis_proactive_behavior(user_id, proactive_data)
            
            # 2. 更新MySQL中的用户关系数据
            await self._update_mysql_relationship_data(user_id, proactive_data)
            
            # 3. 触发记忆整合
            await self._trigger_memory_consolidation(user_id, proactive_data)
            
            logger.info(f"✅ 成功更新用户 {user_id} 的主动表达记忆")
            return True
            
        except Exception as e:
            logger.error(f"❌ 更新主动表达记忆失败 (用户: {user_id}): {e}")
            return False
    
    async def analyze_proactive_triggers(self, user_id: str) -> Dict:
        """
        分析主动表达触发器
        基于集成记忆分析用户的主动表达机会
        """
        try:
            # 获取集成记忆
            integrated_memory = await self.get_integrated_user_memory(user_id)
            
            # 分析触发器
            triggers = {
                "curiosity_triggers": [],
                "empathy_triggers": [],
                "creativity_triggers": [],
                "relationship_triggers": [],
                "timing_triggers": []
            }
            
            # 1. 好奇心触发器分析
            curiosity_triggers = await self._analyze_curiosity_triggers(integrated_memory)
            triggers["curiosity_triggers"] = curiosity_triggers
            
            # 2. 共情触发器分析
            empathy_triggers = await self._analyze_empathy_triggers(integrated_memory)
            triggers["empathy_triggers"] = empathy_triggers
            
            # 3. 创造力触发器分析
            creativity_triggers = await self._analyze_creativity_triggers(integrated_memory)
            triggers["creativity_triggers"] = creativity_triggers
            
            # 4. 关系触发器分析
            relationship_triggers = await self._analyze_relationship_triggers(integrated_memory)
            triggers["relationship_triggers"] = relationship_triggers
            
            # 5. 时机触发器分析
            timing_triggers = await self._analyze_timing_triggers(integrated_memory)
            triggers["timing_triggers"] = timing_triggers
            
            logger.info(f"✅ 成功分析用户 {user_id} 的主动表达触发器")
            return triggers
            
        except Exception as e:
            logger.error(f"❌ 分析主动表达触发器失败 (用户: {user_id}): {e}")
            return {}
    
    async def get_yanran_personality_context(self, user_id: str) -> Dict:
        """
        获取林嫣然人格化上下文
        基于用户关系和记忆生成符合林嫣然人设的表达上下文
        """
        try:
            # 获取集成记忆
            integrated_memory = await self.get_integrated_user_memory(user_id)
            
            # 获取用户关系数据
            relationship_data = integrated_memory.get("mysql_relationship_data", {})
            intimacy_level = relationship_data.get("relationship", {}).get("intimacy_level", 0)
            relationship_type = relationship_data.get("relationship", {}).get("relationship_type", "stranger")
            
            # 林嫣然人格化上下文
            yanran_context = {
                "personality_traits": {
                    "age": 23,
                    "profession": "财经自媒体创作者",
                    "personality": "开朗独立、善解人意、细腻敏感",
                    "communication_style": "温暖亲切、专业可靠",
                    "interests": ["财经分析", "投资理财", "生活分享", "情感交流"]
                },
                "relationship_context": {
                    "intimacy_level": intimacy_level,
                    "relationship_type": relationship_type,
                    "communication_boundary": self._get_communication_boundary(relationship_type),
                    "expression_style": self._get_expression_style(relationship_type),
                    "sharing_depth": self._get_sharing_depth(relationship_type)
                },
                "memory_context": {
                    "recent_interactions": integrated_memory.get("redis_recent_memory", {}),
                    "emotional_state": self._extract_emotional_context(integrated_memory),
                    "shared_experiences": self._extract_shared_experiences(integrated_memory),
                    "user_preferences": self._extract_user_preferences(integrated_memory)
                },
                "proactive_guidelines": {
                    "max_daily_proactive": self._get_max_daily_proactive(relationship_type),
                    "preferred_topics": self._get_preferred_topics(relationship_type),
                    "timing_preferences": self._get_timing_preferences(integrated_memory),
                    "boundary_considerations": self._get_boundary_considerations(relationship_type)
                }
            }
            
            logger.info(f"✅ 成功获取用户 {user_id} 的林嫣然人格化上下文")
            return yanran_context
            
        except Exception as e:
            logger.error(f"❌ 获取林嫣然人格化上下文失败 (用户: {user_id}): {e}")
            return {}
    
    # ==================== 私有方法 ====================
    
    async def _get_redis_recent_memory(self, user_id: str) -> Dict:
        """获取Redis最近记忆"""
        try:
            if not self.redis_connector:
                return {}
            
            recent_memory = {}
            
            # 获取最近对话记录
            recent_conversations_key = f"recent_conversations:{user_id}"
            recent_conversations = self.redis_connector.lrange(recent_conversations_key, 0, 9)  # 最近10条
            
            if recent_conversations:
                recent_memory["conversations"] = [json.loads(conv) for conv in recent_conversations]
            
            # 获取最近情绪状态
            emotion_key = f"user_emotion:{user_id}"
            emotion_data = self.redis_connector.get(emotion_key)
            if emotion_data:
                recent_memory["emotion"] = json.loads(emotion_data)
            
            # 获取最近活动记录
            activity_key = f"user_activity:{user_id}"
            activity_data = self.redis_connector.get(activity_key)
            if activity_data:
                recent_memory["activity"] = json.loads(activity_data)
            
            return recent_memory
            
        except Exception as e:
            logger.error(f"❌ 获取Redis最近记忆失败: {e}")
            return {}
    
    async def _get_redis_episodic_memory(self, user_id: str) -> Dict:
        """获取Redis情景记忆"""
        try:
            if not self.episodic_memory:
                return {}
            
            # 获取用户的情景记忆
            episodic_data = await self.episodic_memory.get_user_episodes(user_id, limit=20)
            
            return {
                "episodes": episodic_data,
                "episode_count": len(episodic_data),
                "latest_episode_time": episodic_data[0].get("timestamp") if episodic_data else None
            }
            
        except Exception as e:
            logger.error(f"❌ 获取Redis情景记忆失败: {e}")
            return {}
    
    async def _get_mysql_relationship_data(self, user_id: str) -> Dict:
        """获取MySQL关系数据"""
        try:
            if not self.mysql_connector:
                return {}
            
            # 查询用户关系数据
            relationship_query = """
            SELECT 
                aur.user_id,
                aur.intimacy_level,
                aur.relationship_type,
                aur.emotional_weight,
                aur.trust_level,
                aur.interaction_frequency,
                aur.last_interaction_time,
                aur.created_at,
                aur.updated_at
            FROM ai_user_relationships aur
            WHERE aur.user_id = %s
            """
            
            success, relationship_result, error = self.mysql_connector.query_one(relationship_query, (user_id,))
            
            # 查询用户交互统计
            stats_query = """
            SELECT 
                uis.user_id,
                uis.days_known,
                uis.total_interactions,
                uis.avg_daily_interactions,
                uis.last_emotion_intensity,
                uis.conversation_depth_avg,
                uis.response_time_avg,
                uis.proactive_interaction_count,
                uis.last_updated
            FROM user_interaction_stats uis
            WHERE uis.user_id = %s
            """
            
            success2, stats_result, error2 = self.mysql_connector.query_one(stats_query, (user_id,))
            
            mysql_data = {}
            if success and relationship_result:
                mysql_data["relationship"] = dict(relationship_result)
            
            if success2 and stats_result:
                mysql_data["stats"] = dict(stats_result)
            
            return mysql_data
            
        except Exception as e:
            logger.error(f"❌ 获取MySQL关系数据失败: {e}")
            return {}
    
    async def _get_vector_historical_memory(self, user_id: str) -> Dict:
        """获取远程向量库历史记忆"""
        try:
            # 这里先返回空数据，后续可以集成向量库查询
            # 由于向量库查询相对较慢，权重设置最低
            return {
                "historical_conversations": [],
                "semantic_memories": [],
                "knowledge_base": []
            }
            
        except Exception as e:
            logger.error(f"❌ 获取远程向量库历史记忆失败: {e}")
            return {}
    
    async def _generate_consolidated_insights(self, integrated_memory: Dict) -> Dict:
        """生成整合洞察"""
        try:
            insights = {
                "user_activity_pattern": {},
                "emotional_trends": {},
                "interaction_preferences": {},
                "relationship_development": {},
                "proactive_opportunities": {}
            }
            
            # 分析用户活动模式
            redis_recent = integrated_memory.get("redis_recent_memory", {})
            if redis_recent.get("activity"):
                insights["user_activity_pattern"] = self._analyze_activity_pattern(redis_recent["activity"])
            
            # 分析情绪趋势
            if redis_recent.get("emotion"):
                insights["emotional_trends"] = self._analyze_emotional_trends(redis_recent["emotion"])
            
            # 分析互动偏好
            mysql_data = integrated_memory.get("mysql_relationship_data", {})
            if mysql_data.get("stats"):
                insights["interaction_preferences"] = self._analyze_interaction_preferences(mysql_data["stats"])
            
            # 分析关系发展
            if mysql_data.get("relationship"):
                insights["relationship_development"] = self._analyze_relationship_development(mysql_data["relationship"])
            
            return insights
            
        except Exception as e:
            logger.error(f"❌ 生成整合洞察失败: {e}")
            return {}
    
    async def _generate_proactive_context(self, integrated_memory: Dict) -> Dict:
        """生成主动表达上下文"""
        try:
            proactive_context = {
                "optimal_timing": {},
                "suitable_topics": [],
                "expression_style": {},
                "boundary_considerations": {},
                "expected_response_patterns": {}
            }
            
            # 基于集成记忆生成主动表达上下文
            mysql_data = integrated_memory.get("mysql_relationship_data", {})
            relationship_type = mysql_data.get("relationship", {}).get("relationship_type", "stranger")
            
            # 设置最佳时机
            proactive_context["optimal_timing"] = {
                "preferred_hours": [10, 14, 19],  # 上午10点、下午2点、晚上7点
                "avoid_hours": [23, 0, 1, 2, 3, 4, 5, 6, 7, 8],  # 深夜到早晨
                "last_interaction_gap": 120  # 最小间隔2小时
            }
            
            # 设置合适话题
            proactive_context["suitable_topics"] = self._get_suitable_topics(relationship_type)
            
            # 设置表达风格
            proactive_context["expression_style"] = self._get_expression_style(relationship_type)
            
            return proactive_context
            
        except Exception as e:
            logger.error(f"❌ 生成主动表达上下文失败: {e}")
            return {}
    
    def _create_empty_memory(self, user_id: str) -> Dict:
        """创建空记忆结构"""
        return {
            "user_id": user_id,
            "timestamp": time.time(),
            "redis_recent_memory": {},
            "redis_episodic_memory": {},
            "mysql_relationship_data": {},
            "vector_historical_memory": {},
            "consolidated_insights": {},
            "proactive_context": {}
        }
    
    async def _update_redis_proactive_behavior(self, user_id: str, proactive_data: Dict):
        """更新Redis主动行为记录"""
        try:
            if not self.redis_connector:
                return
            
            # 记录主动行为
            behavior_key = f"proactive_behavior:{user_id}"
            self.redis_connector.lpush(behavior_key, json.dumps(proactive_data))
            self.redis_connector.ltrim(behavior_key, 0, 99)  # 保持最近100条
            
            # 更新今日计数
            today = datetime.now().strftime("%Y-%m-%d")
            count_key = f"daily_proactive_count:{user_id}:{today}"
            self.redis_connector.incr(count_key)
            self.redis_connector.expire(count_key, 86400)  # 24小时过期
            
        except Exception as e:
            logger.error(f"❌ 更新Redis主动行为记录失败: {e}")
    
    async def _update_mysql_relationship_data(self, user_id: str, proactive_data: Dict):
        """更新MySQL关系数据"""
        try:
            if not self.mysql_connector:
                return
            
            # 更新用户关系表
            update_query = """
            UPDATE ai_user_relationships 
            SET 
                last_interaction_time = NOW(),
                interaction_frequency = interaction_frequency + 1,
                updated_at = NOW()
            WHERE user_id = %s
            """
            
            success, result, error = self.mysql_connector.execute_update(update_query, (user_id,))
            if not success:
                logger.error_status(f"更新记忆数据失败: {error}")
            
            # 更新用户交互统计
            stats_update_query = """
            UPDATE user_interaction_stats 
            SET 
                proactive_interaction_count = proactive_interaction_count + 1,
                last_updated = NOW()
            WHERE user_id = %s
            """
            
            success, result, error = self.mysql_connector.execute_update(stats_update_query, (user_id,))
            if not success:
                logger.error_status(f"更新统计数据失败: {error}")
            
        except Exception as e:
            logger.error(f"❌ 更新MySQL关系数据失败: {e}")
    
    async def _trigger_memory_consolidation(self, user_id: str, proactive_data: Dict):
        """触发记忆整合"""
        try:
            if not self.memory_consolidation:
                return
            
            # 触发记忆整合过程
            await self.memory_consolidation.consolidate_proactive_memory(user_id, proactive_data)
            
        except Exception as e:
            logger.error(f"❌ 触发记忆整合失败: {e}")
    
    # 分析方法
    async def _analyze_curiosity_triggers(self, integrated_memory: Dict) -> List[Dict]:
        """分析好奇心触发器"""
        triggers = []
        
        # 基于用户最近活动分析好奇心触发点
        recent_memory = integrated_memory.get("redis_recent_memory", {})
        conversations = recent_memory.get("conversations", [])
        
        for conv in conversations[-3:]:  # 最近3次对话
            if "?" in conv.get("user_message", ""):
                triggers.append({
                    "type": "user_question_followup",
                    "score": 0.7,
                    "context": conv.get("user_message", "")[:100]
                })
        
        return triggers
    
    async def _analyze_empathy_triggers(self, integrated_memory: Dict) -> List[Dict]:
        """分析共情触发器"""
        triggers = []
        
        # 基于用户情绪状态分析共情触发点
        recent_memory = integrated_memory.get("redis_recent_memory", {})
        emotion_data = recent_memory.get("emotion", {})
        
        if emotion_data.get("current_emotion") in ["sad", "angry", "anxious"]:
            triggers.append({
                "type": "emotional_support",
                "score": 0.8,
                "context": f"检测到用户情绪: {emotion_data.get('current_emotion')}"
            })
        
        return triggers
    
    async def _analyze_creativity_triggers(self, integrated_memory: Dict) -> List[Dict]:
        """分析创造力触发器"""
        triggers = []
        
        # 基于用户兴趣和话题分析创造力触发点
        mysql_data = integrated_memory.get("mysql_relationship_data", {})
        relationship_type = mysql_data.get("relationship", {}).get("relationship_type", "stranger")
        
        if relationship_type in ["friend", "close_friend", "best_friend"]:
            triggers.append({
                "type": "creative_sharing",
                "score": 0.6,
                "context": "可以分享创意想法或有趣内容"
            })
        
        return triggers
    
    async def _analyze_relationship_triggers(self, integrated_memory: Dict) -> List[Dict]:
        """分析关系触发器"""
        triggers = []
        
        # 基于关系发展状态分析关系触发点
        mysql_data = integrated_memory.get("mysql_relationship_data", {})
        relationship_data = mysql_data.get("relationship", {})
        
        last_interaction = relationship_data.get("last_interaction_time")
        if last_interaction:
            # 计算距离上次互动的时间
            time_diff = datetime.now() - last_interaction
            if time_diff.total_seconds() > 24 * 3600:  # 超过24小时
                triggers.append({
                    "type": "relationship_maintenance",
                    "score": 0.7,
                    "context": f"距离上次互动已{time_diff.days}天"
                })
        
        return triggers
    
    async def _analyze_timing_triggers(self, integrated_memory: Dict) -> List[Dict]:
        """分析时机触发器"""
        triggers = []
        
        # 基于用户活动模式分析最佳时机
        current_hour = datetime.now().hour
        
        if 10 <= current_hour <= 11 or 14 <= current_hour <= 15 or 19 <= current_hour <= 20:
            triggers.append({
                "type": "optimal_timing",
                "score": 0.8,
                "context": f"当前时间{current_hour}点是较好的主动时机"
            })
        
        return triggers
    
    # 辅助方法
    def _get_communication_boundary(self, relationship_type: str) -> Dict:
        """获取沟通边界"""
        boundaries = {
            "stranger": {"frequency": "very_low", "depth": "shallow", "topics": "general"},
            "acquaintance": {"frequency": "low", "depth": "light", "topics": "common"},
            "friend": {"frequency": "moderate", "depth": "medium", "topics": "personal"},
            "close_friend": {"frequency": "high", "depth": "deep", "topics": "intimate"},
            "best_friend": {"frequency": "very_high", "depth": "very_deep", "topics": "all"}
        }
        return boundaries.get(relationship_type, boundaries["stranger"])
    
    def _get_expression_style(self, relationship_type: str) -> Dict:
        """获取表达风格"""
        styles = {
            "stranger": {"tone": "polite", "formality": "formal", "emoji": "minimal"},
            "acquaintance": {"tone": "friendly", "formality": "semi_formal", "emoji": "some"},
            "friend": {"tone": "warm", "formality": "casual", "emoji": "moderate"},
            "close_friend": {"tone": "affectionate", "formality": "very_casual", "emoji": "frequent"},
            "best_friend": {"tone": "intimate", "formality": "very_casual", "emoji": "abundant"}
        }
        return styles.get(relationship_type, styles["stranger"])
    
    def _get_sharing_depth(self, relationship_type: str) -> str:
        """获取分享深度"""
        depths = {
            "stranger": "surface",
            "acquaintance": "light",
            "friend": "moderate",
            "close_friend": "deep",
            "best_friend": "intimate"
        }
        return depths.get(relationship_type, "surface")
    
    def _get_max_daily_proactive(self, relationship_type: str) -> int:
        """获取每日最大主动次数"""
        limits = {
            "stranger": 0,
            "acquaintance": 1,
            "friend": 2,
            "close_friend": 3,
            "best_friend": 4
        }
        return limits.get(relationship_type, 0)
    
    def _get_preferred_topics(self, relationship_type: str) -> List[str]:
        """获取偏好话题"""
        topics = {
            "stranger": ["天气", "新闻", "一般问候"],
            "acquaintance": ["工作", "生活", "兴趣爱好"],
            "friend": ["个人经历", "感受分享", "未来计划"],
            "close_friend": ["深度话题", "情感交流", "私人想法"],
            "best_friend": ["一切话题", "内心世界", "秘密分享"]
        }
        return topics.get(relationship_type, topics["stranger"])
    
    def _get_timing_preferences(self, integrated_memory: Dict) -> Dict:
        """获取时机偏好"""
        return {
            "preferred_hours": [10, 14, 19],
            "avoid_hours": [23, 0, 1, 2, 3, 4, 5, 6, 7, 8],
            "min_interval_hours": 2
        }
    
    def _get_boundary_considerations(self, relationship_type: str) -> List[str]:
        """获取边界考虑"""
        considerations = {
            "stranger": ["保持礼貌距离", "避免过于私人", "尊重隐私"],
            "acquaintance": ["适度关心", "避免过于深入", "保持友好"],
            "friend": ["真诚关怀", "适度分享", "尊重个人空间"],
            "close_friend": ["深度关怀", "坦诚交流", "互相支持"],
            "best_friend": ["无话不谈", "深度陪伴", "完全信任"]
        }
        return considerations.get(relationship_type, considerations["stranger"])
    
    def _extract_emotional_context(self, integrated_memory: Dict) -> Dict:
        """提取情绪上下文"""
        recent_memory = integrated_memory.get("redis_recent_memory", {})
        emotion_data = recent_memory.get("emotion", {})
        return {
            "current_emotion": emotion_data.get("current_emotion", "neutral"),
            "emotion_intensity": emotion_data.get("intensity", 0.5),
            "emotion_trend": emotion_data.get("trend", "stable")
        }
    
    def _extract_shared_experiences(self, integrated_memory: Dict) -> List[Dict]:
        """提取共同经历"""
        episodic_memory = integrated_memory.get("redis_episodic_memory", {})
        episodes = episodic_memory.get("episodes", [])
        return episodes[-5:]  # 最近5个共同经历
    
    def _extract_user_preferences(self, integrated_memory: Dict) -> Dict:
        """提取用户偏好"""
        mysql_data = integrated_memory.get("mysql_relationship_data", {})
        stats = mysql_data.get("stats", {})
        return {
            "avg_response_time": stats.get("response_time_avg", 300),
            "conversation_depth": stats.get("conversation_depth_avg", 0.5),
            "interaction_frequency": stats.get("avg_daily_interactions", 1)
        }
    
    def _analyze_activity_pattern(self, activity_data: Dict) -> Dict:
        """分析活动模式"""
        return {
            "most_active_hours": [10, 14, 19],
            "least_active_hours": [23, 0, 1, 2, 3, 4, 5, 6, 7, 8],
            "activity_trend": "stable"
        }
    
    def _analyze_emotional_trends(self, emotion_data: Dict) -> Dict:
        """分析情绪趋势"""
        return {
            "dominant_emotion": emotion_data.get("current_emotion", "neutral"),
            "emotion_stability": emotion_data.get("stability", 0.7),
            "recent_changes": []
        }
    
    def _analyze_interaction_preferences(self, stats_data: Dict) -> Dict:
        """分析互动偏好"""
        return {
            "preferred_response_time": stats_data.get("response_time_avg", 300),
            "conversation_depth_preference": stats_data.get("conversation_depth_avg", 0.5),
            "interaction_frequency_preference": stats_data.get("avg_daily_interactions", 1)
        }
    
    def _analyze_relationship_development(self, relationship_data: Dict) -> Dict:
        """分析关系发展"""
        return {
            "current_stage": relationship_data.get("relationship_type", "stranger"),
            "intimacy_level": relationship_data.get("intimacy_level", 0),
            "trust_level": relationship_data.get("trust_level", 0.5),
            "development_trend": "stable"
        }
    
    def _get_suitable_topics(self, relationship_type: str) -> List[str]:
        """获取合适话题"""
        return self._get_preferred_topics(relationship_type)


# 全局实例
_memory_integration_manager_instance = None

def get_memory_integration_manager() -> MemoryIntegrationManager:
    """获取记忆集成管理器实例"""
    global _memory_integration_manager_instance
    if _memory_integration_manager_instance is None:
        _memory_integration_manager_instance = MemoryIntegrationManager()
    return _memory_integration_manager_instance 