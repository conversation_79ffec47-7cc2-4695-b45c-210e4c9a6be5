"""
语义记忆模块

负责存储和检索知识性信息，包括事实、概念和常识。
从legacy系统的memory_system演化而来，增加了更强的语义检索能力。
"""

import os
import json
import time
from utilities.unified_logger import get_unified_logger, setup_unified_logging
import datetime
from typing import Dict, Any, List, Optional, Tuple

try:
    import chromadb
    from chromadb.config import Settings
except ImportError:
    chromadb = None

# 配置日志
setup_unified_logging()
logger = get_unified_logger(__name__)

class SemanticMemory:
    """
    语义记忆模块类
    
    负责存储和检索知识性信息，包括：
    - 事实性知识（事件、地点、人物等）
    - 概念性知识（概念、定义、分类等）
    - 常识性知识（一般规则、常识等）
    """
    
    _instance = None
    _lock = None
    
    def __new__(cls, *args, **kwargs):
        if cls._lock is None:
            import threading
            cls._lock = threading.Lock()
            
        with cls._lock:
            if cls._instance is None:
                cls._instance = super().__new__(cls)
        return cls._instance
    
    def __init__(self, data_dir=None):
        """
        初始化语义记忆
        
        Args:
            data_dir: 数据目录路径
        """
        # 设置数据目录
        self.data_dir = data_dir or os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))), "data", "memory")
        
        # 知识分类及其子分类
        self.knowledge_categories = {
            "人物": ["家人", "朋友", "同事", "名人", "其他"],
            "地点": ["家", "工作", "娱乐", "旅游", "其他"],
            "概念": ["学术", "技术", "哲学", "文化", "其他"],
            "事件": ["个人", "社交", "工作", "新闻", "其他"],
            "规则": ["社会规范", "道德准则", "法律法规", "个人规则", "其他"],
            "常识": ["日常知识", "科学事实", "文化常识", "实用技巧", "其他"],
            "兴趣": ["爱好", "喜好", "偏好", "特长", "其他"],
            "关系": ["家庭", "友谊", "恋爱", "同事", "其他"]
        }
        
        # 🔥 使用统一缓存管理器
        from utilities.cache_manager import get_cache_manager
        self.cache_manager = get_cache_manager()
        self.cache_expiry = 3600  # 缓存有效期1小时
        
        # 集合字典
        self.collections = {}
        
        # 初始化文件存储
        self._initialize_vector_db()
    
    def _initialize_vector_db(self):
        """初始化语义记忆存储"""
        logger.data_status("按照老板要求，语义记忆只使用文件存储，不使用向量数据库")
        # 确保数据目录存在
        os.makedirs(self.data_dir, exist_ok=True)
        
        # 确保每个分类的目录都存在
        for category in self.knowledge_categories.keys():
            category_dir = os.path.join(self.data_dir, category)
            os.makedirs(category_dir, exist_ok=True)
            logger.info(f"已创建分类目录: {category}")
        
        logger.success("文件存储初始化完成")
    
    def add_memory(self, content: str, metadata: Dict[str, Any] = None) -> str:
        """
        添加语义记忆
        
        Args:
            content: 记忆内容
            metadata: 记忆元数据，包括category(类别)、importance(重要性)等
            
        Returns:
            记忆ID
        """
        if not metadata:
            metadata = {}
        
        # 生成记忆ID
        memory_id = f"mem_{int(time.time())}_{hash(content) % 10000:04d}"
        
        # 准备记忆数据
        timestamp = datetime.datetime.now().isoformat()
        category = metadata.get('category', '常识')
        subcategory = metadata.get('subcategory', '日常知识')
        importance = metadata.get('importance', 5)  # 1-10的重要性
        
        memory_data = {
            "id": memory_id,
            "content": content,
            "category": category,
            "subcategory": subcategory,
            "importance": importance,
            "created_at": timestamp,
            "accessed_at": timestamp,
            "access_count": 0,
            "metadata": metadata
        }
        
        # 存储到文件
        self._save_memory_to_file(memory_data)
        logger.info(f"记忆已保存到文件: {memory_id}")
        
        # 🔥 更新统一缓存
        cache_key = f"id:{memory_id}"
        self.cache_manager.set("semantic_memory", cache_key, memory_data, self.cache_expiry)
        
        return memory_id
    
    def _save_memory_to_file(self, memory_data: Dict[str, Any]) -> bool:
        """保存记忆到文件"""
        memory_id = memory_data["id"]
        category = memory_data["category"]
        
        # 创建分类目录
        category_dir = os.path.join(self.data_dir, category)
        os.makedirs(category_dir, exist_ok=True)
        
        # 保存到文件
        file_path = os.path.join(category_dir, f"{memory_id}.json")
        try:
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(memory_data, f, ensure_ascii=False, indent=2)
            return True
        except Exception as e:
            logger.error_status(f"保存记忆到文件失败: {e}")
            return False
    
    def _load_memory_from_file(self, memory_id: str, category: str = None) -> Optional[Dict[str, Any]]:
        """从文件加载记忆"""
        # 如果没有指定分类，尝试所有分类
        if not category:
            for cat in self.knowledge_categories.keys():
                file_path = os.path.join(self.data_dir, cat, f"{memory_id}.json")
                if os.path.exists(file_path):
                    category = cat
                    break
        
        if not category:
            logger.warning_status(f"找不到记忆: {memory_id}")
            return None
        
        # 加载文件
        file_path = os.path.join(self.data_dir, category, f"{memory_id}.json")
        try:
            if os.path.exists(file_path):
                with open(file_path, 'r', encoding='utf-8') as f:
                    memory_data = json.load(f)
                return memory_data
            else:
                logger.warning_status(f"记忆文件不存在: {file_path}")
                return None
        except Exception as e:
            logger.error_status(f"加载记忆文件失败: {e}")
            return None
    
    def get_memory(self, memory_id: str) -> Optional[Dict[str, Any]]:
        """
        获取特定记忆
        
        Args:
            memory_id: 记忆ID
            
        Returns:
            记忆数据
        """
        # 🔥 检查统一缓存
        cache_key = f"id:{memory_id}"
        memory_data = self.cache_manager.get("semantic_memory", cache_key)
        if memory_data is not None:
            self.cache_hits += 1
            
            # 更新访问信息
            memory_data["accessed_at"] = datetime.datetime.now().isoformat()
            memory_data["access_count"] += 1
            
            # 更新文件和缓存
            self._save_memory_to_file(memory_data)
            self.cache_manager.set("semantic_memory", cache_key, memory_data, self.cache_expiry)
            
            return memory_data
        
        # 缓存未命中，从文件加载
        self.cache_misses += 1
        memory_data = self._load_memory_from_file(memory_id)
        
        if memory_data:
            # 更新访问信息
            memory_data["accessed_at"] = datetime.datetime.now().isoformat()
            memory_data["access_count"] += 1
            
            # 更新文件
            self._save_memory_to_file(memory_data)
            
            # 🔥 更新统一缓存
            self.cache_manager.set("semantic_memory", cache_key, memory_data, self.cache_expiry)
        
        return memory_data
    
    def search_by_content(self, query: str, categories: List[str] = None, limit: int = 10) -> List[Dict[str, Any]]:
        """
        按内容搜索记忆
        
        Args:
            query: 搜索查询
            categories: 要搜索的分类列表
            limit: 返回结果数量限制
            
        Returns:
            匹配的记忆列表
        """
        # 默认搜索所有分类
        if not categories:
            categories = list(self.knowledge_categories.keys())
        
        # 存储搜索结果
        results = []
        
        # 使用文本搜索
        logger.info(f"使用文本搜索: {query}")
        try:
            # 遍历data_dir下的所有JSON文件
            all_files = []
            for root, dirs, files in os.walk(self.data_dir):
                for file in files:
                    if file.endswith('.json') and file.startswith('mem_'):
                        all_files.append(os.path.join(root, file))
            
            logger.info(f"找到 {len(all_files)} 个记忆文件")
            
            # 搜索所有文件
            for file_path in all_files:
                try:
                    with open(file_path, 'r', encoding='utf-8') as f:
                        memory_data = json.load(f)
                    
                    # 确保是有效的记忆数据
                    if not isinstance(memory_data, dict) or "content" not in memory_data:
                        continue
                    
                    # 检查类别是否匹配
                    if "category" in memory_data and memory_data["category"] not in categories:
                        continue
                    
                    # 检查内容是否匹配查询
                    content = memory_data.get("content", "")
                    if query.lower() in content.lower():
                        # 计算相关性
                        relevance = 0.0
                        
                        # 完全匹配得分更高
                        if query.lower() == content.lower():
                            relevance = 1.0
                        else:
                            # 根据匹配度计算相关性
                            query_len = len(query)
                            content_len = len(content)
                            if content_len > 0:
                                # 计算匹配比例
                                query_count = content.lower().count(query.lower())
                                if query_count > 0:
                                    match_percent = (query_count * query_len) / content_len
                                    relevance = min(0.9, match_percent + 0.2)  # 最高0.9
                        
                        # 添加相关性到记忆数据
                        memory_data["relevance"] = relevance
                        results.append(memory_data)
                except Exception as e:
                    logger.error_status(f"读取文件失败 {file_path}: {e}")
            
            # 按相关性排序
            results.sort(key=lambda x: x.get("relevance", 0), reverse=True)
            
            # 去除重复结果（基于ID）
            unique_results = []
            seen_ids = set()
            for result in results:
                result_id = result.get("id")
                if result_id and result_id not in seen_ids:
                    seen_ids.add(result_id)
                    unique_results.append(result)
            
            # 限制结果数量
            return unique_results[:limit]
            
        except Exception as e:
            logger.error_status(f"文本搜索失败: {e}")
            import traceback
            logger.error_status(traceback.format_exc())
            return []
    
    def delete_memory(self, memory_id: str) -> bool:
        """
        删除记忆
        
        Args:
            memory_id: 记忆ID
            
        Returns:
            是否删除成功
        """
        # 获取记忆数据，以确定分类
        memory_data = self.get_memory(memory_id)
        if not memory_data:
            logger.warning_status(f"要删除的记忆不存在: {memory_id}")
            return False
        
        category = memory_data["category"]
        
        # 从文件系统删除
        file_path = os.path.join(self.data_dir, category, f"{memory_id}.json")
        try:
            if os.path.exists(file_path):
                os.remove(file_path)
                logger.info(f"已从文件系统删除记忆: {memory_id}")
            else:
                logger.warning_status(f"记忆文件不存在: {file_path}")
        except Exception as e:
            logger.error_status(f"删除记忆文件失败: {e}")
            return False
        
        # 从缓存中删除
        cache_key = f"id:{memory_id}"
        if cache_key in self.cache:
            del self.cache[cache_key]
        
        return True
    
    def update_memory(self, memory_id: str, content: str = None, metadata: Dict[str, Any] = None) -> bool:
        """
        更新记忆
        
        Args:
            memory_id: 记忆ID
            content: 新的记忆内容
            metadata: 新的元数据
            
        Returns:
            是否更新成功
        """
        # 获取现有记忆
        memory_data = self.get_memory(memory_id)
        if not memory_data:
            logger.warning_status(f"要更新的记忆不存在: {memory_id}")
            return False
        
        # 更新内容
        if content is not None:
            memory_data["content"] = content
        
        # 更新元数据
        if metadata:
            # 更新顶级元数据
            for key, value in metadata.items():
                if key in ["category", "subcategory", "importance"]:
                    memory_data[key] = value
            
            # 更新嵌套元数据
            if "metadata" not in memory_data:
                memory_data["metadata"] = {}
            
            memory_data["metadata"].update(metadata)
        
        # 更新时间戳
        memory_data["updated_at"] = datetime.datetime.now().isoformat()
        
        # 保存到文件
        self._save_memory_to_file(memory_data)
        
        # 更新向量数据库
        category = memory_data["category"]
        if self.chroma_client and category in self.collections and content is not None:
            try:
                # 先删除旧记录
                collection = self.collections[category]
                collection.delete(ids=[memory_id])
                
                # 添加新记录
                collection.add(
                    ids=[memory_id],
                    documents=[content],
                    metadatas=[{
                        "category": memory_data["category"],
                        "subcategory": memory_data["subcategory"],
                        "importance": str(memory_data["importance"]),
                        "created_at": memory_data["created_at"],
                        "updated_at": memory_data["updated_at"]
                    }]
                )
                logger.data_status(f"已更新向量数据库中的记忆: {memory_id}")
            except Exception as e:
                logger.error_status(f"更新向量数据库中的记忆失败: {e}")
        
        # 更新缓存
        cache_key = f"id:{memory_id}"
        self.cache[cache_key] = {
            "data": memory_data,
            "expiry": time.time() + self.cache_expiry
        }
        
        return True
    
    def get_categories(self) -> Dict[str, List[str]]:
        """
        获取所有知识分类
        
        Returns:
            知识分类字典
        """
        return self.knowledge_categories
    
    def get_all_memories_by_category(self, category: str) -> List[Dict[str, Any]]:
        """
        获取指定分类的所有记忆
        
        Args:
            category: 分类名称
            
        Returns:
            记忆列表
        """
        results = []
        
        category_dir = os.path.join(self.data_dir, category)
        if not os.path.exists(category_dir):
            logger.warning_status(f"分类目录不存在: {category}")
            return results
        
        # 遍历分类下的所有文件
        for filename in os.listdir(category_dir):
            if not filename.endswith('.json'):
                continue
                
            file_path = os.path.join(category_dir, filename)
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    memory_data = json.load(f)
                results.append(memory_data)
            except Exception as e:
                logger.error_status(f"读取文件失败 {file_path}: {e}")
        
        # 按重要性和访问次数排序
        results.sort(key=lambda x: (x.get("importance", 0), x.get("access_count", 0)), reverse=True)
        
        return results
    
    def get_memory_stats(self) -> Dict[str, Any]:
        """
        获取记忆统计信息
        
        Returns:
            统计信息字典
        """
        stats = {
            "total_memories": 0,
            "by_category": {},
            "by_importance": {
                "low": 0,      # 1-3
                "medium": 0,   # 4-7
                "high": 0      # 8-10
            },
            "cache": {
                "size": self.cache_manager.get_cache_stats("semantic_memory").get("size", 0),
                "hits": self.cache_hits,
                "misses": self.cache_misses,
                "hit_ratio": self.cache_hits / (self.cache_hits + self.cache_misses) if (self.cache_hits + self.cache_misses) > 0 else 0
            }
        }
        
        # 统计各分类记忆数量
        for category in self.knowledge_categories.keys():
            category_dir = os.path.join(self.data_dir, category)
            if os.path.exists(category_dir):
                # 计算json文件数量
                count = len([f for f in os.listdir(category_dir) if f.endswith('.json')])
                stats["by_category"][category] = count
                stats["total_memories"] += count
        
        # 统计不同重要性级别的记忆
        for category in self.knowledge_categories.keys():
            category_dir = os.path.join(self.data_dir, category)
            if not os.path.exists(category_dir):
                continue
                
            # 遍历分类下的所有文件
            for filename in os.listdir(category_dir):
                if not filename.endswith('.json'):
                    continue
                    
                file_path = os.path.join(category_dir, filename)
                try:
                    with open(file_path, 'r', encoding='utf-8') as f:
                        memory_data = json.load(f)
                    
                    importance = memory_data.get("importance", 5)
                    if 1 <= importance <= 3:
                        stats["by_importance"]["low"] += 1
                    elif 4 <= importance <= 7:
                        stats["by_importance"]["medium"] += 1
                    elif 8 <= importance <= 10:
                        stats["by_importance"]["high"] += 1
                except Exception:
                    continue
        
        return stats
    
    def clear_cache(self):
        """清空缓存"""
        self.cache_manager.clear("semantic_memory")
        logger.info("已清空语义记忆缓存")
    
    def rebuild_vector_db(self) -> bool:
        """
        重建向量数据库
        重新加载所有记忆并更新到向量数据库
        
        Returns:
            是否成功重建
        """
        try:
            if not self.chroma_client or not self.chroma_client.is_available:
                logger.error_status("向量数据库不可用，无法重建")
                return False
            
            logger.data_status("开始重建向量数据库")
            
            # 删除并重新创建集合
            for category in self.knowledge_categories.keys():
                # 使用映射表将中文类别转换为英文，避免中文问题
                category_map = {
                    "人物": "person",
                    "地点": "place",
                    "概念": "concept",
                    "事件": "event",
                    "规则": "rule",
                    "常识": "common",
                    "兴趣": "interest",
                    "关系": "relation"
                }
                
                # 使用英文名作为集合名
                collection_name = f"semantic_{category_map.get(category, 'general')}"
                
                try:
                    # 删除集合
                    self.chroma_client.delete_collection(collection_name)
                    logger.info(f"已删除向量集合: {collection_name}")
                except Exception as e:
                    logger.warning_status(f"删除向量集合失败: {collection_name}, 错误: {e}")
                
                try:
                    # 重新创建集合
                    metadata = {"category": category}
                    collection = self.chroma_client.get_or_create_collection(collection_name, metadata=metadata)
                    self.collections[category] = collection
                    logger.info(f"已重建向量集合: {collection_name}")
                except Exception as e:
                    logger.error_status(f"重建向量集合失败: {collection_name}, 错误: {e}")
                    return False
            
            # 加载所有记忆并添加到向量数据库
            success = True
            total_memories = 0
            added_memories = 0
            
            for category in self.knowledge_categories.keys():
                memories = self.get_all_memories_by_category(category)
                total_memories += len(memories)
                
                if not memories:
                    continue
                
                if category not in self.collections:
                    logger.warning_status(f"找不到集合: {category}")
                    continue
                
                # 批量添加记忆
                batch_size = 100
                for i in range(0, len(memories), batch_size):
                    batch = memories[i:i+batch_size]
                    
                    try:
                        # 准备批量添加数据
                        ids = []
                        documents = []
                        metadatas = []
                        
                        for memory in batch:
                            ids.append(memory["id"])
                            documents.append(memory["content"])
                            metadatas.append({
                                "category": memory["category"],
                                "subcategory": memory["subcategory"],
                                "importance": str(memory["importance"]),
                                "created_at": memory["created_at"]
                            })
                        
                        # 添加到向量数据库
                        result = self.chroma_client.add_documents(
                            collection_name=f"semantic_{category_map.get(category, 'general')}",
                            documents=documents,
                            metadatas=metadatas,
                            ids=ids
                        )
                        
                        if result:
                            added_memories += len(batch)
                            logger.info(f"已添加 {len(batch)} 条记忆到向量数据库, 类别: {category}")
                        else:
                            logger.warning_status(f"添加记忆到向量数据库失败, 类别: {category}")
                            success = False
                    except Exception as e:
                        logger.error_status(f"添加记忆到向量数据库时发生错误: {e}")
                        success = False
            
            logger.success(f"向量数据库重建完成: 共 {total_memories} 条记忆, 成功添加 {added_memories} 条")
            return success
        except Exception as e:
            logger.error_status(f"重建向量数据库失败: {e}")
            import traceback
            logger.error_status(traceback.format_exc())
            return False
    
    def initialize(self, config: Dict[str, Any] = None) -> bool:
        """
        初始化或重新初始化模块
        
        Args:
            config: 新的配置信息
            
        Returns:
            初始化是否成功
        """
        if config:
            self.config = config
            
            # 更新配置项
            self.data_dir = self.config.get('data_dir', self.data_dir)
            self.chroma_dir = os.path.join(self.data_dir, 'chroma')
            self.cache_expiry = self.config.get('cache_expiry', self.cache_expiry)
            
            # 创建必要的目录
            os.makedirs(self.data_dir, exist_ok=True)
            os.makedirs(self.chroma_dir, exist_ok=True)
            
            # 重新初始化向量数据库
            self._initialize_vector_db()
            
            logger.success("语义记忆模块重新初始化完成")
            
        return True
    
    def shutdown(self) -> bool:
        """
        关闭模块，执行必要的清理工作
        
        Returns:
            关闭是否成功
        """
        # 清空缓存
        self.cache = {}
        
        logger.info("语义记忆模块已关闭")
        return True

    def get_by_id(self, memory_id: str) -> Dict[str, Any]:
        """
        通过ID获取记忆
        
        Args:
            memory_id: 记忆ID
            
        Returns:
            记忆数据，如果不存在则返回None
        """
        # 🔥 先检查统一缓存
        cache_key = f"id:{memory_id}"
        memory_data = self.cache_manager.get("semantic_memory", cache_key)
        if memory_data is not None:
            logger.info(f"从缓存获取记忆: {memory_id}")
            return memory_data
        
        # 从文件中查找
        try:
            for category in self.knowledge_categories.keys():
                file_path = os.path.join(self.data_dir, category, f"{memory_id}.json")
                if os.path.exists(file_path):
                    with open(file_path, 'r', encoding='utf-8') as f:
                        memory_data = json.load(f)
                    
                    # 🔥 更新统一缓存
                    self.cache_manager.set("semantic_memory", cache_key, memory_data, self.cache_expiry)
                    
                    logger.info(f"从文件获取记忆: {memory_id}")
                    return memory_data
        except Exception as e:
            logger.error_status(f"从文件获取记忆失败: {e}")
        
        logger.warning_status(f"找不到记忆: {memory_id}")
        return None


# 模块单例访问函数
def get_instance(config: Dict[str, Any] = None) -> SemanticMemory:
    """
    获取语义记忆模块的单例实例
    
    Args:
        config: 配置信息
        
    Returns:
        语义记忆模块实例
    """
    return SemanticMemory(config) 