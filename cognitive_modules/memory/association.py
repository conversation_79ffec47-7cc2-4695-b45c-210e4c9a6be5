"""
记忆关联模块 - Memory Association

该模块负责构建记忆之间的关联关系，并进行关联性思考。
"""

from utilities.unified_logger import get_unified_logger, setup_unified_logging
import random
import time
from typing import Dict, Any, List

logger = get_unified_logger("cognitive_modules.memory.association")

# 关联类型
ASSOCIATION_TYPES = [
    "semantic",     # 语义关联
    "temporal",     # 时序关联
    "causal",       # 因果关联
    "spatial",      # 空间关联
    "emotional",    # 情感关联
    "thematic"      # 主题关联
]

def process(context):
    """
    处理函数
    
    Args:
        context: 上下文数据
    
    Returns:
        处理结果
    """
    logger.info("执行记忆关联处理")
    
    # 获取检索到的记忆
    retrieved_memories = []
    
    # 尝试不同的方式获取记忆
    try:
        # 如果context是ThinkingContext对象
        if hasattr(context, "shared_data"):
            shared_data = context.shared_data
            retrieved_memories = shared_data.get("related_memories", [])
            
            # 如果没有检索到记忆，尝试从步骤结果中获取
            if not retrieved_memories and hasattr(context, "step_results"):
                step_results = context.step_results
                memory_retrieval_result = step_results.get("memory_retrieval", {})
                if memory_retrieval_result:
                    retrieved_memories = memory_retrieval_result.get("memories", [])
        
        # 如果context是字典
        elif isinstance(context, dict):
            # 先从shared_data中获取
            shared_data = context.get("shared_data", {})
            retrieved_memories = shared_data.get("related_memories", [])
            
            # 如果没有检索到记忆，尝试从步骤结果中获取
            if not retrieved_memories:
                step_results = context.get("step_results", {})
                memory_retrieval_result = step_results.get("memory_retrieval", {})
                if memory_retrieval_result:
                    retrieved_memories = memory_retrieval_result.get("memories", [])
            
            # 尝试直接从context中获取
            if not retrieved_memories:
                retrieved_memories = context.get("memories", [])
                if not retrieved_memories:
                    retrieved_memories = context.get("retrieved_memories", [])
        
        # 确保retrieved_memories是列表
        if not isinstance(retrieved_memories, list):
            if retrieved_memories:
                retrieved_memories = [retrieved_memories]
            else:
                retrieved_memories = []
    
    except Exception as e:
        logger.error_status(f"获取记忆时出错: {e}")
        retrieved_memories = []
    
    # 如果仍然没有记忆，生成模拟记忆以防止错误
    if not retrieved_memories:
        logger.warning_status("没有检索到记忆，无法进行关联，使用空结果")
        return {
            "associations": [],
            "inferences": [],
            "count": 0
        }
    
    # 构建记忆关联
    associations = _build_associations(retrieved_memories)
    
    # 基于关联进行推理
    inferences = _make_inferences(retrieved_memories, associations)
    
    result = {
        "associations": associations,
        "inferences": inferences,
        "count": len(associations)
    }
    
    logger.info(f"构建了 {len(associations)} 个记忆关联")
    
    return result

def _build_associations(memories):
    """
    构建记忆之间的关联关系
    
    Args:
        memories: 记忆列表
        
    Returns:
        关联关系列表
    """
    associations = []
    
    # 如果记忆数量不足，无法构建关联
    if len(memories) < 2:
        return associations
    
    # 遍历记忆对，构建关联
    for i in range(len(memories)):
        for j in range(i+1, len(memories)):
            memory_a = memories[i]
            memory_b = memories[j]
            
            # 计算关联强度和类型
            association_type, strength = _calculate_association(memory_a, memory_b)
            
            if strength > 0.3:  # 关联强度阈值
                associations.append({
                    "source": memory_a.get("id"),
                    "target": memory_b.get("id"),
                    "type": association_type,
                    "strength": strength,
                    "description": _generate_association_description(memory_a, memory_b, association_type)
                })
    
    return associations

def _calculate_association(memory_a, memory_b):
    """
    计算两个记忆之间的关联类型和强度
    
    Args:
        memory_a: 记忆A
        memory_b: 记忆B
        
    Returns:
        (关联类型, 关联强度)
    """
    # 这里使用简单的模拟算法
    # 在实际系统中，应该使用更复杂的语义分析
    
    content_a = memory_a.get("content", "").lower()
    content_b = memory_b.get("content", "").lower()
    type_a = memory_a.get("type")
    type_b = memory_b.get("type")
    
    # 初始化各种关联类型的分数
    scores = {
        "semantic": 0.0,
        "temporal": 0.0,
        "causal": 0.0,
        "emotional": 0.0,
        "thematic": 0.0
    }
    
    # 语义关联 - 基于内容相似性
    common_words = set(content_a.split()) & set(content_b.split())
    scores["semantic"] = len(common_words) * 0.1
    
    # 时序关联 - 基于创建时间的接近度
    time_a = memory_a.get("created_at", 0)
    time_b = memory_b.get("created_at", 0)
    time_diff = abs(time_a - time_b)
    if time_diff < 3600:  # 1小时内
        scores["temporal"] = 0.8
    elif time_diff < 86400:  # 1天内
        scores["temporal"] = 0.5
    elif time_diff < 604800:  # 1周内
        scores["temporal"] = 0.2
    
    # 情感关联 - 如果都是情感类型的记忆
    if type_a == "emotional" or type_b == "emotional":
        scores["emotional"] = 0.7
    
    # 主题关联 - 基于记忆类型
    if type_a == type_b:
        scores["thematic"] = 0.6
    
    # 确定最强的关联类型
    strongest_type = max(scores, key=scores.get)
    strength = scores[strongest_type]
    
    return strongest_type, strength

def _generate_association_description(memory_a, memory_b, association_type):
    """
    生成关联描述
    
    Args:
        memory_a: 记忆A
        memory_b: 记忆B
        association_type: 关联类型
        
    Returns:
        关联描述
    """
    content_a = memory_a.get("content", "")
    content_b = memory_b.get("content", "")
    
    if association_type == "semantic":
        return f"这两条记忆在语义上相关"
    elif association_type == "temporal":
        return f"这两条记忆在时间上接近"
    elif association_type == "causal":
        return f"这两条记忆可能存在因果关系"
    elif association_type == "emotional":
        return f"这两条记忆在情感上相关"
    elif association_type == "thematic":
        return f"这两条记忆在主题上相关"
    else:
        return f"这两条记忆存在关联"

def _make_inferences(memories, associations):
    """
    基于记忆和关联进行推理
    
    Args:
        memories: 记忆列表
        associations: 关联列表
        
    Returns:
        推理结果列表
    """
    inferences = []
    
    # 如果没有关联，无法进行推理
    if not associations:
        return inferences
    
    # 寻找强关联进行推理
    for association in associations:
        if association["strength"] > 0.6:
            # 找出相关的记忆
            source_id = association["source"]
            target_id = association["target"]
            source_memory = next((m for m in memories if m.get("id") == source_id), None)
            target_memory = next((m for m in memories if m.get("id") == target_id), None)
            
            if source_memory and target_memory:
                # 生成推理
                inference = _generate_inference(source_memory, target_memory, association)
                if inference:
                    inferences.append(inference)
    
    return inferences

def _generate_inference(memory_a, memory_b, association):
    """
    基于两个记忆和它们的关联生成推理
    
    Args:
        memory_a: 记忆A
        memory_b: 记忆B
        association: 关联信息
        
    Returns:
        推理结果或None
    """
    # 在实际系统中，这里应该使用更复杂的推理机制
    # 这里使用简单的模板生成
    
    content_a = memory_a.get("content", "")
    content_b = memory_b.get("content", "")
    association_type = association.get("type")
    
    # 基于关联类型生成不同的推理
    if association_type == "semantic" and "自己" in content_a + content_b:
        return {
            "type": "identity",
            "content": "用户可能对我的身份比较感兴趣",
            "confidence": 0.7,
            "sources": [memory_a.get("id"), memory_b.get("id")]
        }
    elif association_type == "temporal" and "询问" in content_a + content_b:
        return {
            "type": "user_interest",
            "content": "用户最近经常询问相关问题",
            "confidence": 0.6,
            "sources": [memory_a.get("id"), memory_b.get("id")]
        }
    elif association_type == "emotional" and "喜欢" in content_a + content_b:
        return {
            "type": "user_preference",
            "content": "用户似乎对我的回应表示满意",
            "confidence": 0.8,
            "sources": [memory_a.get("id"), memory_b.get("id")]
        }
    
    # 默认返回None
    return None 