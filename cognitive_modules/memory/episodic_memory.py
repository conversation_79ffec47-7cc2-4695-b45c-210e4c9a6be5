#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
情境记忆模块 (Episodic Memory)

该模块负责存储和检索数字生命体的情境记忆（事件、经历等），实现记忆的形成、
巩固、检索和遗忘等功能。情境记忆是数字生命体连贯自我体验的重要组成部分。

作者: Claude
创建日期: 2024-07-16
版本: 1.0
"""

import os
import sys
import json
import time
import random
from utilities.unified_logger import get_unified_logger, setup_unified_logging
from datetime import datetime
from typing import Dict, List, Any, Optional, Tuple, Union
import threading

# 添加项目根目录到模块搜索路径
current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(current_dir)
root_dir = os.path.dirname(parent_dir)
if root_dir not in sys.path:
    sys.path.append(root_dir)

# 导入项目模块
from utilities.storage_manager import get_instance as get_storage_instance
from core.integrated_event_bus import get_instance as get_event_bus
from cognitive_modules.base.module_base import CognitiveModuleBase

# 设置日志
logger = get_unified_logger("episodic_memory")

class EpisodicMemory(CognitiveModuleBase):
    """情境记忆模块，负责存储和检索数字生命体的经历和事件"""
    
    def __init__(self, module_id: str = "default", config: Dict = None):
        """
        初始化情境记忆模块
        
        Args:
            module_id: 模块ID
            config: 配置参数
        """
        super().__init__(module_id, "情境记忆模块", config)
        self.memories = {}  # 内存中的情境记忆
        self.memory_config = {
            "storage_enabled": True,           # 是否启用持久化存储
            "consolidation_interval": 3600,    # 记忆巩固间隔(秒)
            "recency_weight": 0.6,             # 最近性权重
            "importance_weight": 0.8,          # 重要性权重
            "emotional_weight": 0.7,           # 情感强度权重
            "memory_decay_rate": 0.05,         # 记忆衰减率(每天)
            "forgetting_threshold": 0.2,       # 遗忘阈值
            "vector_search_limit": 10,         # 向量搜索结果数量
            "max_memory_age_days": 365,        # 最大记忆保留天数
        }
        
        # 更新配置
        if config:
            self.memory_config.update(config)
        
        # 存储管理器
        self.storage = None
        
        # 事件总线
        self.event_bus = get_event_bus()
        
        # 记忆索引
        self.memory_index = {
            "by_time": {},       # 按时间索引
            "by_emotion": {},    # 按情感索引
            "by_person": {},     # 按人物索引
            "by_location": {},   # 按地点索引
            "by_topic": {}       # 按主题索引
        }
        
        # 上次记忆巩固时间
        self.last_consolidation = time.time() 

    def initialize(self, config: Dict = None) -> bool:
        """
        初始化情境记忆模块
        
        Args:
            config: 配置参数
        
        Returns:
            bool: 初始化是否成功
        """
        logger.success(f"正在初始化情境记忆模块 (ID: {self.module_id})...")
        
        try:
            # 🔥 老王修复：先调用父类的initialize方法
            if not super().initialize(config):
                return False
            
            # 初始化存储管理器
            if self.memory_config["storage_enabled"]:
                self.storage = get_storage_instance()
                
                # 加载持久化记忆
                self._load_memories()
            
            # 订阅事件
            self._subscribe_events()
            
            # 启动记忆维护
            self._schedule_memory_maintenance()
            
            logger.success(f"情境记忆模块初始化成功 (ID: {self.module_id})")
            return True
            
        except Exception as e:
            logger.error_status(f"情境记忆模块初始化失败: {str(e)}")
            return False
    
    def shutdown(self) -> bool:
        """
        关闭情境记忆模块
        
        Returns:
            bool: 关闭是否成功
        """
        logger.info(f"正在关闭情境记忆模块 (ID: {self.module_id})...")
        
        try:
            # 持久化记忆
            if self.memory_config["storage_enabled"] and self.storage:
                self._save_memories()
            
            # 取消事件订阅
            self._unsubscribe_events()
            
            logger.success(f"情境记忆模块关闭成功 (ID: {self.module_id})")
            return True
            
        except Exception as e:
            logger.error_status(f"情境记忆模块关闭失败: {str(e)}")
            return False
    
    def add_memory(self, memory_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        添加新的情境记忆
        
        Args:
            memory_data: 记忆数据，必须包含content字段
        
        Returns:
            Dict: 添加的记忆对象
        """
        if "content" not in memory_data:
            raise ValueError("记忆数据必须包含content字段")
        
        # 创建记忆ID
        memory_id = f"em_{int(time.time())}_{random.randint(1000, 9999)}"
        
        # 构建记忆对象
        memory = {
            "id": memory_id,
            "type": "episodic",
            "content": memory_data["content"],
            "created_at": time.time(),
            "last_accessed": time.time(),
            "access_count": 0,
            "strength": 1.0,  # 初始强度为1.0
            "importance": memory_data.get("importance", 0.5),
            "emotional_intensity": memory_data.get("emotional_intensity", 0.5),
            "metadata": {}
        }
        
        # 添加可选字段
        optional_fields = [
            "emotion", "persons", "location", "topic", 
            "timestamp", "source", "tags"
        ]
        
        for field in optional_fields:
            if field in memory_data:
                memory[field] = memory_data[field]
                
                # 更新元数据
                if field in ["emotion", "persons", "location", "topic"]:
                    memory["metadata"][field] = memory_data[field]
        
        # 存储记忆
        self.memories[memory_id] = memory
        
        # 更新索引
        self._update_memory_index(memory)
        
        # 发布记忆创建事件
        self.event_bus.publish("memory_created", {
            "memory_id": memory_id,
            "memory_type": "episodic",
            "content": memory["content"]
        })
        
        logger.debug(f"添加了新的情境记忆: {memory_id}")
        
        return memory
    
    def get_memory(self, memory_id: str) -> Optional[Dict[str, Any]]:
        """
        获取指定ID的记忆
        
        Args:
            memory_id: 记忆ID
        
        Returns:
            Dict或None: 记忆对象，如果不存在则返回None
        """
        memory = self.memories.get(memory_id)
        
        if memory:
            # 更新访问信息
            memory["last_accessed"] = time.time()
            memory["access_count"] += 1
            
            # 加强记忆强度(访问增强)
            memory["strength"] = min(1.0, memory["strength"] + 0.05)
            
            # 发布记忆访问事件
            self.event_bus.publish("memory_accessed", {
                "memory_id": memory_id,
                "memory_type": "episodic"
            })
        
        return memory
    
    def search_memories(self, query: str, 
                        filters: Dict = None, 
                        limit: int = 5,
                        exclude_ids: List[str] = None) -> List[Dict[str, Any]]:
        """
        搜索情境记忆
        
        Args:
            query: 搜索查询
            filters: 过滤条件
            limit: 结果数量限制
            exclude_ids: 要排除的记忆ID列表
        
        Returns:
            List: 匹配的记忆列表
        """
        if not query and not filters:
            return []

        # 初始化排除ID集合
        exclude_set = set(exclude_ids or [])

        # 初始化结果
        results = []
        
        # 如果有过滤条件但没有查询，直接应用过滤器
        if not query and filters:
            # 从所有记忆中应用过滤器
            all_memories = [m for m in self.memories.values() if m.get("id") not in exclude_set]
            results = self._filter_memories(all_memories, filters)
            return results[:limit]
        
        # 向量搜索(如果存储管理器支持)
        if self.storage and hasattr(self.storage, "search_vector_data"):
            try:
                # 获取查询文本的向量表示
                # 由于这里可能没有直接获取向量的方法，暂时使用简化的文本搜索
                # 这部分在实际项目中应该通过AI服务获取嵌入向量
                
                # 简化版：使用文本相似度匹配
                memories_with_scores = []
                for memory in self.memories.values():
                    # 🔥 修复：排除指定的记忆ID
                    if memory.get("id") in exclude_set:
                        continue
                        
                    content = memory.get("content", "").lower()
                    query_lower = query.lower()
                    
                    # 简单的相似度分数 - 实际应用应使用向量相似度
                    if query_lower in content:
                        similarity = len(query_lower) / len(content) if len(content) > 0 else 0
                        memories_with_scores.append((memory, similarity))
                
                # 按相似度排序
                memories_with_scores.sort(key=lambda x: x[1], reverse=True)
                vector_results = [memory for memory, _ in memories_with_scores[:limit]]
                
                # 将向量搜索结果添加到总结果
                results.extend(vector_results)
                
            except Exception as e:
                logger.warning_status(f"向量搜索失败: {str(e)}")
        
        # 关键词搜索(备选方案)
        if not results:
            # 提取查询关键词
            keywords = query.lower().split()
            
            # 搜索所有记忆
            for memory in self.memories.values():
                # 🔥 修复：排除指定的记忆ID
                if memory.get("id") in exclude_set:
                    continue
                    
                content = memory.get("content", "").lower()
                score = 0
                
                # 计算关键词匹配分数
                for keyword in keywords:
                    if keyword in content:
                        score += 1
                
                if score > 0:
                    # 计算最终得分(考虑记忆强度和重要性)
                    final_score = score * memory.get("strength", 0.5) * memory.get("importance", 0.5)
                    results.append((memory, final_score))
            
            # 排序并取top结果
            results = [m for m, s in sorted(results, key=lambda x: x[1], reverse=True)]
        
        # 应用过滤条件
        if filters:
            filtered_results = []
            for memory in results:
                match = True
                for key, value in filters.items():
                    if key in memory:
                        if isinstance(memory[key], list):
                            if value not in memory[key]:
                                match = False
                                break
                        elif memory[key] != value:
                            match = False
                            break
                if match:
                    filtered_results.append(memory)
            results = filtered_results
        
        # 更新访问信息
        for memory in results:
            memory["last_accessed"] = time.time()
            memory["access_count"] += 1
        
        # 限制结果数量
        return results[:limit]
    
    def update_memory(self, memory_id: str, 
                      updates: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """
        更新现有记忆
        
        Args:
            memory_id: 记忆ID
            updates: 要更新的字段
        
        Returns:
            Dict或None: 更新后的记忆对象，如果不存在则返回None
        """
        if memory_id not in self.memories:
            return None
        
        memory = self.memories[memory_id]
        
        # 更新字段
        for key, value in updates.items():
            if key not in ["id", "type", "created_at"]:  # 保护不可变字段
                memory[key] = value
        
        # 更新索引
        self._update_memory_index(memory)
        
        # 发布记忆更新事件
        self.event_bus.publish("memory_updated", {
            "memory_id": memory_id,
            "memory_type": "episodic",
            "updates": updates
        })
        
        return memory
    
    def delete_memory(self, memory_id: str) -> bool:
        """
        删除记忆
        
        Args:
            memory_id: 记忆ID
        
        Returns:
            bool: 删除是否成功
        """
        if memory_id not in self.memories:
            return False
        
        memory = self.memories[memory_id]
        
        # 从索引中移除
        self._remove_from_index(memory)
        
        # 删除记忆
        del self.memories[memory_id]
        
        # 发布记忆删除事件
        self.event_bus.publish("memory_deleted", {
            "memory_id": memory_id,
            "memory_type": "episodic"
        })
        
        return True 

    def _load_memories(self) -> None:
        """从持久化存储加载记忆"""
        try:
            # 从JSON文件加载
            memories_data = self.storage.load_json(f"memories/{self.module_id}/episodic_memories.json", default={})
            
            if memories_data and isinstance(memories_data, dict):
                self.memories = memories_data
                
                # 重建索引
                self._rebuild_memory_index()
                
                logger.info(f"已从存储加载 {len(self.memories)} 条情境记忆")
        except Exception as e:
            logger.error_status(f"加载情境记忆失败: {str(e)}")

    def _save_memories(self) -> None:
        """将记忆保存到持久化存储"""
        try:
            if self.memories:
                # 保存到JSON文件
                self.storage.save_json(f"memories/{self.module_id}/episodic_memories.json", self.memories)
                logger.info(f"已将 {len(self.memories)} 条情境记忆保存到存储")
        except Exception as e:
            logger.error_status(f"保存情境记忆失败: {str(e)}")

    def _subscribe_events(self) -> None:
        """订阅相关事件"""
        # 订阅对话事件
        self.event_bus.subscribe("conversation_message", self._on_conversation_message)
        
        # 订阅情感事件
        self.event_bus.subscribe("emotion_updated", self._on_emotion_updated)
        
        # 订阅时间事件
        self.event_bus.subscribe("daily_maintenance", self._on_daily_maintenance)

    def _unsubscribe_events(self) -> None:
        """取消事件订阅"""
        self.event_bus.unsubscribe("conversation_message", self._on_conversation_message)
        self.event_bus.unsubscribe("emotion_updated", self._on_emotion_updated)
        self.event_bus.unsubscribe("daily_maintenance", self._on_daily_maintenance)

    def _on_conversation_message(self, event_data: Dict) -> None:
        """
        处理对话消息事件
        
        Args:
            event_data: 事件数据
        """
        # 只处理用户消息
        if event_data.get("role") != "user":
            return
        
        # 获取消息内容
        content = event_data.get("content", "")
        if not content:
            return
        
        # 简单评估重要性(后续可以用AI增强)
        importance = 0.5
        if len(content) > 100:  # 长消息可能更重要
            importance = 0.6
        
        # 添加到情境记忆
        memory_data = {
            "content": content,
            "importance": importance,
            "source": "conversation",
            "persons": [event_data.get("user_id", "unknown_user")],
            "tags": ["conversation", "user_message"]
        }
        
        # 如果有情感信息，添加到记忆
        if "emotion" in event_data:
            memory_data["emotion"] = event_data["emotion"]
            memory_data["emotional_intensity"] = event_data.get("emotion_intensity", 0.5)
        
        # 添加记忆
        self.add_memory(memory_data)

    def _on_emotion_updated(self, event_data: Dict) -> None:
        """
        处理情感更新事件
        
        Args:
            event_data: 事件数据
        """
        # 只记录强情感事件
        intensity = event_data.get("intensity", 0)
        if intensity < 0.7:  # 只记录强度高的情感
            return
        
        emotion = event_data.get("emotion", "")
        if not emotion:
            return
        
        # 创建情感记忆
        memory_data = {
            "content": f"感受到强烈的{emotion}情感",
            "importance": min(0.8, intensity),  # 重要性与情感强度相关
            "emotion": emotion,
            "emotional_intensity": intensity,
            "source": "emotion_system",
            "tags": ["emotion", emotion]
        }
        
        # 添加事件触发源(如果有)
        if "trigger" in event_data:
            memory_data["content"] += f"，由{event_data['trigger']}触发"
            memory_data["trigger"] = event_data["trigger"]
        
        # 添加记忆
        self.add_memory(memory_data)

    def _on_daily_maintenance(self, event_data: Dict) -> None:
        """
        执行每日维护任务
        
        Args:
            event_data: 事件数据
        """
        # 执行记忆衰减
        self._decay_memories()
        
        # 清理旧记忆
        self._clean_old_memories()
        
        # 保存记忆
        if self.memory_config["storage_enabled"] and self.storage:
            self._save_memories()

    def _schedule_memory_maintenance(self) -> None:
        """安排记忆维护任务"""
        # 记忆巩固(定期)
        consolidation_interval = self.memory_config["consolidation_interval"]
        if consolidation_interval > 0:
            threading.Timer(consolidation_interval, self._consolidate_memories).start()

    def _consolidate_memories(self) -> None:
        """记忆巩固过程，增强长期记忆管理"""
        try:
            # 记录当前时间
            current_time = time.time()
            time_since_last = current_time - self.last_consolidation
            
            # 需要巩固的记忆(最近添加但尚未巩固的)
            recent_memories = [
                memory for memory in self.memories.values()
                if memory["created_at"] > self.last_consolidation
            ]
            
            if recent_memories:
                logger.info(f"正在巩固 {len(recent_memories)} 条记忆...")
                
                # 发布记忆巩固事件
                self.event_bus.publish("memory_consolidation", {
                    "count": len(recent_memories),
                    "time_since_last": time_since_last
                })
                
                # 1. 增强记忆关联 - 查找相似记忆并建立关联
                self._enhance_memory_associations(recent_memories)
                
                # 2. 识别重要模式 - 检测重复出现的主题、情感或实体
                self._identify_memory_patterns(recent_memories)
                
                # 3. 记忆重要性评估 - 根据多维因素调整记忆重要性
                self._evaluate_memory_importance(recent_memories)
                
                # 4. 长期记忆提取 - 将重要记忆标记为长期保留
                self._promote_to_long_term(recent_memories)
                
                # 保存记忆(如果启用)
                if self.memory_config["storage_enabled"] and self.storage:
                    self._save_memories()
            
            # 更新上次巩固时间
            self.last_consolidation = current_time
            
            # 安排下一次巩固
            self._schedule_memory_maintenance()
            
        except Exception as e:
            logger.error_status(f"记忆巩固失败: {str(e)}")
            # 即使失败也要安排下一次巩固
            self._schedule_memory_maintenance()
            
    def _enhance_memory_associations(self, memories: List[Dict]) -> None:
        """增强记忆关联，寻找相似记忆并建立关联"""
        for memory in memories:
            # 跳过已有关联的记忆
            if "associations" in memory and len(memory.get("associations", [])) >= 3:
                continue
                
            memory_id = memory["id"]
            # 准备搜索条件
            search_text = memory.get("content", "")
            if not search_text:
                continue
                
            # 限制搜索范围，避免与最近记忆形成太多关联
            filters = {}
            if "emotion" in memory:
                filters["emotion"] = memory["emotion"]
            if "tags" in memory and len(memory.get("tags", [])) > 0:
                filters["tags"] = memory["tags"][0]  # 使用第一个标签
                
            # 搜索相似记忆
            similar_memories = self.search_memories(
                search_text, 
                filters=filters,
                exclude_ids=[memory_id],
                limit=3
            )
            
            # 为当前记忆创建关联列表(如果不存在)
            if "associations" not in memory:
                memory["associations"] = []
                
            # 添加关联
            for similar in similar_memories:
                # 避免重复关联
                similar_id = similar["id"]
                if any(assoc["id"] == similar_id for assoc in memory["associations"]):
                    continue
                    
                # 计算相似度分数(简化示例)
                similarity_score = 0.7  # 实际应基于内容相似度计算
                
                # 添加关联
                memory["associations"].append({
                    "id": similar_id,
                    "type": "similar_content",
                    "similarity": similarity_score,
                    "created_at": time.time()
                })
                
                # 为相似记忆添加反向关联
                similar_memory = self.memories.get(similar_id)
                if similar_memory:
                    if "associations" not in similar_memory:
                        similar_memory["associations"] = []
                    
                    # 避免重复关联
                    if not any(assoc["id"] == memory_id for assoc in similar_memory["associations"]):
                        similar_memory["associations"].append({
                            "id": memory_id,
                            "type": "similar_content",
                            "similarity": similarity_score,
                            "created_at": time.time()
                        })
    
    def _identify_memory_patterns(self, memories: List[Dict]) -> None:
        """识别记忆模式，检测重复出现的主题、情感或实体"""
        # 统计最近记忆中的主题、情感、人物和地点
        topic_counts = {}
        emotion_counts = {}
        person_counts = {}
        location_counts = {}
        
        # 收集统计数据
        for memory in memories:
            # 主题统计
            if "tags" in memory:
                for tag in memory.get("tags", []):
                    topic_counts[tag] = topic_counts.get(tag, 0) + 1
            
            # 情感统计
            if "emotion" in memory:
                emotion = memory["emotion"]
                emotion_counts[emotion] = emotion_counts.get(emotion, 0) + 1
            
            # 人物统计
            for person in memory.get("persons", []):
                person_counts[person] = person_counts.get(person, 0) + 1
            
            # 地点统计
            if "location" in memory:
                location = memory["location"]
                location_counts[location] = location_counts.get(location, 0) + 1
        
        # 识别频繁模式(频率>2)
        frequent_topics = [topic for topic, count in topic_counts.items() if count > 2]
        frequent_emotions = [emotion for emotion, count in emotion_counts.items() if count > 2]
        frequent_persons = [person for person, count in person_counts.items() if count > 2]
        frequent_locations = [location for location, count in location_counts.items() if count > 2]
        
        # 如果找到频繁模式，创建模式记忆
        if frequent_topics or frequent_emotions or frequent_persons or frequent_locations:
            pattern_description = []
            
            if frequent_topics:
                pattern_description.append(f"频繁讨论的主题: {', '.join(frequent_topics)}")
            
            if frequent_emotions:
                pattern_description.append(f"经常出现的情感: {', '.join(frequent_emotions)}")
            
            if frequent_persons:
                pattern_description.append(f"频繁互动的人物: {', '.join(frequent_persons)}")
            
            if frequent_locations:
                pattern_description.append(f"常去的地点: {', '.join(frequent_locations)}")
            
            # 创建模式记忆
            if pattern_description:
                pattern_memory = {
                    "content": "记忆模式识别: " + "; ".join(pattern_description),
                    "type": "pattern",
                    "importance": 0.8,  # 模式记忆较重要
                    "strength": 0.9,    # 初始强度高
                    "tags": ["pattern", "analysis"] + frequent_topics,
                    "related_emotions": frequent_emotions,
                    "related_persons": frequent_persons,
                    "related_locations": frequent_locations,
                    "is_long_term": True  # 标记为长期记忆
                }
                
                # 添加模式记忆
                self.add_memory(pattern_memory)
    
    def _evaluate_memory_importance(self, memories: List[Dict]) -> None:
        """评估记忆重要性，根据多维因素调整重要性值"""
        for memory in memories:
            # 跳过已评估的记忆
            if memory.get("importance_evaluated", False):
                continue
            
            # 基础重要性(默认或已有值)
            base_importance = memory.get("importance", 0.5)
            
            # 情感因素(情感强度越高，记忆越重要)
            emotional_factor = memory.get("emotional_intensity", 0) * 0.8
            
            # 社交因素(涉及人物越多，可能越重要)
            social_factor = min(0.6, len(memory.get("persons", [])) * 0.2)
            
            # 重复访问因素(访问次数越多，越重要)
            access_factor = min(0.5, memory.get("access_count", 0) * 0.05)
            
            # 关联因素(关联越多，越重要)
            association_factor = min(0.4, len(memory.get("associations", [])) * 0.1)
            
            # 特殊标记因素(特定标签增加重要性)
            special_tags = set(memory.get("tags", [])).intersection(
                {"重要", "关键", "突破", "首次", "成就", "significant", "achievement"}
            )
            tag_factor = min(0.7, len(special_tags) * 0.35)
            
            # 计算总重要性(各因素加权求和)
            importance_factors = [
                base_importance * 0.3,        # 基础重要性
                emotional_factor * 0.25,      # 情感因素
                social_factor * 0.15,         # 社交因素
                access_factor * 0.1,          # 访问因素
                association_factor * 0.1,     # 关联因素
                tag_factor * 0.1             # 标签因素
            ]
            
            # 更新重要性(限制在0-1范围)
            new_importance = min(1.0, sum(importance_factors))
            memory["importance"] = new_importance
            memory["importance_evaluated"] = True
            
            # 记录评估过程(可选)
            memory["importance_factors"] = {
                "base": base_importance,
                "emotional": emotional_factor,
                "social": social_factor,
                "access": access_factor,
                "association": association_factor,
                "tag": tag_factor,
                "final": new_importance
            }
    
    def _promote_to_long_term(self, memories: List[Dict]) -> None:
        """将重要记忆提升为长期记忆"""
        for memory in memories:
            # 重要性阈值(可配置)
            long_term_threshold = 0.7
            
            # 检查是否满足长期记忆条件
            is_important = memory.get("importance", 0) >= long_term_threshold
            is_emotional = memory.get("emotional_intensity", 0) >= 0.8
            has_associations = len(memory.get("associations", [])) >= 2
            
            # 如果满足条件，标记为长期记忆
            if is_important or is_emotional or has_associations:
                memory["is_long_term"] = True
                
                # 增强记忆强度，减缓衰减
                memory["strength"] = max(memory.get("strength", 0.5), 0.85)
                
                # 增加巩固次数计数
                memory["consolidation_count"] = memory.get("consolidation_count", 0) + 1
                
                # 如果是高度重要的记忆，记录到永久记忆区
                if memory.get("importance", 0) >= 0.9:
                    memory["is_permanent"] = True

    def _decay_memories(self) -> None:
        """记忆衰减过程"""
        # 获取衰减率
        daily_decay_rate = self.memory_config["memory_decay_rate"]
        
        # 获取遗忘阈值
        forgetting_threshold = self.memory_config["forgetting_threshold"]
        
        # 要遗忘的记忆ID
        memories_to_forget = []
        
        # 对每个记忆应用衰减
        for memory_id, memory in self.memories.items():
            # 计算记忆年龄(天)
            age_days = (time.time() - memory["created_at"]) / (24 * 3600)
            
            # 基于重要性和情感强度的保护因子
            protection_factor = (
                memory.get("importance", 0.5) * self.memory_config["importance_weight"] +
                memory.get("emotional_intensity", 0.5) * self.memory_config["emotional_weight"]
            ) / (self.memory_config["importance_weight"] + self.memory_config["emotional_weight"])
            
            # 计算年龄衰减因子(年龄越大，衰减越快)
            age_factor = min(1.0, age_days / 365)
            
            # 访问保护(经常访问的记忆衰减较慢)
            access_protection = min(0.5, memory.get("access_count", 0) * 0.01)
            
            # 计算衰减量
            decay_amount = daily_decay_rate * (1 - protection_factor) * (1 + age_factor) * (1 - access_protection)
            
            # 应用衰减
            memory["strength"] = max(0, memory["strength"] - decay_amount)
            
            # 检查是否达到遗忘阈值
            if memory["strength"] < forgetting_threshold:
                memories_to_forget.append(memory_id)
        
        # 遗忘弱记忆
        for memory_id in memories_to_forget:
            self.delete_memory(memory_id)
        
        if memories_to_forget:
            logger.info(f"已遗忘 {len(memories_to_forget)} 条弱记忆")

    def _clean_old_memories(self) -> None:
        """清理过旧的记忆"""
        max_age_days = self.memory_config["max_memory_age_days"]
        if max_age_days <= 0:  # 不限制
            return
        
        # 计算最大年龄的时间戳
        max_age_timestamp = time.time() - (max_age_days * 24 * 3600)
        
        # 要删除的记忆ID
        memories_to_delete = []
        
        # 检查每个记忆的年龄
        for memory_id, memory in self.memories.items():
            # 只删除非重要、非情感强烈的旧记忆
            if (memory["created_at"] < max_age_timestamp and
                memory.get("importance", 0.5) < 0.7 and
                memory.get("emotional_intensity", 0.5) < 0.7):
                memories_to_delete.append(memory_id)
        
        # 删除旧记忆
        for memory_id in memories_to_delete:
            self.delete_memory(memory_id)
        
        if memories_to_delete:
            logger.info(f"已清理 {len(memories_to_delete)} 条过旧记忆")

    def _update_memory_index(self, memory: Dict) -> None:
        """
        更新记忆索引
        
        Args:
            memory: 记忆对象
        """
        memory_id = memory["id"]
        
        # 按时间索引
        timestamp = memory.get("timestamp", memory["created_at"])
        date_str = datetime.fromtimestamp(timestamp).strftime("%Y-%m-%d")
        
        if date_str not in self.memory_index["by_time"]:
            self.memory_index["by_time"][date_str] = []
        
        if memory_id not in self.memory_index["by_time"][date_str]:
            self.memory_index["by_time"][date_str].append(memory_id)
        
        # 按情感索引
        if "emotion" in memory:
            emotion = memory["emotion"]
            if emotion not in self.memory_index["by_emotion"]:
                self.memory_index["by_emotion"][emotion] = []
            
            if memory_id not in self.memory_index["by_emotion"][emotion]:
                self.memory_index["by_emotion"][emotion].append(memory_id)
        
        # 按人物索引
        if "persons" in memory and isinstance(memory["persons"], list):
            for person in memory["persons"]:
                if person not in self.memory_index["by_person"]:
                    self.memory_index["by_person"][person] = []
                
                if memory_id not in self.memory_index["by_person"][person]:
                    self.memory_index["by_person"][person].append(memory_id)
        
        # 按地点索引
        if "location" in memory:
            location = memory["location"]
            if location not in self.memory_index["by_location"]:
                self.memory_index["by_location"][location] = []
            
            if memory_id not in self.memory_index["by_location"][location]:
                self.memory_index["by_location"][location].append(memory_id)
        
        # 按主题索引
        if "topic" in memory:
            topic = memory["topic"]
            if topic not in self.memory_index["by_topic"]:
                self.memory_index["by_topic"][topic] = []
            
            if memory_id not in self.memory_index["by_topic"][topic]:
                self.memory_index["by_topic"][topic].append(memory_id)

    def _remove_from_index(self, memory: Dict) -> None:
        """
        从索引中移除记忆
        
        Args:
            memory: 记忆对象
        """
        memory_id = memory["id"]
        
        # 从时间索引移除
        timestamp = memory.get("timestamp", memory["created_at"])
        date_str = datetime.fromtimestamp(timestamp).strftime("%Y-%m-%d")
        
        if date_str in self.memory_index["by_time"]:
            if memory_id in self.memory_index["by_time"][date_str]:
                self.memory_index["by_time"][date_str].remove(memory_id)
        
        # 从情感索引移除
        if "emotion" in memory:
            emotion = memory["emotion"]
            if emotion in self.memory_index["by_emotion"]:
                if memory_id in self.memory_index["by_emotion"][emotion]:
                    self.memory_index["by_emotion"][emotion].remove(memory_id)
        
        # 从人物索引移除
        if "persons" in memory and isinstance(memory["persons"], list):
            for person in memory["persons"]:
                if person in self.memory_index["by_person"]:
                    if memory_id in self.memory_index["by_person"][person]:
                        self.memory_index["by_person"][person].remove(memory_id)
        
        # 从地点索引移除
        if "location" in memory:
            location = memory["location"]
            if location in self.memory_index["by_location"]:
                if memory_id in self.memory_index["by_location"][location]:
                    self.memory_index["by_location"][location].remove(memory_id)
        
        # 从主题索引移除
        if "topic" in memory:
            topic = memory["topic"]
            if topic in self.memory_index["by_topic"]:
                if memory_id in self.memory_index["by_topic"][topic]:
                    self.memory_index["by_topic"][topic].remove(memory_id)

    def _rebuild_memory_index(self) -> None:
        """重建记忆索引"""
        # 清空索引
        self.memory_index = {
            "by_time": {},
            "by_emotion": {},
            "by_person": {},
            "by_location": {},
            "by_topic": {}
        }
        
        # 重建索引
        for memory in self.memories.values():
            self._update_memory_index(memory)
        
        logger.success("记忆索引重建完成")

    def get_module_info(self) -> Dict[str, Any]:
        """
        获取模块信息
        
        Returns:
            Dict: 模块信息
        """
        return {
            "id": self.module_id,
            "type": self.module_type,
            "memories_count": len(self.memories),
            "indexed_dates": list(self.memory_index["by_time"].keys()),
            "indexed_emotions": list(self.memory_index["by_emotion"].keys()),
            "indexed_persons": list(self.memory_index["by_person"].keys()),
            "indexed_locations": list(self.memory_index["by_location"].keys()),
            "indexed_topics": list(self.memory_index["by_topic"].keys()),
            "last_consolidation": self.last_consolidation
        }

    # 静态方法: 获取模块实例
    @staticmethod
    def get_instance(module_id: str = "default", config: Dict = None) -> 'EpisodicMemory':
        """
        获取情境记忆模块实例
        
        Args:
            module_id: 模块ID
            config: 配置参数
        
        Returns:
            EpisodicMemory: 模块实例
        """
        # 实例字典
        if not hasattr(EpisodicMemory, "_instances"):
            EpisodicMemory._instances = {}
        
        # 如果实例不存在，创建新实例
        if module_id not in EpisodicMemory._instances:
            instance = EpisodicMemory(module_id, config)
            # 配置内存相关参数
            if config:
                # 🔥 老王修复：确保config是字典类型
                if isinstance(config, dict):
                    instance.memory_config.update(config)
                else:
                    logger.warning_status(f"情境记忆模块配置跳过更新: config不是字典类型，而是{type(config)}")
            EpisodicMemory._instances[module_id] = instance
        
        return EpisodicMemory._instances[module_id]

    def _initialize_module(self) -> bool:
        """
        初始化模块内部功能
        
        Returns:
            bool: 初始化是否成功
        """
        try:
            # 初始化存储管理器
            if self.memory_config["storage_enabled"]:
                self.storage = get_storage_instance()
                
                # 加载持久化记忆
                self._load_memories()
            
            # 订阅事件
            self._subscribe_events()
            
            # 启动记忆维护
            self._schedule_memory_maintenance()
            
            logger.success(f"情境记忆模块初始化成功 (ID: {self.module_id})")
            return True
            
        except Exception as e:
            logger.error_status(f"情境记忆模块初始化失败: {str(e)}")
            return False

    def _process_input(self, input_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        处理输入数据
        
        Args:
            input_data: 输入数据
        
        Returns:
            Dict: 处理结果
        """
        action = input_data.get("action", "")
        result = {"success": False, "message": "未知操作"}
        
        try:
            if action == "add_memory":
                memory_data = input_data.get("memory_data", {})
                memory = self.add_memory(memory_data)
                result = {"success": True, "memory": memory}
                
            elif action == "get_memory":
                memory_id = input_data.get("memory_id", "")
                memory = self.get_memory(memory_id)
                result = {"success": True if memory else False, "memory": memory}
                
            elif action == "search_memories":
                query = input_data.get("query", "")
                filters = input_data.get("filters", {})
                limit = input_data.get("limit", 5)
                memories = self.search_memories(query, filters, limit)
                result = {"success": True, "memories": memories}
                
            elif action == "update_memory":
                memory_id = input_data.get("memory_id", "")
                updates = input_data.get("updates", {})
                memory = self.update_memory(memory_id, updates)
                result = {"success": True if memory else False, "memory": memory}
                
            elif action == "delete_memory":
                memory_id = input_data.get("memory_id", "")
                success = self.delete_memory(memory_id)
                result = {"success": success}
                
            elif action == "get_module_info":
                info = self.get_module_info()
                result = {"success": True, "info": info}
                
            else:
                result = {"success": False, "message": f"不支持的操作: {action}"}
                
        except Exception as e:
            result = {"success": False, "message": str(e)}
            
        return result

    def _update_module(self, context: Dict[str, Any]) -> bool:
        """
        更新模块状态
        
        Args:
            context: 上下文信息
        
        Returns:
            bool: 更新是否成功
        """
        try:
            # 检查是否需要进行记忆巩固
            current_time = time.time()
            time_since_last = current_time - self.last_consolidation
            
            if time_since_last >= self.memory_config["consolidation_interval"]:
                # 执行记忆巩固
                self._consolidate_memories()
                
            # 如果上下文中有特定事件，处理它们
            if "events" in context:
                for event in context["events"]:
                    event_type = event.get("type")
                    
                    if event_type == "daily_maintenance":
                        # 执行每日维护
                        self._on_daily_maintenance(event)
                        
                    elif event_type == "conversation":
                        # 处理对话事件
                        message = event.get("message", {})
                        if message.get("role") == "user":
                            self._on_conversation_message(message)
                            
                    elif event_type == "emotion_update":
                        # 处理情感更新
                        self._on_emotion_updated(event)
            
            return True
            
        except Exception as e:
            logger.error_status(f"更新情境记忆模块失败: {str(e)}")
            return False

    def _get_module_state(self) -> Dict[str, Any]:
        """
        获取模块当前状态
        
        Returns:
            Dict: 模块状态
        """
        return {
            "module_id": self.module_id,
            "module_type": self.module_type,
            "memories_count": len(self.memories),
            "indexed_dates": list(self.memory_index["by_time"].keys()),
            "indexed_emotions": list(self.memory_index["by_emotion"].keys()),
            "indexed_persons": list(self.memory_index["by_person"].keys()),
            "indexed_locations": list(self.memory_index["by_location"].keys()),
            "indexed_topics": list(self.memory_index["by_topic"].keys()),
            "last_consolidation": self.last_consolidation,
            "memory_config": self.memory_config
        }

    def _load_module_state(self, state: Dict[str, Any]) -> bool:
        """
        从保存的状态加载模块
        
        Args:
            state: 模块状态
        
        Returns:
            bool: 加载是否成功
        """
        try:
            # 加载配置
            if "memory_config" in state:
                # 🔥 老王修复：确保memory_config是字典类型
                memory_config = state["memory_config"]
                if isinstance(memory_config, dict):
                    self.memory_config.update(memory_config)
                else:
                    logger.warning_status(f"情境记忆模块状态加载跳过配置: memory_config不是字典类型，而是{type(memory_config)}")
                
            # 加载上次巩固时间
            if "last_consolidation" in state:
                self.last_consolidation = state["last_consolidation"]
                
            # 加载记忆内容由_load_memories处理
            return True
            
        except Exception as e:
            logger.error_status(f"加载情境记忆模块状态失败: {str(e)}")
            return False

    def _shutdown_module(self) -> bool:
        """
        关闭模块
        
        Returns:
            bool: 关闭是否成功
        """
        try:
            # 持久化记忆
            if self.memory_config["storage_enabled"] and self.storage:
                self._save_memories()
            
            # 取消事件订阅
            self._unsubscribe_events()
            
            logger.success(f"情境记忆模块关闭成功 (ID: {self.module_id})")
            return True
            
        except Exception as e:
            logger.error_status(f"情境记忆模块关闭失败: {str(e)}")
            return False

    def get_status(self) -> Dict[str, Any]:
        """
        获取模块状态
        
        Returns:
            Dict: 模块状态信息
        """
        status = {
            'module_id': self.module_id,
            'module_type': self.module_type,
            'module_name': self.module_name,
            'is_active': self.is_active,
            'memories_count': len(self.memories),
            'indexed_emotions': list(self.memory_index["by_emotion"].keys()),
            'indexed_persons': len(self.memory_index["by_person"]),
            'indexed_locations': len(self.memory_index["by_location"]),
            'last_consolidation': datetime.fromtimestamp(self.last_consolidation).strftime('%Y-%m-%d %H:%M:%S'),
            'last_error': self.last_error
        }
        return status 

    def _filter_memories(self, memories: List[Dict[str, Any]], filters: Dict) -> List[Dict[str, Any]]:
        """
        根据过滤条件筛选记忆
        
        Args:
            memories: 记忆列表
            filters: 过滤条件
        
        Returns:
            List: 匹配过滤条件的记忆列表
        """
        if not filters or not memories:
            return []
        
        filtered_memories = []
        
        for memory in memories:
            match = True
            
            # 遍历所有过滤条件
            for key, value in filters.items():
                # 处理特殊条件
                if key == "created_after" and "created_at" in memory:
                    if memory["created_at"] < value:
                        match = False
                        break
                        
                elif key == "created_before" and "created_at" in memory:
                    if memory["created_at"] > value:
                        match = False
                        break
                        
                elif key == "tags" and "tags" in memory:
                    # 如果指定标签不在记忆的标签列表中
                    if isinstance(value, list):
                        if not any(tag in memory["tags"] for tag in value):
                            match = False
                            break
                    elif value not in memory["tags"]:
                        match = False
                        break
                        
                # 一般条件
                elif key in memory:
                    if memory[key] != value:
                        match = False
                        break
                else:
                    # 如果记忆中不存在该字段，则不匹配
                    match = False
                    break
            
            if match:
                filtered_memories.append(memory)
        
        return filtered_memories 