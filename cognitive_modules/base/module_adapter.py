#!/usr/bin/env python3
"""
模块适配器 - Module Adapter

该模块提供了适配器类，用于将简单模块（仅有process函数的模块）
适配为符合ICognitiveModule接口规范的模块。

作者: Claude
创建日期: 2025-06-11
版本: 1.0
"""

import os
import sys
from utilities.unified_logger import get_unified_logger, setup_unified_logging
import importlib
from typing import Dict, Any, List, Optional

# 添加项目根目录到模块搜索路径
current_dir = os.path.dirname(os.path.abspath(__file__))
root_dir = os.path.dirname(os.path.dirname(current_dir))
if root_dir not in sys.path:
    sys.path.append(root_dir)

# 导入接口
from cognitive_modules.base.cognitive_interface import ICognitiveModule

# 配置日志
setup_unified_logging()
logger = get_unified_logger("cognitive_modules.base.module_adapter")

class SimpleModuleAdapter(ICognitiveModule):
    """
    简单模块适配器
    
    将只有process函数的简单模块适配为符合ICognitiveModule接口规范的模块。
    """
    
    def __init__(self, module_path: str, module_name: str):
        """
        初始化适配器
        
        Args:
            module_path: 模块路径
            module_name: 模块名称
        """
        self.module_path = module_path
        self.module_name = module_name
        self.module = None
        self.process_func = None
        self.module_id = f"{module_path}.{module_name}"
        self.is_initialized = False
        self.stats = {
            "calls": 0,
            "errors": 0,
            "last_call_time": 0
        }
        
    def initialize(self, config: Dict[str, Any] = None) -> bool:
        """
        初始化模块
        
        Args:
            config: 配置信息
            
        Returns:
            初始化是否成功
        """
        try:
            # 导入模块
            import time
            module_import_path = f"{self.module_path}.{self.module_name}"
            self.module = importlib.import_module(module_import_path)
            
            # 检查是否有process函数
            if hasattr(self.module, "process"):
                self.process_func = getattr(self.module, "process")
                self.is_initialized = True
                logger.success(f"模块适配成功: {self.module_id}")
                return True
            else:
                logger.error_status(f"模块没有process函数: {self.module_id}")
                return False
                
        except Exception as e:
            logger.error_status(f"初始化模块异常: {e}")
            import traceback
            logger.error_status(f"详细错误信息: {traceback.format_exc()}")
            return False
            
    def process(self, input_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        处理输入数据
        
        Args:
            input_data: 输入数据
            
        Returns:
            处理结果
        """
        if not self.is_initialized:
            if not self.initialize():
                return {"status": "error", "message": "模块未初始化"}
        
        try:
            # 调用原始模块的process函数
            import time
            self.stats["calls"] += 1
            self.stats["last_call_time"] = time.time()
            
            result = self.process_func(input_data)
            return result
            
        except Exception as e:
            logger.error_status(f"处理数据异常: {e}")
            self.stats["errors"] += 1
            return {"status": "error", "message": str(e)}
            
    def update(self, context: Dict[str, Any]) -> bool:
        """
        更新模块状态
        
        Args:
            context: 上下文信息
            
        Returns:
            更新是否成功
        """
        # 简单模块没有状态，直接返回成功
        return True
        
    def get_state(self) -> Dict[str, Any]:
        """
        获取模块状态
        
        Returns:
            模块状态
        """
        return {
            "module_id": self.module_id,
            "initialized": self.is_initialized,
            "stats": self.stats
        }
        
    def shutdown(self) -> bool:
        """
        关闭模块
        
        Returns:
            关闭是否成功
        """
        # 简单模块没有需要关闭的资源，直接返回成功
        self.is_initialized = False
        return True
        
    def get_status(self) -> Dict[str, Any]:
        """
        获取模块状态
        
        Returns:
            模块状态
        """
        return {
            "module_id": self.module_id,
            "status": "active" if self.is_initialized else "inactive",
            "calls": self.stats["calls"],
            "errors": self.stats["errors"]
        }

def create_adapter(module_type: str, module_name: str) -> ICognitiveModule:
    """
    创建模块适配器
    
    Args:
        module_type: 模块类型
        module_name: 模块名称
        
    Returns:
        模块适配器
    """
    module_path = f"cognitive_modules.{module_type}"
    adapter = SimpleModuleAdapter(module_path, module_name)
    adapter.initialize()
    return adapter 