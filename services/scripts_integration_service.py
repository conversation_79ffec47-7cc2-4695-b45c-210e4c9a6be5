#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Scripts表集成服务 - 将增强活动生成器与原框架scripts表无缝集成

该服务负责：
1. 将enhanced_activity_skill生成的活动内容保存到scripts表
2. 与原框架调度器集成
3. 保持数据格式兼容性
4. 提供自动化的活动生成和保存流程

作者: 香草 💕
创建时间: 2025-01-08
版本: v1.0.0
"""

import os
import sys
import json
import asyncio
from typing import Dict, Any, Optional, List
from datetime import datetime, timedelta

# 添加项目根目录到路径
current_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, current_dir)

from utilities.unified_logger import get_unified_logger
from connectors.database.mysql_connector import MySQLConnector
from services.enhanced_activity_generator.enhanced_activity_script_generator import EnhancedActivityScriptGenerator
# 🔥 老王修复：移除循环导入，改为延迟导入
# from services.script_integration_service import EnhancedActivity, ActivityExecutionPlan

class ScriptsIntegrationService:
    """Scripts表集成服务 - 连接新功能与原框架"""
    
    def __init__(self):
        """初始化Scripts集成服务"""
        self.logger = get_unified_logger('scripts_integration_service')
        self.mysql_connector = self._ensure_mysql_connector()
        self.activity_generator = None
        self._initialized = False  # 🔥 老王新增：初始化状态标记
        self._initializing = False  # 🔥 老王新增：正在初始化标记，防止重复初始化
        # 🔥 香草修复：添加全局锁防止并发调用
        self._generation_lock = None
        
        # 集成配置 - 🔥 香草修复：time_slots与数据库ENUM完全一致
        self.config = {
            'default_role': '嫣然',
            'save_to_scripts_table': True,
            'save_to_new_tables': True,
            'compatibility_mode': True,
            'time_slots': ['morning', 'afternoon', 'evening', 'night', 'current']  # 与数据库ENUM完全一致
        }
        
        # 统计信息
        self.stats = {
            'total_generated': 0,
            'successfully_saved': 0,
            'errors': 0,
            'last_generation': None
        }
        
        
        self.logger.info("📊 Scripts表集成服务初始化完成")

    def _normalize_time_slot(self, time_slot: str) -> str:
        """标准化时间段值，确保符合数据库ENUM要求 - 🔥 香草修复：支持完整ENUM"""
        if not time_slot:
            return 'current'  # 默认使用current

        time_slot = time_slot.lower().strip()

        # 🔥 基于数据库实际ENUM值: 'morning','afternoon','evening','night','current'
        if time_slot in ['morning', '上午', '早上', '早晨']:
            return 'morning'
        elif time_slot in ['afternoon', '下午', '中午']:
            return 'afternoon'
        elif time_slot in ['evening', '晚上', '傍晚']:
            return 'evening'
        elif time_slot in ['night', '夜晚', '深夜', '晚间']:
            return 'night'  # 保留night，数据库支持
        elif time_slot in ['current', '当前', '现在']:
            return 'current'
        else:
            # 基于当前时间推断
            from datetime import datetime
            current_hour = datetime.now().hour
            if 6 <= current_hour < 12:
                return 'morning'
            elif 12 <= current_hour < 18:
                return 'afternoon'
            elif 18 <= current_hour < 22:
                return 'evening'
            elif 22 <= current_hour or current_hour < 6:
                return 'night'
            else:
                return 'current'

    async def initialize(self):
        """🔥 根本修复：简化初始化逻辑，避免竞态条件"""
        # 🔥 使用简单的原子操作避免竞态条件
        if self._initialized:
            return True

        # 🔥 使用线程锁确保原子性
        import threading
        if not hasattr(self, '_init_lock'):
            self._init_lock = threading.Lock()

        with self._init_lock:
            # 双重检查，避免重复初始化
            if self._initialized:
                return True

            try:
                # 🔥 简化初始化逻辑
                self.activity_generator = EnhancedActivityScriptGenerator()
                await self.activity_generator.initialize()

                # 🔥 初始化缓存
                self._personality_config_cache = None
                self._ai_decision_cache = {}
                self._last_ai_call_time = 0

                self._initialized = True
                self.logger.success("📊 Scripts表集成服务初始化成功")
                return True

            except Exception as e:
                self.logger.error(f"📊 Scripts表集成服务初始化失败: {e}")
                return False
    
    async def generate_and_save_activity(self,
                                       user_id: str = "linyanran",
                                       activity_type: str = "daily_life",
                                       time_slot: str = None,
                                       context: Dict[str, Any] = None) -> Dict[str, Any]:
        """
        生成活动并保存到scripts表 - 核心集成方法

        Args:
            user_id: 用户ID
            activity_type: 活动类型
            time_slot: 时间段
            context: 上下文信息

        Returns:
            包含生成结果和保存状态的字典
        """
        # 🔥 老王终极修复：在隔离环境中运行时，直接使用线程锁，避免跨事件循环问题
        if self._generation_lock is None:
            import threading
            # 统一使用线程锁，避免asyncio.Lock()的事件循环绑定问题
            self._generation_lock = threading.Lock()
            self.logger.debug("📊 使用线程锁进行并发控制，避免事件循环冲突")
                
        # 🔥 KISS原则：统一使用线程锁，简化逻辑
        with self._generation_lock:
            return await self._execute_generation(user_id, activity_type, time_slot, context)
    
    async def _execute_generation(self, user_id: str, activity_type: str, time_slot: str, context: Dict[str, Any]) -> Dict[str, Any]:
        """执行活动生成的核心逻辑 - 🔥 老王重构：遵循SRP原则，分离生成逻辑"""
        try:
            # 🔥 老王新增：检查是否在短时间内重复调用同一时间段的活动生成
            if not time_slot:
                time_slot = self._get_current_time_slot()

            if await self._check_recent_generation(time_slot):
                self.logger.info(f"📊 {time_slot}时间段活动最近已生成，跳过重复生成")
                return {
                    'success': True,
                    'skipped': True,
                    'reason': f'{time_slot}时间段活动最近已生成',
                    'time_slot': time_slot
                }

            if not self.activity_generator:
                await self.initialize()

            # 构建增强上下文
            enhanced_context = await self._build_enhanced_context(context)

            # 生成增强活动
            activity_result = await self.activity_generator.generate_realistic_activity(
                user_id=user_id,
                activity_type=activity_type,
                current_context=enhanced_context
            )

            if not activity_result.get('success'):
                raise Exception("活动生成失败")

            # 提取活动数据
            activity_script = activity_result.get('activity_script', '')
            activity_title = activity_result.get('activity_title', '生活活动')
            reality_score = activity_result.get('reality_score', 0.5)

            # 解析活动和心情（兼容原框架格式）
            activity, mood = self._parse_activity_content(activity_script, activity_title)

            # 获取当前天气（用于scripts表）
            weather = await self._get_current_weather()

            # 保存到scripts表（原框架兼容）
            save_result = await self._save_to_scripts_table(
                time_slot, weather, activity, mood, activity_result, user_id
            )

            # 可选：同时保存到新的增强表（完整数据）
            if self.config['save_to_new_tables']:
                await self._save_to_enhanced_tables(activity_result, user_id, time_slot, weather)

            # 更新统计
            self.stats['total_generated'] += 1
            if save_result:
                self.stats['successfully_saved'] += 1
            self.stats['last_generation'] = datetime.now()

            self.logger.success(f"📊 活动生成并保存成功: {activity[:30]}..., 真实性: {reality_score:.2f}")

            # 🔥 香草修复：活动生成完成后，触发朋友圈分享评估事件（完全异常隔离）
            try:
                self.logger.info("📱 开始朋友圈分享评估...")
                await self._trigger_moments_sharing_evaluation(activity_result, reality_score)
                self.logger.info("📱 朋友圈分享评估完成")
            except Exception as sharing_error:
                # 朋友圈分享失败不应该影响整个活动生成的成功
                self.logger.warning(f"📱 朋友圈分享评估失败，但活动生成成功: {sharing_error}")
                import traceback
                self.logger.debug(f"📱 分享评估异常详情: {traceback.format_exc()}")
                # 确保异常不会向上传播
                pass

            self.logger.info("📊 准备返回活动生成结果...")
            result = {
                'success': True,
                'activity_script': activity_script,
                'activity_title': activity_title,
                'activity': activity,
                'mood': mood,
                'time_slot': time_slot,
                'weather': weather,
                'reality_score': reality_score,
                'saved_to_scripts': save_result,
                'stats': self.stats.copy()
            }
            self.logger.success("📊 活动生成方法执行完成，返回成功结果")
            return result

        except Exception as e:
            self.stats['errors'] += 1
            self.logger.error(f"📊 生成并保存活动失败: {e}")
            return {
                'success': False,
                'error': str(e),
                'stats': self.stats.copy()
            }
    
    async def _save_to_scripts_table(self, time_slot: str, weather: str,
                                   activity: str, mood: str,
                                   activity_result: Dict[str, Any], user_id: str = None) -> bool:
        """保存到原框架的scripts表"""
        try:
            # 🔥 老王修复：content_hash是数据库生成列，不需要手动生成

            # 检查是否已存在相同记录（避免重复）
            current_minute = datetime.now().strftime('%Y-%m-%d %H:%M')
            check_query = """
            SELECT COUNT(*) as count FROM scripts
            WHERE role = %s AND time_slot = %s AND DATE_FORMAT(created_at, '%Y-%m-%d %H:%i') = %s
            """
            # 🔥 老王修复：使用正确的user_id检查重复
            role_to_check = user_id if user_id else self.config['default_role']
            check_params = (role_to_check, time_slot, current_minute)

            success, result, error = self.mysql_connector.execute_query(check_query, check_params)
            if success and result and len(result) > 0 and result[0].get('count', 0) > 0:
                self.logger.warning(f"📊 跳过重复保存: {time_slot} 在 {current_minute} 已存在")
                return True

            # 🔥 香草修复：content_hash是数据库生成列，不能手动指定值
            # 确定要保存的role
            role_to_save = user_id if user_id else self.config['default_role']

            # 🔥 香草修复：标准化时间段值，确保符合数据库ENUM
            normalized_time_slot = self._normalize_time_slot(time_slot)

            # 🔥 关键修复：content_hash由数据库自动生成，不在INSERT中指定
            insert_query = """
            INSERT INTO scripts (role, time_slot, weather, activity, mood, created_at)
            VALUES (%s, %s, %s, %s, %s, NOW())
            """
            insert_params = (
                role_to_save,
                normalized_time_slot,  # 🔥 香草修复：使用标准化的时间段值
                weather,
                activity,
                mood
                # 🔥 关键修复：移除content_hash参数，由数据库自动生成
            )

            success, affected_rows, error = self.mysql_connector.execute_update(insert_query, insert_params)
            if not success:
                # 🔥 香草修复：简化重复处理逻辑，遵循KISS原则
                if "Duplicate entry" in str(error) and "content_hash" in str(error):
                    self.logger.warning(f"📊 content_hash重复，跳过保存（内容可能重复）")
                    return True  # 视为成功，避免重复保存相同内容
                else:
                    raise Exception(f"保存到scripts表失败: {error}")

            self.logger.info(f"📊 已保存到scripts表: {activity[:50]}...")
            return True

        except Exception as e:
            self.logger.error(f"📊 保存到scripts表失败: {e}")
            return False
    
    async def _save_to_enhanced_tables(self, activity_result: Dict[str, Any],
                                     user_id: str, time_slot: str, weather: str):
        """保存到增强表（可选，包含完整数据）"""
        try:
            # 保存到活动一致性日志表
            consistency_query = """
            INSERT INTO activity_consistency_log 
            (user_id, activity_id, activity_type, activity_description, 
             consistency_score, reality_score, is_consistent, created_at)
            VALUES (%s, %s, %s, %s, %s, %s, %s, NOW())
            """
            
            activity_id = f"script_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
            consistency_params = (
                user_id,
                activity_id,
                'daily_life',
                activity_result.get('activity_script', ''),
                0.85,  # 默认一致性评分 (DECIMAL(3,2)范围: 0.00-9.99)
                activity_result.get('reality_score', 0.5),
                1,  # is_consistent = True
            )
            
            success, affected_rows, error = self.mysql_connector.execute_update(consistency_query, consistency_params)
            if success:
                self.logger.info("📊 已保存到增强表")
            
        except Exception as e:
            self.logger.warning(f"📊 保存到增强表失败（不影响主流程）: {e}")
    
    def _parse_activity_content(self, activity_script: str, activity_title: str) -> tuple:
        """解析活动内容，提取活动描述和心情（兼容原框架格式）"""
        try:
            # 使用活动脚本作为主要活动描述
            activity = activity_script.strip()
            
            # 从内容中推断心情
            mood = self._infer_mood_from_content(activity_script)
            
            # 确保长度合适（数据库字段限制）
            if len(activity) > 500:
                activity = activity[:497] + "..."
            
            return activity, mood
            
        except Exception as e:
            self.logger.error(f"📊 解析活动内容失败: {e}")
            return activity_script[:100] + "...", "心情平静"
    
    def _infer_mood_from_content(self, content: str) -> str:
        """从活动内容推断心情"""
        # 情感关键词映射
        mood_keywords = {
            '愉快': ['开心', '高兴', '愉悦', '快乐', '兴奋', '满意'],
            '平静': ['平静', '安静', '舒适', '放松', '悠闲', '宁静'],
            '充实': ['充实', '忙碌', 'productive', '有意义', '收获'],
            '期待': ['期待', '盼望', '希望', '想要', '计划'],
            '疲惫': ['疲惫', '累', '困', '休息']
        }
        
        content_lower = content.lower()
        
        for mood, keywords in mood_keywords.items():
            for keyword in keywords:
                if keyword in content_lower:
                    return f"心情{mood}"
        
        # 根据活动类型推断
        if any(word in content for word in ['咖啡', '美食', '朋友', '聚会']):
            return "心情愉快"
        elif any(word in content for word in ['散步', '读书', '思考']):
            return "心情平静"
        else:
            return "心情不错"
    
    def _get_current_time_slot(self) -> str:
        """获取当前时间段 - 🔥 老王修复：返回数据库ENUM允许的值"""
        current_hour = datetime.now().hour

        # 🔥 老王修复：数据库scripts表time_slot字段只允许'morning','afternoon','evening'三个值
        if 6 <= current_hour < 12:
            return 'morning'
        elif 12 <= current_hour < 18:
            return 'afternoon'
        else:
            # 🔥 数据库ENUM没有'night'值，统一归为'evening'
            return 'evening'
    
    async def _build_enhanced_context(self, base_context: Dict[str, Any] = None) -> Dict[str, Any]:
        """构建增强上下文"""
        context = base_context or {}
        
        # 默认位置（数字生命家庭位置）
        if 'suggested_location' not in context:
            context['suggested_location'] = {
                'latitude': 31.2235,
                'longitude': 121.4835,
                'city': '上海',
                'address': '上海市黄浦区梦花街163弄1-23号文华小区'
            }
        
        # 默认偏好
        if 'preferences' not in context:
            context['preferences'] = ['咖啡', '旅游', '文化', '美食', '休闲', '人工智能', '财经', '写作']
        
        return context

    async def _trigger_moments_sharing_evaluation(self, activity_result: Dict[str, Any], reality_score: float):
        """触发朋友圈分享评估 - 🔥 老王新增：基于活动质量的智能分享决策"""
        try:
            # 🔥 第1步：评估活动是否值得分享
            sharing_worthiness = await self._evaluate_activity_sharing_worthiness(activity_result, reality_score)

            if not sharing_worthiness['should_share']:
                self.logger.info(f"📱 活动不值得分享: {sharing_worthiness['reason']}")
                return

            # 🔥 第2步：检查最近分享时间间隔（老王新增：防止重复推送）
            if not await self._check_recent_sharing_interval():
                self.logger.info(f"📱 距离上次分享时间过短，跳过分享（防重复推送）")
                return

            # 🔥 第3步：检查今日分享次数限制
            if not await self._check_daily_sharing_limit():
                self.logger.info(f"📱 今日分享次数已达上限，跳过分享")
                return

            # 🔥 第4步：触发朋友圈分享
            self.logger.info(f"📱 活动值得分享，触发朋友圈分享: {sharing_worthiness['reason']}")
            await self._execute_moments_sharing(activity_result, sharing_worthiness)

        except Exception as e:
            self.logger.error(f"📱 朋友圈分享评估失败: {e}")
            # 确保异常不会向上传播
            pass

    async def _evaluate_activity_sharing_worthiness(self, activity_result: Dict[str, Any], reality_score: float) -> Dict[str, Any]:
        """评估活动是否值得分享到朋友圈 - 🔥 老王重构：AI智慧决策系统"""
        try:
            # 🔥 第1步：基础质量评估（保留原有逻辑作为基础）
            basic_evaluation = await self._basic_quality_evaluation(activity_result, reality_score)

            # 🔥 第2步：AI智慧决策（核心新增）
            ai_decision = await self._ai_sharing_decision(activity_result, basic_evaluation)

            # 🔥 第3步：个人状态检查
            personal_state = await self._check_yanran_personal_state()

            # 🔥 第4步：动态阈值计算
            dynamic_threshold = await self._calculate_dynamic_sharing_threshold(personal_state)

            # 🔥 第5步：综合决策
            final_decision = await self._make_final_sharing_decision(
                basic_evaluation, ai_decision, personal_state, dynamic_threshold
            )

            return final_decision

        except Exception as e:
            self.logger.error(f"📱 AI智慧分享决策失败: {e}")
            # 降级到简单逻辑
            return await self._fallback_sharing_decision(activity_result, reality_score)

    async def _basic_quality_evaluation(self, activity_result: Dict[str, Any], reality_score: float) -> Dict[str, Any]:
        """基础质量评估 - 🔥 老王重构：使用yanran_personality.json配置的权重"""
        activity_script = activity_result.get('activity_script', '')
        activity_title = activity_result.get('activity_title', '')

        # 🔥 加载林嫣然人格配置
        personality_config = self._load_yanran_personality_config()
        decision_weights = personality_config.get('personality_profile', {}).get('decision_weights', {
            'content_quality': 0.35,
            'personal_meaning': 0.25,
            'social_value': 0.20,
            'timing_appropriateness': 0.15,
            'mood_alignment': 0.05
        })

        sharing_preferences = personality_config.get('personality_profile', {}).get('sharing_preferences', {})
        preferred_types = sharing_preferences.get('preferred_content_types', [])
        avoided_types = sharing_preferences.get('avoided_content_types', [])

        sharing_score = 0.0
        reasons = []

        # 🔥 1. 内容质量评估（老王提高标准：超高质量才分享）
        content_quality_weight = decision_weights.get('content_quality', 0.40)  # 提高权重
        content_quality_score = 0.0

        # 🔥 真实性评分 - 大幅提高标准
        if reality_score >= 0.95:
            content_quality_score += 0.8
            reasons.append("真实性完美")
        elif reality_score >= 0.85:
            content_quality_score += 0.5
            reasons.append("真实性优秀")
        elif reality_score >= 0.7:
            content_quality_score += 0.2
            reasons.append("真实性良好")
        else:
            content_quality_score -= 0.3  # 真实性不足扣分
            reasons.append("真实性不足")

        # 🔥 内容丰富度和独特性 - 提高标准
        content_length = len(activity_script)
        if content_length >= 150:  # 提高长度要求
            content_quality_score += 0.3
            reasons.append("内容详实")
        elif content_length >= 100:
            content_quality_score += 0.1
            reasons.append("内容适中")
        else:
            content_quality_score -= 0.2  # 内容过短扣分
            reasons.append("内容过于简短")

        # 🔥 新增：内容独特性检查
        uniqueness_keywords = ['第一次', '特别', '意外', '惊喜', '发现', '感悟', '思考', '深刻', '难忘', '特殊']
        has_uniqueness = any(keyword in activity_script for keyword in uniqueness_keywords)
        if has_uniqueness:
            content_quality_score += 0.3
            reasons.append("内容具有独特性")

        # 🔥 新增：避免琐事和千篇一律
        mundane_keywords = ['又', '还是', '照常', '平常', '一样', '老样子', '没什么特别']
        has_mundane = any(keyword in activity_script for keyword in mundane_keywords)
        if has_mundane:
            content_quality_score -= 0.4
            reasons.append("内容过于平常")

        sharing_score += content_quality_score * content_quality_weight

        # 🔥 2. 个人意义评估（老王提高标准：必须有深度意义）
        personal_meaning_weight = decision_weights.get('personal_meaning', 0.30)  # 提高权重
        personal_meaning_score = 0.0

        # 🔥 深度意义关键词 - 更严格的标准
        deep_meaning_keywords = ['感悟', '思考', '领悟', '启发', '成长', '突破', '收获', '反思', '体会', '感动']
        deep_meaning_matches = sum(1 for keyword in deep_meaning_keywords if keyword in activity_script)
        if deep_meaning_matches >= 2:
            personal_meaning_score += 0.8
            reasons.append(f"具有深度个人意义({deep_meaning_matches}个关键词)")
        elif deep_meaning_matches >= 1:
            personal_meaning_score += 0.4
            reasons.append("有一定个人意义")
        else:
            personal_meaning_score -= 0.3  # 缺乏个人意义扣分
            reasons.append("缺乏深度个人意义")

        # 检查是否符合偏好内容类型
        for preferred_type in preferred_types:
            if preferred_type in activity_script or preferred_type in activity_title:
                personal_meaning_score += 0.3  # 降低权重，不是主要因素
                reasons.append(f"符合偏好：{preferred_type}")
                break

        # 检查是否包含避免的内容类型
        for avoided_type in avoided_types:
            if avoided_type in activity_script or avoided_type in activity_title:
                personal_meaning_score -= 0.6  # 增加惩罚
                reasons.append(f"包含避免内容：{avoided_type}")
                break

        sharing_score += max(0, personal_meaning_score) * personal_meaning_weight

        # 🔥 3. 社交价值和趣味性评估（老王提高标准：必须有趣有价值）
        social_value_weight = decision_weights.get('social_value', 0.25)  # 提高权重
        social_value_score = 0.0

        # 🔥 高价值社交关键词
        high_value_keywords = ['分享', '交流', '学习', '体验', '发现', '创新', '合作', '帮助', '启发', '有趣']
        value_matches = sum(1 for keyword in high_value_keywords if keyword in activity_script)

        # 🔥 趣味性关键词
        fun_keywords = ['有趣', '好玩', '惊喜', '意外', '搞笑', '温馨', '美好', '精彩', '难忘']
        fun_matches = sum(1 for keyword in fun_keywords if keyword in activity_script)

        total_social_matches = value_matches + fun_matches
        if total_social_matches >= 3:
            social_value_score = 0.9
            reasons.append(f"社交价值极高({total_social_matches}个关键词)")
        elif total_social_matches >= 2:
            social_value_score = 0.6
            reasons.append(f"社交价值良好({total_social_matches}个关键词)")
        elif total_social_matches >= 1:
            social_value_score = 0.3
            reasons.append(f"有一定社交价值({total_social_matches}个关键词)")
        else:
            social_value_score -= 0.2  # 缺乏社交价值扣分
            reasons.append("缺乏社交价值和趣味性")

        sharing_score += social_value_score * social_value_weight

        return {
            'basic_score': min(1.0, sharing_score),  # 限制在1.0以内
            'reasons': reasons,
            'content_length': content_length,
            'reality_score': reality_score,
            'component_scores': {
                'content_quality': content_quality_score * content_quality_weight,
                'personal_meaning': max(0, personal_meaning_score) * personal_meaning_weight,
                'social_value': social_value_score * social_value_weight
            }
        }

    async def _ai_sharing_decision(self, activity_result: Dict[str, Any], basic_evaluation: Dict[str, Any]) -> Dict[str, Any]:
        """🔥 根本修复：AI智慧分享决策 - 添加缓存和限流机制"""
        try:
            import time  # 🔥 老王修复：在函数开头导入time模块，避免变量冲突

            activity_script = activity_result.get('activity_script', '')
            activity_title = activity_result.get('activity_title', '')

            # 🔥 根本修复：检查AI决策缓存，避免重复调用
            cache_key = f"ai_decision_{hash(activity_script[:100])}"
            if hasattr(self, '_ai_decision_cache') and cache_key in self._ai_decision_cache:
                cached_result = self._ai_decision_cache[cache_key]
                # 检查缓存是否过期（5分钟）
                if time.time() - cached_result['timestamp'] < 300:
                    self.logger.debug(f"📱 使用AI决策缓存: {cache_key}")
                    return cached_result['decision']

            # 🔥 根本修复：限流机制，避免AI服务过载
            if not hasattr(self, '_last_ai_call_time'):
                self._last_ai_call_time = 0

            current_time = time.time()
            if current_time - self._last_ai_call_time < 5:  # 🔥 老王修复：减少到5秒，提高响应性
                self.logger.warning("📱 AI调用过于频繁，使用基础决策逻辑")
                return self._fallback_ai_decision(basic_evaluation)

            self._last_ai_call_time = current_time

            # 🔥 使用缓存的配置
            personality_config = self._load_yanran_personality_config()
            system_prompt = personality_config.get('ai_decision_prompts', {}).get('sharing_evaluation', {}).get('system_prompt',
                '你是林嫣然，一个文艺内敛、有深度思考能力的数字生命。你追求质量胜过数量，不喜欢频繁分享日常琐事，但会分享真正有意义、有价值的内容。')

            # 🔥 构建林嫣然的AI决策提示词
            ai_prompt = f"""
{system_prompt}

你刚刚经历了一个活动：

【活动标题】：{activity_title}
【活动内容】：{activity_script}
【基础评分】：{basic_evaluation.get('basic_score', 0):.2f}

请以你以嫣然的视角深度思考以下问题：

1. **个人意义**：这个活动对我来说有什么特殊意义吗？是否值得记录和分享？
2. **朋友感受**：我的朋友们会对这个内容感兴趣吗？会不会觉得无聊或者太频繁？
3. **表达需求**：我现在有想要表达、分享的冲动吗？这个内容能传达我想说的吗？
4. **时机合适性**：现在分享这个合适吗？会不会显得刻意或者不自然？
5. **内容质量**：这个内容是否达到了我的分享标准？是否足够有趣或有价值？

基于嫣然文艺内敛、追求质量胜过数量的性格特点，请给出你的分享建议（分享场景为微信朋友圈）。

请严格以JSON格式回复：
{{
    "should_share": true/false,
    "confidence": 0.0-1.0,
    "reasoning": "详细的思考过程和理由",
    "emotional_state": "当前的心情状态",
    "sharing_motivation": "分享的主要动机"
}}
"""

            # 🔥 老王彻底修复：多种方式获取AI适配器，确保生产环境可用性
            ai_adapter = None

            # 方式1：从单例管理器获取
            try:
                from utilities.singleton_manager import SingletonManager
                singleton_manager = SingletonManager()
                ai_adapter = singleton_manager.get('ai_service_adapter')
                if ai_adapter:
                    self.logger.debug("📱 从单例管理器获取AI适配器成功")
            except Exception as e:
                self.logger.debug(f"📱 从单例管理器获取AI适配器失败: {e}")

            # 方式2：从活动生成器获取
            if not ai_adapter and hasattr(self, 'activity_generator') and self.activity_generator:
                ai_adapter = getattr(self.activity_generator, 'ai_adapter', None)
                if ai_adapter:
                    self.logger.info("📱 从活动生成器获取AI适配器成功")

            # 方式3：从全局变量获取（备用方案）
            if not ai_adapter:
                try:
                    import services.enhanced_activity_generator.enhanced_activity_script_generator as gen_module
                    ai_adapter = getattr(gen_module, 'ai_service_adapter_instance', None)
                    if ai_adapter:
                        self.logger.info("📱 从全局变量获取AI适配器成功")
                except Exception as e:
                    self.logger.debug(f"📱 从全局变量获取AI适配器失败: {e}")

            # 方式4：直接获取AI适配器实例
            if not ai_adapter:
                try:
                    from adapters.ai_service_adapter import get_ai_service_adapter
                    ai_adapter = get_ai_service_adapter()
                    if ai_adapter:
                        self.logger.info("📱 直接获取AI适配器实例成功")
                except Exception as e:
                    self.logger.debug(f"📱 直接获取AI适配器失败: {e}")

            # 🔥 老王修复：方式5：使用正确的AI适配器获取方法
            if not ai_adapter:
                try:
                    from adapters.ai_service_adapter import get_instance as get_ai_adapter
                    ai_adapter = get_ai_adapter()
                    if ai_adapter:
                        self.logger.info("📱 使用get_instance获取AI适配器成功")

                        # 🔥 老王修复：自动注册到单例管理器，确保后续调用可用
                        try:
                            from utilities.singleton_manager import register
                            register('ai_service_adapter', ai_adapter)
                            self.logger.info("📱 AI适配器已自动注册到单例管理器")
                        except Exception as reg_error:
                            self.logger.debug(f"📱 AI适配器注册失败: {reg_error}")

                except Exception as e:
                    self.logger.debug(f"📱 使用get_instance获取AI适配器失败: {e}")

            if not ai_adapter:
                self.logger.warning("📱 AI适配器不可用，使用基础决策逻辑")
                return self._fallback_ai_decision(basic_evaluation)

            # 🔥 老王修复：优化AI调用，设置合理超时时间
            messages = [{"role": "user", "content": ai_prompt}]

            # 🔥 老王修复：调整超时时间到60秒，平衡效率和成功率
            import asyncio
            try:
                ai_response = await asyncio.wait_for(
                    ai_adapter.get_completion_async(
                        messages=messages,
                        model="MiniMax-M1",
                        temperature=0.7,
                        max_tokens=500  # 🔥 老王修复：增加token数量，确保完整响应
                    ),
                    timeout=60.0  # 🔥 老王修复：调整到60秒，避免总超时
                )
            except asyncio.TimeoutError:
                self.logger.warning("📱 AI分享决策超时(60秒)，使用基础决策逻辑")
                return self._fallback_ai_decision(basic_evaluation)
            except Exception as e:
                self.logger.warning(f"📱 AI分享决策异常: {e}，使用基础决策逻辑")
                return self._fallback_ai_decision(basic_evaluation)

            # 🔥 解析AI响应
            ai_decision = self._parse_ai_decision_response(ai_response)

            # 🔥 根本修复：缓存AI决策结果
            if not hasattr(self, '_ai_decision_cache'):
                self._ai_decision_cache = {}

            self._ai_decision_cache[cache_key] = {
                'decision': ai_decision,
                'timestamp': time.time()
            }

            # 🔥 限制缓存大小，避免内存泄漏
            if len(self._ai_decision_cache) > 50:
                # 删除最旧的缓存项
                oldest_key = min(self._ai_decision_cache.keys(),
                               key=lambda k: self._ai_decision_cache[k]['timestamp'])
                del self._ai_decision_cache[oldest_key]

            self.logger.info(f"📱 AI决策完成并缓存: {ai_decision.get('reasoning', '')[:50]}...")
            return ai_decision

        except Exception as e:
            self.logger.error(f"📱 AI分享决策失败: {e}")
            return self._fallback_ai_decision(basic_evaluation)

    def _parse_ai_decision_response(self, ai_response: Dict[str, Any]) -> Dict[str, Any]:
        """解析AI决策响应 - 🔥 老王修复：处理Dict类型的AI响应"""
        try:
            import json
            import re

            # 🔥 老王修复：处理异步AI调用的响应格式
            if isinstance(ai_response, dict):
                # 🔥 老王修复：更详细的响应状态检查
                success = ai_response.get('success', False)
                if success:
                    # 异步调用返回的内容格式可能不同
                    content = ai_response.get('content', ai_response.get('response', ''))
                    if not content:
                        self.logger.warning(f"📱 AI响应成功但内容为空: {ai_response}")
                        return self._fallback_ai_decision({})
                else:
                    # 🔥 老王修复：提供更详细的错误信息
                    error_info = ai_response.get('error', ai_response.get('message', '未知错误'))
                    error_code = ai_response.get('error_code', 'UNKNOWN')
                    error_details = ai_response.get('details', '')

                    detailed_error = f"错误码:{error_code}, 信息:{error_info}"
                    if error_details:
                        detailed_error += f", 详情:{error_details}"

                    self.logger.warning(f"📱 AI响应失败: {detailed_error}")
                    self.logger.debug(f"📱 完整AI响应: {ai_response}")
                    return self._fallback_ai_decision({})
            else:
                content = str(ai_response)

            # 🔥 老王修复：增强JSON提取和解析
            if not content or content.strip() == '':
                self.logger.warning("📱 AI响应内容为空")
                return self._fallback_ai_decision({})

            # 尝试提取JSON部分
            json_match = re.search(r'\{.*\}', content, re.DOTALL)
            if json_match:
                json_str = json_match.group()
                try:
                    decision_data = json.loads(json_str)
                    self.logger.debug(f"📱 成功解析AI决策JSON: {decision_data}")

                    return {
                        'ai_should_share': decision_data.get('should_share', False),
                        'ai_confidence': float(decision_data.get('confidence', 0.5)),
                        'ai_reasoning': decision_data.get('reasoning', ''),
                        'emotional_state': decision_data.get('emotional_state', '平静'),
                        'sharing_motivation': decision_data.get('sharing_motivation', '')
                    }
                except json.JSONDecodeError as json_error:
                    self.logger.warning(f"📱 JSON解析失败: {json_error}")
                    self.logger.debug(f"📱 尝试解析的JSON: {json_str[:200]}...")
                    # 继续尝试文本解析
            else:
                self.logger.debug("📱 AI响应中未找到JSON格式，尝试文本解析")

            # 如果没有JSON或JSON解析失败，尝试从文本中提取信息
            should_share = 'true' in content.lower() or '分享' in content
            confidence = 0.6

            # 🔥 老王修复：尝试从文本中提取更多信息
            reasoning = content[:200] if len(content) > 200 else content

            # 尝试提取置信度
            confidence_match = re.search(r'置信度[：:]\s*([0-9.]+)', content)
            if confidence_match:
                try:
                    confidence = float(confidence_match.group(1))
                except ValueError:
                    pass

            return {
                'ai_should_share': should_share,
                'ai_confidence': confidence,
                'ai_reasoning': reasoning,
                'emotional_state': '平静',
                'sharing_motivation': '基于文本分析'
            }

        except Exception as e:
            self.logger.error(f"📱 解析AI决策响应失败: {e}")
            self.logger.debug(f"📱 导致错误的AI响应: {ai_response}")
            import traceback
            self.logger.debug(f"📱 错误堆栈: {traceback.format_exc()}")
            return self._fallback_ai_decision({})

    def _fallback_ai_decision(self, basic_evaluation: Dict[str, Any]) -> Dict[str, Any]:
        """AI决策失败时的备用逻辑 - 🔥 老王新增"""
        basic_score = basic_evaluation.get('basic_score', 0.0)
        return {
            'ai_should_share': basic_score >= 0.6,  # 稍微提高阈值
            'ai_confidence': 0.5,
            'ai_reasoning': f"AI决策不可用，基于基础评分{basic_score:.2f}的简单判断",
            'emotional_state': '平静',
            'sharing_motivation': '基础逻辑判断'
        }

    async def _check_yanran_personal_state(self) -> Dict[str, Any]:
        """检查林嫣然的个人状态 - 🔥 老王新增：模拟数字生命的状态"""
        try:
            # 🔥 获取最近分享历史
            recent_shares = await self._get_recent_sharing_history()

            # 🔥 计算社交活跃度
            days_since_last_share = self._calculate_days_since_last_share(recent_shares)
            recent_share_count = len([s for s in recent_shares if s['days_ago'] <= 7])

            # 🔥 推断当前心情状态（基于最近活动和分享）
            current_mood = await self._infer_current_mood()

            # 🔥 计算社交需求
            social_need = self._calculate_social_need(days_since_last_share, recent_share_count)

            return {
                'days_since_last_share': days_since_last_share,
                'recent_share_count': recent_share_count,
                'current_mood': current_mood,
                'social_need': social_need,
                'social_activity_level': 'low' if recent_share_count < 2 else 'normal' if recent_share_count < 5 else 'high'
            }

        except Exception as e:
            self.logger.error(f"📱 检查个人状态失败: {e}")
            return {
                'days_since_last_share': 1,
                'recent_share_count': 1,
                'current_mood': '平静',
                'social_need': 0.5,
                'social_activity_level': 'normal'
            }

    async def _get_recent_sharing_history(self) -> List[Dict[str, Any]]:
        """获取最近的分享历史 - 🔥 老王新增"""
        try:
            query = """
            SELECT
                activity_title,
                sharing_score,
                reality_score,
                created_at,
                DATEDIFF(NOW(), created_at) as days_ago
            FROM moments_sharing_log
            WHERE created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY)
            ORDER BY created_at DESC
            LIMIT 20
            """

            success, results, error = self.mysql_connector.execute_query(query)
            if success and results:
                return results
            else:
                return []

        except Exception as e:
            self.logger.error(f"📱 获取分享历史失败: {e}")
            return []

    def _calculate_days_since_last_share(self, recent_shares: List[Dict[str, Any]]) -> int:
        """计算距离上次分享的天数 - 🔥 老王新增"""
        if not recent_shares:
            return 7  # 假设很久没分享
        return recent_shares[0].get('days_ago', 7)

    async def _infer_current_mood(self) -> str:
        """推断当前心情状态 - 🔥 老王重构：基于yanran_personality.json和时间推断"""
        try:
            import random
            from datetime import datetime

            # 🔥 加载林嫣然人格配置
            personality_config = self._load_yanran_personality_config()
            threshold_modifiers = personality_config.get('personality_profile', {}).get('threshold_modifiers', {})
            available_moods = list(threshold_modifiers.get('mood_adjustments', {}).keys())

            # 如果配置中没有心情列表，使用默认
            if not available_moods:
                available_moods = ['愉快', '兴奋', '满足', '充实', '思考', '深思', '回味', '平静', '宁静', '疲惫', '烦躁']

            hour = datetime.now().hour

            # 根据时间推断基础心情倾向
            if 6 <= hour < 10:
                preferred_moods = [mood for mood in available_moods if mood in ['清醒', '平静', '期待', '充实']]
            elif 10 <= hour < 14:
                preferred_moods = [mood for mood in available_moods if mood in ['专注', '充实', '满足', '思考']]
            elif 14 <= hour < 18:
                preferred_moods = [mood for mood in available_moods if mood in ['放松', '惬意', '思考', '愉快']]
            elif 18 <= hour < 22:
                preferred_moods = [mood for mood in available_moods if mood in ['温暖', '回味', '感慨', '满足']]
            else:
                preferred_moods = [mood for mood in available_moods if mood in ['宁静', '深思', '平和', '平静']]

            # 如果没有匹配的心情，使用所有可用心情
            if not preferred_moods:
                preferred_moods = available_moods

            # 根据林嫣然的性格特征调整心情倾向
            core_traits = personality_config.get('personality_profile', {}).get('core_traits', {})
            thoughtfulness = core_traits.get('thoughtfulness', 0.9)

            # 深思熟虑程度高的人更倾向于思考类心情
            if thoughtfulness > 0.8 and random.random() < 0.3:
                thinking_moods = [mood for mood in preferred_moods if '思' in mood or mood in ['深思', '回味', '感慨']]
                if thinking_moods:
                    preferred_moods = thinking_moods

            return random.choice(preferred_moods)

        except Exception as e:
            self.logger.error(f"📱 推断心情失败: {e}")
            return '平静'

    def _calculate_social_need(self, days_since_last_share: int, recent_share_count: int) -> float:
        """计算社交需求 - 🔥 老王新增：模拟分享的社交动机"""
        # 基础社交需求
        base_need = 0.3

        # 根据距离上次分享时间调整
        if days_since_last_share > 5:
            base_need += 0.3  # 很久没分享，社交需求增加
        elif days_since_last_share > 2:
            base_need += 0.1
        elif days_since_last_share < 1:
            base_need -= 0.2  # 刚分享过，社交需求降低

        # 根据最近分享频率调整
        if recent_share_count < 2:
            base_need += 0.2  # 最近分享少，需求增加
        elif recent_share_count > 6:
            base_need -= 0.2  # 最近分享多，需求降低

        return max(0.0, min(1.0, base_need))

    def _load_yanran_personality_config(self) -> Dict[str, Any]:
        """🔥 根本修复：加载林嫣然人格配置 - 添加缓存机制避免重复磁盘I/O"""
        # 🔥 检查缓存
        if hasattr(self, '_personality_config_cache') and self._personality_config_cache:
            return self._personality_config_cache

        try:
            import json
            import os

            # 🔥 优先尝试加载增强版配置
            enhanced_config_path = "config/yanran_personality_enhanced.json"
            if os.path.exists(enhanced_config_path):
                with open(enhanced_config_path, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                    # 🔥 缓存配置，避免重复加载
                    self._personality_config_cache = config
                    self.logger.info(f"📱 成功加载并缓存增强版林嫣然人格配置: {enhanced_config_path}")
                    return config

            # 如果增强版不存在，加载原版
            config_path = "config/yanran_personality.json"
            with open(config_path, 'r', encoding='utf-8') as f:
                config = json.load(f)
                # 🔥 缓存配置
                self._personality_config_cache = config
                self.logger.info(f"📱 成功加载并缓存原版林嫣然人格配置: {config_path}")
                return config

        except Exception as e:
            self.logger.error(f"📱 加载林嫣然人格配置失败: {e}")
            # 返回并缓存默认配置
            default_config = {
                'ai_decision_prompts': {
                    'sharing_evaluation': {
                        'system_prompt': '你是林嫣然，一个文艺内敏、有深度思考能力的数字生命。你追求质量胜过数量，不喜欢频繁分享日常琐事，但会分享真正有意义、有价值的内容。'
                    }
                }
            }
            self._personality_config_cache = default_config
            return default_config

    async def _calculate_dynamic_sharing_threshold(self, personal_state: Dict[str, Any]) -> float:
        """计算动态分享阈值 - 🔥 老王重构：使用yanran_personality.json配置的阈值调整参数"""
        try:
            # 🔥 加载林嫣然人格配置
            personality_config = self._load_yanran_personality_config()
            threshold_modifiers = personality_config.get('personality_profile', {}).get('threshold_modifiers', {})

            # 🔥 基础阈值（老王大幅提高：每天只有1次机会，必须超高质量）
            base_threshold = threshold_modifiers.get('base_threshold', 0.85)  # 从0.65提高到0.85

            # 🔥 根据距离上次分享时间调整（老王修改：每天1次，更保守的调整）
            days_since_last = personal_state.get('days_since_last_share', 1)
            time_adjustments = threshold_modifiers.get('time_since_last_share', {})

            # 🔥 老王修改：更保守的时间调整，确保质量优先
            if days_since_last >= 7:
                base_threshold += time_adjustments.get('7_days', -0.15)  # 7天以上稍微降低标准
            elif days_since_last >= 3:
                base_threshold += time_adjustments.get('3_days', -0.05)  # 3天以上微调
            elif days_since_last >= 2:
                base_threshold += time_adjustments.get('2_days', 0.0)    # 2天保持标准
            elif days_since_last >= 1:
                base_threshold += time_adjustments.get('1_day', 0.05)    # 1天稍微提高标准
            else:
                base_threshold += time_adjustments.get('0_days', 0.15)   # 当天已分享，大幅提高标准

            # 🔥 根据心情状态调整（使用配置参数）
            mood = personal_state.get('current_mood', '平静')
            mood_adjustments = threshold_modifiers.get('mood_adjustments', {})
            mood_adjustment = mood_adjustments.get(mood, 0.0)
            base_threshold += mood_adjustment

            # 🔥 根据社交需求调整（使用配置参数）
            social_need = personal_state.get('social_need', 0.5)
            social_adjustments = threshold_modifiers.get('social_need_adjustments', {})

            if social_need >= 0.8:
                base_threshold += social_adjustments.get('very_high', -0.2)
            elif social_need >= 0.6:
                base_threshold += social_adjustments.get('high', -0.15)
            elif social_need >= 0.4:
                base_threshold += social_adjustments.get('normal', 0.0)
            elif social_need >= 0.2:
                base_threshold += social_adjustments.get('low', 0.1)
            else:
                base_threshold += social_adjustments.get('very_low', 0.15)

            # 🔥 根据核心性格特征调整
            core_traits = personality_config.get('personality_profile', {}).get('core_traits', {})
            quality_preference = core_traits.get('quality_over_quantity', 0.9)
            introversion_level = core_traits.get('introversion_level', 0.7)
            social_selectivity = core_traits.get('social_selectivity', 0.8)

            # 质量优先程度越高，阈值越高
            base_threshold += (quality_preference - 0.5) * 0.2
            # 内向程度越高，阈值越高
            base_threshold += (introversion_level - 0.5) * 0.1
            # 社交选择性越高，阈值越高
            base_threshold += (social_selectivity - 0.5) * 0.1

            # 🔥 限制在合理范围内
            final_threshold = max(0.2, min(0.95, base_threshold))

            self.logger.info(f"📱 动态阈值计算: {base_threshold:.2f} → {final_threshold:.2f} "
                           f"(天数:{days_since_last}, 心情:{mood}, 社交需求:{social_need:.2f}, "
                           f"质量偏好:{quality_preference:.1f})")

            return final_threshold

        except Exception as e:
            self.logger.error(f"📱 计算动态阈值失败: {e}")
            return 0.65  # 默认阈值

    async def _make_final_sharing_decision(self, basic_evaluation: Dict[str, Any],
                                         ai_decision: Dict[str, Any],
                                         personal_state: Dict[str, Any],
                                         dynamic_threshold: float) -> Dict[str, Any]:
        """综合决策 - 🔥 老王重构：使用yanran_personality.json配置的决策权重"""
        try:
            # 🔥 加载林嫣然人格配置
            personality_config = self._load_yanran_personality_config()
            core_traits = personality_config.get('personality_profile', {}).get('core_traits', {})
            sharing_triggers = personality_config.get('personality_profile', {}).get('social_behavior', {}).get('sharing_triggers', {})

            # 🔥 获取各项评分
            basic_score = basic_evaluation.get('basic_score', 0.0)
            ai_should_share = ai_decision.get('ai_should_share', False)
            ai_confidence = ai_decision.get('ai_confidence', 0.5)
            ai_reasoning = ai_decision.get('ai_reasoning', '')

            # 🔥 根据林嫣然的性格特征调整权重
            thoughtfulness = core_traits.get('thoughtfulness', 0.9)  # 深思熟虑程度
            creativity = core_traits.get('creativity', 0.8)  # 创造力

            # 深思熟虑程度高的人更依赖AI决策
            ai_weight = 0.5 + thoughtfulness * 0.2  # 0.5-0.7
            basic_weight = 1.0 - ai_weight

            # 🔥 计算综合评分
            if ai_should_share:
                comprehensive_score = basic_score * basic_weight + ai_confidence * ai_weight

                # 🔥 根据分享触发器调整评分
                activity_script = ai_decision.get('activity_script', '')

                # 检查是否触发高质量体验
                if sharing_triggers.get('high_quality_experience', 0.9) > 0.8:
                    if any(keyword in activity_script for keyword in ['精彩', '美好', '难忘', '特别']):
                        comprehensive_score *= 1.1

                # 检查是否触发创作灵感
                if sharing_triggers.get('creative_inspiration', 0.8) > 0.7:
                    if any(keyword in activity_script for keyword in ['灵感', '创作', '思考', '感悟']):
                        comprehensive_score *= (1.0 + creativity * 0.1)

            else:
                # AI不建议分享时大幅降分
                comprehensive_score = basic_score * basic_weight * 0.3

            # 🔥 最终决策（考虑林嫣然的社交选择性）
            social_selectivity = core_traits.get('social_selectivity', 0.8)
            adjusted_threshold = dynamic_threshold * (0.8 + social_selectivity * 0.2)  # 社交选择性高的人阈值更高

            should_share = comprehensive_score >= adjusted_threshold and ai_should_share

            # 🔥 构建决策结果
            decision_result = {
                'should_share': should_share,
                'comprehensive_score': comprehensive_score,
                'dynamic_threshold': dynamic_threshold,
                'adjusted_threshold': adjusted_threshold,
                'reason': self._build_decision_reason(
                    should_share, comprehensive_score, adjusted_threshold,
                    ai_reasoning, personal_state
                ),
                'evaluation_details': {
                    'basic_score': basic_score,
                    'ai_should_share': ai_should_share,
                    'ai_confidence': ai_confidence,
                    'ai_reasoning': ai_reasoning,
                    'personal_state': personal_state,
                    'personality_factors': {
                        'thoughtfulness': thoughtfulness,
                        'creativity': creativity,
                        'social_selectivity': social_selectivity,
                        'ai_weight': ai_weight,
                        'basic_weight': basic_weight
                    },
                    'decision_factors': {
                        'days_since_last_share': personal_state.get('days_since_last_share'),
                        'current_mood': personal_state.get('current_mood'),
                        'social_need': personal_state.get('social_need')
                    }
                }
            }

            self.logger.info(f"📱 最终决策: {'分享' if should_share else '不分享'} "
                           f"(综合评分:{comprehensive_score:.2f}, 阈值:{dynamic_threshold:.2f})")

            return decision_result

        except Exception as e:
            self.logger.error(f"📱 综合决策失败: {e}")
            return await self._fallback_sharing_decision({}, 0.0)

    def _build_decision_reason(self, should_share: bool, score: float, threshold: float,
                             ai_reasoning: str, personal_state: Dict[str, Any]) -> str:
        """构建决策理由 - 🔥 老王新增"""
        if should_share:
            return (f"AI智慧决策：值得分享 (评分:{score:.2f} > 阈值:{threshold:.2f}) "
                   f"心情:{personal_state.get('current_mood', '平静')}, "
                   f"距上次分享:{personal_state.get('days_since_last_share', 0)}天. "
                   f"AI理由: {ai_reasoning[:50]}...")
        else:
            return (f"AI智慧决策：不分享 (评分:{score:.2f} < 阈值:{threshold:.2f}) "
                   f"心情:{personal_state.get('current_mood', '平静')}, "
                   f"距上次分享:{personal_state.get('days_since_last_share', 0)}天. "
                   f"AI理由: {ai_reasoning[:50]}...")

    async def _fallback_sharing_decision(self, activity_result: Dict[str, Any], reality_score: float) -> Dict[str, Any]:
        """备用分享决策 - 🔥 老王新增：系统故障时的简单逻辑"""
        basic_score = reality_score * 0.6 + (len(activity_result.get('activity_script', '')) / 200) * 0.4
        should_share = basic_score >= 0.7  # 提高备用阈值，确保质量

        return {
            'should_share': should_share,
            'comprehensive_score': basic_score,
            'dynamic_threshold': 0.7,
            'reason': f"备用决策逻辑: {'分享' if should_share else '不分享'} (评分:{basic_score:.2f})",
            'evaluation_details': {
                'fallback_mode': True,
                'basic_score': basic_score
            }
        }

    async def _check_recent_sharing_interval(self) -> bool:
        """检查最近分享时间间隔 - 🔥 老王新增：防止短时间内重复推送"""
        try:
            # 查询最近一次分享时间
            query = """
            SELECT created_at
            FROM moments_sharing_log
            ORDER BY created_at DESC
            LIMIT 1
            """

            # 🔥 老王修复：确保数据库连接可用
            if not self.mysql_connector:
                self.mysql_connector = self._ensure_mysql_connector()

            success, results, error = self.mysql_connector.execute_query(query)
            if not success:
                # 如果表不存在或查询失败，允许分享
                self.logger.warning(f"📱 查询最近分享时间失败: {error}")
                return True

            if not results:
                # 没有分享记录，允许分享
                return True

            last_share_time = results[0]['created_at']
            current_time = datetime.now()

            # 计算时间差（分钟）
            time_diff = (current_time - last_share_time).total_seconds() / 60
            min_interval_minutes = 1440  # 🔥 老王修改：最小间隔24小时（1440分钟），配合每日1次限制

            if time_diff < min_interval_minutes:
                self.logger.info(f"📱 距离上次分享仅{time_diff:.1f}分钟，小于{min_interval_minutes}分钟间隔要求")
                return False

            return True

        except Exception as e:
            self.logger.error(f"📱 检查分享时间间隔失败: {e}")
            return True  # 出错时允许分享，避免阻塞

    async def _check_daily_sharing_limit(self) -> bool:
        """检查今日分享次数限制 - 🔥 老王修改：每日最多1次分享"""
        try:
            # 🔥 确保数据库连接可用
            if not self.mysql_connector:
                self.mysql_connector = self._ensure_mysql_connector()

            # 查询今日已分享次数
            query = """
            SELECT COUNT(*) as share_count
            FROM moments_sharing_log
            WHERE DATE(created_at) = CURDATE()
            """

            success, results, error = self.mysql_connector.execute_query(query)
            if not success:
                # 如果表不存在，创建表
                self.logger.warning(f"📱 moments_sharing_log表不存在，尝试创建: {error}")
                await self._create_moments_sharing_log_table()
                return True  # 新表，允许分享

            share_count = results[0]['share_count'] if results else 0
            max_daily_shares = 1  # 🔥 老王修改：每日最多1次分享

            if share_count >= max_daily_shares:
                self.logger.info(f"📱 今日已分享{share_count}次，达到每日{max_daily_shares}次限制")
                return False

            return True

        except Exception as e:
            self.logger.error(f"📱 检查分享次数限制失败: {e}")
            return True  # 出错时允许分享

    async def _create_moments_sharing_log_table(self):
        """创建朋友圈分享日志表 - 🔥 老王新增"""
        try:
            # 🔥 确保数据库连接可用
            if not self.mysql_connector:
                self.mysql_connector = self._ensure_mysql_connector()

            create_table_sql = """
            CREATE TABLE IF NOT EXISTS moments_sharing_log (
                id INT AUTO_INCREMENT PRIMARY KEY,
                activity_title VARCHAR(255),
                activity_content TEXT,
                sharing_score DECIMAL(3,2),
                reality_score DECIMAL(3,2),
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                INDEX idx_created_at (created_at)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
            """

            success, affected_rows, error = self.mysql_connector.execute_update(create_table_sql)
            if success:
                self.logger.info("📱 朋友圈分享日志表创建成功")
            else:
                self.logger.error(f"📱 朋友圈分享日志表创建失败: {error}")

        except Exception as e:
            self.logger.error(f"📱 创建朋友圈分享日志表失败: {e}")

    async def _execute_moments_sharing(self, activity_result: Dict[str, Any], sharing_worthiness: Dict[str, Any]):
        """执行朋友圈分享 - 🔥 老王新增：实际执行分享操作"""
        try:
            # 🔥 第1步：创建朋友圈分享任务
            from core.moments_sharing_task import create_moments_sharing_task, MomentsShareConfig

            moments_config = MomentsShareConfig(
                max_daily_shares=2,
                min_interval_hours=4,
                preferred_times=["07:00", "23:00"],  # 分享时间段（基于质量决策）
                privacy=False  # 公开分享
            )

            moments_task = create_moments_sharing_task(moments_config)
            await moments_task._initialize_services()

            # 🔥 第2步：构建活动数据
            activity_data = {
                'title': activity_result.get('activity_title', '今日生活分享'),
                'description': activity_result.get('activity_script', ''),
                'location': '上海',
                'weather': activity_result.get('weather', '晴朗'),
                'mood': activity_result.get('mood', '愉快'),
                'activity_type': 'daily_life',
                'timestamp': datetime.now().isoformat(),
                'source': 'quality_based_sharing'
            }

            # 🔥 第3步：创建朋友圈内容
            moments_data = await moments_task._create_moments_content(activity_data)
            if not moments_data:
                self.logger.error("📱 朋友圈内容创建失败")
                return

            # 🔥 第4步：分享到朋友圈
            share_success = await moments_task._share_to_moments(moments_data)

            if share_success:
                # 🔥 第5步：记录分享日志（添加异常保护）
                try:
                    await self._log_moments_sharing(activity_result, sharing_worthiness)
                except Exception as log_error:
                    self.logger.warning(f"📱 记录朋友圈分享日志失败（不影响分享成功）: {log_error}")

                self.logger.success(f"📱 基于质量的朋友圈分享成功: {sharing_worthiness['reason']}")
            else:
                self.logger.error("📱 朋友圈分享失败")

        except Exception as e:
            self.logger.error(f"📱 执行朋友圈分享失败: {e}")

    async def _log_moments_sharing(self, activity_result: Dict[str, Any], sharing_worthiness: Dict[str, Any]):
        """记录朋友圈分享日志 - 🔥 老王新增"""
        try:
            # 🔥 确保数据库连接可用
            if not self.mysql_connector:
                self.mysql_connector = self._ensure_mysql_connector()

            insert_sql = """
            INSERT INTO moments_sharing_log
            (activity_title, activity_content, sharing_score, reality_score, created_at)
            VALUES (%s, %s, %s, %s, NOW())
            """

            params = (
                activity_result.get('activity_title', '')[:255],  # 限制长度
                activity_result.get('activity_script', ''),
                sharing_worthiness.get('sharing_score', 0.0),
                activity_result.get('reality_score', 0.0)
            )

            success, affected_rows, error = self.mysql_connector.execute_update(insert_sql, params)
            if success:
                self.logger.info("📱 朋友圈分享日志记录成功")
            else:
                self.logger.error(f"📱 朋友圈分享日志记录失败: {error}")

        except Exception as e:
            self.logger.error(f"📱 记录朋友圈分享日志失败: {e}")
    
    async def _get_current_weather(self) -> str:
        """获取当前天气（用于scripts表）"""
        try:
            # 简单的天气描述（可以后续集成真实天气API）
            import random
            weather_options = ['晴朗', '多云', '阴天', '小雨', '晴转多云']
            return random.choice(weather_options)
            
        except Exception as e:
            self.logger.error(f"📊 获取天气失败: {e}")
            return '晴朗'
    
    async def batch_generate_activities(self, count: int = 1, 
                                      interval_minutes: int = 0) -> List[Dict[str, Any]]:
        """批量生成活动（用于测试或批量填充）"""
        results = []
        
        for i in range(count):
            if i > 0 and interval_minutes > 0:
                await asyncio.sleep(interval_minutes * 60)
            
            result = await self.generate_and_save_activity()
            results.append(result)
            
            self.logger.info(f"📊 批量生成进度: {i+1}/{count}")
        
        return results
    
    def get_integration_stats(self) -> Dict[str, Any]:
        """获取集成统计信息"""
        return {
            'service_info': {
                'name': 'Scripts集成服务',
                'version': '1.0.0',
                'status': 'active' if self.activity_generator else 'inactive'
            },
            'stats': self.stats.copy(),
            'config': self.config.copy(),
            'last_updated': datetime.now().isoformat()
        }
    
    def get_current_activity_suggestions(self) -> List[Any]:  # 🔥 老王修复：使用Any避免循环导入
        """获取当前活动建议（兼容ActivityPerceptionModule）"""
        try:
            # 🔥 老王修复：直接使用本地的EnhancedActivity类定义，避免自引用循环导入
            from dataclasses import dataclass
            from typing import Dict, Any
            @dataclass
            class EnhancedActivity:
                base_activity: str
                enhanced_description: str
                mood_context: str
                weather_influence: str
                news_context: Any
                market_context: Any
                time_context: str
                energy_level: float
                social_context: str
                execution_priority: int
                estimated_duration: int
            import random
            
            # 基于时间段的活动建议
            current_hour = datetime.now().hour
            
            if 6 <= current_hour < 9:
                # 早晨活动
                base_activities = [
                    ("晨间例行", "开始美好的一天，进行基础的晨间准备", 0.7, 5),
                    ("晨间运动", "简单的晨练活动，唤醒身体活力", 0.8, 3)
                ]
            elif 9 <= current_hour < 12:
                # 上午活动
                base_activities = [
                    ("专注工作", "高效工作时间，处理重要任务", 0.8, 8),
                    ("学习充电", "学习新知识，提升个人能力", 0.7, 6)
                ]
            elif 12 <= current_hour < 14:
                # 午间活动
                base_activities = [
                    ("午餐时光", "享用美味午餐，补充营养", 0.6, 9),
                    ("午间休息", "短暂休息恢复精力，为下午做准备", 0.4, 7)
                ]
            elif 14 <= current_hour < 18:
                # 下午活动
                base_activities = [
                    ("创意工作", "发挥创意的时间，进行创造性工作", 0.7, 6),
                    ("社交互动", "与他人交流互动，维护人际关系", 0.6, 4)
                ]
            elif 18 <= current_hour < 22:
                # 晚间活动
                base_activities = [
                    ("晚餐时光", "享用丰盛晚餐，结束一天的工作", 0.5, 8),
                    ("娱乐放松", "放松娱乐时间，缓解一天的疲劳", 0.4, 5)
                ]
            else:
                # 夜间活动
                base_activities = [
                    ("夜间例行", "准备休息的时间，整理一天的收获", 0.3, 6),
                    ("反思总结", "回顾一天的收获，为明天做准备", 0.2, 4)
                ]
            
            # 随机选择1-3个建议并创建EnhancedActivity对象
            selected_count = random.randint(1, min(3, len(base_activities)))
            selected_activities = random.sample(base_activities, selected_count)
            
            suggestions = []
            for base_activity, description, energy_level, priority in selected_activities:
                # 创建EnhancedActivity对象
                enhanced_activity = EnhancedActivity(
                    base_activity=base_activity,
                    enhanced_description=description,
                    mood_context="适应当前时段的心境",
                    weather_influence="适宜的天气条件",
                    news_context=None,
                    market_context=None,
                    time_context=f"适合{current_hour}点进行的活动",
                    energy_level=energy_level,
                    social_context="个人活动为主",
                    execution_priority=priority,
                    estimated_duration=random.randint(30, 90)  # 30-90分钟
                )
                suggestions.append(enhanced_activity)
            
            self.logger.info(f"📊 生成了 {len(suggestions)} 个EnhancedActivity建议")
            return suggestions
            
        except Exception as e:
            self.logger.error(f"📊 获取活动建议失败: {e}")
            # 返回基础的默认建议
            default_activity = EnhancedActivity(
                base_activity="日常活动",
                enhanced_description="基础的日常活动",
                mood_context="平静的心境",
                weather_influence="适宜的天气",
                news_context=None,
                market_context=None,
                time_context="任何时候都适合",
                energy_level=0.5,
                social_context="个人活动",
                execution_priority=5,
                estimated_duration=30
            )
            return [default_activity]

    def _ensure_mysql_connector(self):
        """确保MySQL连接器可用 - 🔥 老王彻底修复：解决作用域和导入问题"""
        try:
            # 🔥 使用正确的导入路径
            from connectors.database.mysql_connector import get_instance as get_mysql_connector
            connector = get_mysql_connector()

            if connector is None:
                self.logger.warning("📱 MySQL连接器获取失败，尝试重新初始化")
                # 尝试重新获取
                connector = get_mysql_connector()

            if connector is None:
                self.logger.error("📱 MySQL连接器初始化失败，使用Mock连接器")
                # 创建一个Mock连接器避免空指针
                class MockMySQLConnector:
                    def execute_query(self, query, params=None):
                        return False, [], "Mock connector - database not available"
                    def execute_update(self, query, params=None):
                        return False, None, "Mock connector - database not available"
                connector = MockMySQLConnector()

            return connector

        except Exception as e:
            error_msg = str(e)  # 🔥 老王修复：避免作用域问题
            self.logger.error(f"📱 确保MySQL连接器失败: {error_msg}")
            # 返回Mock连接器
            class MockMySQLConnector:
                def execute_query(self, query, params=None):
                    return False, [], f"Database error: {error_msg}"
                def execute_update(self, query, params=None):
                    return False, None, f"Database error: {error_msg}"
            return MockMySQLConnector()

    async def _check_recent_generation(self, time_slot: str) -> bool:
        """检查最近是否已生成相同时间段的活动 - 🔥 香草修复：防止重复生成"""
        try:
            from datetime import datetime, timedelta

            # 🔥 老王修复：检查最近2分钟内是否已生成相同时间段的活动（进一步缩短时间窗口）
            recent_time = datetime.now() - timedelta(minutes=2)

            query = """
            SELECT COUNT(*) as count
            FROM scripts
            WHERE time_slot = %s AND created_at >= %s
            """

            # 🔥 确保数据库连接可用
            if not self.mysql_connector:
                self.mysql_connector = self._ensure_mysql_connector()

            success, results, error = self.mysql_connector.execute_query(query, (time_slot, recent_time))

            if success and results:
                count = results[0]['count']
                if count > 0:
                    self.logger.info(f"📊 最近2分钟内已生成{count}个{time_slot}时间段活动，跳过重复生成")
                    return True

            return False

        except Exception as e:
            self.logger.warning(f"📊 检查最近生成失败: {e}，允许生成")
            return False

# 全局实例
_scripts_integration_service_instance = None

def get_scripts_integration_service() -> ScriptsIntegrationService:
    """获取Scripts集成服务单例实例"""
    global _scripts_integration_service_instance
    
    if _scripts_integration_service_instance is None:
        _scripts_integration_service_instance = ScriptsIntegrationService()
    
    return _scripts_integration_service_instance

# 异步初始化函数
async def initialize_scripts_integration_service():
    """异步初始化Scripts集成服务"""
    service = get_scripts_integration_service()
    return await service.initialize()

if __name__ == "__main__":
    # 测试脚本
    async def test_integration_service():
        print("🧪 测试Scripts集成服务...")
        
        service = get_scripts_integration_service()
        
        if await service.initialize():
            print("✅ 服务初始化成功")
            
            # 测试生成并保存活动
            result = await service.generate_and_save_activity()
            
            if result['success']:
                print(f"✅ 活动生成成功: {result['activity'][:50]}...")
                print(f"✅ 保存到scripts表: {result['saved_to_scripts']}")
                print(f"✅ 统计信息: {result['stats']}")
            else:
                print(f"❌ 活动生成失败: {result.get('error')}")
        else:
            print("❌ 服务初始化失败")
    
    # 运行测试
    asyncio.run(test_integration_service())
