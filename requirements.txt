# 林嫣然数字生命体系统 - 生产环境依赖
# 版本: v3.1.0
# 更新日期: 2024-12-29

# ==================== 核心Web框架 ====================
# Flask - HTTP API服务器
flask>=2.2.3
flask-cors>=4.0.0

# WebSocket服务器
websockets>=11.0.2

# ==================== 异步HTTP客户端 ====================
# 异步HTTP请求
aiohttp>=3.8.0
requests>=2.28.2

# ==================== 数据库和存储 ====================
# MySQL数据库
mysql-connector-python>=8.0.33
pymysql>=1.0.3
sqlalchemy>=2.0.9

# Redis缓存和消息队列
redis>=4.5.4

# 向量数据库
chromadb==0.4.0

# ==================== AI和NLP ====================
# OpenAI API - 🔥 老王修复：升级到新版本支持openai.OpenAI()
openai>=1.0.0

# 自然语言处理
nltk>=3.8.1
transformers>=4.28.1
sentence-transformers>=2.2.2

# ==================== 数据处理 ====================
# 数值计算
numpy>=1.24.2

# 数据分析（用于金融数据处理）
pandas>=2.0.0

# 统计计算（用于风险分析）
scipy>=1.10.0

# 数据可视化（用于图表生成）
matplotlib>=3.7.1

# ==================== 系统工具 ====================
# 配置管理
python-dotenv>=1.0.0
pyyaml>=6.0

# 系统监控
psutil>=5.9.5

# 进度条
tqdm>=4.62.0

# 日期时间处理
python-dateutil>=2.8.2

# ==================== 安全和验证 ====================
# 数据验证
pydantic>=1.10.0

# 加密
cryptography>=3.4.8

# ==================== 开发和调试工具 ====================
# 类型检查
typing-extensions>=4.5.0

# 内存分析
memory-profiler>=0.60.0

# ==================== 可选依赖 ====================
# 机器学习 - 🔥 老王修复：添加趋势分析必需包
scikit-learn>=1.2.0

# 高级日志记录 - 🔥 老王修复：添加系统监控必需包
loguru>=0.7.0

# 网络分析 - 用于复杂关系分析
networkx>=3.0

# 金融数据源（可选）
# akshare>=1.11.0

# 图像处理（可选）
# pillow>=9.5.0

# 语音处理（可选）
# speechrecognition>=3.10.0

# ==================== 版本锁定 ====================
# 为确保生产环境稳定性，锁定关键依赖版本
flask==2.2.3
websockets==11.0.2
redis==4.5.4
mysql-connector-python==8.0.33
openai>=1.0.0
numpy==1.24.2
pandas==2.0.0
python-dotenv==1.0.0
pyyaml==6.0
psutil==5.9.5 