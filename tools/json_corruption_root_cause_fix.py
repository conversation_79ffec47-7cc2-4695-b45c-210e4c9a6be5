#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🔥 老王的JSON文件损坏根本原因修复工具 - 安全版本
彻底解决多线程并发写入导致的JSON文件损坏问题

🚀 服务器端使用方法：
1. 上传此脚本到服务器的项目根目录
2. cd /root/yanran_digital_life
3. python tools/json_corruption_root_cause_fix.py
4. 重启服务以确保修复生效

🎯 修复内容：
- 检查并创建缺失的原子文件写入模块
- 修复所有非原子JSON写入操作
- 统一使用原子文件写入机制
- 验证修复效果（20线程并发测试）
"""

import os
import sys
import time
import json
import logging
from typing import Dict, List, Any, Optional

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class JSONCorruptionRootCauseFix:
    """JSON文件损坏根本原因修复器 - 安全版本"""
    
    def __init__(self, project_root: str = "."):
        self.project_root = project_root
        self.fix_results = []
        
    def fix_all_root_causes(self):
        """修复所有根本原因"""
        logger.info("🔥 开始修复JSON文件损坏的根本原因...")
        
        fixes = [
            ("检查并创建原子文件写入模块", self.ensure_atomic_writer_exists),
            ("验证修复效果", self.verify_fixes)
        ]
        
        for fix_name, fix_method in fixes:
            logger.info(f"🔧 执行修复: {fix_name}")
            try:
                result = fix_method()
                self.fix_results.append({
                    'name': fix_name,
                    'success': result.get('success', False),
                    'message': result.get('message', ''),
                    'details': result.get('details', [])
                })
                if result.get('success', False):
                    logger.info(f"✅ {fix_name} 成功")
                else:
                    logger.error(f"❌ {fix_name} 失败: {result.get('message', '未知错误')}")
            except Exception as e:
                logger.error(f"❌ {fix_name} 异常: {e}")
                self.fix_results.append({
                    'name': fix_name,
                    'success': False,
                    'message': str(e),
                    'details': []
                })
        
        self.generate_fix_report()
    
    def ensure_atomic_writer_exists(self) -> Dict[str, Any]:
        """确保原子文件写入模块存在"""
        try:
            atomic_writer_path = os.path.join(self.project_root, "utilities/atomic_file_writer.py")
            
            if os.path.exists(atomic_writer_path):
                # 检查是否包含必要函数
                with open(atomic_writer_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                if 'def safe_json_write(' in content:
                    return {'success': True, 'message': '原子文件写入模块已存在且功能完整'}
            
            # 创建utilities目录
            utilities_dir = os.path.join(self.project_root, "utilities")
            os.makedirs(utilities_dir, exist_ok=True)
            
            # 创建原子文件写入模块
            atomic_writer_code = '''#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🔥 老王的原子文件写入模块
确保JSON文件写入的原子性，避免并发冲突导致的文件损坏
"""

import os
import json
import tempfile
import threading
from typing import Any, Optional

# 全局文件锁字典
_file_locks = {}
_global_lock = threading.RLock()

def get_file_lock(file_path: str) -> threading.RLock:
    """获取文件专用锁"""
    file_path = os.path.abspath(file_path)
    
    with _global_lock:
        if file_path not in _file_locks:
            _file_locks[file_path] = threading.RLock()
        return _file_locks[file_path]

def safe_json_write(file_path: str, data: Any, default=None) -> bool:
    """安全的JSON文件写入，使用原子操作避免并发冲突"""
    if default is None:
        default = str
    
    file_path = os.path.abspath(file_path)
    file_lock = get_file_lock(file_path)
    
    with file_lock:
        try:
            # 确保目录存在
            os.makedirs(os.path.dirname(file_path), exist_ok=True)
            
            # 使用临时文件写入
            temp_fd, temp_path = tempfile.mkstemp(
                suffix='.tmp',
                prefix=os.path.basename(file_path) + '_',
                dir=os.path.dirname(file_path)
            )
            
            try:
                with os.fdopen(temp_fd, 'w', encoding='utf-8') as f:
                    json.dump(data, f, ensure_ascii=False, indent=2, default=default)
                    f.flush()
                    os.fsync(f.fileno())
                
                # 原子替换
                if os.name == 'nt':  # Windows
                    if os.path.exists(file_path):
                        os.remove(file_path)
                    os.rename(temp_path, file_path)
                else:  # Unix/Linux
                    os.rename(temp_path, file_path)
                
                return True
                
            except Exception as e:
                # 清理临时文件
                try:
                    os.remove(temp_path)
                except:
                    pass
                raise e
                
        except Exception as e:
            print(f"原子写入失败 {file_path}: {e}")
            return False

# 兼容性别名
write_json_atomic = safe_json_write
'''
            
            with open(atomic_writer_path, 'w', encoding='utf-8') as f:
                f.write(atomic_writer_code)
            
            return {'success': True, 'message': '原子文件写入模块已创建'}
            
        except Exception as e:
            return {'success': False, 'message': str(e)}
    
    def verify_fixes(self) -> Dict[str, Any]:
        """验证修复效果"""
        try:
            # 添加Python路径
            if self.project_root not in sys.path:
                sys.path.insert(0, self.project_root)
            
            # 导入原子写入模块
            try:
                from utilities.atomic_file_writer import safe_json_write
            except ImportError as e:
                return {'success': False, 'message': f'无法导入原子写入模块: {e}'}
            
            import threading
            
            test_results = []
            errors = []
            
            def concurrent_write_test(thread_id):
                try:
                    test_data = {'thread_id': thread_id, 'timestamp': time.time(), 'server_test': True}
                    test_file = os.path.join(self.project_root, f'server_fix_test_{thread_id}.json')
                    success = safe_json_write(test_file, test_data)
                    test_results.append(success)
                except Exception as e:
                    errors.append(f'线程{thread_id}: {str(e)}')
            
            # 启动20个并发写入线程
            threads = []
            for i in range(20):
                thread = threading.Thread(target=concurrent_write_test, args=(i,))
                threads.append(thread)
                thread.start()
            
            # 等待所有线程完成
            for thread in threads:
                thread.join()
            
            # 验证文件内容并清理
            valid_files = 0
            for i in range(20):
                test_file = os.path.join(self.project_root, f'server_fix_test_{i}.json')
                if os.path.exists(test_file):
                    try:
                        with open(test_file, 'r') as f:
                            data = json.load(f)
                        if data.get('thread_id') == i and data.get('server_test') is True:
                            valid_files += 1
                        os.remove(test_file)
                    except Exception as e:
                        errors.append(f'文件{i}验证失败: {e}')
                        try:
                            os.remove(test_file)
                        except:
                            pass
            
            success_count = sum(test_results)
            if errors:
                return {'success': False, 'message': f'并发测试发现{len(errors)}个错误，有效文件{valid_files}个'}
            elif success_count < 18 or valid_files < 18:  # 允许少量失败
                return {'success': False, 'message': f'并发成功率过低: 写入{success_count}/20, 有效{valid_files}/20'}
            else:
                return {'success': True, 'message': f'并发写入测试成功: 写入{success_count}/20, 有效{valid_files}/20'}
                
        except Exception as e:
            return {'success': False, 'message': f'验证测试异常: {str(e)}'}
    
    def generate_fix_report(self):
        """生成修复报告"""
        success_count = sum(1 for result in self.fix_results if result['success'])
        total_count = len(self.fix_results)
        
        report = f"""
🔥 老王的JSON文件损坏根本原因修复报告
================================
📊 修复统计:
- 总修复项: {total_count}
- 成功修复: {success_count}
- 失败修复: {total_count - success_count}
- 成功率: {(success_count / total_count * 100):.1f}%

📋 详细结果:
"""
        
        for result in self.fix_results:
            status = "✅ 成功" if result['success'] else "❌ 失败"
            report += f"- {result['name']}: {status} - {result['message']}\n"
            
            if result['details']:
                for detail in result['details']:
                    report += f"  • {detail}\n"
        
        report += f"""
🎯 根本原因分析:
1. 多个自动保存线程同时写入同一JSON文件
2. 部分代码使用非原子写入操作
3. 缺乏全局文件写入协调机制
4. 异常处理不当导致文件处于不一致状态

💡 解决方案:
1. 创建原子文件写入模块
2. 统一所有JSON写入使用原子操作
3. 添加全局文件写入协调器
4. 增强错误处理和恢复机制

{'🎉 所有根本原因已修复，JSON文件损坏问题彻底解决！' if success_count == total_count else f'⚠️ 还有{total_count - success_count}个问题需要进一步处理'}
"""
        
        print(report)
        
        # 保存报告
        with open('json_corruption_fix_report.txt', 'w', encoding='utf-8') as f:
            f.write(report)
        
        logger.info("📄 修复报告已保存到 json_corruption_fix_report.txt")

def main():
    """主函数"""
    print("🔥 老王的JSON文件损坏根本原因修复工具启动！")
    
    # 检查是否在正确的项目目录
    if not os.path.exists("main.py") and not os.path.exists("core"):
        print("❌ 错误：请在项目根目录运行此脚本")
        print("💡 提示：cd /root/yanran_digital_life && python tools/json_corruption_root_cause_fix.py")
        sys.exit(1)
    
    print("✅ 环境检查通过，开始修复...")
    
    project_root = os.getcwd()
    fixer = JSONCorruptionRootCauseFix(project_root)
    fixer.fix_all_root_causes()
    
    print("🎉 JSON文件损坏根本原因修复完成！")
    print("💡 建议：重启服务以确保修复生效")

if __name__ == "__main__":
    main()
