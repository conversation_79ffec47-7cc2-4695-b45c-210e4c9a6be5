#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
MySQL数据库连接器
提供MySQL数据库连接和操作功能，支持邮件数据获取。
作者: Claude
创建日期: 2025-06-30
版本: 1.0
"""
import os
import time
import json
import pymysql
import pandas as pd
from utilities.unified_logger import get_unified_logger, setup_unified_logging
import threading
import traceback
from typing import Dict, Any, List, Tuple, Optional, Union, Callable
from datetime import datetime, timedelta
from utilities.singleton_manager import get_singleton_manager
from core.exception import database_operation, critical_operation, network_operation
# 尝试导入MySQL连接库
try:
    import mysql.connector
    from mysql.connector import pooling
    from mysql.connector.errors import (
        Error, InterfaceError, DatabaseError, DataError, 
        OperationalError, IntegrityError, InternalError, 
        ProgrammingError, NotSupportedError
    )
    MYSQL_AVAILABLE = True
except ImportError:
    MYSQL_AVAILABLE = False
# 配置日志
setup_unified_logging()
logger = get_unified_logger("connectors.database.mysql")
singleton_manager = get_singleton_manager()
class MySQLConnector:
    """MySQL数据库连接器"""
    _instance = None
    _lock = threading.Lock()
    @classmethod
    def get_instance(cls, config: Dict[str, Any] = None) -> 'MySQLConnector':
        """获取MySQL连接器单例实例"""
        with cls._lock:
            if cls._instance is None:
                cls._instance = MySQLConnector(config)
        return cls._instance
    def __init__(self, config: Dict[str, Any] = None):
        # 🔥 老王强制单例：防止绕过单例创建新实例
        if hasattr(MySQLConnector, '_instance') and MySQLConnector._instance is not None:
            if MySQLConnector._instance != self:
                logger.debug("检测到尝试创建新的MySQL连接器实例，使用现有单例")
                # 复制现有实例的配置到当前实例
                existing = MySQLConnector._instance
                self.__dict__.update(existing.__dict__)
                return
        """
        初始化MySQL连接器
        Args:
            config: 配置参数
        """
        # 如果没有传入配置，尝试从配置文件加载
        if config is None:
            config = self._load_config()
        self.config = config or {}
        # 检查MySQL是否可用
        if not MYSQL_AVAILABLE:
            logger.warning_status("MySQL连接库未安装，请安装mysql-connector-python")
            self._is_available = False
            return
        self._is_available = True
        # 连接配置 - 使用远程服务器配置
        self.host = self.config.get("host", "**************")  # 默认使用远程服务器
        self.port = self.config.get("port", 3306)
        self.user = self.config.get("user", "root")
        self.password = self.config.get("password", "55cee73f3102126a")  # 使用远程服务器密码
        self.database = self.config.get("database", "linyanran")  # 使用远程数据库
        self.charset = self.config.get("charset", "utf8mb4")
        # 连接池配置 - 增大连接池以支持并发测试
        self.pool_size = self.config.get("pool_size", 20)  # 增加到20个连接
        self.pool_name = self.config.get("pool_name", "mysqlpool")
        self.pool_reset_session = self.config.get("pool_reset_session", True)
        # 重试配置 - 🔥 老王优化：增加重试次数和延迟，适应生产环境网络
        self.max_retries = self.config.get("max_retries", 5)  # 增加到5次重试
        self.retry_delay = self.config.get("retry_delay", 3)   # 增加延迟到3秒
        # 状态和统计
        self.is_initialized = False
        self.last_error = None
        self.stats = {
            "total_queries": 0,
            "successful_queries": 0,
            "failed_queries": 0,
            "total_connections": 0,
            "connection_errors": 0,
            "last_connection_time": None,
            "last_query_time": None,
            "total_query_time": 0
        }
        # 连接池
        self.connection_pool = None
        # 初始化连接池
        self._initialize_pool()
        # 健康检查线程
        self._health_check_thread = threading.Thread(
            target=self._health_check_loop,
            daemon=True
        )
        self._health_check_thread.start()

        # 🔥 老王修复：注册到韧性系统进行监控和自动恢复
        self._register_to_resilience_system()

        # 🔥 香草修复：注册到单例管理器
        self._register_to_singleton_manager()

        logger.success(f"MySQL连接器初始化完成，已连接到 {self.host}:{self.port}/{self.database}")
    def is_available(self) -> bool:
        """检查连接器是否可用（方法版本）"""
        return hasattr(self, '_is_available') and self._is_available and self.is_initialized
    
    def configure(self, config: Dict[str, Any]) -> bool:
        """
        配置MySQL连接器
        
        Args:
            config: 配置参数
            
        Returns:
            bool: 配置是否成功
        """
        try:
            # 更新配置
            self.config.update(config)
            
            # 更新连接参数
            if "host" in config:
                self.host = config["host"]
            if "port" in config:
                self.port = config["port"]
            if "user" in config:
                self.user = config["user"]
            if "password" in config:
                self.password = config["password"]
            if "database" in config:
                self.database = config["database"]
            if "charset" in config:
                self.charset = config["charset"]
            
            # 重新初始化连接池
            if self.is_initialized:
                self._initialize_pool()
            
            logger.success(f"MySQL连接器配置更新成功")
            return True
            
        except Exception as e:
            logger.error_status(f"MySQL连接器配置失败: {e}")
            return False
    def shutdown(self):
        """
        关闭MySQL连接器，清理资源
        """
        try:
            logger.info("开始关闭MySQL连接器...")
            # 关闭连接池
            if hasattr(self, 'connection_pool') and self.connection_pool:
                try:
                    # MySQL连接池没有直接的close方法，我们清空引用让垃圾回收处理
                    self.connection_pool = None
                    logger.info("MySQL连接池已释放")
                except Exception as e:
                    logger.warning_status(f"释放连接池时出现警告: {e}")
            # 清理统计信息
            if hasattr(self, 'stats'):
                self.stats.clear()
            # 重置连接状态
            self.is_initialized = False
            self._is_available = False
            logger.success("✅ MySQL连接器已成功关闭")
        except Exception as e:
            logger.error_status(f"❌ MySQL连接器关闭失败: {e}")
    def _load_config(self) -> Dict[str, Any]:
        """从配置文件加载MySQL配置"""
        import os
        import json
        # 获取项目根目录
        current_dir = os.path.dirname(os.path.abspath(__file__))
        root_dir = os.path.dirname(os.path.dirname(current_dir))
        # 尝试加载配置文件
        config_paths = [
            os.path.join(root_dir, "config", "database.json"),
            os.path.join(root_dir, "configs", "database.json")
        ]
        for config_path in config_paths:
            try:
                if os.path.exists(config_path):
                    with open(config_path, 'r', encoding='utf-8') as f:
                        config = json.load(f)
                        if "mysql" in config:
                            logger.info(f"从 {config_path} 加载MySQL配置")
                            return config["mysql"]
            except Exception as e:
                logger.warning_status(f"加载配置文件 {config_path} 失败: {e}")
        logger.warning_status("未找到有效的MySQL配置文件，使用默认远程配置")
        return {
            "host": "**************",
            "port": 3306,
            "user": "root", 
            "password": "55cee73f3102126a",
            "database": "linyanran",
            "charset": "utf8mb4"
        }
    @critical_operation
    def _initialize_pool(self) -> bool:
        """初始化连接池 - 增强版稳定性修复"""
        if not self._is_available:
            return False

        # 🔥 增强版：安全清理旧连接池
        if hasattr(self, 'connection_pool') and self.connection_pool:
            try:
                # 先关闭所有连接
                if hasattr(self.connection_pool, '_cnx_queue'):
                    while not self.connection_pool._cnx_queue.empty():
                        try:
                            conn = self.connection_pool._cnx_queue.get_nowait()
                            if conn and conn.is_connected():
                                conn.close()
                        except:
                            pass
                self.connection_pool = None
                logger.debug("已安全清理旧连接池")
            except Exception as e:
                logger.warning(f"清理旧连接池时出现警告: {e}")

        try:
            # 🔥 增强版：高稳定性连接池配置
            pool_config = {
                "host": self.host,
                "port": self.port,
                "user": self.user,
                "password": self.password,
                "database": self.database,
                "charset": self.charset,
                "use_unicode": True,
                "get_warnings": False,       # 关闭warnings避免Warning对象问题
                "autocommit": True,

                # 🔥 核心稳定性配置
                "pool_size": max(5, self.config.get("pool_size", 15)),  # 最小5个，默认15个连接
                "pool_name": f"{self.pool_name}_{int(time.time())}_{id(self)}",  # 唯一池名
                "pool_reset_session": False,  # 关闭会话重置避免packet问题

                # 🔥 修复：只使用支持的连接配置参数
                "connect_timeout": 10,        # 连接超时10秒

                # 🔥 修复：连接保活配置
                "sql_mode": "",               # 避免严格模式问题
                "init_command": "SET SESSION wait_timeout=28800, interactive_timeout=28800",  # 8小时超时
                "buffered": True,            # 启用缓冲
                                "sql_mode": "",
                
                # 🔥 老王Malformed packet终极优化
                "use_pure": True,            # 使用纯Python实现
                "compress": False,           # 禁用压缩
                "init_command": "SET SESSION wait_timeout=28800, interactive_timeout=28800",              # 清空sql_mode避免问题
                "connect_timeout": self.config.get("connection_timeout", 60),       # 🔥 生产环境专用：超长连接超时
                "auth_plugin": "mysql_native_password",  # 🔥 强制使用原生密码认证
                "consume_results": True,     # 🔥 自动消费结果避免packet残留
                "raise_on_warnings": False,  # 🔥 不要因为警告抛出异常
                "allow_local_infile": False, # 🔥 禁用本地文件加载提高安全性
                       # 启用TCP keepalive
                  # 2小时无数据后开始keepalive
                 # keepalive探测间隔
                     # 最多9次探测失败
            }
            # 🔥 增强版：智能重试创建连接池
            for init_retry in range(5):  # 增加到5次重试
                try:
                    # 创建连接池
                    self.connection_pool = pooling.MySQLConnectionPool(**pool_config)

                    # 🔥 增强版：多层连接验证
                    test_connections = []
                    try:
                        # 测试获取多个连接确保池稳定
                        for i in range(min(3, pool_config["pool_size"])):
                            conn = self.connection_pool.get_connection()
                            if conn and conn.is_connected():
                                # 执行健康检查查询
                                cursor = conn.cursor()
                                cursor.execute("SELECT 1, CONNECTION_ID(), @@version_comment")
                                result = cursor.fetchone()
                                cursor.close()

                                if result and result[0] == 1:
                                    test_connections.append(conn)
                                    logger.debug(f"连接 {i+1} 验证成功，连接ID: {result[1]}")
                                else:
                                    conn.close()
                                    raise Exception(f"连接 {i+1} 验证失败")
                            else:
                                raise Exception(f"连接 {i+1} 无效或未连接")

                        # 所有测试连接都成功，释放测试连接
                        for conn in test_connections:
                            conn.close()

                        # 🔥 新增：连接池健康状态初始化
                        self.is_initialized = True
                        self.stats["last_connection_time"] = datetime.now()
                        self.stats["pool_creation_time"] = datetime.now()
                        self.stats["successful_connections"] = len(test_connections)

                        logger.success(f"MySQL连接池初始化成功 (重试 {init_retry + 1}/5, 池大小: {pool_config['pool_size']})")
                        return True

                    except Exception as test_e:
                        # 清理失败的测试连接
                        for conn in test_connections:
                            try:
                                conn.close()
                            except:
                                pass
                        raise test_e

                except Exception as e:
                    error_msg = str(e)
                    self.stats["connection_errors"] += 1

                    if init_retry < 4:  # 0-3次重试
                        wait_time = (init_retry + 1) * 2  # 递增等待时间：2, 4, 6, 8秒
                        logger.warning_status(f"连接池创建失败，{wait_time}秒后重试 {init_retry + 1}/5: {error_msg}")
                        time.sleep(wait_time)
                        continue
                    else:
                        raise e
            return False
        except Exception as e:
            self.last_error = str(e)
            logger.error_status(f"初始化MySQL连接池失败: {e}")
            self.is_initialized = False
            return False
    def _health_check_loop(self) -> None:
        """运行健康检查循环"""
        while True:
            try:
                # 🔥 老王修复：调整健康检查间隔从30秒到3600秒（1小时），减少系统负载
                time.sleep(3600)  # 1小时检查一次，避免过度频繁检查
                # 检查连接池是否正常
                self._check_connection_pool()
            except Exception as e:
                logger.error_status(f"MySQL健康检查异常: {e}")
    def _check_connection_pool(self) -> bool:
        """检查连接池健康状况"""
        if not self._is_available or not self.connection_pool:
            return False
        try:
            # 获取连接并执行简单查询
            conn = self.connection_pool.get_connection()
            cursor = conn.cursor()
            cursor.execute("SELECT 1")
            result = cursor.fetchone()
            cursor.close()
            conn.close()
            # 检查结果
            if result and result[0] == 1:
                return True
            # 如果查询失败，重新初始化连接池
            logger.warning_status("MySQL连接池健康检查失败，尝试重新初始化")
            self._initialize_pool()
            return self.is_initialized
        except Exception as e:
            logger.error_status(f"MySQL连接池健康检查异常: {e}")
            # 尝试重新初始化连接池
            self._initialize_pool()
            return self.is_initialized
    @database_operation
    def get_connection(self) -> Optional[Any]:
        """
        获取数据库连接
        Returns:
            数据库连接对象
        """
        if not self._is_available or not self.connection_pool:
            # 🔥 老王修复：尝试重新初始化连接池
            if self._is_available:
                logger.warning_status("连接池不可用，尝试重新初始化...")

                # 🔥 尝试报告错误给自主纠错系统（如果可用）
                try:
                    self._report_error_to_corrector("mysql_connection_pool_unavailable",
                                                   "MySQL连接池不可用，需要重新初始化")
                except Exception as report_e:
                    logger.debug(f"无法报告错误给纠错系统: {report_e}")

                if self._initialize_pool():
                    logger.success("连接池重新初始化成功")
                else:
                    return None
            else:
                return None
        # 🔥 增强版：智能连接获取与自动恢复机制
        for retry_count in range(7):  # 增加到7次重试，提高成功率
            try:
                # 🔥 增强版：连接池健康状态检查
                if hasattr(self.connection_pool, '_cnx_queue'):
                    queue_size = self.connection_pool._cnx_queue.qsize() if self.connection_pool._cnx_queue else 0
                    pool_size = getattr(self.connection_pool, '_pool_size', 0)

                    logger.debug(f"连接池状态 - 可用连接: {queue_size}/{pool_size}")

                    # 🔥 智能等待策略：根据连接池状态调整等待时间
                    if queue_size == 0:
                        if retry_count < 3:
                            # 前3次短等待，可能有连接很快释放
                            wait_time = 1 + retry_count * 0.5  # 1, 1.5, 2秒
                            logger.warning_status(f"连接池暂时耗尽，短等待 {wait_time}s ({retry_count + 1}/7)")
                            time.sleep(wait_time)
                        else:
                            # 后续长等待，可能需要重建连接池
                            wait_time = 3 + (retry_count - 3) * 2  # 3, 5, 7, 9秒
                            logger.warning_status(f"连接池持续耗尽，长等待 {wait_time}s ({retry_count + 1}/7)")
                            time.sleep(wait_time)

                # 🔥 增强版：连接获取与验证
                conn = self.connection_pool.get_connection()

                if conn and conn.is_connected():
                    # 🔥 新增：连接质量检查
                    try:
                        # 快速ping测试连接质量
                        conn.ping(reconnect=False, attempts=1, delay=0)
                        self.stats["total_connections"] += 1
                        self.stats["last_successful_connection"] = datetime.now()
                        return conn
                    except Exception as ping_e:
                        logger.debug(f"连接ping失败，关闭并重试: {ping_e}")
                        conn.close()
                        continue
                else:
                    # 连接无效，记录并重试
                    logger.warning_status(f"获取到无效连接，重试 {retry_count + 1}/7")
                    if conn:
                        conn.close()

                    # 🔥 智能重建策略：只在必要时重建连接池
                    if retry_count >= 3 and retry_count < 6:
                        logger.info("尝试重建连接池以解决连接问题")
                        if self._initialize_pool():
                            logger.info("连接池重建成功，继续重试")
                        else:
                            logger.warning("连接池重建失败")

            except Exception as e:
                error_msg = str(e)
                self.last_error = error_msg
                self.stats["connection_errors"] += 1

                # 🔥 增强版：错误分类处理
                if any(keyword in error_msg.lower() for keyword in ["pool exhausted", "poolerror", "no more connections"]):
                    # 连接池耗尽错误
                    if retry_count < 6:
                        wait_time = min(10, 2 + retry_count * 1.5)  # 最大等待10秒
                        logger.warning_status(f"连接池耗尽，等待 {wait_time:.1f}s 后重试 ({retry_count + 1}/7)")
                        time.sleep(wait_time)

                        # 在第4次重试时强制重建连接池
                        if retry_count == 3:
                            logger.info("执行连接池强制重建")
                            self._cleanup_and_rebuild_pool()
                            time.sleep(2)
                elif "lost connection" in error_msg.lower() or "mysql server has gone away" in error_msg.lower():
                    # 连接丢失错误
                    logger.warning_status(f"MySQL连接丢失，重建连接池 ({retry_count + 1}/7)")
                    self._cleanup_and_rebuild_pool()
                    time.sleep(1 + retry_count * 0.5)
                elif "malformed packet" in error_msg.lower() or "connection not available" in error_msg.lower():
                    # 🔥 Malformed packet特殊处理
                    logger.warning_status(f"检测到Malformed packet错误，重建连接池 ({retry_count + 1}/7)")
                    self._cleanup_and_rebuild_pool()
                    time.sleep(3 + retry_count)  # 递增等待时间
                else:
                    # 其他错误
                    if retry_count < 6:
                        wait_time = 1 + retry_count * 0.3
                        logger.warning_status(f"连接获取异常，{wait_time:.1f}s后重试 ({retry_count + 1}/7): {error_msg}")
                        time.sleep(wait_time)

        # 🔥 所有重试都失败了
        logger.error_status("获取MySQL连接失败：所有重试都已用尽")
        self.stats["total_failed_connections"] = self.stats.get("total_failed_connections", 0) + 1
        return None
    def _cleanup_and_rebuild_pool(self) -> bool:
        """🔥 增强版：清理并重建连接池"""
        try:
            logger.warning_status("开始清理并重建MySQL连接池...")

            # 🔥 增强版：安全清理现有连接池
            cleanup_success = True
            if hasattr(self, 'connection_pool') and self.connection_pool:
                try:
                    # 记录清理前状态
                    if hasattr(self.connection_pool, '_cnx_queue'):
                        queue_size = self.connection_pool._cnx_queue.qsize() if self.connection_pool._cnx_queue else 0
                        logger.debug(f"清理前连接池状态: {queue_size} 个连接")

                    # 强制关闭所有连接
                    closed_count = 0
                    if hasattr(self.connection_pool, '_cnx_queue'):
                        while not self.connection_pool._cnx_queue.empty():
                            try:
                                conn = self.connection_pool._cnx_queue.get_nowait()
                                if conn:
                                    if conn.is_connected():
                                        conn.close()
                                    closed_count += 1
                            except Exception as conn_e:
                                logger.debug(f"关闭单个连接时出现异常: {conn_e}")

                    logger.debug(f"已关闭 {closed_count} 个连接")

                except Exception as e:
                    logger.debug(f"清理连接池异常: {e}")
                    cleanup_success = False

                # 删除连接池引用
                self.connection_pool = None

            # 🔥 增强版：智能重建策略
            self.is_initialized = False

            # 短暂等待，让系统释放资源
            time.sleep(0.5)

            # 重新初始化连接池
            success = self._initialize_pool()

            if success:
                # 🔥 新增：重建后健康检查
                try:
                    test_conn = self.get_connection()
                    if test_conn:
                        test_conn.close()
                        logger.success("MySQL连接池重建成功，健康检查通过")

                        # 更新统计信息
                        self.stats["pool_rebuilds"] = self.stats.get("pool_rebuilds", 0) + 1
                        self.stats["last_rebuild_time"] = datetime.now()

                        return True
                    else:
                        logger.error_status("MySQL连接池重建后健康检查失败")
                        return False
                except Exception as health_e:
                    logger.error_status(f"MySQL连接池重建后健康检查异常: {health_e}")
                    return False
            else:
                logger.error_status("连接池重建失败")
                return False

        except Exception as e:
            logger.error_status(f"连接池清理重建异常: {e}")
            return False
    def _format_params(self, params: Any) -> Any:
        """
        格式化查询参数
        Args:
            params: 查询参数
        Returns:
            格式化后的参数
        """
        if isinstance(params, dict):
            # 处理字典参数中的日期时间对象
            for key, value in params.items():
                if isinstance(value, datetime):
                    params[key] = value.strftime('%Y-%m-%d %H:%M:%S')
        elif isinstance(params, (list, tuple)):
            # 处理列表参数中的日期时间对象
            params = list(params)
            for i, value in enumerate(params):
                if isinstance(value, datetime):
                    params[i] = value.strftime('%Y-%m-%d %H:%M:%S')
        return params
    def _execute_with_connection(self, query: str, params: Any = None) -> Tuple[bool, Optional[Any], Optional[str], Optional[Any]]:
        """
        使用连接执行查询 - 内部方法
        Args:
            query: SQL查询
            params: 查询参数
        Returns:
            (成功标志, 游标, 错误信息, 连接对象)
        """
        if not self._is_available:
            return False, None, "MySQL连接器不可用", None
        # 格式化参数
        if params is not None:
            params = self._format_params(params)
        conn = None
        cursor = None
        try:
            # 获取连接
            conn = self.get_connection()
            if not conn:
                raise Exception("无法获取数据库连接")
            # 创建游标
            cursor = conn.cursor(dictionary=True)
            # 执行查询
            cursor.execute(query, params)
            return True, cursor, None, conn
        except Exception as e:
            # 🔥 老王修复：彻底解决MySQL Warning对象的str()转换问题
            try:
                # 首先尝试直接转换为字符串
                error_msg = str(e)
            except Exception:
                try:
                    # 如果直接转换失败，尝试获取异常类型和消息
                    error_msg = f"{type(e).__name__}: {repr(e)}"
                except Exception:
                    # 最后的兜底方案
                    error_msg = "MySQL查询错误"
            # 确保error_msg是纯字符串，避免包含任何对象
            safe_error_msg = str(error_msg) if error_msg else "MySQL查询错误"
            logger.error_status("查询执行失败: " + safe_error_msg)
            # 清理资源
            if cursor:
                try:
                    cursor.close()
                except:
                    pass
            if conn:
                try:
                    conn.close()
                except:
                    pass
            # 确保error_msg是字符串
            try:
                final_error_msg = str(error_msg)
            except Exception:
                final_error_msg = "MySQL查询错误"
            # 更新统计信息
            self.stats["failed_queries"] += 1
            self.last_error = final_error_msg
            return False, None, final_error_msg, None
    @database_operation
    def query(self, query: str, params: Any = None) -> Tuple[bool, Optional[List[Dict[str, Any]]], Optional[str]]:
        """
        执行查询并获取所有结果
        Args:
            query: SQL查询
            params: 查询参数
        Returns:
            (成功标志, 结果列表, 错误信息)
        """
        if not self.is_available:
            return False, None, "MySQL连接器不可用"
        # 更新统计信息
        self.stats["total_queries"] += 1
        self.stats["last_query_time"] = datetime.now()
        # 记录查询开始时间
        start_time = time.time()
        # 格式化参数
        if params is not None:
            params = self._format_params(params)
        # 🔥 老王修复：添加重试机制处理Malformed packet错误
        last_error = None
        for retry_count in range(self.max_retries):
            conn = None
            cursor = None
            try:
                # 获取连接
                conn = self.get_connection()
                if not conn:
                    if retry_count < self.max_retries - 1:
                        time.sleep(self.retry_delay)
                        continue
                    raise Exception("无法获取数据库连接")
                # 创建游标
                cursor = conn.cursor(dictionary=True)
                # 执行查询
                cursor.execute(query, params)
                # 获取结果
                results = cursor.fetchall()
                # 更新统计信息
                self.stats["successful_queries"] += 1
                query_time = time.time() - start_time
                self.stats["total_query_time"] += query_time
                return True, results, None
            except Exception as e:
                # 🔥 老王修复：特殊处理Malformed packet错误
                error_msg = str(e)
                last_error = error_msg
                if "Malformed packet" in error_msg or "2027" in error_msg:
                    logger.warning_status(f"检测到Malformed packet错误，重试 {retry_count + 1}/{self.max_retries}: {error_msg}")
                    if retry_count < self.max_retries - 1:
                        # 🔥 老王修复：Malformed packet需要重建连接池
                        try:
                            self._cleanup_and_rebuild_pool()
                        except:
                            pass
                        # 🔥 生产环境专用：更长等待时间
                        wait_time = 10 * (retry_count + 1)  # 10秒、20秒、30秒等
                        logger.warning_status(f"生产环境网络优化：等待 {wait_time} 秒后重试...")
                        time.sleep(wait_time)
                        continue
                # 其他错误不重试，直接退出
                break
            finally:
                # 🔥 老王修复：确保资源被正确释放，避免连接池耗尽
                if cursor:
                    try:
                        cursor.close()
                        logger.debug("游标已关闭")
                    except Exception as e:
                        logger.debug(f"游标关闭异常: {e}")
                if conn:
                    try:
                        # 检查连接状态
                        if conn.is_connected():
                            conn.close()  # 关键：返回连接到连接池
                            logger.debug("连接已返回连接池")
                        else:
                            logger.debug("连接已断开，无需返回连接池")
                    except Exception as e:
                        logger.debug(f"连接关闭异常: {e}")
        # 如果所有重试都失败，返回最后一次的错误
        safe_error_msg = str(last_error) if last_error else "MySQL查询错误"
        logger.error_status("查询执行失败: " + safe_error_msg)
        # 更新统计信息
        self.stats["failed_queries"] += 1
        self.last_error = safe_error_msg
        return False, None, safe_error_msg
    @database_operation
    def query_one(self, query: str, params: Any = None) -> Tuple[bool, Optional[Dict[str, Any]], Optional[str]]:
        """
        执行查询并获取单个结果
        Args:
            query: SQL查询
            params: 查询参数
        Returns:
            (成功标志, 结果字典, 错误信息)
        """
        if not self.is_available:
            return False, None, "MySQL连接器不可用"
        # 更新统计信息
        self.stats["total_queries"] += 1
        self.stats["last_query_time"] = datetime.now()
        # 记录查询开始时间
        start_time = time.time()
        # 格式化参数
        if params is not None:
            params = self._format_params(params)
        conn = None
        cursor = None
        try:
            # 获取连接
            conn = self.get_connection()
            if not conn:
                raise Exception("无法获取数据库连接")
            # 创建游标
            cursor = conn.cursor(dictionary=True)
            # 执行查询
            cursor.execute(query, params)
            # 获取结果
            result = cursor.fetchone()
            # 更新统计信息
            self.stats["successful_queries"] += 1
            query_time = time.time() - start_time
            self.stats["total_query_time"] += query_time
            return True, result, None
        except Exception as e:
            # 安全地转换错误信息为字符串，特别处理MySQL Warning
            try:
                # 检查是否是MySQL Warning对象
                if hasattr(e, 'msg'):
                    error_msg = str(e.msg)
                elif hasattr(e, 'args') and e.args:
                    error_msg = str(e.args[0])
                else:
                    error_msg = str(e)
            except Exception:
                try:
                    error_msg = repr(e)
                except Exception:
                    error_msg = "MySQL查询错误"
            # 确保error_msg是字符串
            try:
                final_error_msg = str(error_msg)
            except Exception:
                final_error_msg = "MySQL查询错误"
            # 避免f-string中的问题，使用字符串拼接
            # 再次确保final_error_msg是字符串
            try:
                safe_error_msg = str(final_error_msg)
            except Exception:
                safe_error_msg = "MySQL查询错误"
            logger.error_status("查询执行失败: " + safe_error_msg)
            # 更新统计信息
            self.stats["failed_queries"] += 1
            self.last_error = safe_error_msg
            return False, None, safe_error_msg
        finally:
            # 确保资源被正确释放
            if cursor:
                try:
                    cursor.close()
                except:
                    pass
            if conn:
                try:
                    conn.close()  # 关键：返回连接到连接池
                except:
                    pass

    @database_operation
    def execute(self, query: str, params: Any = None) -> Tuple[bool, Optional[Any], Optional[str]]:
        """
        执行SQL语句（通用方法）
        
        Args:
            query: SQL查询语句
            params: 查询参数
            
        Returns:
            Tuple[成功标志, 游标对象, 错误信息]
        """
        if not self.is_available:
            return False, None, "MySQL连接器不可用"
        
        # 更新统计信息
        self.stats["total_queries"] += 1
        self.stats["last_query_time"] = datetime.now()
        
        # 记录查询开始时间
        start_time = time.time()
        
        # 格式化参数
        if params is not None:
            params = self._format_params(params)
        
        conn = None
        cursor = None
        
        try:
            # 获取连接
            conn = self.get_connection()
            if not conn:
                raise Exception("无法获取数据库连接")
            
            # 创建游标
            cursor = conn.cursor(dictionary=True)
            
            # 执行查询
            cursor.execute(query, params)
            
            # 更新统计信息
            self.stats["successful_queries"] += 1
            query_time = time.time() - start_time
            self.stats["total_query_time"] += query_time
            
            return True, cursor, None
            
        except Exception as e:
            # 🔥 老王修复：彻底解决MySQL Warning对象的str()转换问题
            try:
                # 首先尝试直接转换为字符串
                error_msg = str(e)
            except Exception:
                try:
                    # 如果直接转换失败，尝试获取异常类型和消息
                    error_msg = f"{type(e).__name__}: {repr(e)}"
                except Exception:
                    # 最后的兜底方案
                    error_msg = "MySQL执行错误"
            
            # 确保error_msg是纯字符串，避免包含任何对象
            safe_error_msg = str(error_msg) if error_msg else "MySQL执行错误"
            logger.error_status("SQL执行失败: " + safe_error_msg)
            
            # 清理资源
            if cursor:
                try:
                    cursor.close()
                except:
                    pass
            if conn:
                try:
                    conn.close()
                except:
                    pass
                    
            # 确保error_msg是字符串
            try:
                final_error_msg = str(error_msg)
            except Exception:
                final_error_msg = "MySQL执行错误"
            
            # 更新统计信息
            self.stats["failed_queries"] += 1
            self.last_error = final_error_msg
            
            return False, None, final_error_msg

    def execute_raw(self, query: str, params: Any = None) -> Tuple[bool, Optional[Any], Optional[str]]:
        """
        执行原始SQL语句 - 老王专门加的方法，修复emotions_intelligence_updater调用问题
        
        Args:
            query: SQL查询语句
            params: 查询参数
            
        Returns:
            Tuple[成功标志, 结果, 错误信息]
        """
        if not self.is_available:
            return False, None, "MySQL连接器不可用"
        
        # 更新统计信息
        self.stats["total_queries"] += 1
        self.stats["last_query_time"] = datetime.now()
        
        # 记录查询开始时间
        start_time = time.time()
        
        # 格式化参数
        if params is not None:
            params = self._format_params(params)
        
        conn = None
        cursor = None
        
        try:
            # 获取连接
            conn = self.get_connection()
            if not conn:
                raise Exception("无法获取数据库连接")
            
            # 创建游标
            cursor = conn.cursor(dictionary=True)
            
            # 执行查询
            cursor.execute(query, params)
            
            # 根据查询类型处理结果
            if query.strip().upper().startswith(('SELECT', 'SHOW', 'DESCRIBE', 'EXPLAIN')):
                # 查询类SQL，获取结果
                result = cursor.fetchall()
            else:
                # 非查询类SQL，获取影响行数
                result = cursor.rowcount
                # 如果有事务，需要提交
                if not conn.autocommit:
                    conn.commit()
            
            # 更新统计信息
            self.stats["successful_queries"] += 1
            query_time = time.time() - start_time
            self.stats["total_query_time"] += query_time
            
            return True, result, None
            
        except Exception as e:
            # 🔥 老王修复：彻底解决MySQL Warning对象的str()转换问题
            try:
                # 首先尝试直接转换为字符串
                error_msg = str(e)
            except Exception:
                try:
                    # 如果直接转换失败，尝试获取异常类型和消息
                    error_msg = f"{type(e).__name__}: {repr(e)}"
                except Exception:
                    # 最后的兜底方案
                    error_msg = "MySQL执行错误"
            
            # 确保error_msg是纯字符串，避免包含任何对象
            safe_error_msg = str(error_msg) if error_msg else "MySQL执行错误"
            logger.error_status("SQL执行失败: " + safe_error_msg)
            
            # 清理资源
            if cursor:
                try:
                    cursor.close()
                except:
                    pass
            if conn:
                try:
                    conn.close()
                except:
                    pass
                    
            # 确保error_msg是字符串
            try:
                final_error_msg = str(error_msg)
            except Exception:
                final_error_msg = "MySQL执行错误"
            
            # 更新统计信息
            self.stats["failed_queries"] += 1
            self.last_error = final_error_msg
            
            return False, None, final_error_msg

    def insert(self, table: str, data: Dict[str, Any]) -> Tuple[bool, Optional[int], Optional[str]]:
        """
        插入数据
        Args:
            table: 表名
            data: 要插入的数据
        Returns:
            (成功标志, 插入ID, 错误信息)
        """
        if not data:
            return False, None, "没有要插入的数据"
        # 构建SQL
        columns = list(data.keys())
        placeholders = ["%s"] * len(columns)
        values = [data[col] for col in columns]
        query = f"INSERT INTO {table} ({', '.join(columns)}) VALUES ({', '.join(placeholders)})"
        # 执行插入
        if not self.is_available:
            return False, None, "MySQL连接器不可用"
        # 更新统计信息
        self.stats["total_queries"] += 1
        self.stats["last_query_time"] = datetime.now()
        # 记录查询开始时间
        start_time = time.time()
        conn = None
        cursor = None
        try:
            # 获取连接
            conn = self.get_connection()
            if not conn:
                raise Exception("无法获取数据库连接")
            # 创建游标
            cursor = conn.cursor(dictionary=True)
            # 执行插入
            cursor.execute(query, values)
            # 获取自增ID
            last_id = cursor.lastrowid
            # 更新统计信息
            self.stats["successful_queries"] += 1
            query_time = time.time() - start_time
            self.stats["total_query_time"] += query_time
            return True, last_id, None
        except Exception as e:
            # 安全地转换错误信息为字符串
            try:
                error_msg = str(e)
            except Exception:
                error_msg = "MySQL插入错误"
            logger.error_status(f"插入数据失败: {error_msg}")
            # 更新统计信息
            self.stats["failed_queries"] += 1
            self.last_error = error_msg
            return False, None, error_msg
        finally:
            # 确保资源被正确释放
            if cursor:
                try:
                    cursor.close()
                except:
                    pass
            if conn:
                try:
                    conn.close()  # 关键：返回连接到连接池
                except:
                    pass
    def insert_many(self, table: str, data_list: List[Dict[str, Any]]) -> Tuple[bool, Optional[int], Optional[str]]:
        """
        批量插入数据
        Args:
            table: 表名
            data_list: 要插入的数据列表
        Returns:
            (成功标志, 影响行数, 错误信息)
        """
        if not data_list:
            return False, None, "没有要插入的数据"
        # 确保所有数据具有相同的字段
        columns = list(data_list[0].keys())
        placeholders = ["%s"] * len(columns)
        # 构建SQL
        query = f"INSERT INTO {table} ({', '.join(columns)}) VALUES ({', '.join(placeholders)})"
        # 准备参数
        params = [[item[col] for col in columns] for item in data_list]
        # 获取连接和游标
        if not self.is_available:
            return False, None, "MySQL连接器不可用"
        conn = None
        cursor = None
        try:
            # 获取连接
            conn = self.get_connection()
            if not conn:
                raise Exception("无法获取数据库连接")
            # 创建游标
            cursor = conn.cursor()
            # 更新统计信息
            self.stats["total_queries"] += 1
            self.stats["last_query_time"] = datetime.now()
            # 执行批量插入
            start_time = time.time()
            cursor.executemany(query, params)
            # 获取影响行数
            row_count = cursor.rowcount
            # 更新统计信息
            self.stats["successful_queries"] += 1
            query_time = time.time() - start_time
            self.stats["total_query_time"] += query_time
            # 关闭游标
            cursor.close()
            return True, row_count, None
        except Exception as e:
            # 安全地转换错误信息为字符串
            try:
                error_msg = str(e)
            except Exception:
                error_msg = "MySQL批量插入错误"
            logger.error_status(f"批量插入失败: {error_msg}")
            # 更新统计信息
            self.stats["failed_queries"] += 1
            self.last_error = error_msg
            # 关闭资源
            if cursor:
                try:
                    cursor.close()
                except:
                    pass
            return False, None, error_msg
        finally:
            # 确保连接返回池中
            if conn:
                try:
                    conn.close()
                except:
                    pass
    @database_operation
    def execute_many(self, query: str, params_list: List[Any]) -> Tuple[bool, Optional[int], Optional[str]]:
        """
        批量执行SQL语句（支持UPDATE、INSERT等）
        Args:
            query: SQL查询语句
            params_list: 参数列表，每个元素对应一次执行的参数
        Returns:
            (成功标志, 影响行数, 错误信息)
        """
        if not self.is_available:
            return False, None, "MySQL连接器不可用"

        if not params_list:
            return False, None, "没有要执行的参数"

        # 更新统计信息
        self.stats["total_queries"] += 1
        self.stats["last_query_time"] = datetime.now()

        # 记录查询开始时间
        start_time = time.time()

        conn = None
        cursor = None
        try:
            # 获取连接
            conn = self.get_connection()
            if not conn:
                raise Exception("无法获取数据库连接")

            # 创建游标
            cursor = conn.cursor()

            # 执行批量操作
            cursor.executemany(query, params_list)

            # 获取影响行数
            affected_rows = cursor.rowcount

            # 提交事务
            conn.commit()

            # 更新统计信息
            self.stats["successful_queries"] += 1
            query_time = time.time() - start_time
            self.stats["total_query_time"] += query_time

            return True, affected_rows, None

        except Exception as e:
            # 安全地转换错误信息为字符串
            try:
                error_msg = str(e)
            except Exception:
                error_msg = "MySQL批量执行错误"

            logger.error_status(f"批量执行SQL失败: {error_msg}")

            # 更新统计信息
            self.stats["failed_queries"] += 1
            self.last_error = error_msg

            # 回滚事务
            if conn:
                try:
                    conn.rollback()
                except:
                    pass

            return False, None, error_msg

        finally:
            # 确保资源被正确释放
            if cursor:
                try:
                    cursor.close()
                except:
                    pass
            if conn:
                try:
                    conn.close()  # 返回连接到连接池
                except:
                    pass

    def update(self, table: str, data: Dict[str, Any], condition: str, params: Any = None) -> Tuple[bool, Optional[int], Optional[str]]:
        """
        更新数据
        Args:
            table: 表名
            data: 要更新的数据
            condition: 更新条件
            params: 条件参数
        Returns:
            (成功标志, 影响行数, 错误信息)
        """
        if not data:
            return False, None, "没有要更新的数据"
        # 构建SET部分
        set_parts = []
        set_values = []
        for column, value in data.items():
            set_parts.append(f"{column} = %s")
            set_values.append(value)
        # 构建SQL
        query = f"UPDATE {table} SET {', '.join(set_parts)} WHERE {condition}"
        # 合并参数
        all_params = set_values
        if params is not None:
            if isinstance(params, (list, tuple)):
                all_params.extend(params)
            else:
                all_params.append(params)
        # 🔥 老王修复：使用execute_update方法避免游标对象被垃圾回收
        return self.execute_update(query, all_params)
    def delete(self, table: str, condition: str, params: Any = None) -> Tuple[bool, Optional[int], Optional[str]]:
        """
        删除数据
        Args:
            table: 表名
            condition: 删除条件
            params: 条件参数
        Returns:
            (成功标志, 影响行数, 错误信息)
        """
        # 构建SQL
        query = f"DELETE FROM {table} WHERE {condition}"
        # 执行删除 - 使用正确的方法
        success, cursor, error, conn = self._execute_with_connection(query, params)
        if not success:
            return False, None, error
        try:
            # 获取影响行数
            row_count = cursor.rowcount
            # 关闭游标
            cursor.close()
            # 关闭连接
            if conn:
                conn.close()
            return True, row_count, None
        except Exception as e:
            # 安全地转换错误信息为字符串
            try:
                error_msg = str(e)
            except Exception:
                error_msg = "MySQL删除错误"
            logger.error_status(f"获取删除影响行数失败: {error_msg}")
            # 关闭资源
            try:
                if cursor:
                    cursor.close()
                if conn:
                    conn.close()
            except:
                pass
            return False, None, error_msg
    def call_procedure(self, procedure: str, params: Any = None) -> Tuple[bool, Optional[List[Dict[str, Any]]], Optional[str]]:
        """
        调用存储过程
        Args:
            procedure: 存储过程名称
            params: 参数
        Returns:
            (成功标志, 结果列表, 错误信息)
        """
        if not self.is_available:
            return False, None, "MySQL连接器不可用"
        conn = None
        cursor = None
        try:
            # 获取连接
            conn = self.get_connection()
            if not conn:
                raise Exception("无法获取数据库连接")
            # 创建游标
            cursor = conn.cursor(dictionary=True)
            # 更新统计信息
            self.stats["total_queries"] += 1
            self.stats["last_query_time"] = datetime.now()
            # 格式化参数
            if params is not None:
                params = self._format_params(params)
                # 构建调用SQL
                placeholders = ", ".join(["%s"] * len(params)) if isinstance(params, (list, tuple)) else "%s"
                query = f"CALL {procedure}({placeholders})"
                # 执行调用
                start_time = time.time()
                cursor.execute(query, params)
            else:
                # 无参数调用
                query = f"CALL {procedure}()"
                # 执行调用
                start_time = time.time()
                cursor.execute(query)
            # 获取结果
            results = []
            # 处理多结果集
            has_results = True
            while has_results:
                if cursor.with_rows:
                    results.extend(cursor.fetchall())
                # 移动到下一个结果集
                has_results = cursor.nextset()
            # 更新统计信息
            self.stats["successful_queries"] += 1
            query_time = time.time() - start_time
            self.stats["total_query_time"] += query_time
            # 关闭游标
            cursor.close()
            return True, results, None
        except Exception as e:
            # 安全地转换错误信息为字符串
            try:
                error_msg = str(e)
            except Exception:
                error_msg = "MySQL存储过程错误"
            logger.error_status(f"调用存储过程失败: {error_msg}")
            # 更新统计信息
            self.stats["failed_queries"] += 1
            self.last_error = error_msg
            # 关闭资源
            if cursor:
                try:
                    cursor.close()
                except:
                    pass
            return False, None, error_msg
        finally:
            # 确保连接返回池中
            if conn:
                try:
                    conn.close()
                except:
                    pass
    def begin_transaction(self) -> Tuple[bool, Optional[Any], Optional[str]]:
        """
        开始事务
        Returns:
            (成功标志, 连接对象, 错误信息)
        """
        if not self.is_available:
            return False, None, "MySQL连接器不可用"
        try:
            # 获取连接
            conn = self.get_connection()
            if not conn:
                raise Exception("无法获取数据库连接")
            # 关闭自动提交
            conn.autocommit = False
            # 开始事务
            conn.start_transaction()
            return True, conn, None
        except Exception as e:
            # 安全地转换错误信息为字符串
            try:
                error_msg = str(e)
            except Exception:
                error_msg = "MySQL事务开始错误"
            logger.error_status(f"开始事务失败: {error_msg}")
            # 关闭连接
            if 'conn' in locals() and conn:
                try:
                    conn.close()
                except:
                    pass
            return False, None, error_msg
    def commit_transaction(self, conn: Any) -> Tuple[bool, Optional[str]]:
        """
        提交事务
        Args:
            conn: 连接对象
        Returns:
            (成功标志, 错误信息)
        """
        if not conn:
            return False, "无效的连接对象"
        try:
            # 提交事务
            conn.commit()
            # 恢复自动提交
            conn.autocommit = True
            # 关闭连接
            conn.close()
            return True, None
        except Exception as e:
            # 安全地转换错误信息为字符串
            try:
                error_msg = str(e)
            except Exception:
                error_msg = "MySQL事务提交错误"
            logger.error_status(f"提交事务失败: {error_msg}")
            # 尝试回滚
            try:
                conn.rollback()
            except:
                pass
            # 关闭连接
            try:
                conn.autocommit = True
                conn.close()
            except:
                pass
            return False, error_msg
    def rollback_transaction(self, conn: Any) -> Tuple[bool, Optional[str]]:
        """
        回滚事务
        Args:
            conn: 连接对象
        Returns:
            (成功标志, 错误信息)
        """
        if not conn:
            return False, "无效的连接对象"
        try:
            # 回滚事务
            conn.rollback()
            # 恢复自动提交
            conn.autocommit = True
            # 关闭连接
            conn.close()
            return True, None
        except Exception as e:
            # 安全地转换错误信息为字符串
            try:
                error_msg = str(e)
            except Exception:
                error_msg = "MySQL事务回滚错误"
            logger.error_status(f"回滚事务失败: {error_msg}")
            # 关闭连接
            try:
                conn.autocommit = True
                conn.close()
            except:
                pass
            return False, error_msg
    def get_tables(self) -> Tuple[bool, Optional[List[str]], Optional[str]]:
        """
        获取所有表名
        Returns:
            (成功标志, 表名列表, 错误信息)
        """
        query = "SHOW TABLES"
        success, cursor, error = self.execute(query)
        if not success:
            return False, None, error
        try:
            # 获取结果
            results = cursor.fetchall()
            # 提取表名
            tables = [list(row.values())[0] for row in results]
            # 关闭游标
            cursor.close()
            return True, tables, None
        except Exception as e:
            # 安全地转换错误信息为字符串
            try:
                error_msg = str(e)
            except Exception:
                error_msg = "MySQL获取表名错误"
            logger.error_status(f"获取表名失败: {error_msg}")
            # 关闭游标
            try:
                cursor.close()
            except:
                pass
            return False, None, error_msg
    def get_table_schema(self, table: str) -> Tuple[bool, Optional[List[Dict[str, Any]]], Optional[str]]:
        """
        获取表结构
        Args:
            table: 表名
        Returns:
            (成功标志, 字段信息列表, 错误信息)
        """
        query = f"DESCRIBE {table}"
        return self.query(query)
    def connect(self) -> bool:
        """
        连接到数据库（保持向后兼容）
        Returns:
            bool: 连接是否成功
        """
        try:
            # 检查连接池是否已初始化
            if self.connection_pool is None:
                self._initialize_pool()
            # 测试连接
            conn = self.get_connection()
            if conn:
                conn.close()
                return True
            return False
        except Exception as e:
            logger.error_status(f"连接数据库失败: {e}")
            return False
    def execute_query(self, query: str, params: Any = None) -> Tuple[bool, Optional[List[Dict[str, Any]]], Optional[str]]:
        """
        执行查询（保持向后兼容）
        Args:
            query: SQL查询语句
            params: 查询参数
        Returns:
            Tuple[成功标志, 查询结果, 错误信息]
        """
        return self.query(query, params)
    def execute_update(self, query: str, params: Any = None) -> Tuple[bool, Optional[int], Optional[str]]:
        """
        执行更新/插入/删除操作
        Args:
            query: SQL语句
            params: 查询参数
        Returns:
            Tuple[成功标志, 影响行数, 错误信息]
        """
        if not self.is_available:
            return False, None, "MySQL连接器不可用"
        # 更新统计信息
        self.stats["total_queries"] += 1
        self.stats["last_query_time"] = datetime.now()
        # 记录查询开始时间
        start_time = time.time()
        # 格式化参数
        if params is not None:
            params = self._format_params(params)
        conn = None
        cursor = None
        try:
            # 获取连接
            conn = self.get_connection()
            if not conn:
                raise Exception("无法获取数据库连接")
            # 创建游标
            cursor = conn.cursor()
            # 执行更新
            cursor.execute(query, params)
            # 🔥 老王修复：立即获取影响行数，避免弱引用失效
            affected_rows = cursor.rowcount
            # 提交事务
            conn.commit()
            # 立即关闭游标，避免弱引用问题
            cursor.close()
            cursor = None  # 清空引用
            # 更新统计信息
            self.stats["successful_queries"] += 1
            query_time = time.time() - start_time
            self.stats["total_query_time"] += query_time
            return True, affected_rows, None
        except Exception as e:
            # 🔥 老王修复：彻底解决MySQL Warning对象的str()转换问题
            try:
                # 首先尝试直接转换为字符串
                error_msg = str(e)
            except Exception:
                try:
                    # 如果直接转换失败，尝试获取异常类型和消息
                    error_msg = f"{type(e).__name__}: {repr(e)}"
                except Exception:
                    # 最后的兜底方案
                    error_msg = "MySQL更新错误"
            # 确保error_msg是纯字符串，避免包含任何对象
            safe_error_msg = str(error_msg) if error_msg else "MySQL更新错误"
            logger.error_status("更新执行失败: " + safe_error_msg)
            # 回滚事务
            if conn:
                try:
                    conn.rollback()
                except:
                    pass
            # 更新统计信息
            self.stats["failed_queries"] += 1
            # 确保有error_msg变量
            try:
                if 'error_msg' in locals():
                    final_error_msg = str(error_msg)
                else:
                    final_error_msg = "MySQL查询错误"
            except Exception:
                final_error_msg = "MySQL查询错误"
            self.last_error = final_error_msg
            return False, None, final_error_msg
        finally:
            # 确保资源被正确释放
            if cursor:
                try:
                    cursor.close()
                except:
                    pass
            if conn:
                try:
                    conn.close()  # 关键：返回连接到连接池
                except:
                    pass
    def close(self) -> None:
        """关闭连接池"""
        if self.connection_pool:
            logger.success("关闭MySQL连接池")
            # 连接池没有显式关闭方法，垃圾回收会处理
            self.connection_pool = None

    def _register_to_resilience_system(self) -> None:
        """🔥 老王修复：注册到韧性系统进行监控和自动恢复"""
        try:
            from core.resilience import get_instance as get_resilience
            resilience = get_resilience()

            # 注册MySQL连接器服务
            success = resilience.register_service("mysql_connector", "database")
            if success:
                logger.success("✅ MySQL连接器已注册到韧性系统")
            else:
                logger.warning("⚠️ MySQL连接器注册到韧性系统失败")

        except Exception as e:
            logger.warning(f"⚠️ 无法注册到韧性系统: {e}")

    def _register_to_singleton_manager(self) -> None:
        """🔥 香草修复：注册到单例管理器"""
        try:
            # 注册MySQL连接器到单例管理器
            singleton_manager.register('mysql_connector', self)
            logger.success("✅ MySQL连接器已注册到单例管理器")
        except Exception as e:
            logger.error(f"❌ 注册到单例管理器失败: {e}")

    def health_check(self) -> bool:
        """🔥 老王修复：为韧性系统提供健康检查接口"""
        try:
            return (self._is_available and
                   self.is_initialized and
                   self.connection_pool is not None and
                   self._check_connection_pool())
        except Exception as e:
            logger.error(f"❌ MySQL健康检查失败: {e}")
            return False

    def _report_error_to_corrector(self, error_type: str, error_message: str) -> None:
        """🔥 增强版：安全报告错误给自主纠错系统"""
        try:
            # 尝试导入纠错系统
            from core.corrector import get_instance as get_corrector
            corrector = get_corrector()

            if corrector and hasattr(corrector, 'handle_error'):
                # 报告错误
                corrector.handle_error(
                    Exception(error_message),
                    context={
                        "component": "mysql_connector",
                        "error_type": error_type,
                        "host": self.host,
                        "port": self.port,
                        "database": self.database,
                        "pool_initialized": self.is_initialized
                }
                )
                logger.debug(f"✅ 已报告错误给自主纠错系统: {error_type}")
            else:
                logger.debug("纠错系统不可用或缺少handle_error方法")

        except ImportError:
            logger.debug("纠错系统模块未找到，跳过错误报告")
        except Exception as e:
            logger.debug(f"无法报告错误给自主纠错系统: {e}")
    def get_status(self) -> Dict[str, Any]:
        """
        获取连接器状态
        Returns:
            状态信息
        """
        # 🔥 老王修复：增加连接池状态监控
        pool_status = {}
        if hasattr(self, 'connection_pool') and self.connection_pool:
            try:
                if hasattr(self.connection_pool, '_cnx_queue'):
                    queue_size = self.connection_pool._cnx_queue.qsize() if self.connection_pool._cnx_queue else 0
                    pool_status = {
                        "pool_available": True,
                        "available_connections": queue_size,
                        "pool_size": self.config.get("pool_size", 5),
                        "pool_name": getattr(self.connection_pool, 'pool_name', 'unknown')
                    }
                else:
                    pool_status = {"pool_available": True, "available_connections": "unknown"}
            except Exception as e:
                pool_status = {"pool_available": False, "error": str(e)}
        else:
            pool_status = {"pool_available": False, "error": "连接池未初始化"}
        return {
            "available": self.is_available,
            "initialized": self.is_initialized,
            "last_error": self.last_error,
            "host": self.host,
            "port": self.port,
            "database": self.database,
            "user": self.user,
            "stats": self.stats,
            "pool_status": pool_status  # 🔥 新增连接池状态
        }
    def is_connected(self) -> bool:
        """
        检查MySQL连接是否可用
        Returns:
            bool: 连接是否可用
        """
        if not self.is_available or not self.is_initialized:
            return False
        try:
            # 尝试获取一个连接来测试
            if self.connection_pool:
                conn = self.connection_pool.get_connection()
                if conn:
                    # 执行一个简单的查询来测试连接
                    cursor = conn.cursor()
                    cursor.execute("SELECT 1")
                    cursor.fetchone()
                    cursor.close()
                    conn.close()
                    return True
            return False
        except Exception as e:
            logger.warning_status(f"MySQL连接检查失败: {e}")
            return False
    # === 数字生命扩展方法 - Digital Life Extensions ===
    def get_latest_weather(self, limit: int = 1) -> Tuple[bool, Optional[List[Dict[str, Any]]], Optional[str]]:
        """
        获取最新天气数据
        Args:
            limit: 返回记录数量限制
        Returns:
            Tuple[成功标志, 天气数据列表, 错误信息]
        """
        query = """
        SELECT city_name, temperature, humidity, wind_direction, wind_power,
               pm25, visibility, rainfall, pressure, update_time
        FROM weather 
        ORDER BY update_time DESC 
        LIMIT %s
        """
        success, result, error = self.query(query, (limit,))
        if success and result:
            logger.info(f"获取到 {len(result)} 条天气数据")
        return success, result, error
    def get_weather_by_city(self, city_name: str, limit: int = 10) -> Tuple[bool, Optional[List[Dict[str, Any]]], Optional[str]]:
        """
        获取指定城市的天气数据
        Args:
            city_name: 城市名称
            limit: 返回记录数量限制
        Returns:
            Tuple[成功标志, 天气数据列表, 错误信息]
        """
        query = """
        SELECT city_name, temperature, humidity, wind_direction, wind_power,
               pm25, visibility, rainfall, pressure, update_time
        FROM weather 
        WHERE city_name = %s
        ORDER BY update_time DESC 
        LIMIT %s
        """
        success, result, error = self.query(query, (city_name, limit))
        if success and result:
            logger.info(f"获取到城市 {city_name} 的 {len(result)} 条天气数据")
        return success, result, error
    def get_latest_financial_emails(self, limit: int = 10) -> Tuple[bool, Optional[List[Dict[str, Any]]], Optional[str]]:
        """
        获取最新的财经邮件摘要
        Args:
            limit: 返回记录数量限制
        Returns:
            Tuple[成功标志, 邮件数据列表, 错误信息]
        """
        query = """
        SELECT sender_email, sender_name, subject, content, 
               receive_time, email_date, content_type
        FROM email_records 
        WHERE (subject LIKE '%财经%' OR subject LIKE '%市场%' 
               OR subject LIKE '%股票%' OR subject LIKE '%经济%'
               OR content LIKE '%财经%' OR content LIKE '%市场%'
               OR content LIKE '%股票%' OR content LIKE '%经济%')
        ORDER BY receive_time DESC 
        LIMIT %s
        """
        success, result, error = self.query(query, (limit,))
        if success and result:
            logger.info(f"获取到 {len(result)} 条财经邮件数据")
        return success, result, error
    def get_latest_scripts(self, limit: int = 10, time_slot: str = None) -> Tuple[bool, Optional[List[Dict[str, Any]]], Optional[str]]:
        """
        获取最新的剧本数据
        Args:
            limit: 返回记录数量限制
            time_slot: 时间段过滤 ('morning', 'afternoon', 'evening')
        Returns:
            Tuple[成功标志, 剧本数据列表, 错误信息]
        """
        base_query = """
        SELECT id, role, time_slot, weather, activity, mood, created_at
        FROM scripts
        """
        params = []
        if time_slot:
            base_query += " WHERE time_slot = %s"
            params.append(time_slot)
        base_query += " ORDER BY created_at DESC LIMIT %s"
        params.append(limit)
        success, result, error = self.query(base_query, params)
        if success and result:
            logger.info(f"获取到 {len(result)} 条剧本数据")
        return success, result, error
    def get_comprehensive_context_data(self, user_id: str = None) -> Dict[str, Any]:
        """
        获取综合上下文数据，用于数字生命的感知和决策
        Args:
            user_id: 用户ID，如果提供则包含用户相关数据
        Returns:
            包含各种数据的综合字典
        """
        context_data = {
            'weather': None,
            'financial_emails': None,
            'scripts': None,
            'timestamp': datetime.now().isoformat()
        }
        try:
            # 获取最新天气数据
            success, weather_data, _ = self.get_latest_weather(3)
            if success and weather_data:
                context_data['weather'] = weather_data
            # 获取最新财经邮件
            success, email_data, _ = self.get_latest_financial_emails(5)
            if success and email_data:
                context_data['financial_emails'] = email_data
            # 获取最新剧本数据
            success, script_data, _ = self.get_latest_scripts(5)
            if success and script_data:
                context_data['scripts'] = script_data
            logger.success(f"获取综合上下文数据完成，用户: {user_id or '无'}")
        except Exception as e:
            logger.error_status(f"获取综合上下文数据时发生错误: {e}")
        return context_data
    def get_email_records(self, limit: int = 10, 
                         sender_filter: List[str] = None,
                         date_from: datetime = None) -> List[Dict[str, Any]]:
        """
        获取邮件记录
        Args:
            limit: 返回记录数量限制
            sender_filter: 发件人过滤列表
            date_from: 开始日期
        Returns:
            邮件记录列表
        """
        if not self.is_available():
            logger.warning("MySQL连接不可用，无法获取邮件记录")
            return []
        try:
            # 构建查询语句
            base_query = """
                SELECT id, sender_email, sender_name, subject, content, 
                       received_date, processed_date, category, importance
                FROM email_records 
                WHERE 1=1
            """
            params = []
            # 添加发件人过滤
            if sender_filter:
                placeholders = ','.join(['%s'] * len(sender_filter))
                base_query += f" AND sender_email IN ({placeholders})"
                params.extend(sender_filter)
            # 添加日期过滤
            if date_from:
                base_query += " AND received_date >= %s"
                params.append(date_from)
            # 添加排序和限制
            base_query += " ORDER BY received_date DESC LIMIT %s"
            params.append(limit)
            result = self.execute_query(base_query, tuple(params))
            logger.success(f"成功获取 {len(result)} 条邮件记录")
            return result
        except Exception as e:
            logger.error(f"获取邮件记录失败: {e}")
            return []
    def get_financial_emails_today(self, limit: int = 5) -> List[Dict[str, Any]]:
        """
        获取今日财经邮件
        Args:
            limit: 返回记录数量限制
        Returns:
            今日财经邮件列表
        """
        # 财经邮箱发件人列表（按照用户提供的逻辑）
        financial_senders = [
            '<EMAIL>',          # 万得资讯
            '<EMAIL>',     # 东方财富
            '<EMAIL>',          # 新浪财经
            '<EMAIL>',           # 网易财经
            '<EMAIL>',          # 中国证券网
            '<EMAIL>',             # 证券时报
            '<EMAIL>',         # 中证网
            '<EMAIL>'        # 财经网
        ]
        # 今日0点开始
        today_start = datetime.now().replace(hour=0, minute=0, second=0, microsecond=0)
        return self.get_email_records(
            limit=limit,
            sender_filter=financial_senders,
            date_from=today_start
        )
    def insert_email_record(self, email_data: Dict[str, Any]) -> bool:
        """
        插入邮件记录
        Args:
            email_data: 邮件数据字典
        Returns:
            是否插入成功
        """
        if not self.is_available():
            logger.error("MySQL连接不可用，无法插入邮件记录")
            return False
        try:
            query = """
                INSERT INTO email_records 
                (sender_email, sender_name, subject, content, received_date, 
                 processed_date, category, importance)
                VALUES (%s, %s, %s, %s, %s, %s, %s, %s)
            """
            params = (
                email_data.get('sender_email', ''),
                email_data.get('sender_name', ''),
                email_data.get('subject', ''),
                email_data.get('content', ''),
                email_data.get('received_date', datetime.now()),
                email_data.get('processed_date'),
                email_data.get('category', '财经'),
                email_data.get('importance', 0.5)
            )
            affected_rows = self.execute_update(query, params)
            if affected_rows > 0:
                logger.success("邮件记录插入成功")
                return True
            else:
                logger.warning("邮件记录插入失败，没有影响行数")
                return False
        except Exception as e:
            logger.error(f"插入邮件记录失败: {e}")
            return False
    def get_table_info(self, table_name: str) -> Dict[str, Any]:
        """
        获取表信息
        Args:
            table_name: 表名
        Returns:
            表信息字典
        """
        if not self.is_available():
            return {"exists": False, "columns": [], "count": 0}
        try:
            # 检查表是否存在
            check_query = """
                SELECT COUNT(*) as table_count 
                FROM information_schema.tables 
                WHERE table_schema = %s AND table_name = %s
            """
            success, result, error = self.query(check_query, (self.database, table_name))
            if not success or not result or result[0]['table_count'] == 0:
                return {"exists": False, "columns": [], "count": 0}
            # 获取列信息
            columns_query = """
                SELECT column_name, data_type, is_nullable, column_default
                FROM information_schema.columns 
                WHERE table_schema = %s AND table_name = %s
                ORDER BY ordinal_position
            """
            success, columns, error = self.query(columns_query, (self.database, table_name))
            if not success:
                columns = []
            # 获取记录数
            count_query = f"SELECT COUNT(*) as record_count FROM {table_name}"
            success, count_result, error = self.query(count_query)
            record_count = count_result[0]['record_count'] if success and count_result else 0
            return {
                "exists": True,
                "columns": columns or [],
                "count": record_count
            }
        except Exception as e:
            logger.error(f"获取表信息失败: {e}")
            return {"exists": False, "columns": [], "count": 0}
# 单例访问方法
def get_instance(config: Dict[str, Any] = None) -> MySQLConnector:
    """
    获取MySQL连接器的单例实例
    Args:
        config: 配置参数
    Returns:
        MySQL连接器实例
    """
    return MySQLConnector.get_instance(config)
def get_mysql_connector(**kwargs) -> MySQLConnector:
    """获取MySQL连接器（向后兼容函数）"""
    return get_instance(**kwargs)