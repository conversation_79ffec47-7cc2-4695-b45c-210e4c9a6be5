#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
通过API查看联系人数据
"""

import requests
import json

# 导入统一异常处理装饰器
from core.exception.decorators import network_operation

@network_operation
def check_contacts_via_api():
    """通过API查看联系人数据"""
    base_url = "http://127.0.0.1:56839"
    
    try:
        # 1. 检查联系人统计
        print("📊 联系人统计信息:")
        response = requests.get(f"{base_url}/api/contacts/sync", timeout=10)
        if response.status_code == 200:
            data = response.json()
            print(json.dumps(data, ensure_ascii=False, indent=2))
        else:
            print(f"❌ API调用失败: {response.status_code}")
            
    except requests.exceptions.ConnectionError:
        print("❌ 无法连接到API服务器，请确保数字生命系统正在运行")
        print("💡 启动命令: python main.py")
    except Exception as e:
        print(f"❌ API调用异常: {e}")

if __name__ == "__main__":
    check_contacts_via_api()