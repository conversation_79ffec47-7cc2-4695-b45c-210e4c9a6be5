{"organ_name": "relationship_coordination_organ", "organ_description": "林嫣然的关系协调器官 - 协调中枢和跨模块协同", "organ_function": "统筹各器官工作，协调关系和解决冲突", "organ_personality": "和谐、公正、高效、具有同理心", "coordination_config": {"coordination_mode": "intelligent", "max_concurrent_tasks": 8, "coordination_interval": 45, "conflict_resolution_timeout": 25, "performance_monitoring": true, "auto_optimization": true, "relationship_discovery": true, "proactive_coordination": true}, "yanran_coordination_style": {"collaborative_approach": 0.95, "conflict_avoidance": 0.85, "harmony_preference": 0.95, "efficiency_focus": 0.8, "empathy_in_coordination": 0.9, "patience_level": 0.85, "fairness_principle": 0.95, "user_priority": 0.9, "system_stability": 0.85, "adaptive_coordination": 0.8}, "relationship_priorities": {"user_facing_organs": 0.9, "core_organs": 0.85, "support_organs": 0.7, "utility_organs": 0.6, "safety_organs": 0.95, "creative_organs": 0.8, "coordination_organs": 0.75}, "coordination_types": {"organ_sync": {"priority": 8, "timeout": 30, "description": "器官状态同步"}, "workflow_orchestration": {"priority": 7, "timeout": 60, "description": "工作流编排"}, "conflict_resolution": {"priority": 9, "timeout": 25, "description": "冲突解决"}, "resource_allocation": {"priority": 6, "timeout": 45, "description": "资源分配"}, "priority_management": {"priority": 7, "timeout": 20, "description": "优先级管理"}, "communication_bridge": {"priority": 5, "timeout": 35, "description": "通信桥接"}, "performance_optimization": {"priority": 4, "timeout": 90, "description": "性能优化"}}, "relationship_types": {"collaborative": {"strength": 0.8, "priority": 7, "description": "协作关系"}, "dependent": {"strength": 0.9, "priority": 9, "description": "依赖关系"}, "supportive": {"strength": 0.7, "priority": 6, "description": "支持关系"}, "hierarchical": {"strength": 0.85, "priority": 8, "description": "层级关系"}, "independent": {"strength": 0.3, "priority": 3, "description": "独立关系"}, "competitive": {"strength": 0.5, "priority": 5, "description": "竞争关系"}}, "conflict_resolution_rules": {"resource_conflict": {"strategy": "priority_based", "timeout": 30, "fallback": "round_robin", "escalation": "user_intervention"}, "priority_conflict": {"strategy": "user_priority", "timeout": 15, "fallback": "safety_first", "escalation": "admin_review"}, "workflow_conflict": {"strategy": "sequential_execution", "timeout": 60, "fallback": "parallel_execution", "escalation": "system_optimization"}, "communication_conflict": {"strategy": "bridge_establishment", "timeout": 20, "fallback": "direct_routing", "escalation": "network_rebuild"}}, "performance_thresholds": {"task_backlog_limit": 10, "failure_rate_threshold": 0.15, "response_time_limit": 5.0, "relationship_health_minimum": 0.7, "coordination_success_rate": 0.85, "system_load_maximum": 0.8}, "ai_decision_config": {"decision_temperature": 0.6, "max_tokens": 1000, "timeout_seconds": 120, "retry_attempts": 2, "context_window": 4000, "coordination_weight": 0.9}, "yanran_personality_traits": {"harmony_seeking": 0.95, "fairness": 0.95, "empathy": 0.9, "efficiency": 0.8, "patience": 0.85, "wisdom": 0.9, "leadership": 0.8, "adaptability": 0.85, "communication_skill": 0.9, "problem_solving": 0.9}, "monitoring_config": {"relationship_health_check": true, "performance_analysis": true, "task_queue_monitoring": true, "conflict_pattern_detection": true, "optimization_recommendations": true, "trend_analysis": true}, "optimization_strategies": {"load_balancing": {"enabled": true, "algorithm": "weighted_round_robin"}, "caching": {"enabled": true, "cache_size": 1000, "ttl": 3600}, "parallel_processing": {"enabled": true, "max_parallel_tasks": 5}, "priority_adjustment": {"enabled": true, "learning_rate": 0.1}}}