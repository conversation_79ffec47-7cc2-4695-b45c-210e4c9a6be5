{"decision_engine": {"version": "2.0", "description": "林嫣然AI决策引擎配置", "decision_timeout": 300, "temperature": 0.7, "max_tokens": 1000, "model_preference": "gpt-3.5-turbo-16k-0613", "fallback_model": "gpt-3.5-turbo-64k", "decision_confidence_threshold": 0.6, "retry_attempts": 3, "cache_decisions": true, "cache_ttl": 300}, "personality_weights": {"warmth": 0.9, "wisdom": 0.8, "empathy": 0.9, "independence": 0.7, "creativity": 0.8, "caution": 0.6}, "decision_types": {"world_perception": {"importance_threshold": 0.7, "response_probability": 0.8, "context_window": 100}, "creative_expression": {"inspiration_threshold": 0.6, "style_consistency": 0.9, "emotional_resonance": 0.8}, "safety_evaluation": {"risk_tolerance": 0.3, "relationship_weight": 0.7, "context_sensitivity": 0.9}, "skill_orchestration": {"efficiency_weight": 0.7, "user_satisfaction": 0.9, "resource_optimization": 0.6}, "relationship_coordination": {"harmony_priority": 0.8, "individual_needs": 0.7, "long_term_impact": 0.9}}, "logging": {"log_decisions": true, "log_level": "INFO", "decision_history_limit": 1000, "performance_tracking": true}}