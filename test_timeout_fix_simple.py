#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🔥 老王的超时修复简化测试脚本
专门测试超时配置修复是否有效
"""

import asyncio
import time
import sys
import os

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from utilities.unified_logger import get_unified_logger

logger = get_unified_logger("test_timeout_fix_simple")

async def test_ai_adapter_basic():
    """测试AI适配器基础功能和超时配置"""
    try:
        logger.info("🔥 测试AI适配器基础功能...")
        
        from adapters.ai_service_adapter import get_ai_service_adapter
        
        ai_adapter = get_ai_service_adapter()
        
        # 测试简单的AI调用
        start_time = time.time()
        
        try:
            response = await ai_adapter.get_completion_async(
                messages=[{"role": "user", "content": "请简单回答：1+1等于几？"}],
                model="MiniMax-M1",
                temperature=0.1,
                max_tokens=50
            )
            
            end_time = time.time()
            execution_time = end_time - start_time
            
            logger.info(f"📊 AI调用完成，耗时: {execution_time:.2f}秒")
            
            if response and isinstance(response, dict):
                success = response.get('success', False)
                if success:
                    content = response.get('content', response.get('response', ''))
                    logger.success(f"✅ AI调用成功: {content[:100]}...")
                    return True
                else:
                    error_info = response.get('error', '未知错误')
                    logger.warning(f"⚠️ AI调用失败: {error_info}")
                    return False
            else:
                logger.warning(f"⚠️ AI响应格式异常: {type(response)}")
                return False
                
        except asyncio.TimeoutError:
            logger.error("❌ AI调用超时")
            return False
            
    except Exception as e:
        logger.error(f"❌ AI适配器测试异常: {e}")
        import traceback
        logger.debug(f"异常详情: {traceback.format_exc()}")
        return False

async def test_scripts_service_basic():
    """测试Scripts集成服务基础功能"""
    try:
        logger.info("🔥 测试Scripts集成服务基础功能...")
        
        from services.scripts_integration_service import get_scripts_integration_service
        
        # 获取服务实例
        service = get_scripts_integration_service()
        
        # 初始化服务
        logger.info("📊 初始化Scripts集成服务...")
        init_success = await service.initialize()
        if not init_success:
            logger.error("❌ Scripts集成服务初始化失败")
            return False
        
        logger.success("✅ Scripts集成服务初始化成功")
        
        # 测试重复生成检查（这个应该很快）
        logger.info("📊 测试重复生成检查...")
        start_time = time.time()
        
        is_recent = service._check_recent_generation("linyanran", "morning")
        
        end_time = time.time()
        execution_time = end_time - start_time
        
        logger.info(f"📊 重复生成检查完成，耗时: {execution_time:.2f}秒")
        logger.info(f"📊 检查结果: {'最近已生成' if is_recent else '可以生成'}")
        
        return True
            
    except Exception as e:
        logger.error(f"❌ Scripts服务测试异常: {e}")
        import traceback
        logger.debug(f"异常详情: {traceback.format_exc()}")
        return False

async def test_timeout_configurations():
    """测试超时配置是否正确设置"""
    try:
        logger.info("🔥 测试超时配置...")
        
        # 检查universal_scheduler的超时配置
        from core.universal_scheduler import UniversalScheduler
        
        logger.info("📊 检查调度器超时配置...")
        
        # 模拟一个长时间运行的任务来测试超时
        async def mock_long_task():
            """模拟一个需要30秒的任务"""
            await asyncio.sleep(30)
            return {"success": True, "message": "长任务完成"}
        
        start_time = time.time()
        
        try:
            # 使用60秒超时测试（应该能完成30秒的任务）
            result = await asyncio.wait_for(mock_long_task(), timeout=60.0)
            
            end_time = time.time()
            execution_time = end_time - start_time
            
            logger.success(f"✅ 长任务测试成功，耗时: {execution_time:.2f}秒")
            return True
            
        except asyncio.TimeoutError:
            end_time = time.time()
            execution_time = end_time - start_time
            logger.error(f"❌ 长任务测试超时，耗时: {execution_time:.2f}秒")
            return False
            
    except Exception as e:
        logger.error(f"❌ 超时配置测试异常: {e}")
        import traceback
        logger.debug(f"异常详情: {traceback.format_exc()}")
        return False

async def test_database_connection():
    """测试数据库连接"""
    try:
        logger.info("🔥 测试数据库连接...")
        
        from connectors.database.mysql import get_mysql_connector
        
        mysql_connector = get_mysql_connector()
        
        # 测试简单查询
        start_time = time.time()
        
        query = "SELECT 1 as test_value"
        result = mysql_connector.execute_query(query)
        
        end_time = time.time()
        execution_time = end_time - start_time
        
        logger.info(f"📊 数据库查询完成，耗时: {execution_time:.2f}秒")
        
        if result and len(result) > 0:
            logger.success(f"✅ 数据库连接正常: {result[0]}")
            return True
        else:
            logger.error("❌ 数据库查询结果异常")
            return False
            
    except Exception as e:
        logger.error(f"❌ 数据库连接测试异常: {e}")
        import traceback
        logger.debug(f"异常详情: {traceback.format_exc()}")
        return False

async def main():
    """主测试函数"""
    logger.info("🔥 老王的超时修复简化测试开始...")
    
    test_results = []
    
    # 测试1：数据库连接
    logger.info("=" * 50)
    logger.info("测试1：数据库连接")
    result1 = await test_database_connection()
    test_results.append(("数据库连接", result1))
    
    # 测试2：AI适配器基础功能
    logger.info("=" * 50)
    logger.info("测试2：AI适配器基础功能")
    result2 = await test_ai_adapter_basic()
    test_results.append(("AI适配器基础功能", result2))
    
    # 测试3：Scripts服务基础功能
    logger.info("=" * 50)
    logger.info("测试3：Scripts服务基础功能")
    result3 = await test_scripts_service_basic()
    test_results.append(("Scripts服务基础功能", result3))
    
    # 测试4：超时配置
    logger.info("=" * 50)
    logger.info("测试4：超时配置")
    result4 = await test_timeout_configurations()
    test_results.append(("超时配置", result4))
    
    # 汇总结果
    logger.info("=" * 50)
    logger.info("🔥 测试结果汇总:")
    
    success_count = 0
    for test_name, success in test_results:
        status = "✅ 通过" if success else "❌ 失败"
        logger.info(f"  {test_name}: {status}")
        if success:
            success_count += 1
    
    total_tests = len(test_results)
    logger.info(f"📊 总体结果: {success_count}/{total_tests} 测试通过")
    
    if success_count >= 3:  # 至少3个测试通过就算成功
        logger.success("🎉 大部分测试通过！超时修复基本成功！")
        return True
    else:
        logger.error(f"❌ 只有{success_count}个测试通过，需要进一步修复")
        return False

if __name__ == "__main__":
    try:
        success = asyncio.run(main())
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        logger.info("测试被用户中断")
        sys.exit(1)
    except Exception as e:
        logger.error(f"测试脚本异常: {e}")
        sys.exit(1)
