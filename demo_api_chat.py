#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
林嫣然数字生命 API Chat 调用示例
演示如何通过API与数字生命进行交互
"""

import requests
import json
import time
import sys
from datetime import datetime
from typing import Dict, Any



class YanranAPIClient:
    """林嫣然数字生命API客户端"""
    
    def __init__(self, base_url: str = "http://localhost:56839", user_id: str = "873854840"):
        self.base_url = base_url
        self.user_id = user_id
        self.session_id = f"session_{int(time.time())}"
        self.chat_endpoint = f"{base_url}/api/chat/message"
        self.api_key = "sk-HJVDtjSNhs0cYIE_K15PmVHHxSbiYaOf0BhHFj1WOXoI0CfWdP2pnE7RbC4"
        
    def send_message(self, user_input, user_id=None, user_name="流年", user_sex="1", 
                     isgroup=0, session_id=None, is_Segment=0, _token="12c4521a-7270-4553-a2ac-3af10244a35b", 
                     _appid="wx_574bN70yW1q0vTSFaaSHU") -> Dict[str, Any]:
        """
        发送消息给数字生命 - 完全兼容旧框架的9个参数调用格式
        
        兼容调用格式：
        send_message(user_input, user_id, user_name, user_sex, isgroup, session_id, is_Segment, _token, _appid)
        
        Args:
            user_input: 用户输入文本
            user_id: 用户ID (默认使用实例的user_id)
            user_name: 用户名称 (默认"流年")
            user_sex: 用户性别 (1:男, 0:女, 默认"1")
            isgroup: 是否群聊 (1:群聊, 0:私聊, 默认0)
            session_id: 会话ID (默认使用实例的session_id)
            is_Segment: 分段标识 (1:分段, 0:非分段, 默认0)
            _token: 身份令牌
            _appid: 应用ID
            
        Returns:
            API响应结果
        """
        # 🔥 使用实例默认值填充None参数
        if user_id is None:
            user_id = self.user_id
        if session_id is None:
            session_id = self.session_id
        
        # 🔥 构建与main.py API完全兼容的参数格式
        payload = {
            "user_input": user_input,     # 使用正确的字段名
            "user_id": user_id,
            "user_name": user_name,
            "user_sex": user_sex,
            "isgroup": isgroup,
            "session_id": session_id,
            "is_Segment": is_Segment,
            "_token": _token,
            "_appid": _appid
        }
        
        try:
            print(f"🚀 发送消息: {user_input}")
            print(f"📤 请求参数: user_id={user_id}, user_name={user_name}, user_sex={user_sex}, isgroup={isgroup}")
            
            response = requests.post(
                self.chat_endpoint,
                json=payload,
                headers={
                    "Content-Type": "application/json",
                    "Authorization": f"Bearer {self.api_key}",  # 添加API密钥认证
                    "User-Agent": "YanranDemo/1.0"
                },
                timeout=300
            )
            
            response.raise_for_status()
            result = response.json()
            
            print(f"✅ 响应状态: {response.status_code}")
            
            # 处理不同的响应格式
            if result.get('success') and result.get('data'):
                response_text = result['data'].get('response', '没有收到回复')
                print(f"🌸 林嫣然: {response_text}")
                # 返回兼容格式
                return {"response": response_text, "success": True}
            elif result.get('response'):
                # 兼容旧格式
                print(f"🌸 林嫣然: {result['response']}")
                return result
            else:
                print(f"⚠️ 响应格式异常: {result}")
                return result
            
        except requests.exceptions.RequestException as e:
            error_msg = f"❌ API请求失败: {e}"
            print(error_msg)
            return {"error": str(e), "success": False}
        except json.JSONDecodeError as e:
            error_msg = f"❌ JSON解析失败: {e}"
            print(error_msg)
            return {"error": "Invalid JSON response", "success": False}
    
    def _get_reply_openai(self, user_input, user_id, user_name, user_sex, isgroup, session_id, is_Segment, _token, _appid):
        """
        完全兼容旧框架的API调用接口
        
        兼容调用格式：
        yanran_response = self.yanranres._get_reply_openai(user_input, user_id, user_name, user_sex, isgroup, session_id, is_Segment, _token, _appid)
        
        Returns:
            str: 数字生命的回复文本
        """
        result = self.send_message(user_input, user_id, user_name, user_sex, isgroup, session_id, is_Segment, _token, _appid)
        
        # 返回纯文本回复，完全兼容旧框架
        if result.get('success', True):
            return result.get('response', '抱歉，没有收到回复')
        else:
            return f"API调用失败: {result.get('error', '未知错误')}"


def interactive_chat():
    """交互式聊天模式"""
    
    print("\n🔄 进入交互式聊天模式")
    print("💡 输入 'quit' 或 'exit' 退出")
    print("-" * 40)
    
    client = YanranAPIClient()
    
    while True:
        try:
            user_input = input("\n👤 您: ").strip()
            
            if user_input.lower() in ['quit', 'exit', '退出']:
                print("👋 再见！")
                break
                
            if not user_input:
                print("⚠️ 请输入消息")
                continue
            
            # 发送消息
            result = client.send_message(user_input)
            
            # 显示回复
            if result.get('success', True):
                response = result.get('response', '没有收到回复')
                print(f"🌸 林嫣然: {response}")
            else:
                print(f"❌ 发送失败: {result.get('error', '未知错误')}")
                
        except KeyboardInterrupt:
            print("\n👋 用户中断，再见！")
            break
        except Exception as e:
            print(f"❌ 发生错误: {e}")

def main():
    """主函数"""
    
    print(f"⏰ 启动时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 交互式模式
    interactive_chat()

if __name__ == "__main__":
    main() 