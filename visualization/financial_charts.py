#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
金融数据可视化组件

提供金融数据的可视化能力，包括K线图、成交量图、技术指标等。
支持多种图表类型和样式定制。
"""

import os
import json
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import matplotlib.dates as mdates
import matplotlib.ticker as mticker
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Union, Tuple
from utilities.unified_logger import get_unified_logger, setup_unified_logging
# 配置日志
logger = get_unified_logger("visualization.financial_charts")

# 默认样式配置
DEFAULT_STYLE = {
    "up_color": "red",        # 上涨颜色
    "down_color": "green",    # 下跌颜色
    "line_color": "blue",     # 线图颜色
    "grid_color": "#E6E6E6",  # 网格颜色
    "text_color": "#333333",  # 文本颜色
    "figsize": (12, 8),       # 图表大小
    "volume_height": 0.2,     # 成交量图高度比例
    "dpi": 100,               # 图表分辨率
    "theme": "light"          # 主题：light或dark
}

class FinancialCharts:
    """金融数据可视化类"""
    
    def __init__(self, style: Dict[str, Any] = None):
        """
        初始化可视化组件
        
        Args:
            style: 样式配置
        """
        # 合并自定义样式和默认样式
        self.style = DEFAULT_STYLE.copy()
        if style:
            self.style.update(style)
        
        # 设置主题样式
        if self.style["theme"] == "dark":
            plt.style.use('dark_background')
            # 更新暗色主题下的颜色
            if "grid_color" not in style:
                self.style["grid_color"] = "#444444"
            if "text_color" not in style:
                self.style["text_color"] = "#CCCCCC"
    
    def _preprocess_data(self, data: Union[pd.DataFrame, List[Dict[str, Any]]]) -> pd.DataFrame:
        """
        预处理数据，将列表转换为DataFrame并确保包含必要的列
        
        Args:
            data: 原始数据，可以是DataFrame或字典列表
            
        Returns:
            处理后的DataFrame
        """
        # 如果是字典列表，转换为DataFrame
        if isinstance(data, list):
            df = pd.DataFrame(data)
        else:
            df = data.copy()
        
        # 确保DataFrame包含必要的列
        required_columns = {'date', 'open', 'high', 'low', 'close'}
        if not required_columns.issubset(set(df.columns)):
            missing = required_columns - set(df.columns)
            raise ValueError(f"数据缺少必要的列: {missing}")
        
        # 如果date列不是datetime类型，尝试转换
        if not pd.api.types.is_datetime64_any_dtype(df['date']):
            try:
                df['date'] = pd.to_datetime(df['date'])
            except Exception as e:
                logger.warning_status(f"无法将日期列转换为datetime类型: {e}")
        
        # 确保数据按日期排序
        df = df.sort_values('date')
        
        return df
    
    def plot_candlestick(self, data: Union[pd.DataFrame, List[Dict[str, Any]]], 
                        title: str = "股票K线图", save_path: str = None, 
                        show_volume: bool = True, show_ma: bool = True,
                        ma_periods: List[int] = [5, 10, 20]) -> plt.Figure:
        """
        绘制K线图
        
        Args:
            data: 股票数据，可以是DataFrame或字典列表
            title: 图表标题
            save_path: 保存路径，如果为None则不保存
            show_volume: 是否显示成交量
            show_ma: 是否显示移动平均线
            ma_periods: 移动平均线周期列表
            
        Returns:
            matplotlib图表对象
        """
        # 预处理数据
        df = self._preprocess_data(data)
        
        # 计算图表高度比例
        if show_volume:
            price_height = 1 - self.style["volume_height"]
        else:
            price_height = 1
        
        # 创建图表
        fig, ax = plt.subplots(figsize=self.style["figsize"], dpi=self.style["dpi"])
        
        if show_volume:
            # 创建成交量子图
            ax_volume = ax.inset_axes([0, 0, 1, self.style["volume_height"]])
            price_ax = ax.inset_axes([0, self.style["volume_height"], 1, price_height])
        else:
            price_ax = ax
        
        # 计算K线图宽度
        width = 0.6 / (df.index[-1] - df.index[0] + 1) * len(df) if len(df) > 1 else 0.6
        
        # 绘制K线图
        for i in range(len(df)):
            # 获取当天数据
            date = df.iloc[i]['date']
            open_price = df.iloc[i]['open']
            close_price = df.iloc[i]['close']
            high = df.iloc[i]['high']
            low = df.iloc[i]['low']
            
            # 确定颜色（上涨为红色，下跌为绿色）
            color = self.style["up_color"] if close_price >= open_price else self.style["down_color"]
            
            # 绘制影线
            price_ax.plot([i, i], [low, high], color=color, linewidth=1)
            
            # 绘制实体
            bottom = min(open_price, close_price)
            height = abs(open_price - close_price)
            price_ax.bar(i, height, bottom=bottom, width=width, color=color, alpha=0.8)
        
        # 绘制移动平均线
        if show_ma:
            for period in ma_periods:
                if len(df) >= period:
                    ma_data = df['close'].rolling(window=period).mean()
                    price_ax.plot(range(len(df)), ma_data, 
                               label=f'MA{period}',
                               linewidth=1.5)
        
        # 绘制成交量
        if show_volume and 'volume' in df.columns:
            # 绘制成交量柱状图
            for i in range(len(df)):
                open_price = df.iloc[i]['open']
                close_price = df.iloc[i]['close']
                volume = df.iloc[i]['volume']
                
                # 确定颜色
                color = self.style["up_color"] if close_price >= open_price else self.style["down_color"]
                
                # 绘制成交量柱
                ax_volume.bar(i, volume, width=width, color=color, alpha=0.5)
            
            # 设置成交量图样式
            ax_volume.set_ylabel('成交量', color=self.style["text_color"])
            ax_volume.tick_params(axis='y', labelcolor=self.style["text_color"])
            ax_volume.grid(axis='y', linestyle='--', alpha=0.3, color=self.style["grid_color"])
            ax_volume.set_xticks([])
        
        # 设置价格图样式
        price_ax.set_title(title, fontsize=14, color=self.style["text_color"])
        price_ax.set_ylabel('价格', color=self.style["text_color"])
        price_ax.tick_params(axis='y', labelcolor=self.style["text_color"])
        price_ax.grid(linestyle='--', alpha=0.3, color=self.style["grid_color"])
        
        # 设置x轴刻度
        step = max(1, len(df) // 10)  # 最多显示10个日期标签
        price_ax.set_xticks(range(0, len(df), step))
        price_ax.set_xticklabels([df.iloc[i]['date'].strftime('%Y-%m-%d') for i in range(0, len(df), step)], 
                            rotation=45, color=self.style["text_color"])
        
        # 显示图例
        if show_ma:
            price_ax.legend(loc='upper left')
        
        plt.tight_layout()
        
        # 保存图表
        if save_path:
            try:
                plt.savefig(save_path, dpi=self.style["dpi"])
                logger.info(f"图表已保存至 {save_path}")
            except Exception as e:
                logger.error_status(f"保存图表失败: {e}")
        
        return fig
    
    def plot_line(self, data: Union[pd.DataFrame, List[Dict[str, Any]]], 
                  column: str = 'close', title: str = "股票走势图",
                  save_path: str = None) -> plt.Figure:
        """
        绘制线图
        
        Args:
            data: 股票数据，可以是DataFrame或字典列表
            column: 要绘制的列名
            title: 图表标题
            save_path: 保存路径，如果为None则不保存
            
        Returns:
            matplotlib图表对象
        """
        # 预处理数据
        df = self._preprocess_data(data)
        
        if column not in df.columns:
            raise ValueError(f"数据中不存在列 '{column}'")
        
        # 创建图表
        fig, ax = plt.subplots(figsize=self.style["figsize"], dpi=self.style["dpi"])
        
        # 绘制线图
        ax.plot(df['date'], df[column], color=self.style["line_color"], linewidth=2)
        
        # 设置图表样式
        ax.set_title(title, fontsize=14, color=self.style["text_color"])
        ax.set_ylabel(column, color=self.style["text_color"])
        ax.tick_params(axis='y', labelcolor=self.style["text_color"])
        ax.tick_params(axis='x', labelcolor=self.style["text_color"])
        ax.grid(linestyle='--', alpha=0.3, color=self.style["grid_color"])
        
        # 设置x轴日期格式
        ax.xaxis.set_major_formatter(mdates.DateFormatter('%Y-%m-%d'))
        plt.xticks(rotation=45)
        
        plt.tight_layout()
        
        # 保存图表
        if save_path:
            try:
                plt.savefig(save_path, dpi=self.style["dpi"])
                logger.info(f"图表已保存至 {save_path}")
            except Exception as e:
                logger.error_status(f"保存图表失败: {e}")
        
        return fig
    
    def plot_technical_indicators(self, data: Union[pd.DataFrame, List[Dict[str, Any]]],
                                 indicators: List[str] = ['RSI', 'MACD', 'Bollinger'],
                                 title: str = "技术指标分析图",
                                 save_path: str = None) -> plt.Figure:
        """
        绘制技术指标图
        
        Args:
            data: 股票数据，可以是DataFrame或字典列表
            indicators: 要绘制的技术指标列表
            title: 图表标题
            save_path: 保存路径，如果为None则不保存
            
        Returns:
            matplotlib图表对象
        """
        # 预处理数据
        df = self._preprocess_data(data)
        
        # 计算技术指标
        df_with_indicators = self._calculate_indicators(df, indicators)
        
        # 计算需要的子图数量
        n_plots = 1  # 价格图
        if 'RSI' in indicators:
            n_plots += 1
        if 'MACD' in indicators:
            n_plots += 1
        
        # 创建图表
        fig, axs = plt.subplots(n_plots, 1, figsize=self.style["figsize"], 
                              dpi=self.style["dpi"], sharex=True,
                              gridspec_kw={'height_ratios': [3] + [1] * (n_plots-1)})
        
        if n_plots == 1:
            axs = [axs]
        
        # 绘制价格图和布林带
        ax_price = axs[0]
        ax_price.plot(df_with_indicators['date'], df_with_indicators['close'], 
                   color=self.style["line_color"], linewidth=1.5, label='收盘价')
        
        # 绘制布林带
        if 'Bollinger' in indicators:
            ax_price.plot(df_with_indicators['date'], df_with_indicators['upper_band'], 
                       'k--', alpha=0.7, label='上轨')
            ax_price.plot(df_with_indicators['date'], df_with_indicators['middle_band'], 
                       'k-', alpha=0.7, label='中轨')
            ax_price.plot(df_with_indicators['date'], df_with_indicators['lower_band'], 
                       'k--', alpha=0.7, label='下轨')
            
            # 填充布林带区域
            ax_price.fill_between(df_with_indicators['date'], 
                               df_with_indicators['upper_band'],
                               df_with_indicators['lower_band'],
                               color='gray', alpha=0.1)
        
        ax_price.set_title(title, fontsize=14, color=self.style["text_color"])
        ax_price.set_ylabel('价格', color=self.style["text_color"])
        ax_price.tick_params(axis='y', labelcolor=self.style["text_color"])
        ax_price.grid(linestyle='--', alpha=0.3, color=self.style["grid_color"])
        ax_price.legend(loc='upper left')
        
        # 绘制其他指标
        plot_idx = 1
        
        # 绘制RSI
        if 'RSI' in indicators:
            ax_rsi = axs[plot_idx]
            ax_rsi.plot(df_with_indicators['date'], df_with_indicators['RSI'], 
                     color='purple', linewidth=1.5)
            ax_rsi.axhline(y=70, color='r', linestyle='--', alpha=0.5)
            ax_rsi.axhline(y=30, color='g', linestyle='--', alpha=0.5)
            ax_rsi.set_ylabel('RSI', color=self.style["text_color"])
            ax_rsi.tick_params(axis='y', labelcolor=self.style["text_color"])
            ax_rsi.grid(linestyle='--', alpha=0.3, color=self.style["grid_color"])
            ax_rsi.set_ylim(0, 100)
            plot_idx += 1
        
        # 绘制MACD
        if 'MACD' in indicators:
            ax_macd = axs[plot_idx]
            ax_macd.plot(df_with_indicators['date'], df_with_indicators['MACD'], 
                      color='blue', linewidth=1.5, label='MACD')
            ax_macd.plot(df_with_indicators['date'], df_with_indicators['signal_line'], 
                      color='red', linewidth=1, label='信号线')
            
            # 绘制MACD柱状图
            for i in range(len(df_with_indicators)):
                if i > 0:  # 跳过第一个点，因为可能没有前一天的数据
                    macd = df_with_indicators.iloc[i]['MACD']
                    signal = df_with_indicators.iloc[i]['signal_line']
                    date = df_with_indicators.iloc[i]['date']
                    
                    # 确定颜色
                    color = self.style["up_color"] if macd > signal else self.style["down_color"]
                    
                    # 绘制柱状图
                    ax_macd.bar(date, macd - signal, color=color, alpha=0.5, width=1)
            
            ax_macd.set_ylabel('MACD', color=self.style["text_color"])
            ax_macd.tick_params(axis='y', labelcolor=self.style["text_color"])
            ax_macd.grid(linestyle='--', alpha=0.3, color=self.style["grid_color"])
            ax_macd.legend(loc='upper left')
        
        # 设置x轴日期格式
        axs[-1].xaxis.set_major_formatter(mdates.DateFormatter('%Y-%m-%d'))
        plt.xticks(rotation=45)
        
        plt.tight_layout()
        
        # 保存图表
        if save_path:
            try:
                plt.savefig(save_path, dpi=self.style["dpi"])
                logger.info(f"图表已保存至 {save_path}")
            except Exception as e:
                logger.error_status(f"保存图表失败: {e}")
        
        return fig
    
    def _calculate_indicators(self, df: pd.DataFrame, indicators: List[str]) -> pd.DataFrame:
        """
        计算技术指标
        
        Args:
            df: 数据DataFrame
            indicators: 要计算的技术指标列表
            
        Returns:
            包含计算结果的DataFrame
        """
        result_df = df.copy()
        
        # 计算RSI
        if 'RSI' in indicators:
            # 计算价格变化
            delta = df['close'].diff()
            
            # 计算涨跌
            gain = delta.where(delta > 0, 0)
            loss = -delta.where(delta < 0, 0)
            
            # 计算平均涨跌
            avg_gain = gain.rolling(window=14).mean()
            avg_loss = loss.rolling(window=14).mean()
            
            # 计算相对强度
            rs = avg_gain / avg_loss
            
            # 计算RSI
            result_df['RSI'] = 100 - (100 / (1 + rs))
        
        # 计算MACD
        if 'MACD' in indicators:
            # 计算EMA
            ema12 = df['close'].ewm(span=12, adjust=False).mean()
            ema26 = df['close'].ewm(span=26, adjust=False).mean()
            
            # 计算MACD线
            result_df['MACD'] = ema12 - ema26
            
            # 计算信号线
            result_df['signal_line'] = result_df['MACD'].ewm(span=9, adjust=False).mean()
        
        # 计算布林带
        if 'Bollinger' in indicators:
            # 计算中轨（20日移动平均线）
            result_df['middle_band'] = df['close'].rolling(window=20).mean()
            
            # 计算标准差
            std = df['close'].rolling(window=20).std()
            
            # 计算上下轨
            result_df['upper_band'] = result_df['middle_band'] + 2 * std
            result_df['lower_band'] = result_df['middle_band'] - 2 * std
        
        return result_df
    
    def plot_comparison(self, data_list: List[Union[pd.DataFrame, List[Dict[str, Any]]]],
                      names: List[str], column: str = 'close',
                      title: str = "股票对比图",
                      save_path: str = None) -> plt.Figure:
        """
        绘制多只股票对比图
        
        Args:
            data_list: 股票数据列表，每项可以是DataFrame或字典列表
            names: 股票名称列表
            column: 要对比的列名
            title: 图表标题
            save_path: 保存路径，如果为None则不保存
            
        Returns:
            matplotlib图表对象
        """
        if len(data_list) != len(names):
            raise ValueError("数据列表和名称列表长度必须一致")
        
        # 创建图表
        fig, ax = plt.subplots(figsize=self.style["figsize"], dpi=self.style["dpi"])
        
        # 设置颜色循环
        colors = plt.cm.tab10.colors
        
        # 绘制每只股票的数据
        for i, (data, name) in enumerate(zip(data_list, names)):
            # 预处理数据
            df = self._preprocess_data(data)
            
            if column not in df.columns:
                logger.warning_status(f"数据 '{name}' 中不存在列 '{column}'，已跳过")
                continue
            
            # 计算百分比变化以便比较
            first_value = df[column].iloc[0]
            normalized = (df[column] / first_value - 1) * 100
            
            # 绘制线图
            color = colors[i % len(colors)]
            ax.plot(df['date'], normalized, color=color, linewidth=2, label=name)
        
        # 设置图表样式
        ax.set_title(title, fontsize=14, color=self.style["text_color"])
        ax.set_ylabel(f'{column} 变化率 (%)', color=self.style["text_color"])
        ax.tick_params(axis='y', labelcolor=self.style["text_color"])
        ax.tick_params(axis='x', labelcolor=self.style["text_color"])
        ax.grid(linestyle='--', alpha=0.3, color=self.style["grid_color"])
        ax.axhline(y=0, color='k', linestyle='-', alpha=0.3)
        
        # 设置x轴日期格式
        ax.xaxis.set_major_formatter(mdates.DateFormatter('%Y-%m-%d'))
        plt.xticks(rotation=45)
        
        # 显示图例
        ax.legend(loc='upper left')
        
        plt.tight_layout()
        
        # 保存图表
        if save_path:
            try:
                plt.savefig(save_path, dpi=self.style["dpi"])
                logger.info(f"图表已保存至 {save_path}")
            except Exception as e:
                logger.error_status(f"保存图表失败: {e}")
        
        return fig 