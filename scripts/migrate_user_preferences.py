#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
用户偏好数据迁移脚本
🔥 老王修复：从现有数据源迁移用户偏好数据，解决用户偏好系统为空的问题
"""

import json
import os
import sys
import time
from typing import Dict, Any, List
from datetime import datetime

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger("user_preference_migrator")
from core.user_preference_manager import get_user_preference_manager


class UserPreferenceMigrator:
    """用户偏好迁移器"""
    
    def __init__(self):
        self.preference_manager = get_user_preference_manager()
        self.migrated_users = []
        self.migration_stats = {
            "total_users": 0,
            "migrated_users": 0,
            "failed_users": 0,
            "data_sources": []
        }
    
    def migrate_all_user_preferences(self):
        """迁移所有用户偏好数据"""
        logger.info("🔥 开始用户偏好数据迁移...")
        
        try:
            # 1. 从用户行为档案迁移
            self._migrate_from_behavior_profiles()
            
            # 2. 从数据库迁移
            self._migrate_from_database()
            
            # 3. 创建默认用户偏好
            self._create_default_preferences()
            
            # 4. 验证迁移结果
            self._validate_migration()
            
            # 5. 生成迁移报告
            self._generate_migration_report()
            
            logger.info(f"✅ 用户偏好迁移完成！共迁移 {self.migration_stats['migrated_users']} 个用户")
            
        except Exception as e:
            logger.error(f"❌ 用户偏好迁移失败: {e}")
            raise
    
    def _migrate_from_behavior_profiles(self):
        """从用户行为档案迁移"""
        try:
            behavior_file = "data/intelligence/user_behavior_profiles.json"
            if not os.path.exists(behavior_file):
                logger.warning("用户行为档案文件不存在，跳过此数据源")
                return
            
            with open(behavior_file, 'r', encoding='utf-8') as f:
                behavior_data = json.load(f)
            
            logger.info(f"从用户行为档案发现 {len(behavior_data)} 个用户")
            self.migration_stats["data_sources"].append("behavior_profiles")
            
            for user_id, profile in behavior_data.items():
                try:
                    # 基于行为档案生成偏好
                    preferences = self._generate_preferences_from_behavior(profile)
                    
                    # 批量更新用户偏好
                    success = self.preference_manager.update_user_preferences(user_id, preferences)
                    
                    if success:
                        self.migrated_users.append(user_id)
                        self.migration_stats["migrated_users"] += 1
                        logger.info(f"✅ 用户 {user_id} 偏好迁移成功")
                    else:
                        self.migration_stats["failed_users"] += 1
                        logger.warning(f"⚠️ 用户 {user_id} 偏好迁移失败")
                        
                except Exception as e:
                    self.migration_stats["failed_users"] += 1
                    logger.error(f"❌ 用户 {user_id} 偏好迁移异常: {e}")
            
            self.migration_stats["total_users"] += len(behavior_data)
            
        except Exception as e:
            logger.error(f"从用户行为档案迁移失败: {e}")
    
    def _generate_preferences_from_behavior(self, profile: Dict[str, Any]) -> Dict[str, Any]:
        """基于用户行为档案生成偏好配置"""
        preferences = {}
        
        # 基于活跃时间设置静默时间
        active_hours = profile.get("active_hours", [])
        if active_hours:
            # 找到最早和最晚的活跃时间
            earliest = min(active_hours)
            latest = max(active_hours)
            
            # 设置静默时间（活跃时间之外）
            quiet_start = f"{(latest + 2) % 24:02d}:00"
            quiet_end = f"{(earliest - 1) % 24:02d}:00"
            preferences["quiet_hours"] = {"start": quiet_start, "end": quiet_end}
        
        # 基于消息类型偏好设置通知类型
        preferred_types = profile.get("preferred_message_types", {})
        notification_types = []
        if "financial_morning_report" in preferred_types:
            notification_types.extend(["financial", "morning_greeting"])
        if "sharing" in preferred_types:
            notification_types.append("sharing")
        if not notification_types:
            notification_types = ["greeting", "sharing", "caring"]
        preferences["notification_types"] = notification_types
        
        # 基于参与度设置表达频率
        engagement_score = profile.get("engagement_score", 0.5)
        if engagement_score > 0.8:
            preferences["expression_frequency"] = "high"
        elif engagement_score > 0.6:
            preferences["expression_frequency"] = "normal"
        elif engagement_score > 0.4:
            preferences["expression_frequency"] = "low"
        else:
            preferences["expression_frequency"] = "very_low"
        
        # 基于响应率设置主动表达
        response_rate = profile.get("response_rate", 0.5)
        preferences["proactive_expression"] = response_rate > 0.3
        
        # 基于消息长度偏好设置响应风格
        message_length = profile.get("message_length_preference", "medium")
        if message_length == "long":
            preferences["response_style"] = "detailed"
        elif message_length == "short":
            preferences["response_style"] = "concise"
        else:
            preferences["response_style"] = "friendly"
        
        # 设置默认值
        preferences.update({
            "morning_greeting": True,
            "language": "zh-CN",
            "migrated_from": "behavior_profiles",
            "migration_time": time.time()
        })
        
        return preferences
    
    def _migrate_from_database(self):
        """从数据库迁移用户数据"""
        try:
            # 尝试从数据库获取用户信息
            from adapters.legacy_adapter import get_instance as get_legacy_adapter
            legacy_adapter = get_legacy_adapter()
            
            if not legacy_adapter or not legacy_adapter.mysql or not legacy_adapter.mysql.is_available:
                logger.warning("数据库不可用，跳过数据库迁移")
                return
            
            # 查询所有用户
            user_query = "SELECT id, name, created_at, first_chat_time, last_chat_time FROM users LIMIT 100"
            success, results, error = legacy_adapter.mysql.query(user_query)
            
            if not success:
                logger.warning(f"数据库查询失败: {error}")
                return
            
            logger.info(f"从数据库发现 {len(results)} 个用户")
            self.migration_stats["data_sources"].append("database")
            
            for user_data in results:
                try:
                    user_id = user_data.get("id")
                    if not user_id or user_id in self.migrated_users:
                        continue  # 跳过已迁移的用户
                    
                    # 基于数据库信息生成偏好
                    preferences = self._generate_preferences_from_database(user_data)
                    
                    # 更新用户偏好
                    success = self.preference_manager.update_user_preferences(user_id, preferences)
                    
                    if success:
                        self.migrated_users.append(user_id)
                        self.migration_stats["migrated_users"] += 1
                        logger.info(f"✅ 数据库用户 {user_id} 偏好迁移成功")
                    else:
                        self.migration_stats["failed_users"] += 1
                        
                except Exception as e:
                    self.migration_stats["failed_users"] += 1
                    logger.error(f"❌ 数据库用户迁移异常: {e}")
            
            self.migration_stats["total_users"] += len(results)
            
        except Exception as e:
            logger.error(f"从数据库迁移失败: {e}")
    
    def _generate_preferences_from_database(self, user_data: Dict[str, Any]) -> Dict[str, Any]:
        """基于数据库用户信息生成偏好配置"""
        preferences = {
            "morning_greeting": True,
            "proactive_expression": True,
            "expression_frequency": "normal",
            "notification_types": ["greeting", "sharing", "caring"],
            "quiet_hours": {"start": "22:00", "end": "07:00"},
            "language": "zh-CN",
            "response_style": "friendly",
            "migrated_from": "database",
            "migration_time": time.time()
        }
        
        # 基于用户名设置个性化偏好
        user_name = user_data.get("name", "")
        if user_name:
            preferences["user_name"] = user_name
        
        # 基于创建时间设置用户类型
        created_at = user_data.get("created_at") or user_data.get("first_chat_time")
        if created_at:
            preferences["user_since"] = str(created_at)
        
        return preferences
    
    def _create_default_preferences(self):
        """为系统创建默认用户偏好"""
        try:
            # 创建一些默认用户
            default_users = [
                "liu_defei_cool",  # 测试用户
                "default_user",    # 默认用户
                "system_user"      # 系统用户
            ]
            
            for user_id in default_users:
                if user_id not in self.migrated_users:
                    preferences = {
                        "morning_greeting": True,
                        "proactive_expression": True,
                        "expression_frequency": "normal",
                        "notification_types": ["greeting", "sharing", "caring", "financial"],
                        "quiet_hours": {"start": "22:00", "end": "07:00"},
                        "language": "zh-CN",
                        "response_style": "friendly",
                        "migrated_from": "default_creation",
                        "migration_time": time.time()
                    }
                    
                    success = self.preference_manager.update_user_preferences(user_id, preferences)
                    if success:
                        self.migrated_users.append(user_id)
                        self.migration_stats["migrated_users"] += 1
                        logger.info(f"✅ 创建默认用户 {user_id} 偏好")
            
        except Exception as e:
            logger.error(f"创建默认用户偏好失败: {e}")
    
    def _validate_migration(self):
        """验证迁移结果"""
        try:
            # 检查偏好文件是否有数据
            storage_path = self.preference_manager.storage_path
            if os.path.exists(storage_path):
                with open(storage_path, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                
                actual_users = len(data)
                logger.info(f"✅ 验证成功：偏好文件包含 {actual_users} 个用户")
                
                if actual_users != self.migration_stats["migrated_users"]:
                    logger.warning(f"⚠️ 数据不一致：预期 {self.migration_stats['migrated_users']} 个用户，实际 {actual_users} 个")
            else:
                logger.error("❌ 验证失败：偏好文件不存在")
                
        except Exception as e:
            logger.error(f"验证迁移结果失败: {e}")
    
    def _generate_migration_report(self):
        """生成迁移报告"""
        try:
            report = {
                "migration_time": datetime.now().isoformat(),
                "statistics": self.migration_stats,
                "migrated_users": self.migrated_users,
                "success_rate": self.migration_stats["migrated_users"] / max(self.migration_stats["total_users"], 1)
            }
            
            report_file = "data/user_preference_migration_report.json"
            with open(report_file, 'w', encoding='utf-8') as f:
                json.dump(report, f, ensure_ascii=False, indent=2)
            
            logger.info(f"✅ 迁移报告已保存到 {report_file}")
            
        except Exception as e:
            logger.error(f"生成迁移报告失败: {e}")


def main():
    """主函数"""
    try:
        migrator = UserPreferenceMigrator()
        migrator.migrate_all_user_preferences()
        
    except Exception as e:
        logger.error(f"用户偏好迁移脚本执行失败: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
