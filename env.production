# 林嫣然数字生命体系统 - 生产环境配置
# 版本: v3.1.0

# ==================== 系统配置 ====================
YANRAN_ENVIRONMENT=production
YANRAN_LOG_LEVEL=WARNING
YANRAN_DEBUG=false
YANRAN_VERSION=3.1.0

# ==================== 服务配置 ====================
API_HOST=0.0.0.0
API_PORT=56839
WEBSOCKET_PORT=8765
MAX_WORKERS=4
WORKER_TIMEOUT=300

# ==================== 数据库配置 ====================
# MySQL配置
MYSQL_HOST=**************
MYSQL_PORT=3306
MYSQL_DATABASE=linyanran
MYSQL_USER=root
MYSQL_PASSWORD=55cee73f3102126a

# Redis配置
REDIS_HOST=127.0.0.1
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_DB=0

# ChromaDB配置
CHROMA_HOST=**************
CHROMA_PORT=5001
CHROMA_API_KEY=sk-92bf1dcc3b5d4d9da90b72cd9327c0f2

# 向量嵌入API配置
EMBEDDING_API_KEY=sk-lJ1R156TicBrrU9G2a789f03F5C14515993256Fe306d4691

# ==================== AI服务配置 ====================
# OpenAI配置
OPENAI_API_KEY=sk-HJVDtjSNhs0cYIE_K15PmVHHxSbiYaOf0BhHFj1WOXoI0CfWdP2pnE7RbC4
OPENAI_API_BASE=https://oneapi.xiongmaodaxia.online/v1
OPENAI_MODEL=MiniMax-M1

# 其他AI服务
DEEPSEEK_API_KEY=sk-HJVDtjSNhs0cYIE_K15PmVHHxSbiYaOf0BhHFj1WOXoI0CfWdP2pnE7RbC4
ZHIPU_API_KEY=sk-HJVDtjSNhs0cYIE_K15PmVHHxSbiYaOf0BhHFj1WOXoI0CfWdP2pnE7RbC4
QIANWEN_API_KEY=sk-HJVDtjSNhs0cYIE_K15PmVHHxSbiYaOf0BhHFj1WOXoI0CfWdP2pnE7RbC4

# ==================== 路径配置 ====================
# 数据目录
DATA_DIR=/var/lib/yanran/data
LOG_DIR=/var/log/yanran
CONFIG_DIR=/etc/yanran/config
BACKUP_DIR=/var/backups/yanran
PLUGINS_DIR=/usr/share/yanran/plugins

# 运行时文件
PID_FILE=/var/run/yanran.pid
LOCK_FILE=/var/lock/yanran.lock

# ==================== 性能配置 ====================
# 内存限制
MAX_MEMORY=8G
GC_THRESHOLD=0.8

# 并发配置
MAX_CONCURRENT_REQUESTS=50
PROCESS_POOL_SIZE=4
THREAD_POOL_SIZE=20

# 超时配置
AI_REQUEST_TIMEOUT=300
DATABASE_TIMEOUT=30
WEBSOCKET_TIMEOUT=300

# ==================== 安全配置 ====================
# 速率限制
RATE_LIMIT_REQUESTS_PER_MINUTE=100
RATE_LIMIT_BURST_SIZE=20

# 内容过滤
CONTENT_FILTERING_ENABLED=true
SECURITY_ENABLED=true

# ==================== 监控配置 ====================
# 健康检查
HEALTH_CHECK_INTERVAL=60
PERFORMANCE_MONITORING=true

# 日志配置
LOG_ROTATION=true
LOG_MAX_SIZE=100MB
LOG_BACKUP_COUNT=10

# ==================== 备份配置 ====================
# 自动备份
BACKUP_ENABLED=true
BACKUP_INTERVAL=3600
BACKUP_RETENTION_DAYS=30

# ==================== 服务用户配置 ====================
SERVICE_USER=yanran
SERVICE_GROUP=yanran

# ==================== 其他配置 ====================
# 时区设置
TZ=Asia/Shanghai

# 语言设置
LANG=zh_CN.UTF-8
LC_ALL=zh_CN.UTF-8