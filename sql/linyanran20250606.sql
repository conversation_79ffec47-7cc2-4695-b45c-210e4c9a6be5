-- MySQL dump 10.13  Distrib 8.0.42, for macos15 (x86_64)
--
-- Host: 127.0.0.1    Database: linyanran
-- ------------------------------------------------------
-- Server version	5.7.40-log

/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!50503 SET NAMES utf8 */;
/*!40103 SET @OLD_TIME_Z ONE=@@TIME_ZONE */;
/*!40103 SET TIME_ZONE='+00:00' */;
/*!40014 SET @OLD_UNIQUE_CHECKS=@@UNIQUE_CHECKS, UNIQUE_CHECKS=0 */;
/*!40014 SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0 */;
/*!40101 SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO' */;
/*!40111 SET @OLD_SQL_NOTES=@@SQL_NOTES, SQL_NOTES=0 */;


--
-- Table structure for table `authenticity_system`
--

DROP TABLE IF EXISTS `authenticity_system`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `authenticity_system` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `behavioral_variations` json DEFAULT NULL COMMENT '行为变异',
  `personality_bias` json DEFAULT NULL COMMENT '性格偏差',
  `habit_patterns` json DEFAULT NULL COMMENT '习惯模式',
  `response_variations` json DEFAULT NULL COMMENT '响应变化',
  `mood_fluctuations` json DEFAULT NULL COMMENT '情绪波动',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `user_id` (`user_id`),
  CONSTRAINT `authenticity_system_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `block_history`
--

DROP TABLE IF EXISTS `block_history`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `block_history` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `user_id` varchar(64) NOT NULL COMMENT '用户ID',
  `block_type` tinyint(4) NOT NULL COMMENT '封禁类型：1-自动封禁，2-手动封禁',
  `block_reason` varchar(255) DEFAULT NULL COMMENT '封禁原因',
  `disgust_level` int(11) NOT NULL COMMENT '触发封禁时的厌恶度',
  `block_start_time` datetime NOT NULL COMMENT '封禁开始时间',
  `block_end_time` datetime DEFAULT NULL COMMENT '实际解封时间',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_block_time` (`block_start_time`,`block_end_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='封禁历史记录表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `context_awareness`
--

DROP TABLE IF EXISTS `context_awareness`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `context_awareness` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `context_type` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '情境类型',
  `context_data` json DEFAULT NULL COMMENT '情境数据',
  `environment_factors` json DEFAULT NULL COMMENT '环境因素',
  `user_state` json DEFAULT NULL COMMENT '用户状态',
  `device_info` json DEFAULT NULL COMMENT '设备信息',
  `timestamp` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `user_id` (`user_id`),
  CONSTRAINT `context_awareness_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `disgust_levels`
--

DROP TABLE IF EXISTS `disgust_levels`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `disgust_levels` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '反感度ID',
  `user_id` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '用户ID',
  `level` int(11) NOT NULL COMMENT '反感度',
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `user_id` (`user_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='反感度表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `email_records`
--

DROP TABLE IF EXISTS `email_records`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `email_records` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `sender_email` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '发件人邮箱',
  `sender_name` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '发件人显示名称',
  `subject` varchar(500) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '邮件主题',
  `content` text COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '邮件正文',
  `receive_time` datetime NOT NULL COMMENT '接收时间',
  `email_date` datetime NOT NULL COMMENT '邮件发送时间',
  `message_id` varchar(200) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '邮件唯一ID',
  `content_type` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '邮件内容类型(plain/html)',
  `has_attachment` tinyint(1) DEFAULT '0' COMMENT '是否包含附件(0:否 1:是)',
  `status` tinyint(1) DEFAULT '1' COMMENT '处理状态(0:失败 1:成功)',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `retry_count` int(11) DEFAULT '0' COMMENT '重试次数',
  `error_message` text COLLATE utf8mb4_unicode_ci COMMENT '错误信息',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_message_id` (`message_id`),
  KEY `idx_sender_email` (`sender_email`),
  KEY `idx_receive_time` (`receive_time`)
) ENGINE=InnoDB AUTO_INCREMENT=508 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='邮件记录表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `email_sync_status`
--

DROP TABLE IF EXISTS `email_sync_status`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `email_sync_status` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `sender_email` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '发件人邮箱',
  `last_sync_time` datetime NOT NULL COMMENT '最后同步时间',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_sender_email` (`sender_email`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='邮件同步状态表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `emotional_resilience`
--

DROP TABLE IF EXISTS `emotional_resilience`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `emotional_resilience` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `conflict_history` json DEFAULT NULL COMMENT '冲突历史',
  `resolution_patterns` json DEFAULT NULL COMMENT '解决模式',
  `adaptation_ability` json DEFAULT NULL COMMENT '适应能力',
  `recovery_speed` int(11) DEFAULT NULL COMMENT '恢复速度',
  `resilience_score` int(11) DEFAULT NULL COMMENT '韧性得分',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `user_id` (`user_id`),
  CONSTRAINT `emotional_resilience_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `emotions`
--

DROP TABLE IF EXISTS `emotions`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `emotions` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '情感ID',
  `user_id` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '用户ID',
  `emotion` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '情感',
  `intensity` int(11) NOT NULL COMMENT '强度',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `need_reply` varchar(3) COLLATE utf8mb4_unicode_ci DEFAULT 'no',
  PRIMARY KEY (`id`),
  KEY `user_id` (`user_id`),
  CONSTRAINT `emotions_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=111 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='情感表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `friends`
--

DROP TABLE IF EXISTS `friends`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `friends` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `friends_id` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '好友微信号',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=301 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `growth_system`
--

DROP TABLE IF EXISTS `growth_system`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `growth_system` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `ability_scores` json DEFAULT NULL COMMENT '能力值评分',
  `experience_points` int(11) DEFAULT NULL COMMENT '经验值',
  `level_info` json DEFAULT NULL COMMENT '等级信息',
  `achievements` json DEFAULT NULL COMMENT '成就记录',
  `growth_path` json DEFAULT NULL COMMENT '成长路径',
  `milestones` json DEFAULT NULL COMMENT '里程碑',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `user_id` (`user_id`),
  CONSTRAINT `growth_system_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `health`
--

DROP TABLE IF EXISTS `health`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `health` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '健康状态ID',
  `user_id` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '用户ID',
  `status` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '状态',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `user_id` (`user_id`),
  CONSTRAINT `health_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=368 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='健康状态表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `interaction_depth`
--

DROP TABLE IF EXISTS `interaction_depth`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `interaction_depth` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `interaction_level` int(11) DEFAULT NULL COMMENT '互动层次',
  `intimacy_progress` json DEFAULT NULL COMMENT '亲密度进展',
  `topic_depth` json DEFAULT NULL COMMENT '话题深度',
  `emotional_connection` json DEFAULT NULL COMMENT '情感连接',
  `trust_level` int(11) DEFAULT NULL COMMENT '信任等级',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `user_id` (`user_id`),
  CONSTRAINT `interaction_depth_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `life_experience`
--

DROP TABLE IF EXISTS `life_experience`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `life_experience` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `experience_type` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '经历类型',
  `experience_content` json DEFAULT NULL COMMENT '经历内容',
  `impact_level` int(11) DEFAULT NULL COMMENT '影响程度',
  `memory_weight` float DEFAULT NULL COMMENT '记忆权重',
  `wisdom_tags` json DEFAULT NULL COMMENT '智慧标签',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `user_id` (`user_id`),
  CONSTRAINT `life_experience_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `memory_summaries`
--

DROP TABLE IF EXISTS `memory_summaries`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `memory_summaries` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '记录ID',
  `user_id` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '用户ID',
  `summary_type` enum('daily','monthly','quarterly','yearly') COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '总结类型',
  `content` text COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '总结内容',
  `timestamp` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '时间戳',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=181354 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `memory_weights`
--

DROP TABLE IF EXISTS `memory_weights`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `memory_weights` (
  `id` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '权重ID',
  `message_id` int(11) NOT NULL COMMENT '消息ID',
  `weight` int(11) NOT NULL COMMENT '权重',
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `message_id` (`message_id`),
  CONSTRAINT `memory_weights_ibfk_1` FOREIGN KEY (`message_id`) REFERENCES `messages_old` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='记忆权重表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `messages`
--

DROP TABLE IF EXISTS `messages`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `messages` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '消息ID',
  `user_id` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '用户ID',
  `role` enum('user','assistant') COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '角色',
  `content` text COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '消息内容',
  `timestamp` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '时间戳',
  `embedded` int(11) DEFAULT '0' COMMENT '是否已向量化',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=40450 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;


--
-- Table structure for table `personalities`
--

DROP TABLE IF EXISTS `personalities`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `personalities` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '个性特征ID',
  `user_id` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '用户ID',
  `trait` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '个性特征',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `level` int(11) NOT NULL COMMENT '特征强度',
  PRIMARY KEY (`id`),
  KEY `user_id` (`user_id`),
  CONSTRAINT `personalities_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='个性特征表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `personality_system`
--

DROP TABLE IF EXISTS `personality_system`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `personality_system` (
  `user_id` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '用户的唯一标识符，通常是一个字符串类型的ID',
  `personality_data` json NOT NULL COMMENT '存储用户的个性数据，包括特征、行为模式、发展历史等，格式为JSON',
  `created_at` datetime NOT NULL COMMENT '记录创建时间，通常用于记录用户个性系统的初始化时间',
  `updated_at` datetime DEFAULT NULL COMMENT '记录更新时间，通常用于记录用户个性数据的最后更新时间',
  PRIMARY KEY (`user_id`) COMMENT '主键，确保每个用户的个性系统记录唯一'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='存储用户个性系统数据的表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `relationships`
--

DROP TABLE IF EXISTS `relationships`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `relationships` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '记录ID',
  `user_id` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '用户ID',
  `friend_user_id` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '好友用户ID',
  `friendliness` int(11) NOT NULL COMMENT '友好度',
  `trust` int(11) NOT NULL COMMENT '信任度',
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `user_id` (`user_id`),
  KEY `friend_user_id` (`friend_user_id`),
  CONSTRAINT `relationships_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`),
  CONSTRAINT `relationships_ibfk_2` FOREIGN KEY (`friend_user_id`) REFERENCES `users` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='关系表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `schedules`
--

DROP TABLE IF EXISTS `schedules`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `schedules` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '记录ID',
  `user_id` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '用户ID',
  `activity` text COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '活动',
  `start_time` time NOT NULL COMMENT '开始时间',
  `end_time` time NOT NULL COMMENT '结束时间',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `user_id` (`user_id`),
  CONSTRAINT `schedules_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='日程表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `scripts`
--

DROP TABLE IF EXISTS `scripts`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `scripts` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '剧本ID',
  `role` varchar(4000) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '角色',
  `time_slot` enum('morning','afternoon','evening') COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '时间段',
  `weather` varchar(4000) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '天气',
  `activity` text COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '活动',
  `mood` text COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '心情',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=6795 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='剧本表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `self_awareness`
--

DROP TABLE IF EXISTS `self_awareness`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `self_awareness` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `self_model` json DEFAULT NULL COMMENT '自我模型',
  `capability_boundaries` json DEFAULT NULL COMMENT '能力边界',
  `personality_traits` json DEFAULT NULL COMMENT '性格特征',
  `behavioral_patterns` json DEFAULT NULL COMMENT '行为模式',
  `self_evaluation` json DEFAULT NULL COMMENT '自我评估',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `user_id` (`user_id`),
  CONSTRAINT `self_awareness_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `t_calendar`
--

DROP TABLE IF EXISTS `t_calendar`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `t_calendar` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `date_for_date` date NOT NULL COMMENT '公历日期',
  `lunar_info` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '农历信息',
  `day_of_week` varchar(10) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '星期',
  `festivals` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '节日',
  `ri_left` text COLLATE utf8mb4_unicode_ci COMMENT '宜',
  `ri_right` text COLLATE utf8mb4_unicode_ci COMMENT '忌',
  `jia` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '休假标识',
  `remark` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=732 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='日历信息表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `temp_messages`
--

DROP TABLE IF EXISTS `temp_messages`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `temp_messages` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '记录ID',
  `user_id` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '用户ID',
  `temp_message` text COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '临时消息',
  `message_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '消息发送时间',
  `reply_delay` float NOT NULL COMMENT '间隔多久后回复',
  `isgroup` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否群消息 ',
  `session_id` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '群ID',
  `from_user` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '发送者',
  `_token` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT 'App_token',
  `_appid` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT 'App_appid',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=137 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `timer_schedule`
--

DROP TABLE IF EXISTS `timer_schedule`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `timer_schedule` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `timer_type` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '定时器类型',
  `description` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '定时器描述',
  `time_str` varchar(5) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '定时时间字符串',
  PRIMARY KEY (`id`),
  UNIQUE KEY `timer_type` (`timer_type`)
) ENGINE=InnoDB AUTO_INCREMENT=4 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='定时器时间表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `url_summaries`
--

DROP TABLE IF EXISTS `url_summaries`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `url_summaries` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `url` varchar(2048) NOT NULL,
  `content` mediumtext,
  `summary` text,
  `img_url` text,
  `created_at` datetime NOT NULL,
  `updated_at` datetime NOT NULL,
  `status` tinyint(4) NOT NULL DEFAULT '0',
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_url` (`url`(255)),
  KEY `idx_created_at` (`created_at`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB AUTO_INCREMENT=6428 DEFAULT CHARSET=utf8mb4;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `user_block_status`
--

DROP TABLE IF EXISTS `user_block_status`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `user_block_status` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `user_id` varchar(64) NOT NULL COMMENT '用户ID',
  `is_blocked` tinyint(1) NOT NULL DEFAULT '0' COMMENT '封禁状态：0-未封禁，1-已封禁',
  `block_start_time` datetime DEFAULT NULL COMMENT '封禁开始时间',
  `expected_unblock_time` datetime DEFAULT NULL COMMENT '预计解封时间',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_user_id` (`user_id`),
  KEY `idx_block_status` (`is_blocked`,`expected_unblock_time`)
) ENGINE=InnoDB AUTO_INCREMENT=27 DEFAULT CHARSET=utf8mb4 COMMENT='用户封禁状态表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `user_id_sendmail`
--

DROP TABLE IF EXISTS `user_id_sendmail`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `user_id_sendmail` (
  `id` int(11) NOT NULL,
  `user_id` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='emai发送表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `user_morning_news`
--

DROP TABLE IF EXISTS `user_morning_news`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `user_morning_news` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '早报用户ID',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=5 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='早报用户';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `user_profiles`
--

DROP TABLE IF EXISTS `user_profiles`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `user_profiles` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `basic_info` json DEFAULT NULL COMMENT '基础画像信息',
  `personality_traits` json DEFAULT NULL COMMENT '性格特征',
  `interaction_preferences` json DEFAULT NULL COMMENT '互动偏好',
  `emotional_traits` json DEFAULT NULL COMMENT '情感特征',
  `value_orientation` json DEFAULT NULL COMMENT '价值观取向',
  `social_style` json DEFAULT NULL COMMENT '社交风格',
  `learning_style` json DEFAULT NULL COMMENT '学习方式',
  `life_status` json DEFAULT NULL COMMENT '生活状态',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `user_id` (`user_id`),
  CONSTRAINT `user_profiles_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `users`
--

DROP TABLE IF EXISTS `users`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `users` (
  `id` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '用户ID',
  `name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '用户名',
  `first_chat_time` timestamp NULL DEFAULT NULL COMMENT '首次聊天时间',
  `last_chat_time` timestamp NULL DEFAULT NULL COMMENT '最后一次聊天时间',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `passwd` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `phonenumber` varchar(20) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `api_key` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `value_system`
--

DROP TABLE IF EXISTS `value_system`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `value_system` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `core_values` json DEFAULT NULL COMMENT '核心价值观',
  `moral_standards` json DEFAULT NULL COMMENT '道德标准',
  `belief_system` json DEFAULT NULL COMMENT '信念体系',
  `decision_weights` json DEFAULT NULL COMMENT '决策权重',
  `value_conflicts` json DEFAULT NULL COMMENT '价值观冲突记录',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `user_id` (`user_id`),
  CONSTRAINT `value_system_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `weather`
--

DROP TABLE IF EXISTS `weather`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `weather` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '天气记录ID',
  `city_id` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '城市ID',
  `city_name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '城市名称',
  `temperature` varchar(10) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '温度',
  `humidity` varchar(10) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '湿度',
  `wind_direction` varchar(10) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '风向',
  `wind_power` varchar(10) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '风力',
  `pm25` varchar(10) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT 'PM2.5',
  `visibility` varchar(10) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '能见度',
  `rainfall` varchar(10) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '降雨量',
  `pressure` varchar(10) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '气压',
  `update_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=7157 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='天气数据表';
/*!40101 SET character_set_client = @saved_cs_client */;
/*!40103 SET TIME_ZONE=@OLD_TIME_ZONE */;

/*!40101 SET SQL_MODE=@OLD_SQL_MODE */;
/*!40014 SET FOREIGN_KEY_CHECKS=@OLD_FOREIGN_KEY_CHECKS */;
/*!40014 SET UNIQUE_CHECKS=@OLD_UNIQUE_CHECKS */;
/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
/*!40111 SET SQL_NOTES=@OLD_SQL_NOTES */;

-- Dump completed on 2025-06-06 17:46:11
