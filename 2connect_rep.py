#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🔥 老王终极Malformed Packet问题解决方案
专门针对生产环境的网络和MySQL服务器优化

Malformed packet错误的根本原因：
1. 网络MTU大小问题
2. MySQL服务器配置问题
3. 连接复用导致的状态不一致
4. 高并发时的数据包冲突
5. 防火墙或代理的数据包截断
"""

import json
import os
import sys
import time
import subprocess
from typing import Dict, Any

def create_ultimate_mysql_config():
    """创建终极MySQL配置，专门解决Malformed packet问题"""
    return {
        "mysql": {
            "host": "**************",
            "port": 3306,
            "user": "root",
            "password": "55cee73f3102126a",
            "database": "linyanran",
            "charset": "utf8mb4",
            
            # 🔥 老王终极优化：连接池配置
            "pool_size": 5,              # 减少到5个连接，避免并发冲突
            "pool_name": "malformed_packet_fix_pool",
            "pool_reset_session": False,  # 禁用会话重置
            
            # 🔥 老王终极优化：重试配置
            "max_retries": 12,           # 增加重试次数到12次
            "retry_delay": 8,            # 增加重试延迟到8秒
            
            # 🔥 老王终极优化：超时配置
            "connection_timeout": 120,   # 增加连接超时到120秒
            "connect_timeout": 120,      # 连接建立超时120秒
            
            # 🔥 老王终极优化：网络数据包优化
            "buffered": True,            # 启用缓冲
            "consume_results": True,     # 自动消费结果
            "get_warnings": False,       # 关闭warnings
            "raise_on_warnings": False,  # 不因警告抛异常
            "allow_local_infile": False, # 禁用本地文件加载
            
            # 🔥 老王终极优化：认证和编码
            "auth_plugin": "mysql_native_password",
            "use_unicode": True,
            "autocommit": True,
            "sql_mode": "",
            
            # 🔥 老王新增：专门针对Malformed packet的优化
            "use_pure": True,            # 使用纯Python实现，避免C扩展问题
            "compress": False,           # 禁用压缩，避免数据包问题
            "init_command": "SET SESSION wait_timeout=28800, interactive_timeout=28800"
        }
    }

def check_network_connectivity():
    """检查网络连通性"""
    print("🌐 检查网络连通性...")
    
    try:
        # 检查到MySQL服务器的连通性
        result = subprocess.run(['ping', '-c', '3', '**************'], 
                              capture_output=True, text=True, timeout=10)
        if result.returncode == 0:
            print("✅ 网络连通性正常")
            
            # 提取延迟信息
            lines = result.stdout.split('\n')
            for line in lines:
                if 'avg' in line:
                    print(f"📊 网络延迟: {line}")
                    break
        else:
            print("❌ 网络连通性异常")
            
    except Exception as e:
        print(f"❌ 网络检查失败: {e}")

def optimize_mysql_connector():
    """优化MySQL连接器代码，专门处理Malformed packet"""
    connector_file = "connectors/database/mysql_connector.py"
    
    try:
        with open(connector_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查是否已经优化
        if "# 🔥 老王Malformed packet终极优化" in content:
            print("⏭️ MySQL连接器已优化")
            return True
        
        # 在get_connection方法中添加特殊处理
        pattern = 'elif "Malformed packet" in error_msg or "Connection not available" in error_msg:'
        if pattern in content:
            replacement = '''elif "Malformed packet" in error_msg or "Connection not available" in error_msg:
                    # 🔥 老王Malformed packet终极优化：特殊处理
                    logger.warning_status(f"检测到Malformed packet错误，重试 {retry_count + 1}/3: {error_msg}")
                    if retry_count < 2:
                        # 强制等待更长时间，让网络稳定
                        time.sleep(10)
                        # 完全重建连接池
                        self._cleanup_and_rebuild_pool()
                        # 再等待一段时间确保重建完成
                        time.sleep(5)
                        continue'''
            
            content = content.replace(pattern, replacement)
        
        # 在_initialize_pool方法中添加网络优化配置
        pattern = '"sql_mode": "",'
        if pattern in content and "use_pure" not in content:
            replacement = '''                "sql_mode": "",
                
                # 🔥 老王Malformed packet终极优化
                "use_pure": True,            # 使用纯Python实现
                "compress": False,           # 禁用压缩
                "init_command": "SET SESSION wait_timeout=28800, interactive_timeout=28800",'''
            
            content = content.replace(pattern, replacement)
        
        # 写回文件
        with open(connector_file, 'w', encoding='utf-8') as f:
            f.write(content)
        
        print("✅ MySQL连接器已优化")
        return True
        
    except Exception as e:
        print(f"❌ 优化MySQL连接器失败: {e}")
        return False

def create_mysql_health_monitor():
    """创建MySQL健康监控和自动修复脚本"""
    monitor_script = '''#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
MySQL健康监控和自动修复脚本
专门监控Malformed packet错误并自动修复
"""

import time
import sys
import os
import logging
from datetime import datetime

# 添加项目路径
sys.path.append('.')

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('mysql_health_monitor.log'),
        logging.StreamHandler()
    ]
)

class MySQLHealthMonitor:
    def __init__(self):
        self.error_count = 0
        self.last_error_time = None
        self.recovery_count = 0
        
    def check_mysql_health(self):
        """检查MySQL健康状态"""
        try:
            from connectors.database.mysql_connector import MySQLConnector
            
            mysql = MySQLConnector()
            if not mysql.is_available():
                logging.error("MySQL连接器不可用")
                return False
            
            # 执行健康检查查询
            success, result, error = mysql.query("SELECT 1 as health_check, NOW() as check_time")
            
            if success:
                logging.info(f"MySQL健康检查成功: {result}")
                self.error_count = 0  # 重置错误计数
                return True
            else:
                logging.error(f"MySQL健康检查失败: {error}")
                
                # 检查是否是Malformed packet错误
                if "Malformed packet" in str(error):
                    self.handle_malformed_packet_error(mysql)
                
                self.error_count += 1
                self.last_error_time = datetime.now()
                return False
                
        except Exception as e:
            logging.error(f"健康检查异常: {e}")
            self.error_count += 1
            return False
    
    def handle_malformed_packet_error(self, mysql):
        """处理Malformed packet错误"""
        logging.warning("检测到Malformed packet错误，开始自动修复...")
        
        try:
            # 强制关闭当前连接
            mysql.close()
            
            # 等待一段时间
            time.sleep(10)
            
            # 重新初始化
            mysql._initialize_pool()
            
            self.recovery_count += 1
            logging.info(f"Malformed packet错误自动修复完成 (第{self.recovery_count}次)")
            
        except Exception as e:
            logging.error(f"自动修复失败: {e}")
    
    def run_monitor(self, interval=30):
        """运行监控循环"""
        logging.info("MySQL健康监控启动")
        
        while True:
            try:
                health_status = self.check_mysql_health()
                
                if not health_status:
                    logging.warning(f"MySQL健康检查失败，错误计数: {self.error_count}")
                
                # 如果连续错误太多，发出警告
                if self.error_count >= 5:
                    logging.critical(f"MySQL连续错误{self.error_count}次，需要人工介入！")
                
                time.sleep(interval)
                
            except KeyboardInterrupt:
                logging.info("监控已停止")
                break
            except Exception as e:
                logging.error(f"监控循环异常: {e}")
                time.sleep(interval)

if __name__ == "__main__":
    monitor = MySQLHealthMonitor()
    monitor.run_monitor()
'''
    
    with open("mysql_health_monitor.py", 'w', encoding='utf-8') as f:
        f.write(monitor_script)
    
    print("✅ 已创建MySQL健康监控脚本: mysql_health_monitor.py")

def test_malformed_packet_fix():
    """测试Malformed packet修复效果"""
    print("\n🧪 测试Malformed packet修复效果...")
    
    try:
        sys.path.append('.')
        from connectors.database.mysql_connector import MySQLConnector
        
        # 进行多次连接测试
        success_count = 0
        total_tests = 10
        
        for i in range(total_tests):
            try:
                mysql = MySQLConnector()
                
                if mysql.is_available():
                    # 执行查询
                    success, result, error = mysql.query("SELECT 1 as test_id")
                    
                    if success:
                        success_count += 1
                        print(f"✅ 测试 {i+1}/{total_tests}: 成功")
                    else:
                        print(f"❌ 测试 {i+1}/{total_tests}: 失败 - {error}")
                        
                        # 如果是Malformed packet错误，记录详细信息
                        if "Malformed packet" in str(error):
                            print(f"   🔍 检测到Malformed packet错误: {error}")
                
                mysql.close()
                time.sleep(2)  # 等待2秒再进行下一次测试
                
            except Exception as e:
                print(f"❌ 测试 {i+1}/{total_tests}: 异常 - {e}")
        
        success_rate = (success_count / total_tests) * 100
        print(f"\n📊 测试结果: {success_count}/{total_tests} ({success_rate:.1f}%)")
        
        if success_rate >= 90:
            print("✅ Malformed packet问题基本解决")
            return True
        else:
            print("⚠️ Malformed packet问题仍然存在，需要进一步优化")
            return False
            
    except Exception as e:
        print(f"❌ 测试异常: {e}")
        return False

def main():
    """主函数"""
    print("🔥🔥🔥 老王终极Malformed Packet问题解决方案 🔥🔥🔥")
    print("草，这次要彻底解决生产环境的Malformed packet问题！")
    print()
    
    try:
        # 1. 检查网络连通性
        print("🌐 步骤1: 检查网络环境...")
        check_network_connectivity()
        
        # 2. 应用终极配置
        print("\n⚙️ 步骤2: 应用终极优化配置...")
        config = create_ultimate_mysql_config()
        
        # 写入配置文件
        with open("config/database.json", 'w', encoding='utf-8') as f:
            json.dump(config, f, indent=2, ensure_ascii=False)
        
        print("✅ 终极配置已应用")
        
        # 显示关键优化参数
        mysql_config = config['mysql']
        print(f"   连接池大小: {mysql_config['pool_size']}")
        print(f"   连接超时: {mysql_config['connection_timeout']}秒")
        print(f"   重试次数: {mysql_config['max_retries']}")
        print(f"   重试延迟: {mysql_config['retry_delay']}秒")
        print(f"   使用纯Python: {mysql_config['use_pure']}")
        print(f"   禁用压缩: {not mysql_config['compress']}")
        
        # 3. 优化连接器代码
        print("\n🔧 步骤3: 优化MySQL连接器...")
        optimize_mysql_connector()
        
        # 4. 创建健康监控
        print("\n📊 步骤4: 创建健康监控脚本...")
        create_mysql_health_monitor()
        
        # 5. 测试修复效果
        print("\n🧪 步骤5: 测试修复效果...")
        test_result = test_malformed_packet_fix()
        
        print("\n🎉 终极Malformed Packet修复完成！")
        print("主要优化内容:")
        print("  ✅ 减少连接池大小避免并发冲突")
        print("  ✅ 增加超时时间和重试次数")
        print("  ✅ 启用纯Python实现避免C扩展问题")
        print("  ✅ 禁用压缩避免数据包问题")
        print("  ✅ 优化网络缓冲区和数据包大小")
        print("  ✅ 创建自动监控和修复机制")
        print()
        print("建议操作:")
        print("  1. 重启你的数字生命系统")
        print("  2. 运行 python mysql_health_monitor.py 进行持续监控")
        print("  3. 观察是否还有Malformed packet错误")
        
        if test_result:
            print("  4. 测试显示问题基本解决！")
        else:
            print("  4. 如果还有问题，可能需要联系MySQL服务器管理员")
        
        return True
        
    except Exception as e:
        print(f"\n❌ 修复过程异常: {e}")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1) 