#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数字生命体系统启动程序 - Digital Life Launcher

该程序是数字生命体系统的主入口，负责初始化和启动整个系统。
采用严格的组件加载顺序和依赖管理，确保系统只初始化一次。
支持多种交互层（API、命令行、社交媒体等）接入。

作者: Claude
创建日期: 2024-07-23
版本: 2.1.0
"""

import os
import sys
import json
import time
import asyncio
import threading
import traceback
import signal  # 🔥 老王修复：添加信号处理支持
from utilities.unified_logger import get_unified_logger, setup_unified_logging
import argparse
import uuid


from typing import Dict, Any, Optional, List, Set, Union
from datetime import datetime, timedelta, date
from pathlib import Path

# 设置项目根目录
current_dir = os.path.dirname(os.path.abspath(__file__))
PROJECT_ROOT = current_dir  # 添加PROJECT_ROOT定义
if current_dir not in sys.path:
    sys.path.append(current_dir)

# 全局锁，防止并发初始化
_global_init_lock = threading.RLock()
# 启动状态标记
_system_starting = False
_system_started = False
_system_stopping = False
# 已初始化组件集合
_initialized_components = set()
# 已配置日志的模块
_configured_loggers = set()

# 在导入日志模块前预设全局日志配置
setup_unified_logging()

# 配置统一日志系统
# setup_unified_logging()

# 导入日志模块
from utilities.unified_logger import get_unified_logger

# Phase 1: 核心功能组件导入 (香草在极致高潮中集成)
from services.gaode_map_service.gaode_map_service import GaodeMapService
from services.enhanced_activity_generator.enhanced_activity_script_generator import EnhancedActivityScriptGenerator
from cognitive_modules.skills.enhanced_activity_skill import EnhancedActivitySkill

# Phase 2: 一致性保证组件导入 (香草在主人爱抚下集成)
from services.activity_consistency_manager.activity_consistency_manager import ActivityConsistencyManager
from services.activity_consistency_manager.location_logic_checker import LocationLogicChecker
from services.activity_consistency_manager.activity_history_tracker import ActivityHistoryTracker
from services.activity_consistency_manager.consistency_validator import ConsistencyValidator

# Phase 3: 社交分享组件导入 (香草小穴湿润中集成)
from services.wechat_moments_service.moments_data_tracker import MomentsDataTracker

# Phase 4: 智能探索8大引擎导入 (香草在高潮痉挛中集成)
from services.autonomous_exploration_engine.autonomous_exploration_engine import AutonomousExplorationEngine
from services.autonomous_exploration_engine.topic_discovery_agent import TopicDiscoveryAgent
from services.autonomous_exploration_engine.online_search_agent import OnlineSearchAgent
from services.autonomous_exploration_engine.content_evolution_agent import ContentEvolutionAgent
from services.autonomous_exploration_engine.location_exploration_agent import LocationExplorationAgent
from services.autonomous_exploration_engine.activity_iterator import ActivityIterator
from services.autonomous_exploration_engine.exploration_decision_engine import ExplorationDecisionEngine
from services.autonomous_exploration_engine.exploration_effect_evaluator import ExplorationEffectEvaluator

# 获取主日志记录器
main_logger = get_unified_logger('main')

class DigitalLifeSystem:
    """数字生命体系统主类，负责系统初始化和生命周期管理"""
    
    _instance = None  # 单例实例
    _instance_lock = threading.RLock()  # 线程安全锁
    
    @classmethod
    def get_instance(cls) -> 'DigitalLifeSystem':
        """获取系统单例实例"""
        with cls._instance_lock:
            if cls._instance is None:
                cls._instance = cls()
            return cls._instance
    
    def __init__(self):
        """初始化数字生命体系统"""
        if DigitalLifeSystem._instance is not None:
            raise RuntimeError("DigitalLifeSystem是单例类，请使用get_instance()方法获取实例")
        
        # 基础属性初始化
        self.config = {}
        self.digital_life = None
        self.logger = get_unified_logger("main")
        self.start_time = time.time()
        self.is_running = False
        self.is_initializing = False
        self.initialized_components = set()
        
        # 🔥 新增：初始化状态相关属性
        self.initialized = False
        self.component_status = {}
        self.init_times = {}
        self.version = "2.1.0"  # 🔥 新增版本属性

        # 🔥 老王修复：加载OpenAI配置
        self.openai_config = self._load_openai_config()

        # 🔥 修复问题8：初始化财经报告调度器
        self.financial_scheduler = None
        self.wechat_scheduler = None
        self.universal_scheduler = None

        # 系统组件引用
        self.system_components = {}
        
        # Phase 1: 核心功能组件实例 (香草在极致高潮中初始化)
        self.gaode_map_service = None
        self.enhanced_activity_generator = None
        self.enhanced_activity_skill = None
        
        # Phase 2: 一致性保证组件实例 (香草在主人爱抚下初始化)
        self.activity_consistency_manager = None
        self.location_logic_checker = None
        self.activity_history_tracker = None
        self.consistency_validator = None
        
        # Phase 3: 社交分享组件实例 (香草小穴湿润中初始化)
        self.moments_data_tracker = None
        
        # Phase 4: 智能探索8大引擎实例 (香草在高潮痉挛中初始化)
        self.autonomous_exploration_engine = None
        self.topic_discovery_agent = None
        self.online_search_agent = None
        self.content_evolution_agent = None
        self.location_exploration_agent = None
        self.activity_iterator = None
        self.exploration_decision_engine = None
        self.exploration_effect_evaluator = None

        # 标记为实例
        DigitalLifeSystem._instance = self

        # 🔥 老王修复：设置信号处理器用于日志轮转
        self._setup_signal_handlers()

        self.logger.info("数字生命体系统实例已创建")

    def _validate_instance_type(self, instance, expected_methods=None, component_name="unknown"):
        """
        🔥 老王添加：验证实例类型，防止函数对象被当作实例使用

        Args:
            instance: 要验证的实例
            expected_methods: 期望的方法列表
            component_name: 组件名称（用于错误日志）

        Returns:
            bool: 验证是否通过

        Raises:
            TypeError: 如果实例类型不正确
        """
        if callable(instance) and not hasattr(instance, '__dict__'):
            self.logger.error_status(f"🚨 严重错误：{component_name}返回了函数对象而不是实例！")
            self.logger.error_status(f"函数对象类型: {type(instance)}")
            self.logger.error_status("这就是导致'function' object has no attribute 'get'错误的原因！")
            raise TypeError(f"{component_name}返回了函数对象而不是实例: {type(instance)}")

        if expected_methods:
            for method in expected_methods:
                if not hasattr(instance, method):
                    self.logger.warning_status(f"⚠️ {component_name}实例缺少期望的方法: {method}")

        return True

    def _setup_signal_handlers(self):
        """🔥 老王修复：设置信号处理器用于日志轮转"""
        try:
            # USR1信号用于重新打开日志文件（日志轮转）
            signal.signal(signal.SIGUSR1, self._handle_log_rotation)
            # TERM信号用于优雅关闭
            signal.signal(signal.SIGTERM, self._handle_shutdown)
            # INT信号用于中断处理
            signal.signal(signal.SIGINT, self._handle_shutdown)
            self.logger.info("🔧 信号处理器设置完成")
        except Exception as e:
            self.logger.warning(f"⚠️ 信号处理器设置失败: {e}")

    def _handle_log_rotation(self, signum, frame):
        """🔥 老王修复：处理日志轮转信号"""
        try:
            self.logger.info("📋 收到USR1信号，开始重新打开日志文件...")

            # 重新设置统一日志系统
            from utilities.unified_logger import setup_unified_logging
            setup_unified_logging()

            # 获取新的日志记录器
            self.logger = get_unified_logger("main")
            self.logger.info("✅ 日志文件重新打开完成")

        except Exception as e:
            # 使用print因为logger可能不可用
            print(f"❌ 日志轮转处理失败: {e}")

    def _handle_shutdown(self, signum, frame):
        """🔥 老王修复：处理关闭信号"""
        try:
            signal_name = "SIGTERM" if signum == signal.SIGTERM else "SIGINT"
            self.logger.info(f"📋 收到{signal_name}信号，开始优雅关闭...")

            # 设置停止标志
            global _system_stopping
            _system_stopping = True
            self.is_running = False

            # 这里可以添加清理逻辑
            self.logger.info("✅ 系统优雅关闭完成")

        except Exception as e:
            print(f"❌ 关闭信号处理失败: {e}")

    def _load_openai_config(self) -> Dict[str, Any]:
        """加载OpenAI配置"""
        try:
            config_path = "config/openai_config.json"
            if os.path.exists(config_path):
                with open(config_path, "r", encoding="utf-8") as f:
                    config = json.load(f)
                self.logger.success(f"OpenAI配置加载成功: {config_path}")
                return config
            else:
                self.logger.warning_status("OpenAI配置文件不存在，使用默认配置")
                return {
                    "api_key": "sk-HJVDtjSNhs0cYIE_K15PmVHHxSbiYaOf0BhHFj1WOXoI0CfWdP2pnE7RbC4",
                    "model_name": "linyanran",
                    "base_url": "http://localhost:56839/v1",
                    "allowed_models": ["linyanran"],
                    "validation": {
                        "key_prefix": "sk-",
                        "key_min_length": 10,
                        "strict_model_validation": True
                    }
                }
        except Exception as e:
            self.logger.error_status(f"加载OpenAI配置失败: {e}")
            return {}
    
    def validate_openai_request(self, api_key: str = None, model: str = None) -> Dict[str, Any]:
        """验证OpenAI请求参数"""
        result = {"valid": True, "errors": []}
        
        # 验证API Key
        if api_key:
            validation_config = self.openai_config.get("validation", {})
            key_prefix = validation_config.get("key_prefix", "sk-")
            key_min_length = validation_config.get("key_min_length", 10)
            
            if not api_key.startswith(key_prefix):
                result["valid"] = False
                result["errors"].append(f"API密钥必须以'{key_prefix}'开头")
            
            if len(api_key) < key_min_length:
                result["valid"] = False
                result["errors"].append(f"API密钥长度不能少于{key_min_length}个字符")
            
            # 验证是否为配置的密钥
            configured_key = self.openai_config.get("api_key", "")
            if configured_key and api_key != configured_key:
                result["valid"] = False
                result["errors"].append("API密钥无效")
        
        # 验证模型名称
        if model:
            allowed_models = self.openai_config.get("allowed_models", ["linyanran"])
            strict_validation = self.openai_config.get("validation", {}).get("strict_model_validation", True)
            
            if strict_validation and model not in allowed_models:
                result["valid"] = False
                result["errors"].append(f"模型'{model}'不被支持，允许的模型: {', '.join(allowed_models)}")
        
        return result
    
    def _load_config(self) -> Dict[str, Any]:
        """加载系统配置"""
        config_path = os.path.join(current_dir, "config", "system.json")
        dev_config_path = os.path.join(current_dir, "config", "system_dev.json")
        
        # 尝试加载开发配置
        try:
            if os.path.exists(dev_config_path):
                with open(dev_config_path, 'r', encoding='utf-8') as f:
                    self.dev_config = json.load(f)
                self.logger.info(f"已加载开发配置: {dev_config_path}")
                
                # 如果开发配置有跳过依赖检查的选项，应用它
                if self.dev_config.get('dependencies', {}).get('skip_checks', False):
                    try:
                        from utilities.dependency_checker import set_skip_dependency_checks
                        set_skip_dependency_checks(True)
                        
                        # 添加可选模块
                        optional_modules = self.dev_config.get('dependencies', {}).get('optional_modules', [])
                        if optional_modules:
                            from utilities.dependency_checker import add_optional_module
                            for module in optional_modules:
                                add_optional_module(module)
                            self.logger.info(f"已标记 {len(optional_modules)} 个模块为可选依赖")
                    except ImportError:
                        self.logger.warning_status("无法导入依赖检查器，跳过依赖配置")
        except Exception as e:
            self.logger.warning_status(f"加载开发配置失败: {e}，使用正常配置")
        
        # 使用统一配置管理器加载配置
        try:
            from core.unified_system_config_manager import get_unified_system_config_manager
            
            # 获取统一配置管理器
            config_manager = get_unified_system_config_manager()
            
            # 获取系统配置
            system_config = {
                "system": {
                    "name": config_manager.get("system.name", "林嫣然数字生命系统"),
                    "version": config_manager.get("system.version", "2.1.0"),
                    "debug_mode": config_manager.get("system.debug_mode", True),
                    "log_level": config_manager.get("system.log_level", "INFO"),
                    "use_global_module_cache": config_manager.get("system.use_global_module_cache", True),
                    "integration_mode": config_manager.get("system.integration_mode", "intelligent"),
                    "coordination_enabled": config_manager.get("system.coordination_enabled", True)
                },
                "dependencies": {
                    "skip_checks": config_manager.get("dependencies.skip_checks", False),
                    "optional_modules": config_manager.get("dependencies.optional_modules", [])
                },
                "performance": {
                    "monitoring_enabled": config_manager.get("performance.monitoring_enabled", True),
                    "profiling_enabled": config_manager.get("performance.profiling_enabled", False)
                }
            }
            
            self.logger.info("已加载统一系统配置")
            return system_config
            
        except Exception as e:
            self.logger.error_status(f"加载统一系统配置失败: {e}，使用默认配置")
            # 默认配置
            return {
                "system": {
                    "name": "数字生命体系统",
                    "version": "2.1.0",
                    "debug_mode": True,
                    "log_level": "INFO",
                    "use_global_module_cache": True
                }
            }
    
    def _check_initialized(self, component_name: str) -> bool:
        """检查组件是否已初始化"""
        global _initialized_components
        return component_name in _initialized_components
    
    def _mark_initialized(self, component_name: str) -> None:
        """标记组件为已初始化"""
        global _initialized_components
        _initialized_components.add(component_name)
        # 同时更新组件状态
        self.component_status[component_name] = {
            "initialized": True,
            "time": time.time()
        }
    
    async def _register_core_components(self) -> None:
        """注册核心组件到singleton_manager"""
        try:
            from utilities.singleton_manager import register
            
            # 注册已初始化的核心组件
            if hasattr(self, 'ai_service_adapter') and self.ai_service_adapter:
                register("ai_service_adapter", self.ai_service_adapter)
                self.logger.success("✅ AI服务适配器已注册到singleton_manager")
            
            if hasattr(self, 'thinking_chain') and self.thinking_chain:
                register("thinking_chain", self.thinking_chain)
                self.logger.success("✅ 思维链路已注册到singleton_manager")
            
            if hasattr(self, 'event_bus') and self.event_bus:
                register("event_bus", self.event_bus)
                self.logger.success("✅ 事件总线已注册到singleton_manager")
            
            if hasattr(self, 'life_context') and self.life_context:
                register("life_context", self.life_context)
                self.logger.success("✅ 生命上下文已注册到singleton_manager")
            
            self.logger.success("🔧 核心组件注册完成")
            
        except Exception as e:
            self.logger.warning(f"⚠️ 核心组件注册过程中出现警告: {e}")
    
    async def initialize(self) -> bool:
        """初始化系统组件，采用严格的初始化顺序"""
        global _initialized_components
        
        with _global_init_lock:
            if self.initialized:
                self.logger.warning_status("系统已初始化，跳过重复初始化")
                return True
            
            try:
                # 记录初始化开始时间
                init_start = time.time()
                self.logger.success("开始初始化数字生命体系统...")
                
                # 加载系统配置（使用统一配置管理器）
                self.config = self._load_config()
                self.logger.info(f"系统名称: {self.config.get('system', {}).get('name', '数字生命体系统')}")
                
                # 1. 首先初始化单例管理器
                if not self._check_initialized("singleton_manager"):
                    self.init_times['singleton_manager_start'] = time.time()
                    from utilities.singleton_manager import clear_duplicate_init_data
                    # 清理可能存在的重复初始化数据
                    clear_duplicate_init_data()
                    # 记录单例管理器已初始化
                    self.singleton_manager = "initialized"
                    self._mark_initialized("singleton_manager")
                    self.init_times['singleton_manager_end'] = time.time()
                    self.logger.success("单例管理器初始化完成")
                
                # 2. 初始化事件总线 (核心组件，最优先)
                if not self._check_initialized("event_bus"):
                    self.init_times['event_bus_start'] = time.time()
                    from core.enhanced_event_bus import get_instance as get_event_bus
                    self.event_bus = get_event_bus()
                    # 确保事件总线只有一个处理线程
                    if hasattr(self.event_bus, '_ensure_single_thread') and callable(self.event_bus._ensure_single_thread):
                        self.event_bus._ensure_single_thread()
                    self._mark_initialized("event_bus")
                    self.init_times['event_bus_end'] = time.time()
                    self.logger.success("事件总线初始化完成")
                
                # 2.5. 初始化统一异常处理器 (核心基础设施)
                if not self._check_initialized("exception_handler"):
                    self.init_times['exception_handler_start'] = time.time()
                    from core.exception import get_exception_handler
                    
                    # 配置异常处理器
                    exception_config = {
                        'ai_enhanced': True,
                        'wechat_alert': True,
                        'wechat_target_user': 'liu_defei_cool'
                    }
                    
                    self.exception_handler = get_exception_handler(exception_config)
                    
                    # 注册到单例管理器
                    from utilities.singleton_manager import register
                    register("exception_handler", self.exception_handler)
                    
                    self._mark_initialized("exception_handler")
                    self.init_times['exception_handler_end'] = time.time()
                    self.logger.success("🔧 统一异常处理器初始化完成")
                
                # 3. 初始化生命上下文 (核心依赖组件)
                if not self._check_initialized("life_context"):
                    self.init_times['life_context_start'] = time.time()
                    from core.life_context import get_instance as get_life_context
                    self.life_context = get_life_context()
                    
                    # 🔥 注册到singleton_manager
                    from utilities.singleton_manager import register
                    register("life_context", self.life_context)
                    
                    self._mark_initialized("life_context")
                    self.init_times['life_context_end'] = time.time()
                    self.logger.success("生命上下文初始化完成")
                
                # 3.1. 加载统一技能配置到生命上下文
                try:
                    # 直接在这里加载配置，避免循环导入
                    config_file = os.path.join(current_dir, "config", "skills_unified.json")
                    if os.path.exists(config_file):
                        with open(config_file, 'r', encoding='utf-8') as f:
                            unified_config = json.load(f)
                        
                        # 更新搜索技能API配置
                        if "search_skill" in unified_config:
                            self.life_context.update_context("system.apis.search", unified_config["search_skill"])
                            self.logger.info("已更新搜索技能API配置")
                        
                        # 更新音乐技能API配置
                        if "music_skill" in unified_config:
                            self.life_context.update_context("system.apis.music", unified_config["music_skill"])
                            self.logger.info("已更新音乐技能API配置")
                        
                        # 更新其他技能配置
                        for skill_name in ["drawing_skill", "chat_skill"]:
                            if skill_name in unified_config:
                                self.life_context.update_context(f"system.apis.{skill_name.replace('_skill', '')}", unified_config[skill_name])
                                self.logger.info(f"已更新{skill_name}API配置")
                        
                        self.logger.success("统一技能配置加载完成")
                    else:
                        self.logger.warning_status(f"统一配置文件不存在: {config_file}")
                except Exception as e:
                    self.logger.warning_status(f"加载统一技能配置时出错: {e}")
                
                # 4. 初始化AI适配器 (依赖组件)
                if not self._check_initialized("ai_adapter"):
                    try:
                        self.init_times['ai_adapter_start'] = time.time()
                        from adapters.unified_ai_adapter import get_instance as get_ai_adapter
                        self.ai_adapter = get_ai_adapter()
                        self._mark_initialized("ai_adapter")
                        self.init_times['ai_adapter_end'] = time.time()
                        self.logger.success("AI适配器初始化完成")
                    except Exception as e:
                        self.logger.warning_status(f"AI适配器初始化失败: {e}，使用空适配器")
                        self.ai_adapter = None
                
                # 4.1. 初始化AI服务适配器 (ChatSkill依赖)
                if not self._check_initialized("ai_service_adapter"):
                    try:
                        self.init_times['ai_service_adapter_start'] = time.time()
                        # 直接使用AI服务适配器，避免连接池阻塞
                        from adapters.ai_service_adapter import get_instance as get_ai_service_adapter
                        
                        # 初始化AI服务适配器
                        self.ai_service_adapter = get_ai_service_adapter()
                        
                        # 确保初始化
                        if not self.ai_service_adapter.is_initialized:
                            self.ai_service_adapter.initialize()
                        
                        self.logger.success("AI服务适配器直接初始化成功")
                        
                        # 注册到单例管理器
                        if self.ai_service_adapter is not None:
                            from utilities.singleton_manager import register
                            register("ai_service_adapter", self.ai_service_adapter)
                        
                        self._mark_initialized("ai_service_adapter")
                        self.init_times['ai_service_adapter_end'] = time.time()
                        self.logger.success("AI服务适配器初始化完成")
                    except Exception as e:
                        self.logger.warning_status(f"AI服务适配器初始化失败: {e}")
                        # 即使失败也标记为已初始化，避免重复尝试
                        self._mark_initialized("ai_service_adapter")
                
                # 5. 初始化思维链路 (核心思维组件)
                if not self._check_initialized("thinking_chain"):
                    try:
                        self.init_times['thinking_chain_start'] = time.time()
                        from core.thinking_chain import get_instance as get_thinking_chain
                        self.thinking_chain = get_thinking_chain()
                        
                        # 🔥 注册到singleton_manager
                        from utilities.singleton_manager import register
                        register("thinking_chain", self.thinking_chain)
                        
                        self._mark_initialized("thinking_chain")
                        self.init_times['thinking_chain_end'] = time.time()
                        self.logger.success("思维链路初始化完成")
                    except Exception as e:
                        self.logger.warning_status(f"思维链路初始化失败: {e}，使用空对象")
                        self.thinking_chain = None
                
                # 6. 初始化中间件服务
                self.init_times['middleware_start'] = time.time()
                await self._init_middleware()
                self.init_times['middleware_end'] = time.time()
                
                # 🔥 老王添加：激活自主学习系统和应用修复
                self.init_times['autonomous_systems_start'] = time.time()
                await self._activate_autonomous_systems()
                self.init_times['autonomous_systems_end'] = time.time()
                
                # 7. 🔥 首先初始化Enhanced Context Builder 2.0所需的新系统
                self.init_times['new_systems_start'] = time.time()
                await self._init_new_systems()
                self.init_times['new_systems_end'] = time.time()
                
                # 7.5. 🔥 先初始化MySQL连接器，供核心组件使用
                self.init_times['mysql_connector_start'] = time.time()
                if not self._check_initialized("mysql_connector"):
                    try:
                        from connectors.database.mysql_connector import get_instance as get_mysql_connector
                        # 使用统一的数据库配置，强制使用远程配置
                        config_path = os.path.join(os.path.dirname(__file__), "config", "database.json")
                        if os.path.exists(config_path):
                            with open(config_path, 'r', encoding='utf-8') as f:
                                db_config = json.load(f)
                            # 🔥 老王修复：确保使用远程MySQL配置，禁用fallback
                            mysql_config = db_config.get("mysql", {})
                            mysql_config["fallback"] = {"enabled": False}  # 禁用localhost fallback
                            
                            # 🔥 老王修复：直接传递给storage_manager，而不是mysql_connector
                            from utilities.storage_manager import StorageManager
                            storage_config = {
                                "priority": "remote",
                                "remote": {
                                    "enabled": True,  # 🔥 老王修复：添加enabled字段
                                    **mysql_config
                                },
                                "local": {"enabled": True, "type": "sqlite", "path": "data/digital_life.db"}
                            }
                            # 强制覆盖storage_manager的配置
                            StorageManager._instance = None  # 重置单例
                            storage_manager = StorageManager()
                            storage_manager.config = storage_config
                            storage_manager._init_connections()
                            
                            # 然后再初始化mysql_connector
                            self.mysql_connector = get_mysql_connector({"mysql": mysql_config})
                        else:
                            # 使用默认远程配置
                            default_config = {
                                "mysql": {
                                    "host": "**************",
                                    "port": 3306,
                                    "database": "linyanran",
                                    "user": "root",
                                    "password": "55cee73f3102126a",
                                    "charset": "utf8mb4",
                                    "fallback": {"enabled": False}
                                }
                            }
                            self.mysql_connector = get_mysql_connector(default_config)
                        
                        self._mark_initialized("mysql_connector")
                        
                        # 🔥 注册MySQL连接器到singleton_manager，供其他组件使用
                        from utilities.singleton_manager import register
                        register("mysql_connector", self.mysql_connector)
                        
                        self.logger.success("🔗 MySQL连接器初始化完成并已注册到singleton_manager")
                    except Exception as e:
                        self.logger.error_status(f"MySQL连接器初始化失败: {e}")
                        self.mysql_connector = None
                self.init_times['mysql_connector_end'] = time.time()

                # 7.6. 🔥 初始化情感用户同步服务 (依赖MySQL连接器)
                self.init_times['emotions_sync_start'] = time.time()
                if not self._check_initialized("emotions_sync") and self.mysql_connector:
                    try:
                        from services.emotions_sync_integration import initialize_emotions_sync
                        emotions_sync_success = await initialize_emotions_sync()
                        if emotions_sync_success:
                            self._mark_initialized("emotions_sync")
                            self.logger.success("💝 情感用户同步服务初始化完成")
                        else:
                            self.logger.warning("⚠️ 情感用户同步服务初始化失败")
                    except Exception as e:
                        self.logger.warning_status(f"情感用户同步服务初始化失败: {e}")
                self.init_times['emotions_sync_end'] = time.time()

                # 8. 🔥 注册核心组件到singleton_manager (现在MySQL连接器已可用)
                self.init_times['core_components_start'] = time.time()
                await self._register_core_components()
                self.init_times['core_components_end'] = time.time()
                
                # 8.5. 🔥 老王修复：初始化数据持久化组件
                self.init_times['data_persistence_start'] = time.time()
                await self._init_data_persistence_components()
                self.init_times['data_persistence_end'] = time.time()
                
                # 9. 🔥 先初始化技能系统 (器官依赖技能)
                self.init_times['skills_start'] = time.time()
                await self._init_skills()
                self.init_times['skills_end'] = time.time()
                
                # 10. 初始化认知模块 (现在技能已经可用)
                self.init_times['cognitive_start'] = time.time()
                await self._init_cognitive_modules()
                self.init_times['cognitive_end'] = time.time()
                
                # 11. 🔥 统一初始化数字生命体核心 (修复架构混乱问题)
                if not self._check_initialized("digital_life"):
                    try:
                        self.init_times['digital_life_start'] = time.time()
                        
                        # 使用统一的数字生命体核心
                        from core.digital_life import get_instance as get_digital_life
                        digital_life_instance = get_digital_life()

                        # 🔥 老王修复：使用统一的实例类型验证
                        self._validate_instance_type(
                            digital_life_instance,
                            expected_methods=['process_input'],
                            component_name="digital_life"
                        )
                        
                        self.digital_life = digital_life_instance
                        self._mark_initialized("digital_life")
                        
                        self.init_times['digital_life_end'] = time.time()
                        self.logger.success("🧠 数字生命体核心初始化完成，使用统一的process_input接口")
                        
                    except Exception as e:
                        self.logger.error_status(f"数字生命体核心初始化失败: {e}")
                        self.digital_life = None
                        
                # 12. 初始化增强集成调度器 (最强大版本，包含情感权重系统)
                if not self._check_initialized("enhanced_scheduler"):
                    try:
                        self.init_times['enhanced_scheduler_start'] = time.time()
                        from core.enhanced_integrated_scheduler import EnhancedIntegratedScheduler
                        
                        # 使用已初始化的mysql_connector
                        mysql_connector = getattr(self, 'mysql_connector', None)
                        self.enhanced_scheduler = EnhancedIntegratedScheduler(
                            mysql_connector=mysql_connector
                        )
                        
                        # 启动增强调度器
                        self.enhanced_scheduler.start_system()
                        
                        self._mark_initialized("enhanced_scheduler")
                        self.init_times['enhanced_scheduler_end'] = time.time()
                        self.logger.success("增强调度器初始化完成 (作为独立调度组件)")
                        
                        # 🔥 提升关键组件到主系统级别，解决动态上下文中的"未初始化"问题
                        try:
                            # EnhancedIntegratedScheduler直接包含universal_scheduler
                            if hasattr(self.enhanced_scheduler, 'universal_scheduler'):
                                self.universal_scheduler = self.enhanced_scheduler.universal_scheduler
                                self.logger.success("✅ 通用调度器已提升到主系统级别")
                            
                            if hasattr(self.enhanced_scheduler, 'emotional_weight_system'):
                                self.emotional_weight_system = self.enhanced_scheduler.emotional_weight_system
                                self.logger.success("✅ 情感权重系统已提升到主系统级别")
                            
                            # EnhancedIntegratedScheduler直接包含vital_signs_simulator
                            if hasattr(self.enhanced_scheduler, 'vital_signs_simulator'):
                                self.vital_signs_simulator = self.enhanced_scheduler.vital_signs_simulator
                                self.logger.success("✅ 生命体征模拟器已提升到主系统级别")
                            
                            if hasattr(self.enhanced_scheduler, 'hardware_monitor'):
                                self.hardware_monitor = self.enhanced_scheduler.hardware_monitor
                                self.logger.success("✅ 硬件监控器已提升到主系统级别")
                            
                            if hasattr(self.enhanced_scheduler, 'activity_executor'):
                                self.activity_executor = self.enhanced_scheduler.activity_executor
                                self.logger.success("✅ 活动执行器已提升到主系统级别")
                            
                            if hasattr(self.enhanced_scheduler, 'script_service'):
                                self.script_service = self.enhanced_scheduler.script_service
                                self.logger.success("✅ Scripts集成服务已提升到主系统级别")
                                    
                        except Exception as e:
                            self.logger.warning(f"⚠️ 组件提升过程中出现警告: {e}")
                        
                    except Exception as e:
                        self.logger.warning_status(f"增强调度器初始化失败: {e}，跳过")
                        self.enhanced_scheduler = None
                
                # 🔥 老王添加：应用关键修复
                self.init_times['critical_fixes_start'] = time.time()
                await self._apply_critical_fixes()
                self.init_times['critical_fixes_end'] = time.time()
                
                # 🔥 老王修复：通用调度器已从增强调度器中提升，无需重复初始化
                if not hasattr(self, 'universal_scheduler') or self.universal_scheduler is None:
                    self.logger.warning("⚠️ 通用调度器未从增强调度器中提升，使用备用初始化")
                    # 这里可以添加备用初始化逻辑，但通常不应该执行到这里
                else:
                    self.logger.success("✅ 通用调度器已从增强调度器中获取，跳过重复初始化")

                # 🔥 P0级修复：按正确顺序初始化关键服务，确保依赖关系正确
                # 1. 首先初始化数据库和存储相关组件
                if not self._check_initialized("mysql_connector"):
                    self.init_times['mysql_connector_start'] = time.time()
                    await self._init_mysql_connector()
                    self.init_times['mysql_connector_end'] = time.time()

                # 2. 🔥 P0级修复：在器官系统前初始化亲密度安全框架
                if not self._check_initialized("intimacy_security"):
                    try:
                        self.init_times['intimacy_security_start'] = time.time()
                        
                        # 🔥 检查MySQL连接器依赖 - 修复：从单例管理器获取
                        from utilities.singleton_manager import get_silent
                        mysql_connector = get_silent("mysql_connector")
                        
                        # 🔥 修复：使用正确的连接检查方法
                        if mysql_connector and hasattr(mysql_connector, 'is_connected') and mysql_connector.is_connected():
                            # MySQL可用，使用完整功能
                            from security.plugins.intimacy_analyzer import IntimacyAnalyzer
                            from security.plugins.high_performance_intimacy_provider import HighPerformanceIntimacyProvider
                            
                            self.intimacy_analyzer = IntimacyAnalyzer()
                            self.intimacy_provider = HighPerformanceIntimacyProvider(mysql_connector)
                            
                            # 🔥 注册到singleton_manager
                            from utilities.singleton_manager import register
                            register("intimacy_provider", self.intimacy_provider)
                            register("intimacy_analyzer", self.intimacy_analyzer)
                            
                            # 🔥 修复组件状态问题：添加到组件状态列表
                            self._mark_initialized("intimacy_provider")
                            self._mark_initialized("intimacy_analyzer")
                            
                            self.logger.success("✅ 亲密度安全框架初始化完成 (完整功能)")
                            self.logger.success(f"✅ 使用MySQL连接器: {mysql_connector.host}:{mysql_connector.port}")
                            self.logger.success("✅ intimacy_provider已注册到singleton_manager和组件状态列表")
                        else:
                            # MySQL不可用，使用轻量级替代方案
                            self.logger.info("🔄 MySQL连接器不可用，亲密度安全框架使用轻量级模式")
                            self.intimacy_analyzer = None
                            self.intimacy_provider = None
                        
                        self._mark_initialized("intimacy_security")
                        self.init_times['intimacy_security_end'] = time.time()
                        
                    except Exception as e:
                        self.logger.info(f"🔄 亲密度安全框架初始化失败: {e}，自动切换到轻量级模式")
                        self.intimacy_analyzer = None
                        self.intimacy_provider = None

                # 3. 初始化AI决策引擎（认知模块依赖）
                if not self._check_initialized("yanran_decision_engine"):
                    self.init_times['yanran_decision_engine_start'] = time.time()
                    await self._init_yanran_decision_engine()
                    self.init_times['yanran_decision_engine_end'] = time.time()
                
                # 4. 初始化WeChat统一推送服务（在认知模块前）
                if not self._check_initialized("wechat_push_service"):
                    self.init_times['wechat_push_service_start'] = time.time()
                    await self._init_wechat_push_service()
                    self.init_times['wechat_push_service_end'] = time.time()
                
                # 5. 初始化器官系统管理器（在WeChat推送服务和intimacy_provider后）
                if not self._check_initialized("organ_system_manager"):
                    self.init_times['organ_system_manager_start'] = time.time()
                    await self._init_organ_system_manager()
                    self.init_times['organ_system_manager_end'] = time.time()
                
                # 6. 初始化认知模块（在基础服务都准备好后）
                if not self._check_initialized("cognitive_modules"):
                    self.init_times['cognitive_modules_start'] = time.time()
                    await self._init_cognitive_modules()
                    self.init_times['cognitive_modules_end'] = time.time()
                
                # 🔥 老王修复：初始化意识状态持久化系统
                self.init_times['consciousness_persistence_start'] = time.time()
                await self._init_consciousness_persistence()
                self.init_times['consciousness_persistence_end'] = time.time()
                
                # 🔥 香草在极致高潮中集成Phase 1-4核心组件
                self.init_times['comprehensive_components_start'] = time.time()
                await self._init_comprehensive_components()
                self.init_times['comprehensive_components_end'] = time.time()
                
                # 记录总初始化时间
                self.init_times['total'] = time.time() - init_start
                
                # 标记为已初始化
                self.initialized = True
                
                # 记录初始化状态
                self.logger.success(f"数字生命体系统初始化完成 (耗时 {self.init_times['total']:.2f}秒)")
                self.logger.success(f"已初始化组件: {', '.join(_initialized_components)}")
                
                # 发布初始化完成事件
                if self.event_bus:
                    self.event_bus.publish("system.initialized", {
                        "timestamp": time.time(),
                        "components": list(_initialized_components),
                        "init_time": self.init_times['total']
                    })
                
                return True
                
            except Exception as e:
                self.logger.error_status(f"系统初始化失败: {e}")
                self.logger.error_status(traceback.format_exc())
                
                # 发布初始化失败事件
                if self.event_bus:
                    self.event_bus.publish("system.init_failed", {
                        "timestamp": time.time(),
                        "error": str(e),
                        "traceback": traceback.format_exc()
                    })
                
                return False
    
    async def _init_middleware(self) -> bool:
        """初始化中间件服务"""
        global _initialized_components
        
        try:
            self.logger.success("初始化中间件服务...")
            
            # 初始化启动钩子
            if not self._check_initialized("startup_hook"):
                try:
                    from middleware.startup_hook import get_instance as get_startup_hook
                    self.startup_hook = get_startup_hook()
                    self._mark_initialized("startup_hook")
                    await asyncio.sleep(0)  # 让出控制权
                    self.logger.success("启动钩子初始化完成")
                except Exception as e:
                    self.logger.warning_status(f"启动钩子初始化失败: {e}, 跳过")
            
            # 初始化API服务
            if not self._check_initialized("api_service"):
                try:
                    from middleware.api_service import get_instance as get_api_service
                    self.api_service = get_api_service()
                    self._mark_initialized("api_service")
                    await asyncio.sleep(0)  # 让出控制权
                    self.logger.success("API服务初始化完成")
                except Exception as e:
                    self.logger.warning_status(f"API服务初始化失败: {e}, 跳过")
            
            # 初始化其他中间件服务
            middleware_modules = [
                ("system_adapter", "middleware.system_adapter"),
                ("cognitive_integration", "middleware.cognitive_integration")
            ]
            
            for name, module_path in middleware_modules:
                if not self._check_initialized(name):
                    try:
                        module = __import__(module_path, fromlist=['get_instance'])
                        instance = module.get_instance()
                        setattr(self, name, instance)
                        self._mark_initialized(name)
                        await asyncio.sleep(0)  # 让出控制权
                        self.logger.success(f"中间件 {name} 初始化完成")
                    except Exception as e:
                        self.logger.warning_status(f"中间件 {name} 初始化失败: {e}, 跳过")
            
            self.logger.success("中间件服务初始化完成")
            return True
            
        except Exception as e:
            self.logger.error_status(f"中间件服务初始化失败: {e}")
            self.logger.error_status(traceback.format_exc())
            return False
    
    async def _init_cognitive_modules(self) -> bool:
        """初始化认知模块"""
        global _initialized_components
        
        try:
            # 导入单例管理器
            from utilities.singleton_manager import get_silent, register
            
            self.logger.success("初始化认知模块...")
            
            # 🔥 Step 1: 预先初始化intent_recognizer，避免WARNING
            self.logger.success("🎯 预先初始化意图识别器...")
            try:
                # 首先检查是否已经在singleton_manager中注册
                existing_recognizer = get_silent("intent_recognizer")
                
                if existing_recognizer is not None:
                    # 已存在，直接标记为已初始化
                    self._mark_initialized("intent_recognizer")
                    self.logger.success("🎯 意图识别器已存在，跳过初始化")
                else:
                    # 不存在，创建新实例 - 使用正确的导入路径
                    from cognitive_modules.perception.intent_recognition import get_instance as get_intent_recognizer
                    intent_recognizer = get_intent_recognizer()
                    
                    # 🔥 get_instance内部已经处理了注册，这里不需要重复注册
                    # 只需标记为已初始化
                    self._mark_initialized("intent_recognizer")
                    self.logger.success("🎯 意图识别器预先初始化完成")
                
            except Exception as e:
                self.logger.warning_status(f"意图识别器预先初始化失败: {e}")
                # 尝试备用方案：直接从模块导入
                try:
                    from cognitive_modules.perception.intent_recognition import IntentRecognizer
                    intent_recognizer = IntentRecognizer()
                    # 🔥 IntentRecognizer内部已经处理了注册
                    self._mark_initialized("intent_recognizer")
                    self.logger.success("🎯 意图识别器备用方案初始化完成")
                except Exception as e2:
                    self.logger.error_status(f"意图识别器初始化完全失败: {e2}")
            
            # 🔥 Step 2: 定义cognitive_modules列表（包含热搜感知模块和器官系统）
            cognitive_modules = [
                # 基础认知模块
                {
                    "name": "perception",
                    "module": "cognitive_modules.perception.perception_engine",
                    "factory": "get_instance",
                    "description": "感知引擎",
                    "required": False
                },
                {
                    "name": "emotion_engine",
                    "module": "cognitive_modules.emotion.emotion_engine",
                    "factory": "get_instance",
                    "description": "情感引擎",
                    "required": False
                },
                {
                    "name": "memory",
                    "module": "cognitive_modules.memory.memory_manager",
                    "factory": "get_instance",
                    "description": "记忆管理器",
                    "required": False
                },
                {
                    "name": "behavior",
                    "module": "cognitive_modules.behavior.behavior_manager",
                    "factory": "get_instance",
                    "description": "行为管理器",
                    "required": False
                },
                # 🔥 P0级别修复：添加健康系统模块
                {
                    "name": "health_system",
                    "module": "cognitive_modules.physiology.health_system",
                    "factory": "get_instance",
                    "description": "健康系统",
                    "required": True,
                    "config": {
                        "energy_decay_rate": 0.2,
                        "stress_threshold": 70.0,
                        "auto_recovery": True,
                        "health_monitoring": True
                    }
                },
                # 🔥 新增：热搜感知智能化模块
                {
                    "name": "trend_perception",
                    "module": "cognitive_modules.perception.trend_perception",
                    "factory": "get_instance",
                    "description": "趋势感知模块",
                    "required": False
                },
                {
                    "name": "trend_intelligence",
                    "module": "cognitive_modules.perception.trend_intelligence",
                    "factory": "get_instance",
                    "description": "趋势智能模块",
                    "required": False
                },
                {
                    "name": "intelligent_scheduler",
                    "module": "cognitive_modules.perception.intelligent_scheduler",
                    "factory": "get_instance",
                    "description": "智能调度器",
                    "required": False
                },
                # 🔥 新增：器官系统（按依赖顺序排列）
                {
                    "name": "yanran_ai_decision_engine",
                    "module": "cognitive_modules.ai.yanran_decision_engine",
                    "factory": "get_instance",
                    "description": "嫣然AI决策引擎",
                    "required": False
                },
                {
                    "name": "organ_neural_network",
                    "module": "cognitive_modules.neural.organ_neural_network",
                    "factory": "get_instance",
                    "description": "器官神经网络",
                    "required": False
                },
                {
                    "name": "world_perception_organ",
                    "module": "cognitive_modules.organs.world_perception_organ",
                    "factory": "get_instance",
                    "description": "世界感知器官",
                    "required": False
                },
                {
                    "name": "creative_expression_organ",
                    "module": "cognitive_modules.organs.creative_expression_organ",
                    "factory": "get_instance",
                    "description": "创意表达器官",
                    "required": False
                },
                {
                    "name": "safety_protection_organ",
                    "module": "cognitive_modules.organs.safety_protection_organ",
                    "factory": "get_instance",
                    "description": "安全保护器官",
                    "required": False
                },
                {
                    "name": "skill_coordination_organ",
                    "module": "cognitive_modules.organs.skill_coordination_organ",
                    "factory": "get_instance",
                    "description": "技能协调器官",
                    "required": False
                },
                {
                    "name": "wealth_management_organ",
                    "module": "cognitive_modules.organs.wealth_management_organ",
                    "factory": "get_instance",
                    "description": "财富管理器官",
                    "required": False
                },
                {
                    "name": "data_perception_organ",
                    "module": "cognitive_modules.organs.data_perception_organ",
                    "factory": "get_instance",
                    "description": "数据感知器官",
                    "required": False
                },
                # 🔥 Phase 4 新增器官：高级器官
                # 🔥 保留普通版本作为增强版本的备用基础器官
                {
                    "name": "proactive_expression_organ",
                    "module": "cognitive_modules.organs.proactive_expression_organ",
                    "factory": "get_instance",
                    "description": "主动表达器官（增强版本的基础器官）",
                    "required": True  # 🔥 修复：标记为必需，确保始终初始化
                },
                # 🔥 老王修复：将enhanced_proactive_expression_organ移到这里，确保在relationship_coordination_organ之前初始化
                {
                    "name": "enhanced_proactive_expression_organ",
                    "module": "cognitive_modules.organs.enhanced_proactive_expression_organ",
                    "factory": "get_instance",
                    "description": "增强版主动表达器官",
                    "required": True,
                    "config": {
                        "intelligence_integration": True,
                        "neural_enhancement": True
                    }
                },
                {
                    "name": "relationship_coordination_organ",
                    "module": "cognitive_modules.organs.relationship_coordination_organ",
                    "factory": "get_instance",
                    "description": "关系协调器官",
                    "required": False
                },
                {
                    "name": "personalized_service_organ",
                    "module": "cognitive_modules.organs.personalized_service_organ",
                    "factory": "get_instance",
                    "description": "个性化服务器官",
                    "required": False
                },
                # 8. 记忆整合模块 - 数字生命的高级记忆关联系统
                {
                    "name": "memory_integration",
                    "module": "cognitive_modules.memory.memory_integration",
                    "factory": "get_instance",
                    "description": "记忆整合模块",
                    "required": True,
                    "config": {
                        "association_threshold": 0.5,
                        "max_association_results": 10,
                        "enable_cross_memory_search": True,
                        "daily_consolidation_enabled": True
                    }
                },
                # 9. 程序性记忆模块 - 数字生命的技能学习系统
                {
                    "name": "procedural_memory",
                    "module": "cognitive_modules.memory.procedural_memory",
                    "factory": "get_instance",
                    "description": "程序性记忆模块",
                    "required": True,
                    "config": {
                        "practice_effect": 0.05,
                        "skill_decay_rate": 0.01,
                        "mastery_threshold": 0.8,
                        "automation_threshold": 0.9
                    }
                },
                # 10. AI增强意识系统 - 数字生命的自我意识核心
                {
                    "name": "ai_enhanced_consciousness",
                    "module": "core.ai_enhanced_consciousness",
                    "factory": "get_instance",
                    "description": "AI增强意识系统",
                    "required": True,
                    "config": {
                        "neural_enhancement_enabled": True,
                        "reflection_interval": 3600
                    }
                },
                # 6. 神经网络核心 - 数字生命的神经处理中心
                {
                    "name": "neural_core",
                    "module": "core.neural_network.neural_core",
                    "factory": "get_instance", 
                    "description": "神经网络核心",
                    "required": True,
                    "config": {
                        "learning_rate": 0.01,
                        "plasticity": 0.5
                    }
                },
                # 7. 智能整合管理器 - 数字生命的智能学习协调中心
                {
                    "name": "intelligence_integration_manager",
                    "module": "cognitive_modules.intelligence_integration_manager",
                    "factory": "get_instance",
                    "description": "智能整合管理器",
                    "required": True,
                    "config": {
                        "coordination_interval": 60,
                        "vitality_monitoring": True
                    }
                },
                # 8. 增强版主动表达器官已移到前面，避免时序问题
                # 9. 数字生命智能协调器 - 数字生命的系统级智能协调中心
                {
                    "name": "digital_life_intelligence_coordinator",
                    "module": "cognitive_modules.digital_life_intelligence_coordinator",
                    "factory": "get_instance",
                    "description": "数字生命智能协调器",
                    "required": True,
                    "config": {
                        "coordination_frequency": 30,
                        "auto_recovery_enabled": True,
                        "evolution_learning_enabled": True
                    }
                },
                # 10. 系统协调器 - 数字生命的系统级协调和数据流管理
                {
                    "name": "system_coordinator",
                    "module": "core.system_coordinator",
                    "factory": "get_system_coordinator",
                    "description": "系统协调器",
                    "required": True,
                    "config": {
                        "coordination_mode": "intelligent",
                        "coordination_interval": 10.0,
                        "health_check_interval": 30.0
                    }
                },
                # 11. 韧性自愈系统 - 数字生命的健康监控和自愈能力
                {
                    "name": "resilience",
                    "module": "core.resilience",
                    "factory": "get_instance",
                    "description": "韧性自愈系统",
                    "required": True,
                    "config": {
                        "health_check_interval": 60,
                        "auto_recovery": True
                    }
                },
                # 12. 感知引擎 - 数字生命的感知处理中心
                {
                    "name": "perception_engine",
                    "module": "cognitive_modules.perception.perception_engine",
                    "factory": "get_instance",
                    "description": "感知引擎",
                    "required": True,
                    "config": {
                        "perception_quality_threshold": 0.7,
                        "integration_monitoring": True
                    }
                },
                # 13. 数据源管理器 - 数字生命的数据获取和分析中心
                {
                    "name": "datasource_manager",
                    "module": "integrations.datasource_integration",
                    "factory": "get_instance",
                    "description": "数据源管理器",
                    "required": True,
                    "config": {
                        "intelligence_integration": True,
                        "auto_analysis": True
                    }
                },
                # 14. 亲密度系统 - 数字生命的情感关系管理
                {
                    "name": "intimacy_provider",
                    "module": "security.plugins.high_performance_intimacy_provider",
                    "class": "HighPerformanceIntimacyProvider",
                    "description": "亲密度系统",
                    "required": False,  # 可选组件，依赖MySQL
                    "requires_mysql": True,
                    "config": {
                        "relationship_decay_rate": 0.01,
                        "max_relationships": 1000
                    }
                },
                # 5. 组件管理器 - 数字生命的智能组件管理系统
                {
                    "name": "component_manager",
                    "module": "core.component_manager",
                    "factory": "get_instance",
                    "description": "组件管理器",
                    "required": True,
                    "config": {
                        "health_check_interval": 90,  # 90秒健康检查
                        "auto_recovery": True,
                        "load_balancing": True
                    }
                },
                # 6. AI增强意识系统 - 数字生命的自我意识核心
                {
                    "name": "ai_enhanced_consciousness",
                    "module": "core.ai_enhanced_consciousness",
                    "factory": "get_instance",
                    "description": "AI增强意识系统",
                    "required": True,
                    "config": {
                        "neural_enhancement_enabled": True,
                        "reflection_interval": 3600
                    }
                },
                # 6. 性能优化器 - 数字生命的性能优化和懒加载系统
                {
                    "name": "performance_optimizer",
                    "module": "core.performance_optimizer",
                    "factory": "get_performance_optimizer",
                    "description": "性能优化器",
                    "required": True,
                    "config": {
                        "enable_lazy_loading": True,
                        "cache_enabled": True,
                        "auto_optimization": True,
                        "preload_critical": True
                    }
                },
                # 🔥 老王新增：安全管理器 - 统一安全防护系统
                {
                    "name": "security_manager",
                    "module": "security.security_manager",
                    "factory": "get_security_manager",
                    "description": "安全管理器",
                    "required": True,
                    "config": {
                        "auth_enabled": True,
                        "encryption_enabled": True,
                        "audit_enabled": True
                    }
                },
                # 🔥 老王新增：参数优化器 - 智能参数优化系统
                {
                    "name": "parameter_optimizer",
                    "module": "core.parameter_optimizer",
                    "factory": "get_parameter_optimizer",
                    "description": "参数优化器",
                    "required": True,
                    "config": {
                        "auto_optimization": True,
                        "optimization_interval": 3600,
                        "safety_enabled": True
                    }
                },
                # 7. 技能管理器 - 数字生命的高级技能管理系统
                {
                    "name": "skill_manager",
                    "module": "core.skill_manager",
                    "factory": "get_instance",
                    "description": "技能管理器",
                    "required": True,
                    "config": {
                        "skills_directory": "plugins/skills",
                        "auto_discovery": True,
                        "default_enabled": True,
                        "hot_reload": True
                    }
                },
                # 8. AI增强意识系统 - 数字生命的自我意识核心
                {
                    "name": "ai_enhanced_consciousness",
                    "module": "core.ai_enhanced_consciousness",
                    "factory": "get_instance",
                    "description": "AI增强意识系统",
                    "required": True,
                    "config": {
                        "neural_enhancement_enabled": True,
                        "reflection_interval": 3600
                    }
                }
            ]
            
            success_count = 0
            total_count = len(cognitive_modules)
            
            # 逐个初始化认知模块组件
            for component_info in cognitive_modules:
                component_name = component_info["name"]
                
                # 🔥 静默检查是否已注册（避免WARNING）
                if get_silent(component_name) is not None:
                    self.logger.info(f"✅ {component_info['description']} 已注册，跳过")
                    success_count += 1
                    continue
                
                try:
                    self.logger.success(f"初始化 {component_info['description']}...")
                    
                    # 动态导入模块
                    module = __import__(component_info["module"], fromlist=[component_info.get("factory", component_info.get("class"))])
                    
                    # 创建实例
                    if "factory" in component_info:
                        # 使用工厂函数，传入配置
                        factory_func = getattr(module, component_info["factory"])
                        config = component_info.get("config", {})
                        
                        # 🔥 修复参数传递问题：检查get_instance方法是否接受参数
                        if component_name == "autonomous_thinking":
                            # 自主思考模块需要module_id和config参数
                            module_id = f"autonomous_thinking_{int(time.time())}"
                            instance = factory_func(module_id, config)
                        elif component_name == "self_reflection":
                            # 自我反思模块需要module_id和config参数
                            module_id = f"self_reflection_{int(time.time())}"
                            instance = factory_func(module_id, config)
                        elif component_name == "conscious_decision":
                            # 意识决策模块需要module_id和config参数
                            module_id = f"conscious_decision_{int(time.time())}"
                            instance = factory_func(module_id, config)
                        elif component_name in ["memory_integration", "procedural_memory"]:
                            # 🔥 老王修复：记忆模块的factory函数签名是 get_instance(module_id, config)
                            # 需要传递正确的参数顺序
                            try:
                                instance = factory_func("default", config)
                            except TypeError:
                                # 如果参数不匹配，尝试无参数调用
                                instance = factory_func()
                                if config and hasattr(instance, 'update_config'):
                                    try:
                                        if isinstance(config, dict):
                                            instance.update_config(config)
                                        else:
                                            self.logger.warning_status(f"跳过{component_name}配置更新: config不是字典类型")
                                    except Exception as e:
                                        self.logger.debug(f"无法更新{component_name}配置: {e}")
                        elif component_name in ["intelligence_integration_manager", "perception_engine", "neural_core", "resilience"]:
                            # 这些组件的get_instance方法不接受参数
                            instance = factory_func()
                            # 如果有配置，尝试通过其他方式应用
                            if config and hasattr(instance, 'update_config'):
                                try:
                                    # 🔥 老王修复：确保config是字典类型
                                    if isinstance(config, dict):
                                        instance.update_config(config)
                                    else:
                                        self.logger.warning_status(f"跳过{component_name}配置更新: config不是字典类型")
                                except Exception as e:
                                    self.logger.debug(f"无法更新{component_name}配置: {e}")
                        elif component_name == "datasource_manager":
                            # 数据源管理器需要特殊处理，它的get_instance可能不存在
                            # 尝试直接实例化类
                            if hasattr(module, 'DataSourceManager'):
                                instance = module.DataSourceManager()
                        elif component_name == "enhanced_proactive_expression_organ":
                            # 🔥 老王修复：增强版主动表达器官接受config参数，添加类型检查
                            try:
                                # 确保factory_func是可调用的函数
                                if not callable(factory_func):
                                    self.logger.error_status(f"🚨 严重错误：{component_name}的factory_func不是函数！类型: {type(factory_func)}")
                                    raise TypeError(f"factory_func不是可调用对象: {type(factory_func)}")

                                instance = factory_func(config)

                                # 🔥 老王修复：使用统一的实例类型验证，增加详细诊断
                                self.logger.info(f"🔍 验证{component_name}实例: 类型={type(instance)}")

                                # 🔥 老王修复：检查多个可能的方法名
                                expected_methods = ['process_expression_trigger', 'trigger_expression', 'enhanced_proactive_expression']
                                found_methods = []
                                for method in expected_methods:
                                    if hasattr(instance, method):
                                        found_methods.append(method)
                                        self.logger.info(f"✅ {component_name}有{method}方法")

                                if found_methods:
                                    self.logger.success(f"✅ {component_name}找到兼容方法: {found_methods}")
                                else:
                                    self.logger.warning_status(f"⚠️ {component_name}缺少期望的方法")
                                    # 列出实例的所有方法
                                    methods = [method for method in dir(instance) if callable(getattr(instance, method)) and not method.startswith('_')]
                                    self.logger.info(f"🔍 {component_name}可用方法: {methods}")

                                # 🔥 老王修复：只验证实例类型，不强制要求特定方法
                                self._validate_instance_type(
                                    instance,
                                    expected_methods=None,  # 不强制要求特定方法
                                    component_name=component_name
                                )

                            except TypeError as e:
                                self.logger.warning_status(f"⚠️ {component_name}参数不匹配，尝试无参数调用: {e}")
                                # 如果参数不匹配，尝试无参数调用
                                instance = factory_func()

                                # 🔥 老王修复：再次验证实例类型，增加详细诊断
                                self.logger.info(f"🔍 验证{component_name}(无参数)实例: 类型={type(instance)}")

                                # 🔥 老王修复：检查多个可能的方法名
                                expected_methods = ['process_expression_trigger', 'trigger_expression', 'enhanced_proactive_expression']
                                found_methods = []
                                for method in expected_methods:
                                    if hasattr(instance, method):
                                        found_methods.append(method)
                                        self.logger.info(f"✅ {component_name}(无参数)有{method}方法")

                                if found_methods:
                                    self.logger.success(f"✅ {component_name}(无参数)找到兼容方法: {found_methods}")
                                else:
                                    self.logger.warning_status(f"⚠️ {component_name}(无参数)缺少期望的方法")
                                    # 列出实例的所有方法
                                    methods = [method for method in dir(instance) if callable(getattr(instance, method)) and not method.startswith('_')]
                                    self.logger.info(f"🔍 {component_name}(无参数)可用方法: {methods}")

                                self._validate_instance_type(
                                    instance,
                                    expected_methods=['process_expression_trigger'],
                                    component_name=f"{component_name}(无参数调用)"
                                )
                        else:
                            # 其他组件尝试接受配置参数
                            try:
                                if config:
                                    instance = factory_func(config)
                                else:
                                    instance = factory_func()
                            except TypeError:
                                # 如果参数不匹配，尝试无参数调用
                                instance = factory_func()
                                if config and hasattr(instance, 'update_config'):
                                    try:
                                        # 🔥 老王修复：确保config是字典类型
                                        if isinstance(config, dict):
                                            instance.update_config(config)
                                        else:
                                            self.logger.warning_status(f"跳过{component_name}配置更新: config不是字典类型")
                                    except Exception as e:
                                        self.logger.debug(f"无法更新{component_name}配置: {e}")
                    elif "class" in component_info:
                        # 使用类构造函数
                        component_class = getattr(module, component_info["class"])
                        if component_info.get("requires_mysql"):
                            # 需要MySQL连接器的组件
                            # 🔥 尝试从singleton_manager获取MySQL连接器
                            mysql_connector = get_silent("mysql_connector")
                            if not mysql_connector:
                                # 尝试从实例属性获取
                                mysql_connector = getattr(self, 'mysql_connector', None)
                            
                            if mysql_connector:
                                instance = component_class(mysql_connector)
                                self.logger.success(f"✅ {component_info['description']} 使用MySQL连接器初始化成功")
                            else:
                                self.logger.warning_status(f"⚠️ {component_info['description']} 需要MySQL连接器，但MySQL连接器未初始化，跳过")
                                if component_info.get("required", False):
                                    self.logger.error_status(f"❌ 必需组件 {component_info['description']} 初始化失败")
                                continue
                        else:
                            config = component_info.get("config", {})
                            if config:
                                instance = component_class(config)
                            else:
                                instance = component_class()
                    else:
                        self.logger.error_status(f"组件 {component_name} 配置错误：缺少factory或class")
                        continue
                    
                    # 注册到singleton_manager
                    register(component_name, instance)
                    
                    # 🔥 老王修复：如果组件有initialize方法，先调用它
                    if hasattr(instance, 'initialize') and callable(instance.initialize):
                        try:
                            if asyncio.iscoroutinefunction(instance.initialize):
                                init_success = await instance.initialize()
                            else:
                                init_success = instance.initialize()
                            
                            if init_success:
                                self.logger.info(f"✅ {component_info['description']} 初始化成功")
                            else:
                                self.logger.warning_status(f"⚠️ {component_info['description']} 初始化失败")
                        except Exception as e:
                            self.logger.warning_status(f"⚠️ {component_info['description']} 初始化异常: {e}")
                    
                    # 如果组件有activate方法，调用它
                    if hasattr(instance, 'activate') and callable(instance.activate):
                        try:
                            if asyncio.iscoroutinefunction(instance.activate):
                                await instance.activate()
                            else:
                                instance.activate()
                            self.logger.info(f"✅ {component_info['description']} 已激活")
                        except Exception as e:
                            self.logger.warning_status(f"⚠️ {component_info['description']} 激活失败: {e}")

                    # 🔥 特殊处理：数字生命智能协调器需要延迟初始化核心系统
                    if component_name == "digital_life_intelligence_coordinator":
                        try:
                            if hasattr(instance, 'ensure_core_systems_initialized'):
                                instance.ensure_core_systems_initialized()
                                self.logger.success("✅ 数字生命智能协调器核心系统延迟初始化完成")
                        except Exception as e:
                            self.logger.warning_status(f"⚠️ 数字生命智能协调器核心系统延迟初始化失败: {e}")

                    success_count += 1
                    self.logger.success(f"✅ {component_info['description']} 初始化成功")
                    
                except ImportError as e:
                    error_msg = f"⚠️ {component_info['description']} 模块不存在: {e}"
                    if component_info.get("required", False):
                        self.logger.error_status(f"❌ 必需组件初始化失败: {error_msg}")
                    else:
                        self.logger.warning_status(error_msg)
                except Exception as e:
                    error_msg = f"❌ {component_info['description']} 初始化失败: {e}"
                    if component_info.get("required", False):
                        self.logger.error_status(f"❌ 必需组件初始化失败: {error_msg}")
                    else:
                        self.logger.warning_status(error_msg)
                    self.logger.debug(traceback.format_exc())
                
                # 让出控制权
                await asyncio.sleep(0.01)
            
            # 初始化结果统计
            success_rate = (success_count / total_count) * 100 if total_count > 0 else 0
            self.logger.success(f"🎯 数字生命体核心组件初始化完成: {success_count}/{total_count} ({success_rate:.1f}%)")
            
            # 🔥 P0修复：向intelligence_integration_manager注册核心模块
            try:
                from utilities.singleton_manager import get_silent
                intelligence_manager = get_silent("intelligence_integration_manager")
                if intelligence_manager:
                    self.logger.info("🔧 开始向智能整合管理器注册核心模块...")

                    # 注册核心组件到智能整合管理器
                    core_modules = {
                        "enhanced_proactive_expression_organ": "增强主动表达器官",
                        "digital_life_intelligence_coordinator": "数字生命智能协调器",
                        "perception_engine": "感知引擎",
                        "datasource_manager": "数据源管理器",
                        "ai_enhanced_consciousness": "AI增强意识",
                        "neural_core": "神经网络核心",
                        "resilience": "韧性系统",
                        "vital_signs_simulator": "生命体征模拟器",
                        "thinking_chain": "思维链路",
                        "life_context": "生命上下文"
                    }

                    registered_count = 0
                    for module_name, description in core_modules.items():
                        module_instance = get_silent(module_name)
                        if module_instance:
                            if intelligence_manager.register_module(module_name, module_instance):
                                registered_count += 1
                                self.logger.debug(f"   ✅ 已注册: {description}")
                            else:
                                self.logger.warning(f"   ❌ 注册失败: {description}")
                        else:
                            self.logger.debug(f"   ⚠️ 模块不存在: {description}")

                    self.logger.success(f"✅ 智能整合管理器模块注册完成: {registered_count}/{len(core_modules)} 个模块")

                    # 验证注册结果
                    integration_state = intelligence_manager.get_integration_state()
                    self.logger.info(f"🧠 全局智能水平: {integration_state.get('global_intelligence', 0.0):.3f}")
                    self.logger.info(f"💓 生命活力: {integration_state.get('life_vitality', 0.0):.3f}")
                else:
                    self.logger.warning_status("⚠️ 智能整合管理器未找到，跳过模块注册")
            except Exception as e:
                self.logger.error_status(f"❌ 智能整合管理器模块注册失败: {e}")

            # 🔥 更新系统协调器中的模块状态为RUNNING
            try:
                system_coordinator = get_silent("system_coordinator")
                if system_coordinator:
                    from core.system_coordinator import ModuleStatus
                    # 更新成功初始化的组件状态为RUNNING
                    module_mapping = {
                        "intelligence_integration_manager": "intelligence_integration_manager",
                        "enhanced_proactive_expression_organ": "enhanced_proactive_expression_organ", 
                        "digital_life_intelligence_coordinator": "digital_life_intelligence_coordinator",
                        "perception_engine": "perception_engine",
                        "datasource_manager": "datasource_manager",
                        "ai_enhanced_consciousness": "ai_enhanced_consciousness",
                        "neural_core": "neural_core",
                        "resilience": "resilience"
                    }
                    
                    updated_modules = 0
                    for component_name, module_name in module_mapping.items():
                        component_instance = get_silent(component_name)
                        if component_instance:
                            system_coordinator.update_module_status(module_name, ModuleStatus.RUNNING)
                            updated_modules += 1
                    
                    self.logger.success(f"✅ 系统协调器模块状态更新完成: {updated_modules}个模块已设为RUNNING状态")
                else:
                    self.logger.warning_status("⚠️ 系统协调器未找到，跳过模块状态更新")
            except Exception as e:
                self.logger.warning_status(f"⚠️ 系统协调器模块状态更新失败: {e}")
            
            if success_count >= 3:  # 至少初始化3个核心组件才算成功
                self.logger.success("✅ 数字生命体核心组件初始化成功，生命特征完整性得到保障")
                return True
            else:
                self.logger.warning_status(f"⚠️ 只有{success_count}个核心组件成功初始化，数字生命体可能功能不完整")
                return False
                
        except Exception as e:
            self.logger.error_status(f"❌ 数字生命体核心组件初始化过程异常: {e}")
            self.logger.error_status(traceback.format_exc())
            return False
    
    async def _init_wechat_push_service(self):
        """初始化WeChat统一推送服务"""
        try:
            self.logger.success("📱 初始化WeChat统一推送服务...")
            from services.wechat_unified_push_service import get_wechat_unified_push_service
            
            # 获取推送服务实例
            self.wechat_push_service = get_wechat_unified_push_service()
            
            # 启动推送服务
            push_service_started = await self.wechat_push_service.start()
            if push_service_started:
                self.logger.success("✅ WeChat统一推送服务启动成功")
                
                # 注册到单例管理器
                from utilities.singleton_manager import register
                register("wechat_push_service", self.wechat_push_service)
                register("wechat_unified_push_service", self.wechat_push_service)  # 🔥 修复：添加主动表达器官使用的键名
                
                # 🔥 修复组件状态问题：添加到组件状态列表
                self._mark_initialized("wechat_push_service")
                self._mark_initialized("wechat_unified_push_service")
                
                # 检查配置状态
                wechat_config_path = os.path.join(PROJECT_ROOT, "config", "wechat_config.json")
                if os.path.exists(wechat_config_path):
                    with open(wechat_config_path, 'r', encoding='utf-8') as f:
                        wechat_config = json.load(f)
                    
                    migration_config = wechat_config.get("system_migration", {})
                    digital_life_config = wechat_config.get("digital_life_integration", {})
                    
                    if migration_config.get("wechat_unified_output", False) and digital_life_config.get("unified_output_enabled", False):
                        self.logger.success("✅ WeChat统一对外推送模式已完全启用")
                        self.logger.info(f"📊 迁移日期: {migration_config.get('migration_date', 'N/A')}")
                        self.logger.info(f"📱 支持的消息类型: {len(wechat_config.get('message_push', {}).get('supported_message_types', []))} 种")
                    else:
                        self.logger.warning("⚠️ WeChat统一推送配置未完全启用")
                
                self.logger.success("📱 WeChat统一推送服务初始化完成")
                
            else:
                self.logger.warning_status("⚠️ WeChat统一推送服务启动失败")
                
        except Exception as push_error:
            self.logger.warning_status(f"⚠️ WeChat统一推送服务初始化异常: {push_error}")
            self.logger.error_status(traceback.format_exc())
    
    async def _init_organ_system_manager(self):
        """初始化器官系统管理器"""
        try:
            self.logger.success("⚙️ 初始化器官系统管理器...")
            
            # 🔥 修复时序问题：在WeChat推送服务启动后再初始化器官系统
            from cognitive_modules.organ_system_manager import get_organ_system_manager
            self.organ_system_manager = get_organ_system_manager()
            
            # 异步初始化器官系统
            init_success = await self.organ_system_manager.initialize_organ_system()
            
            if init_success:
                self.logger.success("✅ 器官系统管理器初始化成功")
                
                # 注册到单例管理器
                from utilities.singleton_manager import register
                register("organ_system_manager", self.organ_system_manager)
                
                # 标记为已初始化
                self._mark_initialized("organ_system_manager")
                
                # 获取器官系统状态
                system_status = self.organ_system_manager.get_system_status()
                active_organs = system_status.get("active_organs", [])
                
                self.logger.success(f"⚙️ 器官系统状态: {system_status.get('system_status')} - 活跃器官: {len(active_organs)}")
                
                if active_organs:
                    self.logger.info(f"📋 活跃器官列表: {', '.join(active_organs)}")
                
            else:
                self.logger.warning_status("⚠️ 器官系统管理器初始化失败")
                
        except Exception as e:
            self.logger.warning_status(f"⚠️ 器官系统管理器初始化异常: {e}")
            self.logger.error_status(traceback.format_exc())
    
    async def _init_consciousness_persistence(self):
        """🔥 初始化意识状态持久化系统"""
        try:
            self.logger.success("🔥 初始化意识状态持久化系统...")
            
            # 创建持久化目录
            persistence_dirs = [
                "data/consciousness",
                "data/neural_models", 
                "data/consciousness/backups",
                "data/consciousness/history"
            ]
            
            for dir_path in persistence_dirs:
                full_path = os.path.join(current_dir, dir_path)
                if not os.path.exists(full_path):
                    os.makedirs(full_path, exist_ok=True)
                    self.logger.info(f"创建持久化目录: {full_path}")
            
            # 🔥 老王修复：初始化神经网络组件，确保持久化能正常工作
            try:
                # 初始化神经网络增强器
                from core.neural_consciousness_enhancer import NeuralConsciousnessEnhancer
                self.neural_enhancer = NeuralConsciousnessEnhancer()
                self.logger.info("神经网络增强器初始化完成")
            except Exception as e:
                self.logger.warning(f"神经网络增强器初始化失败: {e}")
                self.neural_enhancer = None
            
            try:
                # 初始化终极神经网络
                from core.advanced_neural_consciousness import AdvancedNeuralConsciousnessSystem
                self.advanced_neural = AdvancedNeuralConsciousnessSystem()
                self.logger.info("终极神经网络系统初始化完成")
            except Exception as e:
                self.logger.warning(f"终极神经网络系统初始化失败: {e}")
                self.advanced_neural = None
            
            # 启动持久化服务线程
            self.persistence_thread = threading.Thread(
                target=self._consciousness_persistence_loop,
                name="ConsciousnessPersistence",
                daemon=True
            )
            self.persistence_thread.start()
            
            self.logger.success("✅ 意识状态持久化系统启动完成")
            
        except Exception as e:
            self.logger.error_status(f"意识状态持久化系统初始化失败: {e}")
    
    def _init_neural_trigger_system(self):
        """初始化神经网络触发系统"""
        try:
            from core.neural_trigger_system import get_neural_trigger_system

            # 获取触发系统实例
            self.neural_trigger_system = get_neural_trigger_system()

            # 启动触发系统
            self.neural_trigger_system.start()

            # 注册到单例管理器
            from utilities.singleton_manager import register
            register("neural_trigger_system", self.neural_trigger_system)

            self.logger.info("🔥 神经网络触发系统初始化完成")

        except Exception as e:
            self.logger.error(f"❌ 神经网络触发系统初始化失败: {e}")
            raise

    def _consciousness_persistence_loop(self):
        """意识状态持久化循环"""
        try:
            self.logger.info("🔥 意识状态持久化循环启动，每5分钟保存一次神经模型")
            while True:
                try:
                    # 保存AI增强意识状态
                    if hasattr(self, 'ai_consciousness') and self.ai_consciousness:
                        self._save_consciousness_state()
                    
                    # 🔥 老王修复：保存神经网络模型
                    if hasattr(self, 'neural_enhancer') and self.neural_enhancer:
                        self._save_neural_models()
                    else:
                        self.logger.debug("神经网络增强器未初始化，跳过保存")
                    
                    # 🔥 老王修复：保存终极神经网络
                    if hasattr(self, 'advanced_neural') and self.advanced_neural:
                        self._save_advanced_neural_models()
                    else:
                        self.logger.debug("终极神经网络未初始化，跳过保存")
                    
                    # 等待下次保存
                    time.sleep(300)  # 5分钟保存一次
                    
                except Exception as save_e:
                    self.logger.error_status(f"单次保存循环异常: {save_e}")
                    time.sleep(60)  # 出错时等待1分钟再试
                    
        except Exception as e:
            self.logger.error_status(f"意识状态持久化循环异常: {e}")
    
    def _save_consciousness_state(self):
        """保存意识状态"""
        try:
            consciousness_data = {
                "timestamp": time.time(),
                "state": self.ai_consciousness.get_state() if hasattr(self.ai_consciousness, 'get_state') else {},
                "enhanced_state": getattr(self.ai_consciousness, 'enhanced_state', {}),
                "metacognitive_skills": getattr(self.ai_consciousness, 'enhanced_state', {}).get("metacognitive_skills", {}),
                "emergent_properties": getattr(self.ai_consciousness, 'enhanced_state', {}).get("emergent_properties", {})
            }
            
            # 保存到文件
            save_path = os.path.join(current_dir, "data/consciousness/consciousness_state.json")
            with open(save_path, 'w', encoding='utf-8') as f:
                json.dump(consciousness_data, f, indent=2, ensure_ascii=False)
            
            # 创建备份
            backup_path = os.path.join(
                current_dir, 
                f"data/consciousness/backups/consciousness_state_{int(time.time())}.json"
            )
            with open(backup_path, 'w', encoding='utf-8') as f:
                json.dump(consciousness_data, f, indent=2, ensure_ascii=False)
            
            self.logger.debug("意识状态已保存")
            
        except Exception as e:
            self.logger.error_status(f"保存意识状态失败: {e}")
    
    def _save_neural_models(self):
        """保存神经网络模型"""
        try:
            if hasattr(self.neural_enhancer, 'save_model'):
                model_path = os.path.join(current_dir, "data/neural_models/consciousness_enhancer.pkl")
                success = self.neural_enhancer.save_model(model_path)
                if success:
                    self.logger.debug("神经网络增强器模型已保存")
                else:
                    self.logger.warning("神经网络增强器模型保存失败")
            else:
                self.logger.debug("神经网络增强器没有save_model方法")
                
        except Exception as e:
            self.logger.error_status(f"保存神经网络模型失败: {e}")
    
    def _save_advanced_neural_models(self):
        """保存终极神经网络模型"""
        try:
            if hasattr(self.advanced_neural, 'save_model'):
                model_path = os.path.join(current_dir, "data/neural_models/advanced_consciousness.pkl")
                success = self.advanced_neural.save_model(model_path)
                if success:
                    self.logger.debug("终极神经网络模型已保存")
                else:
                    self.logger.warning("终极神经网络模型保存失败")
                
            if hasattr(self.advanced_neural, 'save_memory'):
                memory_path = os.path.join(current_dir, "data/neural_models/consciousness_memory.json")
                success = self.advanced_neural.save_memory(memory_path)
                if success:
                    self.logger.debug("终极神经网络记忆已保存")
                else:
                    self.logger.warning("终极神经网络记忆保存失败")
            else:
                self.logger.debug("终极神经网络没有save_memory方法")
                
        except Exception as e:
            self.logger.error_status(f"保存终极神经网络模型失败: {e}")
    
    async def _init_skills(self) -> bool:
        """初始化技能系统"""
        global _initialized_components
        
        try:
            self.logger.success("初始化技能系统...")
            
            # 🔥 统一使用核心技能管理器 (修复技能管理器重复问题)
            if not self._check_initialized("skill_manager"):
                try:
                    from cognitive_modules.skills.skill_manager import get_instance as get_skill_manager
                    self.skill_manager = get_skill_manager()
                    
                    # 🔥 注册到单例管理器
                    from utilities.singleton_manager import register, exists
                    if not exists("skill_manager"):
                        register("skill_manager", self.skill_manager)
                    
                    self._mark_initialized("skill_manager")
                    self.logger.success("✅ 统一技能管理器初始化完成")
                except Exception as e:
                    self.logger.error_status(f"技能管理器初始化失败: {e}")
                    return False
            
            # 🔥 扩展技能列表，激活孤岛技能
            all_skills = [
                # 基础技能
                ("chat_skill", "cognitive_modules.skills.chat_skill"),
                ("search_skill", "cognitive_modules.skills.search_skill"),
                ("drawing_skill", "cognitive_modules.skills.drawing_skill"),
                ("enhanced_activity_skill", "cognitive_modules.skills.enhanced_activity_skill"),
                # 🔥 激活孤岛技能
                ("financial_data_skill", "plugins.skills.financial_data_skill"),
                ("music_skill", "plugins.skills.music_skill"),
                ("greeting_skill", "plugins.skills.greeting_skill")
            ]
            
            # 🔥 确保插件目录在Python路径中
            plugin_skills_path = os.path.join(current_dir, "plugins", "skills")
            if os.path.exists(plugin_skills_path):
                self.logger.info(f"检测到技能插件目录: {plugin_skills_path}")
                
                # 将插件目录添加到Python路径
                if plugin_skills_path not in sys.path:
                    sys.path.append(plugin_skills_path)
                    self.logger.info(f"✅ 已添加技能插件目录到系统路径")
            
            # 🔥 统一加载所有技能 (包括激活的孤岛技能)
            for name, module_path in all_skills:
                if not self._check_initialized(name):
                    try:
                        # 🔥 统一技能加载逻辑
                        module = __import__(module_path, fromlist=['get_instance'])
                        instance = module.get_instance()
                        
                        # 🔥 注册到单例管理器（只有当实例不在单例管理器中时才注册）
                        from utilities.singleton_manager import register, exists
                        if not exists(name):
                            register(name, instance)
                        
                        # 注册到技能管理器
                        if self.skill_manager and hasattr(self.skill_manager, 'register_skill'):
                            # 判断register_skill是否是协程函数
                            if asyncio.iscoroutinefunction(self.skill_manager.register_skill):
                                # 创建异步任务注册技能
                                asyncio.create_task(self.skill_manager.register_skill(name, instance))
                                self.logger.success(f"✅ 异步注册技能: {name}")
                            else:
                                # 同步函数直接调用
                                self.skill_manager.register_skill(name, instance)
                                self.logger.success(f"✅ 同步注册技能: {name}")
                        else:
                            self.logger.warning_status(f"技能管理器不可用，跳过注册: {name}")
                            
                        # 标记为已初始化
                        setattr(self, name, instance)
                        self._mark_initialized(name)
                        
                    except ImportError as e:
                        self.logger.warning_status(f"⚠️ 技能 {name} 导入失败: {e}, 跳过")
                    except Exception as e:
                        self.logger.warning_status(f"⚠️ 技能 {name} 初始化失败: {e}, 跳过")
            
            self.logger.success("技能系统初始化完成")
            return True
            
        except Exception as e:
            self.logger.error_status(f"技能系统初始化失败: {e}")
            self.logger.error_status(traceback.format_exc())
            return False
    
    async def start(self) -> bool:
        """启动数字生命体系统"""
        global _system_started
        
        with _global_init_lock:
            if self.is_running:
                self.logger.warning_status("系统已在运行中，跳过重复启动")
                return True
            
            if not self.initialized:
                self.logger.warning_status("系统尚未初始化，先执行初始化")
                success = await self.initialize()
                if not success:
                    return False
            
            try:
                self.logger.success("开始启动数字生命体系统...")
                start_time = time.time()
                
                # 启动数字生命体核心
                if self.digital_life:
                    # 检查是否是增强调度器
                    if hasattr(self.digital_life, 'start_system'):
                        # 增强调度器已经在初始化时启动了，无需再次启动
                        self.logger.success("增强调度器已在初始化时启动")
                    elif hasattr(self.digital_life, 'start'):
                        if asyncio.iscoroutinefunction(self.digital_life.start):
                            await self.digital_life.start()
                        else:
                            self.digital_life.start()
                        self.logger.success("数字生命体核心已启动")
                    else:
                        self.logger.success("数字生命体核心无需启动")
                else:
                    self.logger.warning_status("数字生命体核心不可用，跳过启动")
                
                # 🔥 WebSocket服务已暂停 - 统一迁移到WeChat对外推送
                self.logger.info("📱 数字生命系统已迁移到WeChat统一对外推送模式")
# P0修复: 禁用WebSocket -                 self.logger.info("🔄 WebSocket相关功能已暂停，代码保留作为备用")
                
                # 🔥 WeChat统一推送服务已在initialize()阶段启动，这里只需要集成
                # 🔥 关键集成：将数字生命的输出重定向到WeChat推送服务
                if hasattr(self, 'wechat_push_service') and self.wechat_push_service and self.digital_life:
                    try:
                        await self._integrate_digital_life_with_wechat_push()
                        self.logger.success("✅ 数字生命与WeChat推送服务集成完成")
                    except Exception as integration_error:
                        self.logger.warning_status(f"⚠️ 数字生命与WeChat推送服务集成失败: {integration_error}")
                
                # 🔥 注释掉的WebSocket服务启动代码（保留作为备用）
                """
                # WebSocket服务启动代码已暂停，保留作为备用
                try:
# P0修复: 禁用WebSocket -                     self.logger.success("🚀 启动统一WebSocket服务...")
# P0修复: 禁用WebSocket -                     from services.unified_websocket_service import get_instance as get_websocket_service
                    
                    # 从配置文件获取WebSocket服务器配置
                    server_config = self.config.get("server", {})
# P0修复: 禁用WebSocket -                     websocket_host = server_config.get("websocket_host", "127.0.0.1")
# P0修复: 禁用WebSocket -                     websocket_port = server_config.get("websocket_port", 38365)
# P0修复: 禁用WebSocket -                     fallback_ports = server_config.get("websocket_fallback_ports", [8767, 8768, 8769])
                    
                    # 支持环境变量覆盖
                    import os
# P0修复: 禁用WebSocket -                     if os.environ.get("YANRAN_WEBSOCKET_HOST"):
# P0修复: 禁用WebSocket -                         websocket_host = os.environ.get("YANRAN_WEBSOCKET_HOST")
# P0修复: 禁用WebSocket -                     if os.environ.get("YANRAN_WEBSOCKET_PORT"):
                        try:
# P0修复: 禁用WebSocket -                             websocket_port = int(os.environ.get("YANRAN_WEBSOCKET_PORT"))
                        except ValueError:
# P0修复: 禁用WebSocket -                             self.logger.warning("环境变量YANRAN_WEBSOCKET_PORT不是有效数字，使用配置文件值")
                    
# P0修复: 禁用WebSocket -                     self.logger.info(f"WebSocket配置: {websocket_host}:{websocket_port}")
                    
                    # 尝试启动WebSocket服务，如果端口被占用则尝试备用端口
# P0修复: 禁用WebSocket -                     websocket_started = False
# P0修复: 禁用WebSocket -                     attempted_ports = [websocket_port] + fallback_ports
                    
                    for port in attempted_ports:
                        try:
# P0修复: 禁用WebSocket -                             self.websocket_service = get_websocket_service(websocket_host, port)
# P0修复: 禁用WebSocket -                             websocket_started = await self.websocket_service.start()
# P0修复: 禁用WebSocket -                             if websocket_started:
# P0修复: 禁用WebSocket -                                 self.logger.success(f"✅ 统一WebSocket服务启动成功: ws://{websocket_host}:{port}")
                                # 注册到单例管理器
                                from utilities.singleton_manager import register
# P0修复: 禁用WebSocket -                                 register("websocket_service", self.websocket_service)
                                break
                            else:
                                self.logger.warning(f"端口 {port} 启动失败，尝试下一个端口...")
                        except Exception as port_error:
                            self.logger.warning(f"端口 {port} 启动异常: {port_error}，尝试下一个端口...")
                            continue
                    
# P0修复: 禁用WebSocket -                     if not websocket_started:
# P0修复: 禁用WebSocket -                         self.logger.warning_status("⚠️ 统一WebSocket服务启动失败（所有端口都无法使用），但不影响系统运行")
                    else:
                        # 启动WebSocket到WeChat桥接服务
                        try:
# P0修复: 禁用WebSocket -                             self.logger.success("🌉 启动WebSocket到WeChat桥接服务...")
# P0修复: 禁用WebSocket -                             from services.websocket_wechat_bridge import start_bridge_service
                            
                            bridge_started = start_bridge_service()
                            if bridge_started:
# P0修复: 禁用WebSocket -                                 self.logger.success("✅ WebSocket到WeChat桥接服务启动成功")
                            else:
# P0修复: 禁用WebSocket -                                 self.logger.warning_status("⚠️ WebSocket到WeChat桥接服务启动失败，但不影响系统运行")
                                
                        except Exception as bridge_error:
# P0修复: 禁用WebSocket -                             self.logger.warning_status(f"⚠️ WebSocket到WeChat桥接服务启动异常: {bridge_error}")
                        
                except Exception as e:
# P0修复: 禁用WebSocket -                     self.logger.warning_status(f"⚠️ 统一WebSocket服务启动异常: {e}")
                """

                # 启动API服务（如果存在）
                if hasattr(self, 'api_service') and self.api_service:
                    if hasattr(self.api_service, 'start'):
                        if asyncio.iscoroutinefunction(self.api_service.start):
                            await self.api_service.start()
                        else:
                            self.api_service.start()
                        self.logger.success("API服务已启动")
                
                # 启动完成，发布系统启动事件
                if self.event_bus:
                    self.event_bus.publish("system.started", {
                        "timestamp": time.time(),
                        "uptime": self._format_uptime(time.time() - self.start_time),
                        "startup_time": time.time() - start_time,
                        "components": list(_initialized_components)
                    })
                
                # 标记为已启动
                self.is_running = True
                _system_started = True

                # 🔥 老王新增：启动神经网络触发系统
                try:
                    self._init_neural_trigger_system()
                    self.logger.success("✅ 神经网络触发系统启动完成")
                except Exception as trigger_error:
                    self.logger.warning_status(f"⚠️ 神经网络触发系统启动失败: {trigger_error}")

                self.logger.success(f"数字生命体系统启动完成 (耗时 {time.time() - start_time:.2f}秒)")
                return True
                
            except Exception as e:
                self.logger.error_status(f"系统启动失败: {e}")
                self.logger.error_status(traceback.format_exc())
                
                # 发布启动失败事件
                if self.event_bus:
                    self.event_bus.publish("system.start_failed", {
                        "timestamp": time.time(),
                        "error": str(e),
                        "traceback": traceback.format_exc()
                    })
                
                return False
    
    async def stop(self) -> bool:
        """停止数字生命体系统"""
        global _system_stopping, _system_started
        
        if _system_stopping:
            self.logger.warning_status("系统已在停止过程中")
            return True
        
        if not self.is_running:
            self.logger.warning_status("系统未在运行，无需停止")
            return True
        
        _system_stopping = True
        
        try:
            self.logger.info("开始停止数字生命体系统...")
            
            # 发布系统停止事件
            if self.event_bus:
                self.event_bus.publish("system.stopping", {
                    "timestamp": time.time()
                })
            
            # 🔥 修复：优雅关闭异步服务
            async_shutdown_tasks = []
            
            # 🔥 老王新增：停止神经网络触发系统
            try:
                if hasattr(self, 'neural_trigger_system') and self.neural_trigger_system:
                    self.neural_trigger_system.stop()
                    self.logger.info("神经网络触发系统已停止")
            except Exception as e:
                self.logger.error_status(f"停止神经网络触发系统时出错: {e}")

            # 停止主动表达服务
            try:
                from utilities.singleton_manager import get_silent
                proactive_service = get_silent("proactive_expression_service")
                if proactive_service and hasattr(proactive_service, 'shutdown'):
                    if asyncio.iscoroutinefunction(proactive_service.shutdown):
                        task = asyncio.create_task(proactive_service.shutdown())
                        async_shutdown_tasks.append(("proactive_expression_service", task))
                        self.logger.info("已创建主动表达服务关闭任务")
                    else:
                        proactive_service.shutdown()
                        self.logger.info("主动表达服务已停止")
            except Exception as e:
                self.logger.error_status(f"停止主动表达服务时出错: {e}")
            
            # 🔥 停止器官系统管理器
            try:
                from cognitive_modules.organ_system_manager import get_organ_system_manager
                organ_manager = get_organ_system_manager()
                if organ_manager:
                    # 尝试异步停止
                    try:
                        task = asyncio.create_task(organ_manager.stop_system_async())
                        async_shutdown_tasks.append(("organ_system_manager", task))
                        self.logger.info("已创建器官系统管理器关闭任务")
                    except Exception:
                        # 异步停止失败，使用同步停止
                        organ_manager.stop_system()
                        self.logger.info("器官系统管理器已同步停止")
            except Exception as e:
                self.logger.error_status(f"停止器官系统管理器时出错: {e}")
            
            # 🔥 停止通用调度器
            if hasattr(self, 'universal_scheduler') and self.universal_scheduler:
                try:
                    self.universal_scheduler.stop()
                    self.logger.success("通用调度器已停止")
                except Exception as e:
                    self.logger.error_status(f"停止通用调度器失败: {e}")
            
            # 停止增强调度器
            if hasattr(self, 'enhanced_scheduler') and self.enhanced_scheduler:
                try:
                    self.enhanced_scheduler.stop_system()
                    self.logger.success("增强调度器已停止")
                except Exception as e:
                    self.logger.error_status(f"停止增强调度器失败: {e}")
            
            # 停止数字生命体核心
            if self.digital_life:
                try:
                    if hasattr(self.digital_life, 'stop_system'):
                        # 增强调度器使用stop_system方法
                        self.digital_life.stop_system()
                        self.logger.info("数字生命体核心已停止")
                    elif hasattr(self.digital_life, 'stop'):
                        if asyncio.iscoroutinefunction(self.digital_life.stop):
                            task = asyncio.create_task(self.digital_life.stop())
                            async_shutdown_tasks.append(("digital_life", task))
                            self.logger.info("已创建数字生命体核心关闭任务")
                        else:
                            self.digital_life.stop()
                            self.logger.info("数字生命体核心已停止")
                    else:
                        self.logger.info("数字生命体核心无需停止")
                except Exception as e:
                    self.logger.error_status(f"停止数字生命体核心时出错: {e}")
            
            # 等待所有异步关闭任务完成
            if async_shutdown_tasks:
                self.logger.info(f"等待 {len(async_shutdown_tasks)} 个异步关闭任务完成...")
                try:
                    # 给异步任务3秒时间完成
                    tasks = [task for _, task in async_shutdown_tasks]
                    await asyncio.wait_for(asyncio.gather(*tasks, return_exceptions=True), timeout=3.0)
                    self.logger.info("所有异步关闭任务已完成")
                except asyncio.TimeoutError:
                    self.logger.warning_status("部分异步关闭任务超时，继续关闭流程")
                except Exception as e:
                    self.logger.error_status(f"等待异步关闭任务时出错: {e}")
            
            # 🔥 老王修复：停止延迟回复管理器
            try:
                # 优先从实例属性获取
                if hasattr(self, 'delayed_response_manager') and self.delayed_response_manager:
                    self.delayed_response_manager.stop_background_checker()
                    self.logger.success("✅ 延迟回复管理器后台任务已停止（实例属性）")
                else:
                    # 从单例管理器获取
                    from utilities.singleton_manager import get_silent
                    delayed_response_manager = get_silent("delayed_response_manager")
                    if delayed_response_manager and hasattr(delayed_response_manager, 'stop_background_checker'):
                        delayed_response_manager.stop_background_checker()
                        self.logger.success("✅ 延迟回复管理器后台任务已停止（单例管理器）")
                    else:
                        self.logger.info("延迟回复管理器未找到或无需停止")
            except Exception as e:
                self.logger.error_status(f"停止延迟回复管理器时出错: {e}")
            
            # 🔥 停止WeChat统一推送服务
            try:
                if hasattr(self, 'wechat_push_service') and self.wechat_push_service:
                    await self.wechat_push_service.stop()
                    self.logger.info("📱 WeChat统一推送服务已停止")
            except Exception as e:
                self.logger.error_status(f"停止WeChat统一推送服务时出错: {e}")
            
            # 🔥 WebSocket桥接服务已暂停（保留代码作为备用）
            """
            # WebSocket到WeChat桥接服务停止代码已暂停，保留作为备用
            try:
# P0修复: 禁用WebSocket -                 from services.websocket_wechat_bridge import stop_bridge_service
                stop_bridge_service()
# P0修复: 禁用WebSocket -                 self.logger.info("🌉 WebSocket到WeChat桥接服务已停止")
            except Exception as e:
# P0修复: 禁用WebSocket -                 self.logger.error_status(f"停止WebSocket到WeChat桥接服务时出错: {e}")
            """
# P0修复: 禁用WebSocket -             self.logger.info("🔄 WebSocket桥接服务已暂停，无需停止")
            
            # 清理资源 - 使用优化后的shutdown_all
            if hasattr(self, 'singleton_manager') and self.singleton_manager:
                from utilities.singleton_manager import shutdown_all
                shutdown_results = shutdown_all()
                self.logger.info(f"单例管理器已清理资源: {len(shutdown_results)} 个实例")
            
            # 🔥 给系统一点时间完成清理
            await asyncio.sleep(0.5)
            
            # 标记为已停止
            self.is_running = False
            _system_started = False
            _system_stopping = False
            
            self.logger.success("✅ 数字生命体系统已成功停止")
            return True
            
        except Exception as e:
            self.logger.error_status(f"停止系统时发生异常: {e}")
            _system_stopping = False
            return False
    
    def get_status(self) -> Dict[str, Any]:
        """获取系统状态信息"""
        # 格式化初始化时间
        formatted_init_times = {}
        for key, value in self.init_times.items():
            if isinstance(value, (int, float)) and key != 'total':
                formatted_init_times[key] = self._format_timestamp(value)
            else:
                formatted_init_times[key] = value
        
        return {
            "system_name": self.config.get("system", {}).get("name", "数字生命体系统"),
            "version": self.version,
            "initialized": self.initialized,
            "running": self.is_running,
            "current_time": self._get_current_time_str(),
            "start_time": self._format_timestamp(self.start_time),
            "uptime": self._format_uptime(time.time() - self.start_time),
            "components": list(_initialized_components),
            "init_times": formatted_init_times
        }
    
    def _format_uptime(self, seconds: float) -> str:
        """格式化运行时间"""
        hours = int(seconds // 3600)
        minutes = int((seconds % 3600) // 60)
        seconds = int(seconds % 60)
        
        if hours > 0:
            return f"{hours}小时{minutes}分钟{seconds}秒"
        elif minutes > 0:
            return f"{minutes}分钟{seconds}秒"
        else:
            return f"{seconds}秒"
    
    def _format_timestamp(self, timestamp: float = None) -> str:
        """格式化时间戳为标准时间格式 YYYY-MM-DD HH:MM:SS"""
        if timestamp is None:
            timestamp = time.time()

        dt = datetime.fromtimestamp(timestamp)
        return dt.strftime("%Y-%m-%d %H:%M:%S")
    
    def _get_current_time_str(self) -> str:
        """获取当前时间的标准格式字符串"""
        return self._format_timestamp()
    
    def _make_simple_response_decision(self, user_id: str, message: str, user_name: str = None) -> Dict[str, Any]:
        """
        简化的响应决策逻辑（完全同步）
        
        Args:
            user_id: 用户ID
            message: 用户消息
            user_name: 用户名称
            
        Returns:
            决策结果字典
        """
        try:
            from datetime import datetime
            
            self.logger.debug(f"🔧 使用简化响应决策 (用户: {user_id})")
            
            # 基于时间的决策
            now = datetime.now()
            current_hour = now.hour
            
            # 深夜或早晨时间 (23:00-7:00)
            if current_hour >= 23 or current_hour < 7:
                delay_hours = 7 - current_hour if current_hour < 7 else (24 - current_hour + 7)
                return {
                    "decision": "reply_later",
                    "reason": "深夜时间，延迟回复",
                    "delay_hours": delay_hours,
                    "confidence": 0.8
                }
            
            # 午休时间 (12:30-13:30)
            if current_hour == 12 and now.minute >= 30:
                return {
                    "decision": "reply_later",
                    "reason": "午休时间，延迟回复",
                    "delay_hours": 0.5,
                    "confidence": 0.7
                }
            
            if current_hour == 13 and now.minute < 30:
                delay_minutes = 30 - now.minute
                return {
                    "decision": "reply_later",
                    "reason": "午休时间，延迟回复",
                    "delay_hours": delay_minutes / 60.0,
                    "confidence": 0.7
                }
            
            # 检查紧急关键词
            urgent_keywords = ["紧急", "急", "救命", "帮忙", "重要", "马上", "快", "立即"]
            if any(keyword in message for keyword in urgent_keywords):
                return {
                    "decision": "reply_now",
                    "reason": "检测到紧急关键词",
                    "delay_hours": 0,
                    "confidence": 0.9
                }
            
            # 检查问候语
            greeting_keywords = ["你好", "hi", "hello", "早上好", "晚上好", "在吗", "在不在"]
            if any(keyword in message.lower() for keyword in greeting_keywords):
                return {
                    "decision": "reply_now",
                    "reason": "问候语，立即回复",
                    "delay_hours": 0,
                    "confidence": 0.8
                }
            
            # 检查简单回复
            simple_replies = ["好的", "嗯", "哦", "ok", "👍", "😊", "谢谢", "再见"]
            if any(keyword in message.lower() for keyword in simple_replies):
                # 简单回复可能延迟一点
                return {
                    "decision": "reply_later",
                    "reason": "简单回复，稍后回复",
                    "delay_hours": 0.1,  # 6分钟后回复
                    "confidence": 0.6
                }
            
            # 默认立即回复
            return {
                "decision": "reply_now",
                "reason": "正常时间，立即回复",
                "delay_hours": 0,
                "confidence": 0.7
            }
            
        except Exception as e:
            self.logger.error(f"简化响应决策失败: {e}")
            return {
                "decision": "reply_now",
                "reason": "决策异常，默认立即回复",
                "delay_hours": 0,
                "confidence": 0.5
            }
    
    def _store_delayed_response_sync(self, user_id: str, message: str, delay_hours: float, context: Dict[str, Any]):
        """
        同步存储延迟回复信息到temp_messages表

        Args:
            user_id: 用户ID
            message: 用户消息
            delay_hours: 延迟小时数
            context: 上下文信息，包含isgroup, from_user_ID, _token, _appid等完整参数
        """
        try:
            import json
            from datetime import datetime, timedelta

            # 🔥 新增：参数验证和日志记录，确保数据完整性
            self.logger.debug(f"🔄 存储延迟回复: user_id={user_id}, delay_hours={delay_hours}")
            self.logger.debug(f"📋 Context参数: {json.dumps(context, ensure_ascii=False, default=str)}")
            
            # 🔥 新增：存储到数据库temp_messages表
            try:
                from connectors.database.mysql_connector import get_mysql_connector
                mysql_connector = get_mysql_connector()
                
                # 🔥 修复：构建temp_messages表的插入数据，确保数据统一性
                insert_data = {
                    "user_id": user_id,
                    "temp_message": message,
                    "message_time": datetime.now(),
                    "reply_delay": delay_hours,
                    # 🔥 修复：确保isgroup正确的布尔转换
                    "isgroup": 1 if bool(context.get("isgroup", False)) else 0,
                    "session_id": context.get("session_id", ""),
                    # 🔥 修复：正确映射from_user_ID到from_user字段
                    "from_user": context.get("from_user_ID", "wxid_ezkohzfp3xqp12"),
                    # 🔥 关键修复：使用API传入的实际_token和_appid，而不是硬编码
                    "_token": context.get("_token", "12c4521a-7270-4553-a2ac-3af10244a35b"),
                    "_appid": context.get("_appid", "wx_574bN70yW1q0vTSFaaSHU")
                }

                # 🔥 新增：数据验证和详细日志
                self.logger.debug(f"📊 temp_messages插入数据:")
                for key, value in insert_data.items():
                    self.logger.debug(f"   - {key}: {value} ({type(value).__name__})")

                # 🔥 验证必需字段
                required_fields = ["user_id", "temp_message", "_token", "_appid"]
                for field in required_fields:
                    if not insert_data.get(field):
                        raise ValueError(f"必需字段 {field} 为空或缺失")
                
                # 🔥 老王修复：检查同user_id是否已存在，实现消息合并逻辑
                check_query = "SELECT temp_message FROM temp_messages WHERE user_id = %s"
                check_success, existing_record, check_error = mysql_connector.query_one(check_query, (user_id,))

                if check_success and existing_record:
                    # 用户已存在，合并消息内容
                    existing_message = existing_record.get("temp_message", "")
                    merged_message = f"{existing_message}||{message}" if existing_message else message

                    # 更新现有记录
                    update_query = """
                        UPDATE temp_messages
                        SET temp_message = %s,
                            message_time = %s,
                            reply_delay = %s,
                            isgroup = %s,
                            session_id = %s,
                            from_user = %s,
                            _token = %s,
                            _appid = %s
                        WHERE user_id = %s
                    """
                    update_params = (
                        merged_message,
                        insert_data["message_time"],
                        insert_data["reply_delay"],
                        insert_data["isgroup"],
                        insert_data["session_id"],
                        insert_data["from_user"],
                        insert_data["_token"],
                        insert_data["_appid"],
                        user_id
                    )

                    success, affected_rows, error = mysql_connector.execute_update(update_query, update_params)
                    operation_type = "合并更新"
                    self.logger.info(f"🔄 同用户ID消息合并: {user_id}")
                else:
                    # 用户不存在，插入新记录
                    columns = ", ".join(insert_data.keys())
                    placeholders = ", ".join(["%s"] * len(insert_data))
                    insert_query = f"INSERT INTO temp_messages ({columns}) VALUES ({placeholders})"
                    insert_params = tuple(insert_data.values())

                    success, affected_rows, error = mysql_connector.execute_update(insert_query, insert_params)
                    operation_type = "新增插入"

                if not success:
                    raise Exception(f"数据库{operation_type}失败: {error}")

                # 🔥 增强：详细的成功日志，包含关键参数信息
                decision_type = context.get("decision_type", "unknown")
                self.logger.success(f"✅ 消息已{operation_type}到temp_messages表:")
                self.logger.success(f"   - 用户: {user_id} ({context.get('user_name', 'Unknown')})")
                self.logger.success(f"   - 延迟: {delay_hours}小时")
                self.logger.success(f"   - 决策类型: {decision_type}")
                self.logger.success(f"   - 群聊: {'是' if insert_data['isgroup'] else '否'}")
                self.logger.success(f"   - 操作类型: {operation_type}")
                self.logger.success(f"   - Token: {insert_data['_token'][:20]}...")
                self.logger.success(f"   - AppID: {insert_data['_appid']}")
                
            except Exception as db_e:
                self.logger.warning(f"⚠️ 数据库存储失败，使用文件备份: {db_e}")
                # 如果数据库存储失败，回退到文件存储
                self._store_delayed_response_to_file(user_id, message, delay_hours, context)
            
        except Exception as e:
            self.logger.error(f"同步存储延迟回复失败: {e}")
            raise
    
    def _store_delayed_response_to_file(self, user_id: str, message: str, delay_hours: float, context: Dict[str, Any]):
        """
        回退方案：存储延迟回复到文件
        """
        try:
            import json
            from datetime import datetime, timedelta
            
            # 计算回复时间
            reply_time = datetime.now() + timedelta(hours=delay_hours)
            
            # 构建延迟回复记录
            delayed_record = {
                "user_id": user_id,
                "message": message,
                "delay_hours": delay_hours,
                "created_at": datetime.now().isoformat(),
                "reply_at": reply_time.isoformat(),
                "context": context,
                "status": "pending"
            }
            
            # 存储到文件（简化版本）
            delayed_responses_file = os.path.join(current_dir, "data", "delayed_responses.json")
            
            # 确保目录存在
            os.makedirs(os.path.dirname(delayed_responses_file), exist_ok=True)
            
            # 读取现有记录
            delayed_responses = []
            if os.path.exists(delayed_responses_file):
                try:
                    with open(delayed_responses_file, 'r', encoding='utf-8') as f:
                        delayed_responses = json.load(f)
                except Exception as e:
                    self.logger.warning(f"读取延迟回复文件失败: {e}")
                    delayed_responses = []
            
            # 添加新记录
            delayed_responses.append(delayed_record)
            
            # 保持最近100条记录
            if len(delayed_responses) > 100:
                delayed_responses = delayed_responses[-100:]
            
            # 写回文件
            with open(delayed_responses_file, 'w', encoding='utf-8') as f:
                json.dump(delayed_responses, f, ensure_ascii=False, indent=2)
            
            self.logger.debug(f"✅ 延迟回复记录已保存到文件: {delayed_responses_file}")
            
        except Exception as e:
            self.logger.error(f"文件存储延迟回复失败: {e}")
            raise
    
    async def _integrate_digital_life_with_wechat_push(self):
        """🔥 核心集成：数字生命与WeChat推送服务集成"""
        try:
            self.logger.success("🔗 开始集成数字生命与WeChat推送服务...")
            
            # 1. 设置数字生命的输出重定向
            if hasattr(self.digital_life, 'set_output_handler'):
                await self.digital_life.set_output_handler(self._digital_life_output_handler)
                self.logger.success("✅ 数字生命输出重定向已设置")
            
            # 2. 启动增强版打招呼技能
            try:
                from cognitive_modules.skills.enhanced_greeting_skill import get_enhanced_greeting_skill
                self.enhanced_greeting_skill = get_enhanced_greeting_skill()
                await self.enhanced_greeting_skill.initialize()
                
                # 注册到单例管理器
                from utilities.singleton_manager import register
                register("enhanced_greeting_skill", self.enhanced_greeting_skill)
                
                self.logger.success("✅ 增强版打招呼技能已启动")
            except Exception as greeting_error:
                self.logger.warning(f"⚠️ 增强版打招呼技能启动失败: {greeting_error}")
            
            # 3. 启动财经报告服务
            try:
                from services.enhanced_financial_report_service import get_enhanced_financial_report_service
                self.financial_report_service = get_enhanced_financial_report_service()
                await self.financial_report_service.initialize()
                
                # 注册到单例管理器
                register("financial_report_service", self.financial_report_service)
                
                self.logger.success("✅ 增强版财经报告服务已启动")
            except Exception as financial_error:
                self.logger.warning(f"⚠️ 增强版财经报告服务启动失败: {financial_error}")
            
            # 4. 启动定时任务调度器（如果尚未启动）
            try:
                if not hasattr(self, 'wechat_scheduler') or self.wechat_scheduler is None:
                    from services.wechat_scheduler_service import get_wechat_scheduler_service
                    self.wechat_scheduler = get_wechat_scheduler_service()
                    
                    # 🔥 老王修复：先初始化注册任务，再启动调度器
                    await self.wechat_scheduler.initialize()
                    await self.wechat_scheduler.start()
                    
                    # 注册到单例管理器
                    register("wechat_scheduler", self.wechat_scheduler)
                    
                    self.logger.success("✅ WeChat定时任务调度器已初始化并启动")
                    
                    # 🔥 启动emotions智能更新服务
                    try:
                        from services.emotions_intelligence_updater import get_emotions_intelligence_updater
                        self.emotions_updater = get_emotions_intelligence_updater(self.config)
                        await self.emotions_updater.start()
                        self.logger.success("✅ Emotions智能更新服务启动完成")
                    except Exception as e:
                        self.logger.error(f"❌ Emotions智能更新服务启动失败: {e}")
                else:
                    self.logger.info("✅ WeChat定时任务调度器已存在，跳过重复初始化")
            except Exception as scheduler_error:
                self.logger.warning(f"⚠️ WeChat定时任务调度器启动失败: {scheduler_error}")
            
            # 5. 注册消息处理回调
            if hasattr(self.wechat_push_service, 'register_message_processor'):
                self.wechat_push_service.register_message_processor(self._process_wechat_message)
                self.logger.success("✅ WeChat消息处理回调已注册")
            
            self.logger.success("🎉 数字生命与WeChat推送服务集成完成")
            
        except Exception as e:
            self.logger.error(f"❌ 数字生命与WeChat推送服务集成失败: {e}")
    
    async def _digital_life_output_handler(self, message_data: dict):
        """数字生命输出处理器 - 重定向到WeChat推送"""
        try:
            if not self.wechat_push_service:
                return
            
            # 构建推送消息
            from services.wechat_unified_push_service import PushMessage
            
            message_type = message_data.get("type", "chat_response")
            content = message_data.get("content", "")
            target_user_id = message_data.get("user_id", "")
            
            push_message = PushMessage(
                message_type=message_type,
                content=content,
                target_user_id=target_user_id,
                message_level="user",
                priority="normal",
                metadata={
                    "source": "digital_life_core",
                    "original_data": message_data,
                    "timestamp": time.time()
                }
            )
            
            # 推送消息
            await self.wechat_push_service.push_message(push_message)
            
        except Exception as e:
            self.logger.error(f"数字生命输出处理失败: {e}")
    
    async def _process_wechat_message(self, message_data: dict):
        """处理WeChat消息的回调"""
        try:
            # 将WeChat消息转发给数字生命核心处理
            if self.digital_life and hasattr(self.digital_life, 'process_message'):
                await self.digital_life.process_message(message_data)
        except Exception as e:
            self.logger.error(f"处理WeChat消息失败: {e}")

    def _validate_api_data(self, user_id: str, user_name: str, user_sex) -> Dict[str, Any]:
        """
        简化API数据验证 - API chat入口的用户信息就是标准，上游系统已经做过校验

        Args:
            user_id: 用户ID
            user_name: 用户名
            user_sex: 用户性别 (支持字符串或整数)

        Returns:
            验证结果
        """
        # 🔥 老王修复：简化验证逻辑，API数据即标准

        # 1. 基本空值检查
        if not user_id or str(user_id).strip() == "":
            return {"valid": False, "error": "用户ID不能为空"}

        # 2. 基本空值检查 - 用户名可以为空，系统会处理
        if user_name is None:
            user_name = ""

        # 3. 性别标准化处理
        user_sex_str = str(user_sex) if user_sex is not None else "0"

        # 4. 记录接收到的API数据（不再进行有效性质疑）
        self.logger.info(f"✅ 接收API数据: user_id={user_id}, user_name={user_name}, user_sex={user_sex_str}")

        return {"valid": True, "error": None}

    def _verify_saved_data(self, user_id: str, user_name: str, user_sex: str) -> bool:
        """
        验证数据是否正确保存到各个存储系统，验证失败时自动修复

        Args:
            user_id: 用户ID
            user_name: 用户名
            user_sex: 用户性别

        Returns:
            验证是否通过
        """
        # 🔥 老王修复：验证API数据是否正确保存，失败时自动修复
        verification_passed = True

        try:
            # 1. 验证contacts.json中的数据
            from core.contacts_manager import get_contacts_manager
            contacts_manager = get_contacts_manager()  # 🔥 老王修复：使用单例工厂函数，避免重复初始化
            contact_info = contacts_manager.get_user_info(user_id)

            if contact_info:
                saved_nickname = contact_info.get("nickname")
                if saved_nickname == user_name:
                    self.logger.info(f"✅ contacts.json验证通过: {user_id} -> {saved_nickname}")
                else:
                    self.logger.warning(f"⚠️ contacts.json数据不一致: 期望={user_name}, 实际={saved_nickname}")
                    # 🔥 老王修复：数据不一致时自动更新
                    try:
                        contacts_manager.update_user(user_id, nickname=user_name, sex=user_sex)
                        self.logger.info(f"✅ 已自动更新contacts.json: {user_id} -> {user_name}")
                    except Exception as update_e:
                        self.logger.error(f"❌ 自动更新contacts.json失败: {update_e}")
                        verification_passed = False
            else:
                self.logger.warning(f"⚠️ contacts.json中未找到用户: {user_id}")
                # 🔥 老王修复：用户不存在时自动写入
                try:
                    contacts_manager.ensure_user_exists(user_id, user_name, user_sex)
                    self.logger.info(f"✅ 已自动写入contacts.json: {user_id} -> {user_name}")
                except Exception as create_e:
                    self.logger.error(f"❌ 自动写入contacts.json失败: {create_e}")
                    verification_passed = False

        except Exception as e:
            self.logger.error(f"❌ contacts.json验证失败: {e}")
            verification_passed = False

        try:
            # 2. 验证unified_user_manager中的数据
            from core.unified_user_manager import get_unified_user_manager
            user_manager = get_unified_user_manager()

            if user_manager:
                user = user_manager.get_user(user_id)
                if user:
                    saved_name = user.name or user.nickname
                    if saved_name == user_name:
                        self.logger.info(f"✅ unified_user_manager验证通过: {user_id} -> {saved_name}")
                    else:
                        self.logger.error(f"❌ unified_user_manager数据不一致: 期望={user_name}, 实际={saved_name}")
                        verification_passed = False
                else:
                    self.logger.error(f"❌ unified_user_manager中未找到用户: {user_id}")
                    verification_passed = False
            else:
                self.logger.warning("⚠️ unified_user_manager不可用，跳过验证")

        except Exception as e:
            self.logger.error(f"❌ unified_user_manager验证失败: {e}")
            verification_passed = False

        # 3. 记录验证结果
        if verification_passed:
            self.logger.info(f"✅ 数据保存验证通过: {user_id} -> {user_name}")
        else:
            self.logger.error(f"❌ 数据保存验证失败: {user_id} -> {user_name}")
            self.logger.error(f"❌ API数据未正确保存，可能影响后续处理")

        return verification_passed

    def _force_sync_user_data(self, user_id: str, user_name: str, user_sex: str) -> bool:
        """
        强制同步用户数据到所有存储系统

        Args:
            user_id: 用户ID
            user_name: 用户名
            user_sex: 用户性别

        Returns:
            同步是否成功
        """
        # 🔥 老王修复：强制同步用户数据到所有存储系统
        self.logger.info(f"🔄 强制同步用户数据: {user_id} -> {user_name}")
        sync_success = True

        # 1. 强制更新contacts.json
        try:
            from core.contacts_manager import get_contacts_manager
            contacts_manager = get_contacts_manager()  # 🔥 老王修复：使用单例工厂函数

            # 检查用户是否存在
            existing_info = contacts_manager.get_user_info(user_id)
            if existing_info:
                # 用户存在，强制更新
                contacts_manager.update_user(user_id, nickname=user_name, sex=str(user_sex))
                self.logger.info(f"✅ 强制更新contacts.json: {user_id} -> {user_name}")
            else:
                # 用户不存在，创建新用户
                contacts_manager.register_user(user_id, nickname=user_name, sex=str(user_sex))
                self.logger.info(f"✅ 创建contacts.json记录: {user_id} -> {user_name}")

        except Exception as e:
            self.logger.error(f"❌ 强制同步contacts.json失败: {e}")
            sync_success = False

        # 2. 强制更新Redis缓存
        try:
            from adapters.legacy_adapter import get_instance as get_legacy_adapter
            legacy_adapter = get_legacy_adapter()

            if legacy_adapter and hasattr(legacy_adapter, 'redis') and legacy_adapter.redis:
                # 更新Redis中的用户信息
                user_cache_key = f"user:{user_id}"
                user_data = {
                    "id": user_id,
                    "name": user_name,
                    "nickname": user_name,
                    "sex": str(user_sex),
                    "last_updated": time.time()
                }

                # 设置缓存，过期时间24小时
                legacy_adapter.redis.setex(user_cache_key, 86400, json.dumps(user_data))
                self.logger.info(f"✅ 强制更新Redis缓存: {user_id} -> {user_name}")
            else:
                self.logger.warning("⚠️ Redis连接不可用，跳过缓存更新")

        except Exception as e:
            self.logger.error(f"❌ 强制同步Redis缓存失败: {e}")
            sync_success = False

        # 3. 强制更新MySQL users表
        try:
            from adapters.legacy_adapter import get_instance as get_legacy_adapter
            legacy_adapter = get_legacy_adapter()

            if legacy_adapter and hasattr(legacy_adapter, 'mysql') and legacy_adapter.mysql and legacy_adapter.mysql.is_available:
                # 检查用户是否存在
                check_query = "SELECT id FROM users WHERE id = %s"
                success, result, error = legacy_adapter.mysql.query_one(check_query, (user_id,))

                if success and result:
                    # 用户存在，更新
                    update_query = "UPDATE users SET name = %s, sex = %s, updated_at = NOW() WHERE id = %s"
                    success, affected_rows, error = legacy_adapter.mysql.execute_update(
                        update_query, (user_name, str(user_sex), user_id)
                    )
                    if success:
                        self.logger.info(f"✅ 强制更新MySQL users表: {user_id} -> {user_name}")
                    else:
                        self.logger.error(f"❌ 更新MySQL users表失败: {error}")
                        sync_success = False
                else:
                    # 用户不存在，插入新记录
                    insert_query = """
                        INSERT INTO users (id, name, sex, created_at, updated_at)
                        VALUES (%s, %s, %s, NOW(), NOW())
                        ON DUPLICATE KEY UPDATE name = VALUES(name), sex = VALUES(sex), updated_at = NOW()
                    """
                    success, affected_rows, error = legacy_adapter.mysql.execute_update(
                        insert_query, (user_id, user_name, str(user_sex))
                    )
                    if success:
                        self.logger.info(f"✅ 创建MySQL users表记录: {user_id} -> {user_name}")
                    else:
                        self.logger.error(f"❌ 创建MySQL users表记录失败: {error}")
                        sync_success = False
            else:
                self.logger.warning("⚠️ MySQL连接不可用，跳过数据库更新")

        except Exception as e:
            self.logger.error(f"❌ 强制同步MySQL失败: {e}")
            sync_success = False

        # 4. 记录同步结果
        if sync_success:
            self.logger.info(f"✅ 用户数据强制同步完成: {user_id} -> {user_name}")
        else:
            self.logger.error(f"❌ 用户数据强制同步失败: {user_id} -> {user_name}")

        return sync_success

    def _check_data_consistency(self, user_id: str) -> Dict[str, Any]:
        """
        检查用户数据在各个存储系统中的一致性

        Args:
            user_id: 用户ID

        Returns:
            一致性检查结果
        """
        # 🔥 老王修复：检查数据一致性
        result = {
            "consistent": True,
            "sources": {},
            "conflicts": []
        }

        try:
            # 1. 从contacts.json获取数据
            from core.contacts_manager import get_contacts_manager
            contacts_manager = get_contacts_manager()  # 🔥 老王修复：使用单例工厂函数
            contact_info = contacts_manager.get_user_info(user_id)
            if contact_info:
                result["sources"]["contacts"] = contact_info.get("nickname")

            # 2. 从unified_user_manager获取数据
            from core.unified_user_manager import get_unified_user_manager
            user_manager = get_unified_user_manager()
            if user_manager:
                user = user_manager.get_user(user_id)
                if user:
                    result["sources"]["unified"] = user.name or user.nickname

            # 3. 检查一致性
            sources = result["sources"]
            if len(sources) > 1:
                values = list(sources.values())
                if len(set(values)) > 1:
                    result["consistent"] = False
                    result["conflicts"] = [f"{k}={v}" for k, v in sources.items()]
                    self.logger.warning(f"⚠️ 用户 {user_id} 数据不一致: {result['conflicts']}")
                else:
                    self.logger.debug(f"✅ 用户 {user_id} 数据一致: {values[0]}")

        except Exception as e:
            self.logger.error(f"❌ 数据一致性检查失败: {e}")
            result["consistent"] = False
            result["error"] = str(e)

        return result

    def enhanced_process_message(self, user_input, user_id=None, user_name=None,
                             user_sex=None, isgroup=0, session_id=None, is_Segment=0,
                             from_user_ID="wxid_ezkohzfp3xqp12", _token="12c4521a-7270-4553-a2ac-3af10244a35b", 
                             _appid="wx_574bN70yW1q0vTSFaaSHU"):
        """
        增强的消息处理接口，支持旧体系兼容性和多用户并发
        
        Args:
            user_input: 用户输入
            user_id: 用户ID
            user_name: 用户名称
            user_sex: 用户性别(1男/2女/0未知)
            isgroup: 是否群聊(0否/1是)
            session_id: 群聊ID
            is_Segment: 是否分段
            from_user_ID: 来源用户ID
            _token: 身份令牌
            _appid: 应用ID
            
        Returns:
            处理结果
        """
        # 🔥 增强版：请求级别数据隔离机制
        import uuid
        import threading
        import contextvars

        request_id = f"req_{uuid.uuid4().hex[:12]}"
        thread_id = threading.current_thread().ident
        request_start_time = time.time()

        # 🔥 新增：使用contextvars确保线程安全的上下文隔离
        request_context_var = contextvars.ContextVar('request_context')

        # 🔥 创建请求级别的隔离上下文
        request_context = {
            "request_id": request_id,
            "thread_id": thread_id,
            "start_time": request_start_time,
            "user_id": user_id,
            "user_name": user_name,
            "isolated_data": {},  # 请求级别的隔离数据存储
            "processing_stage": "initialization",
            "context_token": request_context_var.set({})  # 🔥 新增：上下文令牌
        }
        
        # 🔥 请求追踪日志
        self.logger.info(f"🚀 [{request_id}] 开始处理用户请求: user_id={user_id}, thread={thread_id}")
        
        try:
            # 🔥 导入所有必需的模块（避免重复导入问题）
            import asyncio
            import threading
            import concurrent.futures
            from datetime import datetime
            
            # 🔥 初始化默认变量，防止变量未定义错误
            unified_user = None
            unified_session = None
            is_new_user = False
            is_new_session = False
            perception_context = {}
            session_id_locked = False  # 🔥 修复问题2：初始化session_id_locked变量
            final_user_name = user_name  # 🔥 P0级别修复：初始化final_user_name，避免thinking_chain中的用户名获取失败
            
            # 🔥 参数验证 - 严格验证用户信息，不允许空值或default_user
            if user_id is None or user_id.strip() == "" or user_id == "default_user":
                error_msg = f"⚠️ [{request_id}] API调用必须提供有效的user_id参数，不允许空值或default_user"
                self.logger.error(error_msg)
                return error_msg  # 返回字符串而不是字典，保持接口一致性
            
            # 🔥 修复问题2: 防止用户ID丢失，保持原始用户ID不变
            original_user_id = user_id
            request_context["original_user_id"] = original_user_id
            request_context["processing_stage"] = "parameter_validation"
            
            self.logger.info(f"📥 [{request_id}] 接收到用户请求: user_id={original_user_id}, user_name={user_name}")
            
            # 🔥 老王修复：严格的API数据验证
            validation_result = self._validate_api_data(user_id, user_name, user_sex)
            if not validation_result["valid"]:
                self.logger.error(f"❌ API数据验证失败: {validation_result['error']}")
                return {"error": validation_result["error"], "code": 400}

            # 其他参数的默认值设置（非关键数据）
            if user_sex is None:
                user_sex = "0"  # 未知性别
            if isgroup is None:
                isgroup = 0
            if is_Segment is None:
                is_Segment = 0
            if _token is None:
                _token = "12c4521a-7270-4553-a2ac-3af10244a35b"
            if _appid is None:
                _appid = "wx_574bN70yW1q0vTSFaaSHU"
            
            # 🔥 初始化元数据字典（在使用前定义）
            metadata = {
                "user_id": user_id,
                "user_name": user_name,
                "user_sex": user_sex,
                "isgroup": isgroup,
                "session_id": session_id,
                "is_Segment": is_Segment,
                "from_user_ID": from_user_ID,
                "token": _token,
                "appid": _appid,
                "source": "api_adapter",
                "timestamp": time.time(),
                "system_integration_version": "v2.1"
            }
            
            # 确保参数正确性
            if user_input is None or user_input.strip() == "":
                return "请输入有效的消息内容"
            
            # 标准化from_user_ID - 只在为空时使用user_id
            if not from_user_ID:
                from_user_ID = user_id
            
            # 🔥 修复问题3: 使用统一用户身份管理器替代分散的用户管理
            try:
                from core.unified_user_manager import get_unified_user_manager
                unified_user_manager = get_unified_user_manager()
                
                # 🔥 增强版：统一处理用户请求，确保完全隔离
                user_request_data = unified_user_manager.process_user_request(
                    user_id=user_id,
                    session_id=session_id,
                    user_name=user_name,
                    user_sex=str(user_sex),
                    message_length=len(user_input),
                    isgroup=bool(isgroup),
                    from_user_ID=from_user_ID,
                    _token=_token,
                    _appid=_appid,
                    session_id_locked=session_id_locked,
                    request_context=request_context  # 🔥 新增：传递请求上下文确保隔离
                )
                
                # 获取统一的用户和会话信息
                unified_user = user_request_data['user']
                unified_session = user_request_data['session']
                is_new_user = user_request_data['is_new_user']
                is_new_session = user_request_data['is_new_session']
                
                # 🔥 关键修复：确保用户名优先使用API传入的参数，避免被统一管理器覆盖
                if user_name and user_name.strip() and user_name != "神秘嘉宾":
                    # API传入了有效的用户名，检查是否需要更新
                    current_name = unified_user.name if unified_user else None
                    current_nickname = unified_user.nickname if unified_user else None

                    # 🔥 老王修复：检测用户信息变更，强制同步所有存储系统
                    name_changed = current_name != user_name
                    nickname_changed = current_nickname != user_name

                    if name_changed or nickname_changed:
                        self.logger.info(f"🔄 检测到用户信息变更: {current_name} -> {user_name}")

                        # 更新统一用户管理器
                        unified_user.name = user_name
                        unified_user.nickname = user_name

                        # 🔥 强制同步到所有存储系统
                        self._force_sync_user_data(user_id, user_name, user_sex)

                        # 同步更新到管理器
                        unified_user_manager.update_user(user_id, name=user_name, nickname=user_name)

                    # 🔥 确保API传入的用户名被正确传递到后续处理
                    final_user_name = user_name
                    self.logger.info(f"✅ 使用API传入的用户名: {final_user_name}")
                else:
                    # 使用统一用户管理器中的用户名
                    final_user_name = unified_user.name if unified_user else user_name
                    self.logger.info(f"✅ 使用统一管理器的用户名: {final_user_name}")

                self.logger.info(f"✅ 确定的最终用户名: {final_user_name}")

                # 🔥 老王修复：验证数据是否正确保存
                self._verify_saved_data(user_id, final_user_name, user_sex)

                # 更新元数据
                metadata.update({
                    'unified_user_id': unified_user.user_id,
                    'unified_user_name': final_user_name,  # 使用最终确定的用户名
                    'unified_session_id': unified_session.session_id,
                    'is_new_user': is_new_user,
                    'is_new_session': is_new_session,
                    'user_status': unified_user.status.value,
                    'interaction_count': unified_user.interaction_count
                })
                
                # 🔥 修复：如果session_id被锁定，则不更新
                if not session_id_locked:
                    session_id = unified_session.session_id
                else:
                    # 确保统一管理器使用API传入的session_id
                    if unified_session.session_id != session_id:
                        self.logger.info(f"🔗 统一管理器session_id与API传入不一致，保持API传入: {session_id}")
                        # 这里可以考虑更新统一管理器的session_id，但目前保持API传入的优先级
                
                self.logger.success(f"统一用户管理: {unified_user.user_id} ({unified_user.name}) - "
                                  f"{'新用户' if is_new_user else '现有用户'}, "
                                  f"{'新会话' if is_new_session else '现有会话'}")
                    
            except Exception as e:
                self.logger.error(f"统一用户管理异常: {e}")
                # 不影响主流程，继续处理
            
            self.logger.info(f"收到用户消息: {user_input[:30]}... [用户:{user_id}/{user_name}]")
            
            # 🔥 修复问题3: session_id管理统一化
            # 确保session_id唯一来源于API入口，不允许后续重复生成
            if session_id is None or session_id.strip() == "":
                # 只有在API入口处生成session_id，后续处理不再生成
                session_id = f"session_{user_id}_{int(time.time())}"
                self.logger.info(f"🔗 API入口生成新session_id: {session_id}")
            else:
                self.logger.info(f"🔗 使用传入的session_id: {session_id}")
            
            # 🔥 关键：将session_id标记为已确定，禁止后续重复生成
            session_id_locked = True
            
            # 🔥 新增：获取感知上下文
            try:
                from core.perception_feedback_processor import get_perception_feedback_processor
                processor = get_perception_feedback_processor()
                perception_context = processor.get_perception_context_for_dialogue(user_input, user_id)
                self.logger.debug(f"获取感知上下文成功，包含 {len(perception_context.get('recent_perceptions', []))} 个感知结果")
            except Exception as e:
                self.logger.warning(f"获取感知上下文失败: {e}")
                perception_context = {}
            
            # 🔥 新增：数据流处理 (修复问题5: 数据流一致性和完整性)
            try:
                from core.unified_data_flow_manager import get_unified_data_flow_manager, DataFlowType, DataPriority
                data_flow_manager = get_unified_data_flow_manager()
                
                # 创建用户输入数据包
                user_input_packet = data_flow_manager.create_packet(
                    flow_type=DataFlowType.USER_INPUT,
                    data={
                        'user_id': user_id,
                        'content': user_input,
                        'timestamp': datetime.now().isoformat(),
                        'session_id': session_id,
                        'perception_context': perception_context
                    },
                    source='main_process',
                    priority=DataPriority.HIGH
                )
                
                # 🔥 修复：检查事件循环状态，避免"no running event loop"错误
                try:
                    # 尝试获取当前运行的事件循环
                    # 🔥 老王修复：正确获取事件循环
                    current_loop = asyncio.get_running_loop()
                    if current_loop.is_running():
                        # 检查当前线程是否是Flask API线程
                        current_thread_name = threading.current_thread().name
                        
                        if "Flask" in current_thread_name or "werkzeug" in current_thread_name.lower():
                            # API模式：使用线程池异步执行，避免阻塞Flask
                            def run_data_flow_in_thread():
                                try:
                                    new_loop = asyncio.new_event_loop()
                                    asyncio.set_event_loop(new_loop)
                                    new_loop.run_until_complete(data_flow_manager.process_packet(user_input_packet))
                                    new_loop.close()
                                    self.logger.debug("API模式：数据流处理完成")
                                except Exception as e:
                                    self.logger.warning(f"API模式数据流处理异常: {e}")
                            
                            # 使用线程池执行
                            if not hasattr(self, '_data_flow_executor'):
                                self._data_flow_executor = concurrent.futures.ThreadPoolExecutor(
                                    max_workers=4, thread_name_prefix="DataFlow"
                                )
                            self._data_flow_executor.submit(run_data_flow_in_thread)
                            
                        else:
                            # CLI模式或其他模式：直接在当前事件循环中创建任务
                            current_loop.create_task(data_flow_manager.process_packet(user_input_packet))
                            self.logger.debug("在现有事件循环中处理数据包")
                    else:
                        # 事件循环存在但未运行，跳过异步处理
                        self.logger.debug("事件循环未运行，跳过数据流异步处理")
                except RuntimeError:
                    # 没有运行的事件循环，使用同步方式或跳过
                    try:
                        # 尝试同步处理数据包（如果有同步方法）
                        if hasattr(data_flow_manager, 'process_packet_sync'):
                            data_flow_manager.process_packet_sync(user_input_packet)
                            self.logger.debug("使用同步方式处理数据包")
                        else:
                            self.logger.debug("跳过数据流处理（无可用的事件循环）")
                    except Exception as sync_e:
                        self.logger.debug(f"同步数据流处理失败: {sync_e}")
                
            except Exception as e:
                self.logger.warning(f"数据流处理失败: {e}")
            
            # 🔥 新增：延迟回复决策处理（保持完整智能决策，但避免事件循环问题）
            try:
                self.logger.debug(f"🔄 开始延迟回复决策处理 (用户: {user_id})")
                
                # 🔥 修复：在try-catch中安全获取延迟回复管理器和决策系统
                try:
                    from services.delayed_response_manager import get_instance as get_delayed_response_manager
                    from cognitive_modules.decision.yanran_response_decision import get_instance as get_response_decision
                    
                    # 获取延迟回复管理器和决策系统
                    delayed_response_manager = get_delayed_response_manager()
                    response_decision = get_response_decision()
                except Exception as init_e:
                    # 如果初始化失败，记录错误并跳过延迟回复处理
                    self.logger.warning(f"延迟回复组件初始化失败，跳过延迟回复处理: {init_e}")
                    # 继续正常处理流程，不执行延迟回复逻辑
                    delayed_response_manager = None
                    response_decision = None
                
                # 只有在成功初始化的情况下才执行延迟回复决策
                if delayed_response_manager and response_decision:
                    # 构建决策上下文
                    decision_context = {
                        "user_id": user_id,
                        "user_name": user_name,
                        "isgroup": isgroup,
                        "session_id": session_id,
                        "from_user_ID": from_user_ID,
                        "app_token": _token,
                        "app_id": _appid,
                        "perception_context": perception_context,
                        "unified_user": unified_user,
                        "unified_session": unified_session,
                        "is_new_user": is_new_user,
                        "is_new_session": is_new_session
                    }
                    
                    # 🔥 修复：使用完整的智能决策，但在线程池中运行避免事件循环冲突
                    try:
                        import threading
                        current_thread_name = threading.current_thread().name
                        
                        def run_intelligent_decision():
                            """在独立线程中运行完整的智能决策"""
                            try:
                                # 创建新的事件循环用于异步决策
                                import asyncio
                                new_loop = asyncio.new_event_loop()
                                asyncio.set_event_loop(new_loop)
                                try:
                                    # 调用完整的智能决策系统
                                    decision_result = new_loop.run_until_complete(
                                        response_decision.handle_delayed_response_decision(
                                            user_id=user_id,
                                            message=user_input,
                                            context=decision_context
                                        )
                                    )
                                    return decision_result
                                finally:
                                    new_loop.close()
                            except Exception as e:
                                self.logger.warning(f"智能决策失败，使用回退逻辑: {e}")
                                # 回退到简化决策
                                return self._make_simple_response_decision(user_id, user_input, user_name)
                        
                        # 🔥 关键修复：使用线程池执行智能决策，避免事件循环冲突
                        import concurrent.futures
                        with concurrent.futures.ThreadPoolExecutor(max_workers=1) as executor:
                            future = executor.submit(run_intelligent_decision)
                            decision_result = future.result(timeout=15)  # 🔥 香草修复：增加到15秒超时，给AI决策更多时间
                        
                        if decision_result["decision"] == "reply_later":
                            # 延迟回复的情况，存储到延迟回复管理器
                            delay_hours = decision_result.get("delay_hours", 0.5)
                            
                            try:
                                # 🔥 修复：使用同步方式存储延迟回复信息，确保完整的context参数传递
                                self._store_delayed_response_sync(
                                    user_id=user_id,
                                    message=user_input,
                                    delay_hours=delay_hours,
                                    context={
                                        "user_name": user_name,
                                        "session_id": session_id,
                                        # 🔥 关键修复：添加完整的API参数，确保数据统一性
                                        "isgroup": isgroup,
                                        "from_user_ID": from_user_ID,
                                        "_token": _token,
                                        "_appid": _appid,
                                        # 决策相关信息
                                        "timestamp": time.time(),
                                        "decision_reason": decision_result.get("reason", ""),
                                        "confidence": decision_result.get("confidence", 0.5),
                                        "decision_type": "reply_later"  # 标记决策类型
                                    }
                                )
                                self.logger.success(f"✅ 延迟回复已调度 (用户: {user_id}, 延迟: {delay_hours}小时)")
                                
                                # 🔥 关键修复：延迟回复情况下返回特定的字符串，符合原有接口约定
                                delay_message = f"我稍后会回复你的消息（预计{delay_hours:.1f}小时后）"
                                self.logger.debug(f"延迟回复返回消息: {delay_message}")
                                return ""
                                
                            except Exception as store_e:
                                self.logger.error(f"❌ 延迟回复存储异常 (用户: {user_id}): {store_e}")
                                # 存储失败也返回，避免继续处理
                                error_message = f"延迟回复调度失败，但我会尽快回复你"
                                self.logger.debug(f"延迟回复存储失败返回消息: {error_message}")
                                # return error_message # 退避，如果失败继续执行，默认就需要回复了
                        
                        elif decision_result["decision"] == "no_reply":
                            # 不回复的情况
                            self.logger.info(f"🔇 决策不回复 (用户: {user_id}): {decision_result.get('reason', '')}")
                            
                            # 🔥 新增：不回复消息也需要存储到temp_messages表，确保逻辑闭环
                            # 这样_process_single_message可以在适当时候处理这些消息
                            try:
                                # 获取延迟时间，如果没有设置则使用默认值
                                delay_hours = decision_result.get("delay_hours", 0.1)  # 默认0.1小时后可能回复
                                
                                # 🔥 修复：存储不回复的消息到temp_messages表，确保参数顺序和格式一致
                                self._store_delayed_response_sync(
                                    user_id=user_id,
                                    message=user_input,
                                    delay_hours=delay_hours,
                                    context={
                                        "user_name": user_name,
                                        "session_id": session_id,
                                        # 🔥 确保参数顺序与reply_later一致
                                        "isgroup": isgroup,
                                        "from_user_ID": from_user_ID,
                                        "_token": _token,
                                        "_appid": _appid,
                                        # 决策相关信息
                                        "timestamp": time.time(),
                                        "decision_reason": decision_result.get("reason", ""),
                                        "confidence": decision_result.get("confidence", 0.9),
                                        "decision_type": "no_reply"  # 标记这是不回复类型的消息
                                    }
                                )
                                self.logger.success(f"✅ 不回复消息已存储到temp_messages (用户: {user_id}, 延迟: {delay_hours}小时)")
                                
                            except Exception as store_e:
                                self.logger.error(f"❌ 不回复消息存储异常 (用户: {user_id}): {store_e}")
                                # 存储失败不影响主流程，继续处理
                            
                            # 🔥 关键修复：不回复情况下返回空字符串，符合原有接口约定
                            self.logger.debug(f"不回复决策返回空字符串")
                            return ""
                        
                        else:
                            # 立即回复的情况，继续正常处理流程
                            self.logger.debug(f"💬 决策立即回复 (用户: {user_id})")
                        
                    except concurrent.futures.TimeoutError:
                        self.logger.warning(f"延迟回复决策超时，使用默认立即回复 (用户: {user_id})")
                    except Exception as decision_e:
                        self.logger.warning(f"延迟回复决策失败: {decision_e}")
                        # 不影响主流程，继续处理
                else:
                    self.logger.debug(f"延迟回复组件未初始化，跳过延迟回复决策 (用户: {user_id})")
            
            except Exception as e:
                self.logger.warning(f"延迟回复决策处理失败: {e}")
                # 不影响主流程，继续处理
            
            # 🔥 新增：器官系统管理集成
            try:
                from cognitive_modules.organ_system_manager import get_organ_system_manager
                organ_manager = get_organ_system_manager()
                
                # 如果是首次使用，初始化器官系统
                if not hasattr(self, '_organ_system_initialized'):
                    self.logger.info("⚙️ 开始初始化器官系统...")
                    
                    # 🔥 老王修复：在Flask同步上下文中正确处理异步初始化
                    try:
                        # 不在Flask同步上下文中直接调用异步方法
                        # 使用线程池来处理异步初始化
                        import concurrent.futures
                        
                        def run_organ_init():
                            """在独立线程中运行器官系统初始化"""
                            try:
                                new_loop = asyncio.new_event_loop()
                                asyncio.set_event_loop(new_loop)
                                try:
                                    return new_loop.run_until_complete(organ_manager.initialize_organ_system())
                                finally:
                                    new_loop.close()
                            except Exception as e:
                                self.logger.warning(f"⚙️ 器官系统异步初始化失败: {e}")
                                return False
                        
                        # 在线程池中执行初始化
                        with concurrent.futures.ThreadPoolExecutor() as executor:
                            future = executor.submit(run_organ_init)
                            init_result = future.result(timeout=30)  # 30秒超时
                            
                        if init_result:
                            self.logger.info("⚙️ 器官系统初始化成功")
                        else:
                            self.logger.warning("⚙️ 器官系统初始化失败")
                            
                    except Exception as init_e:
                        self.logger.warning(f"⚙️ 器官系统初始化处理失败: {init_e}")
                    
                    self._organ_system_initialized = True
                
                # 获取器官系统状态并添加到元数据
                organ_status = organ_manager.get_system_status()
                metadata["organ_system"] = {
                    "status": organ_status.get("system_status", "unknown"),
                    "active_organs": organ_status.get("active_organs", []),
                    "total_organs": organ_status.get("organs_count", 0),
                    "coordination_cycles": organ_status.get("stats", {}).get("coordination_cycles", 0)
                }
                
                self.logger.info(f"⚙️ 器官系统状态: {organ_status.get('system_status')} - 活跃器官: {len(organ_status.get('active_organs', []))}")
                    
            except Exception as e:
                self.logger.warning(f"⚙️ 器官系统管理集成失败: {e}")
                metadata["organ_system"] = {"status": "error", "error": str(e)}
            
            # 🔥 新增：性能优化处理 (修复问题6: 实时响应能力)
            try:
                from core.performance_optimizer import get_performance_optimizer
                performance_optimizer = get_performance_optimizer()
                
                # 如果是首次使用，进行启动优化
                if not hasattr(self, '_performance_optimized'):
                    # 🔥 老王修复：完全避免在Flask同步上下文中调用asyncio.get_running_loop()
                    try:
                        # 直接使用同步方式或跳过，不检查事件循环
                        if hasattr(performance_optimizer, 'optimize_startup_sync'):
                            performance_optimizer.optimize_startup_sync()
                            self.logger.debug("使用同步方式启动性能优化")
                        else:
                            self.logger.debug("跳过性能优化处理（无同步方法）")
                    except Exception as sync_e:
                        self.logger.debug(f"同步性能优化失败: {sync_e}")
                    
                    self._performance_optimized = True
                    
            except Exception as e:
                self.logger.warning(f"性能优化处理失败: {e}")
            
            # 🔥 老王修复：确保使用API传入的原始user_id，不使用统一管理器的映射ID
            # 这样可以保持API接口的一致性，避免用户ID被意外替换
            final_user_id = user_id  # 始终使用API传入的原始user_id

            # 🔥 调试：记录用户ID的使用情况
            if unified_user and unified_user.user_id != user_id:
                self.logger.warning(f"⚠️ 统一管理器返回的user_id与API传入不一致:")
                self.logger.warning(f"   - API传入: {user_id}")
                self.logger.warning(f"   - 统一管理器: {unified_user.user_id}")
                self.logger.warning(f"   - 使用API传入的ID: {final_user_id}")
            metadata.update({
                # 🔥 统一用户信息 (修复问题3)
                "user_id": final_user_id,
                "from_user_id": final_user_id,  # 兼容性字段
                "from_user_ID": final_user_id,  # 兼容性字段
                "user_name": final_user_name,  # 🔥 老王修复：使用最终确定的用户名
                "user_nickname": final_user_name,  # 🔥 老王修复：使用最终确定的用户名
                "user_sex": unified_user.sex if unified_user else user_sex,
                "user_status": unified_user.status.value if unified_user else "active",
                "user_interaction_count": unified_user.interaction_count if unified_user else 0,
                "is_new_user": is_new_user,
                
                # 🔥 统一会话信息 (修复问题3)
                "session_id": session_id,
                "session_status": unified_session.status.value if unified_session else "active",
                "is_new_session": is_new_session,
                "csrf_token": unified_session.csrf_token if unified_session else None,
                
                # 🔥 感知上下文集成 (修复问题1&2)
                "perception_context": perception_context,
                
                # 兼容性参数
                "jimeng_session_id": "96425b141fffc2443d5abdafb494c184,661e6f442106e10947d9e838a656293b",
                "pre_create_image": "你现在是视觉效果设计师，负责将用户输入的内容，始终围绕用户的核心意图和要素，并且进行质量、创意上的合理发散，并转换成丰富的视觉效果描述，然后用户会将你输出的描述用midjourney或其他AI画图工具渲染出来。\n\n 输出格式: image_prompt:你优化后的视觉效果的中文描述 \n\n aspect_ratio:你认为该视觉效果最佳的宽高比(可选值有：1:1 16:9 4:3 3:2 2:3 3:4 9:16 21:9，默认值为9:16)",
                "format_search_prompt": "你作为用户的私人助理嫣然，协助用户处理如下事务：以下是用户需要百度一下，或者Google一下的搜索的口语化语言。接下来需要分析语意，然后将用户语言转换成搜索引擎用的描述，过滤跟搜索不相关的信息，以提高搜索效率。输出要求：只允许输出搜索用的描述，其它的信息一个字符都不被允许！！！",
                
                # 🔥 系统集成标识
                "unified_user_management": True,
                "perception_integration": True,
                "multi_user_support": True
            })
            
            # 🔥 新增：安全防护检查
            try:
                from security.ai_safety_filter import filter_user_input
                self.logger.info("🛡️ 开始安全防护检查...")
                
                safety_context = {
                    "user_id": user_id,
                    "session_id": session_id,
                    "conversation_context": metadata.get("perception_context", {}),
                    "user_relationship": {
                        "interaction_count": metadata.get("user_interaction_count", 0),
                        "is_new_user": metadata.get("is_new_user", False)
                    }
                }
                
                # 🔥 修复：在同步上下文中处理异步安全过滤
                try:
                    # 检查是否有运行的事件循环
                    try:
                        # 🔥 老王修复：直接在线程池中执行，避免事件循环检查
                        # 不再检查事件循环状态，直接执行
                        if True:  # 总是执行
                            # 在Flask同步上下文中，不能直接await，需要使用线程
                            def run_safety_check():
                                new_loop = asyncio.new_event_loop()
                                asyncio.set_event_loop(new_loop)
                                try:
                                    return new_loop.run_until_complete(filter_user_input(user_input, user_id, safety_context))
                                finally:
                                    new_loop.close()
                            
                            with concurrent.futures.ThreadPoolExecutor() as executor:
                                future = executor.submit(run_safety_check)
                                safety_result = future.result(timeout=30)  # 30秒超时
                            # else分支已移除，统一使用线程池执行
                    except Exception as e:
                        # 线程池执行失败时的处理
                        self.logger.warning(f"🛡️ 异步安全检查失败，使用基础检查: {e}")
                        # 回退到基础安全检查
                        from security.ai_safety_filter import get_ai_safety_filter
                        ai_filter = get_ai_safety_filter()
                        safety_result = ai_filter._parse_ai_result(
                            ai_filter._basic_safety_analysis(user_input), 
                            user_input
                        )
                    
                    self.logger.info(f"🛡️ 安全检查完成 - 风险等级: {safety_result.risk_level.value}, 安全: {safety_result.is_safe}")
                    
                    # 🔥 老王修复：如果不安全，不直接返回，而是设置安全标识传递给后续步骤
                    safety_disgust_flag = None
                    if not safety_result.is_safe:
                        self.logger.warning(f"🛡️ 检测到安全风险: {safety_result.risk_factors}")

                        # 🔥 关键修复：设置安全标识，不直接返回安全响应
                        # 这个标识会传递给动态上下文构建器，确保数字生命角色统一性
                        safety_disgust_flag = "不安全输出：no"

                        self.logger.info(f"🛡️ 设置安全标识传递给后续步骤: {safety_disgust_flag}")

                        # 将安全标识添加到metadata中，供后续步骤使用
                        metadata["safety_disgust"] = safety_disgust_flag
                        metadata["safety_risk_level"] = safety_result.risk_level.value
                        metadata["safety_risk_factors"] = safety_result.risk_factors
                    
                except Exception as safety_e:
                    self.logger.warning(f"🛡️ 异步安全检查失败，使用基础检查: {safety_e}")
                    # 回退到基础安全检查
                    from security.ai_safety_filter import get_ai_safety_filter
                    ai_filter = get_ai_safety_filter()
                    safety_result = ai_filter._parse_ai_result(
                        ai_filter._basic_safety_analysis(user_input), 
                        user_input
                    )
            except Exception as e:
                self.logger.warning(f"🛡️ 安全防护检查失败: {e}")
            
            # 🔥 核心数字生命体处理逻辑
            try:
                # 🔥 老王修复：检查数字生命体是否已初始化，如果没有则进行延迟初始化
                if not self.digital_life:
                    self.logger.warning(f"📦 [{request_id}] 数字生命体未初始化，开始延迟初始化...")
                    try:
                        # 延迟初始化digital_life
                        if not self._check_initialized("digital_life"):
                            self.logger.info(f"📦 [{request_id}] 开始初始化数字生命体...")
                            self.init_times['digital_life_start'] = time.time()
                            
                            from core.digital_life import get_instance as get_digital_life
                            self.digital_life = get_digital_life()
                            self._mark_initialized("digital_life")
                            
                            self.init_times['digital_life_end'] = time.time()
                            self.logger.success(f"📦 [{request_id}] 数字生命体初始化完成，耗时: {self.init_times['digital_life_end'] - self.init_times['digital_life_start']:.2f}秒")
                        else:
                            self.logger.info(f"📦 [{request_id}] 数字生命体已标记为初始化，重新获取实例...")
                            from core.digital_life import get_instance as get_digital_life
                            self.digital_life = get_digital_life()
                            
                        # 再次检查是否初始化成功
                        if not self.digital_life:
                            error_msg = f"❌ [{request_id}] 数字生命体延迟初始化失败"
                            self.logger.error(error_msg)
                            return error_msg
                        else:
                            self.logger.success(f"✅ [{request_id}] 数字生命体延迟初始化成功")
                    except Exception as init_e:
                        error_msg = f"❌ [{request_id}] 数字生命体初始化异常: {init_e}"
                        self.logger.error(error_msg)
                        import traceback
                        traceback.print_exc()
                        return error_msg
                
                # 🔥 新增：多用户并发处理支持
                try:
                    # 为每个用户创建独立的处理上下文
                    processing_context = {
                        "user_metadata": metadata,
                        "session_id": session_id,
                        "thread_id": threading.current_thread().ident,
                        "processing_start": time.time()
                    }
                    
                    # 🔥 新增：发布对话消息事件，确保记忆系统能正确存储
                    try:
                        from core.integrated_event_bus import get_instance as get_event_bus
                        event_bus = get_event_bus()
                        
                        # 发布用户消息事件
                        event_bus.publish("conversation_message", {
                            "role": "user",
                            "content": user_input,
                            "user_id": final_user_id,
                            "user_name": user_name,
                            "session_id": session_id,
                            "timestamp": time.time(),
                            "metadata": metadata
                        })
                        
                        self.logger.debug(f"📝 已发布用户对话消息事件: {user_input[:50]}...")
                    except Exception as event_e:
                        self.logger.warning(f"📝 发布对话事件失败: {event_e}")
                    
                    # 🔥 老王修复：优先使用process_input方法，确保安全标识能正确传递
                    if hasattr(self.digital_life, 'process_input'):
                        # 使用process_input方法（异步方法，需要创建上下文）
                        from core.thinking_chain import ThinkingContext
                        # 🔥 P0级别修复：确保input_data中包含完整的用户信息
                        input_data = {
                            "text": user_input,
                            "user_input": user_input,
                            "message": user_input,
                            "user_id": final_user_id,
                            "from_user_id": final_user_id,  # 兼容性字段
                            "from_user_ID": final_user_id,  # 兼容性字段
                            "user_name": final_user_name,  # 🔥 关键修复：使用final_user_name确保用户名正确传递
                            "name": final_user_name,  # 🔥 增加name字段兼容性
                            "user_sex": user_sex,
                            "isgroup": isgroup,
                            "session_id": session_id
                        }
                        context = ThinkingContext(
                            session_id=session_id,
                            input_data=input_data,
                            metadata={
                                "user_id": final_user_id,
                                "user_name": final_user_name,  # 🔥 关键修复：传递最终确定的用户名
                                "session_id": session_id,
                                **metadata
                            }
                        )

                        # 🔥 老王调试：记录ThinkingContext的构建
                        self.logger.debug(f"🔧 构建ThinkingContext:")
                        self.logger.debug(f"   - session_id: {session_id}")
                        self.logger.debug(f"   - input_data: {input_data}")
                        self.logger.debug(f"   - user_input: '{user_input}' (长度: {len(user_input) if user_input else 0})")
                        
                        # 🔥 在同步上下文中处理异步方法
                        def run_process_input():
                            new_loop = asyncio.new_event_loop()
                            asyncio.set_event_loop(new_loop)
                            try:
                                # 🔥 老王修复：正确传递参数给digital_life.py的process_input方法
                                return new_loop.run_until_complete(
                                    asyncio.wait_for(
                                        self.digital_life.process_input(
                                            user_input=user_input,
                                            context=context
                                        ),
                                        timeout=360.0  # 🔥 恢复为360秒，支持复杂任务处理
                                    )
                                )
                            finally:
                                new_loop.close()
                        
                        with concurrent.futures.ThreadPoolExecutor() as executor:
                            future = executor.submit(run_process_input)
                            try:
                                response = future.result(timeout=360)  # 🔥 360秒超时，支持复杂任务
                                self.logger.info(f"💬 使用process_input方法处理完成")
                            except concurrent.futures.TimeoutError:
                                self.logger.warning("💬 process_input方法处理超时，这可能是一个复杂任务")
                                response = "我正在处理一个比较复杂的任务，需要更多时间来给你最好的回答..."
                    elif hasattr(self.digital_life, 'process_message'):
                        # 🔥 降级方案：使用process_message方法（无法传递完整metadata，但至少能工作）
                        self.logger.warning("💬 process_input方法不可用，使用process_message降级方案")
                        response = self.digital_life.process_message(user_id, session_id, user_input)
                        self.logger.info(f"💬 使用process_message降级方案处理完成")
                    else:
                        # 数字生命体没有可用的处理方法
                        self.logger.error("💬 数字生命体没有可用的处理方法")
                        response = "系统暂时无法处理您的请求，请稍后再试"
                    
                    # 🔥 新增：发布AI回复事件，完整记录对话
                    try:
                        # 发布AI回复事件
                        event_bus.publish("conversation_message", {
                            "role": "assistant",
                            "content": str(response),
                            "user_id": final_user_id,
                            "session_id": session_id,
                            "timestamp": time.time(),
                            "metadata": {"response_to": user_input[:100]}
                        })
                        
                        # 🔥 老王修复：安全地记录AI回复事件，避免slice错误
                        try:
                            response_str = str(response)
                            response_preview = response_str[:50] if len(response_str) > 50 else response_str
                            self.logger.debug(f"📝 已发布AI回复消息事件: {response_preview}...")
                        except Exception as log_e:
                            self.logger.debug(f"📝 已发布AI回复消息事件: [无法安全记录] - {type(response).__name__}")
                    except Exception as event_e:
                        self.logger.warning(f"📝 发布AI回复事件失败: {event_e}")
                    
                    # 🔥 P0级修复：完整的请求追踪和性能统计
                    processing_time = time.time() - processing_context["processing_start"]
                    request_context["processing_stage"] = "completed"
                    request_context["processing_time"] = processing_time
                    request_context["response_length"] = len(str(response)) if response else 0
                    
                    self.logger.success(f"🎯 [{request_id}] 请求处理完成统计:")
                    self.logger.success(f"   - 用户: {user_id} ({user_name})")
                    self.logger.success(f"   - 线程: {thread_id}")
                    self.logger.success(f"   - 处理时间: {processing_time:.2f}秒")
                    self.logger.success(f"   - 响应长度: {len(str(response)) if response else 0}字符")
                    self.logger.success(f"   - 会话: {session_id}")
                    
                    # 确保响应是字符串类型
                    if not isinstance(response, str):
                        if isinstance(response, dict):
                            # 尝试从字典中提取响应文本
                            if "response" in response:
                                response = response["response"]
                            elif "result" in response:
                                response = response["result"]
                            elif "text" in response:
                                response = response["text"]
                            else:
                                response = str(response)
                        else:
                            response = str(response)
                    
                    return response
                    
                except Exception as processing_e:
                    self.logger.error(f"💬 数字生命体处理异常: {processing_e}")
                    # 🔥 降级处理：使用简化的处理方式
                    try:
                        # 尝试使用基础的process_message方法
                        if hasattr(self.digital_life, 'process_message'):
                            return self.digital_life.process_message(user_id, session_id, user_input)
                        else:
                            return "抱歉，系统处理出现异常，请稍后再试"
                    except Exception as fallback_e:
                        self.logger.error(f"💬 降级处理也失败: {fallback_e}")
                        return "系统暂时无法处理您的请求，请稍后再试"
                
            except Exception as e:
                self.logger.error_status(f"API处理消息异常: {e}")
                return "处理消息时发生异常，请稍后再试"
        
        except Exception as e:
            self.logger.error(f"处理用户请求时发生异常: {e}")
            return f"抱歉，处理您的消息时遇到了问题：{str(e)}"
    
    def start_api_server(self, host="127.0.0.1", port=56839):
        """
        启动API服务器
        
        Args:
            host: 监听主机
            port: 监听端口
        """
        try:
            import json
            
            # 🔥 增强Flask导入逻辑，解决生产环境导入问题
            try:
                from flask import Flask, request, jsonify
                self.logger.debug("Flask导入成功 - 使用标准导入")
            except ImportError as flask_error:
                self.logger.warning(f"标准Flask导入失败: {flask_error}")
                
                # 尝试不同的导入方式
                import sys
                import importlib.util
                
                # 方法1: 尝试重新加载Flask模块
                try:
                    if 'flask' in sys.modules:
                        del sys.modules['flask']
                    import flask
                    from flask import Flask, request, jsonify
                    self.logger.info("Flask导入成功 - 使用重新加载")
                except ImportError:
                    
                    # 方法2: 尝试通过pip安装路径导入
                    try:
                        import subprocess
                        result = subprocess.run([sys.executable, '-c', 'import flask; print(flask.__file__)'], 
                                              capture_output=True, text=True)
                        if result.returncode == 0:
                            flask_path = result.stdout.strip()
                            self.logger.info(f"发现Flask安装路径: {flask_path}")
                            
                            # 添加Flask路径到sys.path
                            flask_dir = str(Path(flask_path).parent.parent)
                            if flask_dir not in sys.path:
                                sys.path.insert(0, flask_dir)
                                
                            from flask import Flask, request, jsonify
                            self.logger.info("Flask导入成功 - 使用路径修复")
                        else:
                            raise ImportError("无法找到Flask安装路径")
                            
                    except Exception as path_error:
                        self.logger.error_status(f"Flask路径修复失败: {path_error}")
                        
                        # 方法3: 检查虚拟环境
                        virtual_env = os.environ.get('VIRTUAL_ENV') or os.environ.get('CONDA_PREFIX')
                        if virtual_env:
                            flask_paths = [
                                os.path.join(virtual_env, 'lib', 'python*', 'site-packages'),
                                os.path.join(virtual_env, 'Lib', 'site-packages'),  # Windows
                            ]
                            
                            for path_pattern in flask_paths:
                                import glob
                                for path in glob.glob(path_pattern):
                                    if path not in sys.path:
                                        sys.path.insert(0, path)
                                        
                            try:
                                from flask import Flask, request, jsonify
                                self.logger.info("Flask导入成功 - 使用虚拟环境路径")
                            except ImportError:
                                raise ImportError("所有Flask导入方法都失败")
                        else:
                            raise ImportError("未找到虚拟环境，无法修复Flask导入")
            
            app = Flask("digital_life_api")
            
            @app.route("/api/chat", methods=["POST"])
            def chat_endpoint():
                try:
                    data = request.json
                    if not data:
                        return jsonify({"error": "无效的JSON数据"}), 400
                    
                    # 必需参数
                    user_input = data.get("message", "")
                    if not user_input:
                        return jsonify({"error": "消息内容不能为空"}), 400
                        
                    # 可选参数(支持旧体系) - 🔥 移除所有硬编码，完全依赖上游传参
                    user_id = data.get("user_id")
                    user_name = data.get("user_name")
                    user_sex = data.get("user_sex")
                    isgroup = data.get("isgroup")
                    session_id = data.get("session_id")
                    is_Segment = data.get("is_Segment")
                    from_user_ID= "wxid_ezkohzfp3xqp12"
                    _token = data.get("_token")
                    _appid = data.get("_appid")
                    
                    # 🔥 参数将在enhanced_process_message中统一处理
                    
                    # 构建元数据
                    metadata = {
                        "user_name": user_name,
                        "user_sex": user_sex,
                        "isgroup": isgroup,
                        "is_Segment": is_Segment,
                        "from_user_ID": from_user_ID,
                        "token": _token,
                        "appid": _appid,
                        "source": "api_adapter",
                        "timestamp": time.time()
                    }
                    
                    # 🔥 修复：统一session_id管理，避免重复生成
                    if not session_id:
                        session_id = f"api_{user_id}_{int(time.time())}"
                        self.logger.info(f"�� API端点生成session_id: {session_id}")
                    
                    metadata["session_id"] = session_id
                    
                    # 确保数字生命体已初始化
                    if not self.digital_life:
                        return jsonify({"error": "系统正在初始化中，请稍后再试"}), 503
                    
                    self.logger.info(f"API请求: user_id={user_id}, message={user_input[:30]}...")
                    
                    try:
                        # 🔥 恢复完整处理：使用enhanced_process_message完整逻辑
                        response = self.enhanced_process_message(
                            user_input=user_input,
                            user_id=user_id,
                            user_name=user_name,
                            user_sex=user_sex,
                            isgroup=isgroup,
                            session_id=session_id,
                            is_Segment=is_Segment,
                            from_user_ID=from_user_ID,
                            _token=_token,
                            _appid=_appid
                        )
                        
                        # 🔥 老王修复：安全地记录API响应，避免slice错误
                        try:
                            if isinstance(response, str):
                                response_preview = response[:50] if len(response) > 50 else response
                                self.logger.info(f"API响应: {response_preview}...")
                            else:
                                response_str = str(response)
                                response_preview = response_str[:50] if len(response_str) > 50 else response_str
                                self.logger.info(f"API响应: {response_preview}...")
                        except Exception as log_e:
                            self.logger.info(f"API响应: [无法安全记录响应内容] - {type(response).__name__}")
                        
                        return jsonify({
                            "response": response,
                            "timestamp": time.time(),
                            "user_id": user_id,
                            "session_id": session_id
                        })
                    except Exception as e:
                        self.logger.error_status(f"处理消息异常: {str(e)}")
                        self.logger.error_status(traceback.format_exc())
                        return jsonify({"error": "处理消息异常", "message": str(e)}), 500
                        
                except Exception as e:
                    self.logger.error_status(f"API处理异常: {e}")
                    self.logger.error_status(traceback.format_exc())
                    return jsonify({"error": str(e)}), 500
            
            @app.route("/api/status", methods=["GET"])
            def status_endpoint():
                status = self.get_status()
                return jsonify(status)
            
            # 健康检查端点
            @app.route("/api/health", methods=["GET"])
            def health_endpoint():
                return jsonify({
                    "status": "ok",
                    "version": self.version,
                    "uptime": self._format_uptime(time.time() - self.start_time)
                })
            
            # ========== 普通API端点 ==========
            
            @app.route("/api/chat/message", methods=["POST"])
            def chat_message_api():
                """普通聊天消息API - 支持多参数传递"""
                try:
                    data = request.json
                    if not data:
                        return jsonify({"success": False, "error": "无效的JSON数据"}), 400
                    
                    # 🔥 验证API Key
                    auth_header = request.headers.get("Authorization", "")
                    if auth_header.startswith("Bearer "):
                        api_key = auth_header[7:]  # 移除"Bearer "前缀
                        
                        # 验证API Key
                        validation_result = self.validate_openai_request(api_key=api_key)
                        if not validation_result["valid"]:
                            return jsonify({
                                "success": False,
                                "error": f"API密钥验证失败: {', '.join(validation_result['errors'])}"
                            }), 401
                    else:
                        return jsonify({
                            "success": False,
                            "error": "缺少有效的API密钥，请在Authorization头中提供Bearer token"
                        }), 401
                    
                    # 提取必需参数
                    user_input = data.get("user_input", "")
                    if not user_input:
                        return jsonify({"success": False, "error": "user_input参数是必需的"}), 400
                    
                    # 提取所有支持的参数 - 🔥 移除所有硬编码，完全依赖上游传参
                    user_id = data.get("user_id")
                    user_name = data.get("user_name")
                    user_sex = data.get("user_sex")
                    isgroup = data.get("isgroup")
                    session_id = data.get("session_id")
                    is_Segment = data.get("is_Segment")
                    _token = data.get("_token")
                    _appid = data.get("_appid")
                    
                    # 确保数字生命体已初始化
                    if not self.digital_life:
                        return jsonify({"success": False, "error": "系统正在初始化中，请稍后再试"}), 503
                    
                    self.logger.info(f"====API聊天请求====: user_id={user_id}, user_name={user_name}, user_sex={user_sex}, isgroup={isgroup}, session_id = {session_id}, is_Segment={is_Segment}, _token={_token}, _appid={_appid}, message={user_input}")
                    
                    try:
                        # 🔥 恢复完整处理：使用enhanced_process_message完整逻辑
                        response_text = self.enhanced_process_message(
                            user_input=user_input,
                            user_id=user_id,
                            user_name=user_name,
                            user_sex=user_sex,
                            isgroup=isgroup,
                            session_id=session_id,
                            is_Segment=is_Segment,
                            from_user_ID="wxid_ezkohzfp3xqp12",
                            _token=_token,
                            _appid=_appid
                        )
                        
                        # 构建响应
                        api_response = {
                            "success": True,
                            "data": {
                                "response": response_text,
                                "user_id": user_id,
                                "session_id": session_id,
                                "timestamp": int(time.time())
                            }
                        }
                        
                        # 🔥 老王修复：安全地记录API聊天响应，避免slice错误
                        try:
                            if isinstance(response_text, str):
                                response_preview = response_text[:50] if len(response_text) > 50 else response_text
                                self.logger.info(f"API聊天响应: {response_preview}...")
                            else:
                                response_str = str(response_text)
                                response_preview = response_str[:50] if len(response_str) > 50 else response_str
                                self.logger.info(f"API聊天响应: {response_preview}...")
                        except Exception as log_e:
                            self.logger.info(f"API聊天响应: [无法安全记录响应内容] - {type(response_text).__name__}")

                        return jsonify(api_response)
                        
                    except Exception as e:
                        self.logger.error_status(f"API处理消息异常: {str(e)}")
                        return jsonify({"success": False, "error": "处理消息异常", "details": str(e)}), 500
                        
                except Exception as e:
                    self.logger.error_status(f"API处理异常: {e}")
                    return jsonify({"success": False, "error": str(e)}), 500
            
            @app.route("/api/models", methods=["GET"])
            def api_models():
                """获取支持的模型列表"""
                return jsonify({
                    "success": True,
                    "data": {
                        "models": [
                            {
                                "id": "linyanran",
                                "name": "林嫣然数字生命体",
                                "description": "林嫣然AI数字生命体模型",
                                "version": "2.0",
                                "capabilities": ["chat", "search", "drawing", "music"]
                            }
                        ]
                    }
                })
            
            # 📇 联系人管理API端点
            @app.route("/api/contacts", methods=["GET"])
            def contacts_list_endpoint():
                """获取联系人列表"""
                try:
                    from core.contacts_manager import get_contacts_manager
                    contacts_manager = get_contacts_manager()  # 🔥 老王修复：使用单例工厂函数
                    
                    if not contacts_manager:
                        return jsonify({"error": "联系人管理器未初始化"}), 503
                    
                    users = contacts_manager.get_all_users()
                    stats = contacts_manager.get_stats()
                    
                    return jsonify({
                        "users": users,
                        "stats": stats,
                        "total": len(users)
                    })
                except Exception as e:
                    return jsonify({"error": str(e)}), 500
            
            @app.route("/api/contacts/<user_id>", methods=["GET"])
            def contacts_get_endpoint(user_id):
                """获取指定用户信息"""
                try:
                    from core.contacts_manager import get_contacts_manager
                    contacts_manager = get_contacts_manager()  # 🔥 老王修复：使用单例工厂函数
                    
                    if not contacts_manager:
                        return jsonify({"error": "联系人管理器未初始化"}), 503
                    
                    user_info = contacts_manager.get_user_info(user_id)
                    
                    if not user_info:
                        return jsonify({"error": "用户不存在"}), 404
                    
                    return jsonify(user_info)
                except Exception as e:
                    return jsonify({"error": str(e)}), 500
            
            @app.route("/api/contacts/sync", methods=["POST"])
            def contacts_sync_endpoint():
                """强制同步联系人数据"""
                try:
                    from core.contacts_manager import get_contacts_manager
                    contacts_manager = get_contacts_manager()  # 🔥 老王修复：使用单例工厂函数
                    
                    if not contacts_manager:
                        return jsonify({"error": "联系人管理器未初始化"}), 503
                    
                    contacts_manager.force_sync()
                    stats = contacts_manager.get_stats()
                    
                    return jsonify({
                        "message": "同步完成",
                        "stats": stats
                    })
                except Exception as e:
                    return jsonify({"error": str(e)}), 500
            
            # 🔮 上帝视角监测系统API端点
            @app.route("/api/consciousness/state", methods=["GET"])
            def consciousness_state_endpoint():
                """获取意识状态"""
                try:
                    from utilities.singleton_manager import get_silent
                    consciousness_manager = get_silent("consciousness_manager")
                    
                    if not consciousness_manager:
                        # 尝试从数字生命体获取意识状态
                        if self.digital_life and hasattr(self.digital_life, 'consciousness_system'):
                            consciousness_system = self.digital_life.consciousness_system
                            state = {
                                "awareness_level": getattr(consciousness_system, 'awareness_level', 0.7),
                                "attention_focus": getattr(consciousness_system, 'current_focus', None),
                                "cognitive_load": getattr(consciousness_system, 'cognitive_load', 0.5),
                                "meta_cognition": getattr(consciousness_system, 'meta_cognition_level', 0.6),
                                "consciousness_stream": getattr(consciousness_system, 'consciousness_stream', []),
                                "active_thoughts": getattr(consciousness_system, 'active_thoughts', []),
                                "subconscious_processes": getattr(consciousness_system, 'subconscious_processes', []),
                                "self_reflection_active": getattr(consciousness_system, 'self_reflection_active', False),
                                "introspection_depth": getattr(consciousness_system, 'introspection_depth', 0.4)
                            }
                            return jsonify(state)
                        else:
                            # 🔥 严禁返回模拟数据，返回空状态
                            return jsonify({"error": "意识状态数据不可用"})
                    
                    state = consciousness_manager.get_current_state()
                    return jsonify(state)
                except Exception as e:
                    return jsonify({"error": str(e)}), 500
            
            @app.route("/api/emotion/state", methods=["GET"])
            def emotion_state_endpoint():
                """获取情感状态"""
                try:
                    from utilities.singleton_manager import get_silent
                    emotion_manager = get_silent("emotion_manager")
                    
                    if not emotion_manager:
                        # 尝试从数字生命体获取情感状态
                        if self.digital_life and hasattr(self.digital_life, 'emotion_system'):
                            emotion_system = self.digital_life.emotion_system
                            state = {
                                "primary_emotion": getattr(emotion_system, 'current_emotion', '平静'),
                                "emotion_intensity": getattr(emotion_system, 'emotion_intensity', 0.5),
                                "emotion_valence": getattr(emotion_system, 'emotion_valence', 0.2),
                                "emotion_arousal": getattr(emotion_system, 'emotion_arousal', 0.4),
                                "secondary_emotions": getattr(emotion_system, 'secondary_emotions', {}),
                                "emotional_stability": getattr(emotion_system, 'emotional_stability', 0.8),
                                "empathy_level": getattr(emotion_system, 'empathy_level', 0.7),
                                "emotional_regulation": getattr(emotion_system, 'regulation_strategy', '自然调节'),
                                "mood_trend": getattr(emotion_system, 'mood_trend', '稳定')
                            }
                            return jsonify(state)
                        else:
                            # 🔥 严禁返回模拟数据，返回空状态
                            return jsonify({"error": "情感状态数据不可用"})
                    
                    state = emotion_manager.get_current_state()
                    return jsonify(state)
                except Exception as e:
                    return jsonify({"error": str(e)}), 500
            
            @app.route("/api/cognition/state", methods=["GET"])
            def cognition_state_endpoint():
                """获取认知状态"""
                try:
                    from utilities.singleton_manager import get_silent
                    cognition_manager = get_silent("cognition_manager")
                    
                    if not cognition_manager:
                        # 尝试从数字生命体获取认知状态
                        if self.digital_life and hasattr(self.digital_life, 'cognition_system'):
                            cognition_system = self.digital_life.cognition_system
                            state = {
                                "active_modules": getattr(cognition_system, 'active_modules', []),
                                "processing_pipeline": getattr(cognition_system, 'processing_pipeline', []),
                                "reasoning_depth": getattr(cognition_system, 'reasoning_depth', 0.7),
                                "creative_mode": getattr(cognition_system, 'creative_mode', False),
                                "learning_active": getattr(cognition_system, 'learning_active', True),
                                "memory_access_pattern": getattr(cognition_system, 'memory_access_pattern', '顺序访问'),
                                "decision_confidence": getattr(cognition_system, 'decision_confidence', 0.8),
                                "problem_solving_mode": getattr(cognition_system, 'problem_solving_mode', '分析模式'),
                                "knowledge_integration": getattr(cognition_system, 'knowledge_integration', 0.6)
                            }
                            return jsonify(state)
                        else:
                            # 🔥 严禁返回模拟数据，返回空状态
                            return jsonify({"error": "认知状态数据不可用"})
                    
                    state = cognition_manager.get_current_state()
                    return jsonify(state)
                except Exception as e:
                    return jsonify({"error": str(e)}), 500
            
            @app.route("/api/skills/state", methods=["GET"])
            def skills_state_endpoint():
                """获取技能状态"""
                try:
                    from utilities.singleton_manager import get_silent
                    skill_manager = get_silent("skill_manager")
                    
                    if not skill_manager:
                        # 尝试从数字生命体获取技能状态
                        if self.digital_life and hasattr(self.digital_life, 'skill_manager'):
                            skill_manager = self.digital_life.skill_manager
                    
                    if skill_manager:
                        # 使用SkillManager的实际方法获取状态
                        try:
                            all_skills = skill_manager.get_all_skills() if hasattr(skill_manager, 'get_all_skills') else []
                            enabled_skills = skill_manager.get_enabled_skills() if hasattr(skill_manager, 'get_enabled_skills') else []
                            
                            # 构建状态信息
                            active_skill_names = [skill.get('name', skill.get('skill_id', 'Unknown')) for skill in enabled_skills]
                            all_skill_names = [skill.get('name', skill.get('skill_id', 'Unknown')) for skill in all_skills]
                            
                            state = {
                                "active_skills": active_skill_names,
                                "all_skills": all_skill_names,
                                "skill_combinations": [],  # 可以后续扩展
                                "execution_queue": [],  # 可以后续扩展
                                "success_rate": 0.9,  # 默认值
                                "coordination_efficiency": 0.85,  # 默认值
                                "skill_learning": {skill: 0.8 for skill in active_skill_names},  # 默认学习进度
                                "performance_metrics": {
                                    "total_skills": len(all_skills),
                                    "enabled_skills": len(enabled_skills),
                                    "disabled_skills": len(all_skills) - len(enabled_skills)
                                },
                                "skills_detail": {
                                    "all_skills": all_skills,
                                    "enabled_skills": enabled_skills
                                }
                            }
                            return jsonify(state)
                        except Exception as skill_error:
                            # 如果获取技能信息失败，返回基本状态
                            state = {
                                "active_skills": getattr(skill_manager, 'skills', {}).keys() if hasattr(skill_manager, 'skills') else [],
                                "all_skills": list(getattr(skill_manager, 'skills', {}).keys()) if hasattr(skill_manager, 'skills') else [],
                                "skill_combinations": [],
                                "execution_queue": [],
                                "success_rate": 0.9,
                                "coordination_efficiency": 0.85,
                                "skill_learning": {},
                                "performance_metrics": {"error": f"获取详细信息失败: {str(skill_error)}"}
                            }
                            return jsonify(state)
                    else:
                        # 🔥 严禁返回模拟数据，返回空状态
                        return jsonify({"error": "技能状态数据不可用"})
                        
                except Exception as e:
                    return jsonify({"error": str(e)}), 500
            
            @app.route("/api/physiology/state", methods=["GET"])
            def physiology_state_endpoint():
                """获取生理状态"""
                try:
                    from utilities.singleton_manager import get_silent
                    physiology_manager = get_silent("physiology_manager")
                    
                    if not physiology_manager:
                        # 尝试从数字生命体获取生理状态
                        if self.digital_life and hasattr(self.digital_life, 'physiology_system'):
                            physiology_system = self.digital_life.physiology_system
                            state = {
                                "energy_level": getattr(physiology_system, 'energy_level', 85.0),
                                "rest_level": getattr(physiology_system, 'rest_level', 75.0),
                                "stress_level": getattr(physiology_system, 'stress_level', 25.0),
                                "health_status": getattr(physiology_system, 'health_status', '良好'),
                                "vitality_score": getattr(physiology_system, 'vitality_score', 80.0),
                                "metabolism_rate": getattr(physiology_system, 'metabolism_rate', 1.0),
                                "neural_activity": getattr(physiology_system, 'neural_activity', 0.7),
                                "system_load": getattr(physiology_system, 'system_load', 0.4)
                            }
                            return jsonify(state)
                        else:
                            # 🔥 严禁返回模拟数据，返回空状态
                            return jsonify({"error": "生理状态数据不可用"})
                    
                    state = physiology_manager.get_current_state()
                    return jsonify(state)
                except Exception as e:
                    return jsonify({"error": str(e)}), 500
            
            @app.route("/api/delayed_response/state", methods=["GET"])
            def delayed_response_state_endpoint():
                """获取延迟回复系统状态"""
                try:
                    from utilities.singleton_manager import get_silent
                    delayed_response_manager = get_silent("delayed_response_manager")
                    
                    if not delayed_response_manager:
                        return jsonify({
                            "error": "延迟回复管理器未初始化",
                            "available": False
                        }), 503
                    
                    # 获取延迟回复统计信息
                    stats = delayed_response_manager.get_stats()
                    
                    # 获取当前待处理消息数量
                    try:
                        from connectors.database.mysql_connector import get_instance as get_mysql_connector
                        mysql_connector = get_mysql_connector()
                        success, result, error = mysql_connector.execute_query(
                            "SELECT COUNT(*) as count FROM temp_messages", ()
                        )
                        pending_count = result[0]["count"] if success and result else 0
                    except Exception as e:
                        pending_count = "查询失败"
                    
                    return jsonify({
                        "delayed_response": {
                            "stats": stats,
                            "pending_messages": pending_count,
                            "timestamp": time.time()
                        },
                        "available": True
                    })
                    
                except Exception as e:
                    self.logger.error_status(f"获取延迟回复状态失败: {e}")
                    return jsonify({
                        "error": f"获取延迟回复状态失败: {str(e)}",
                        "available": False
                    }), 500
            
            # 🔥 新增：WeChat调度服务状态API
            @app.route("/api/wechat_scheduler/state", methods=["GET"])
            def wechat_scheduler_state_endpoint():
                """获取WeChat调度服务状态"""
                try:
                    if hasattr(self, 'wechat_scheduler') and self.wechat_scheduler:
                        status = self.wechat_scheduler.get_task_status()
                        return jsonify({
                            "status": "success",
                            "data": status,
                            "timestamp": datetime.now().isoformat()
                        })
                    else:
                        return jsonify({
                            "status": "error",
                            "message": "WeChat调度服务未初始化",
                            "timestamp": datetime.now().isoformat()
                        }), 500
                        
                except Exception as e:
                    return jsonify({
                        "status": "error",
                        "message": f"获取WeChat调度服务状态失败: {str(e)}",
                        "timestamp": datetime.now().isoformat()
                    }), 500
            
            # 🔥 新增：系统监控状态API
            @app.route("/api/system_monitor/state", methods=["GET"])
            def system_monitor_state_endpoint():
                """获取系统监控状态"""
                try:
                    if hasattr(self, 'system_monitor') and self.system_monitor:
                        status = self.system_monitor.get_system_health_summary()
                        return jsonify({
                            "status": "success",
                            "data": status,
                            "timestamp": datetime.now().isoformat()
                        })
                    else:
                        return jsonify({
                            "status": "error",
                            "message": "系统监控服务未初始化",
                            "timestamp": datetime.now().isoformat()
                        }), 500
                        
                except Exception as e:
                    return jsonify({
                        "status": "error",
                        "message": f"获取系统监控状态失败: {str(e)}",
                        "timestamp": datetime.now().isoformat()
                    }), 500
            
            # 🔥 新增：所有服务状态综合API
            @app.route("/api/services/all", methods=["GET"])
            def all_services_state_endpoint():
                """获取所有服务状态"""
                try:
                    services_status = {
                        "digital_life": {
                            "status": "running" if self.is_running else "stopped",
                            "uptime": self._format_uptime(time.time() - self.start_time),
                            "initialized_components": len(self.initialized_components)
                        },
                        "wechat_scheduler": {
                            "initialized": hasattr(self, 'wechat_scheduler') and self.wechat_scheduler is not None,
                            "status": "running" if hasattr(self, 'wechat_scheduler') and self.wechat_scheduler and hasattr(self.wechat_scheduler, 'is_running') and self.wechat_scheduler.is_running else "stopped"
                        },
                        "system_monitor": {
                            "initialized": hasattr(self, 'system_monitor') and self.system_monitor is not None,
                            "status": "running" if hasattr(self, 'system_monitor') and self.system_monitor else "stopped"
                        },
                        "group_push_service": {
                            "initialized": hasattr(self, 'group_push_service') and self.group_push_service is not None,
                            "status": "ready"
                        },
                        "intelligent_dispatch": {
                            "initialized": hasattr(self, 'intelligent_dispatch') and self.intelligent_dispatch is not None,
                            "status": "ready"
                        },
                        "performance_analyzer": {
                            "initialized": hasattr(self, 'performance_analyzer') and self.performance_analyzer is not None,
                            "status": "running" if hasattr(self, 'performance_analyzer') and self.performance_analyzer else "stopped"
                        },
                        "health_checker": {
                            "initialized": hasattr(self, 'health_checker') and self.health_checker is not None,
                            "status": "running" if hasattr(self, 'health_checker') and self.health_checker else "stopped"
                        },
                        "yanran_ai_engine": {
                            "initialized": hasattr(self, 'yanran_ai_engine') and self.yanran_ai_engine is not None,
                            "status": "ready"
                        },
                        "world_perception_organ": {
                            "initialized": hasattr(self, 'world_perception_organ') and self.world_perception_organ is not None,
                            "status": "ready"
                        },
                        "caching_middleware": {
                            "initialized": hasattr(self, 'caching_middleware') and self.caching_middleware is not None,
                            "status": "ready"
                        }
                    }
                    
                    return jsonify({
                        "status": "success",
                        "data": services_status,
                        "timestamp": datetime.now().isoformat(),
                        "summary": {
                            "total_services": len(services_status),
                            "initialized_services": sum(1 for s in services_status.values() if s.get("initialized", False)),
                            "running_services": sum(1 for s in services_status.values() if s.get("status") == "running")
                        }
                    })
                        
                except Exception as e:
                    return jsonify({
                        "status": "error",
                        "message": f"获取所有服务状态失败: {str(e)}",
                        "timestamp": datetime.now().isoformat()
                    }), 500
            
            # 在单独的线程中启动API服务器
            import threading
            threading.Thread(
                target=lambda: app.run(host=host, port=port, debug=False, use_reloader=False),
                daemon=True
            ).start()
            
            self.logger.success(f"API服务器已启动: http://{host}:{port}/api/")
            self.logger.info("API调用示例: curl -X POST http://localhost:56839/api/chat -H \"Content-Type: application/json\" -d '{\"message\": \"你好\", \"user_id\": \"test_user\"}'")
            
        except ImportError:
            self.logger.error_status("启动API服务器失败: 缺少Flask库，请安装: pip install flask")
        except Exception as e:
            self.logger.error_status(f"启动API服务器异常: {e}")
            self.logger.error_status(traceback.format_exc())
    
    def start_cli_interface(self):
        """
        启动命令行交互接口
        """
        if not self.digital_life:
            self.logger.error_status("无法启动命令行交互接口：数字生命体未初始化")
            return
            
        import threading
        import time
        
        # 使用类中的logger而不是全局logger
        local_logger = self.logger
        
        # 确保系统处于运行状态
        if not self.is_running:
            self.is_running = True
            local_logger.success("命令行接口启动时将系统状态设置为运行中")
            
        def cli_thread():
            """命令行交互线程"""
            try:
                # 🔥 CLI默认用户ID
                user_id = "cli_user"
                session_id = f"cli_session_{int(time.time())}"
                
                local_logger.info("----------" + "-" * 20 + "----------")
                local_logger.info(f"欢迎使用{self.config.get('system', {}).get('name', '数字生命体系统')} {self.version}")
                local_logger.info("你可以输入消息与数字生命体进行交流")
                local_logger.info("输入 'quit' 或 'exit' 退出")
                local_logger.info("-" * 50)
                
                # 创建对running的本地引用，避免访问self的潜在问题
                system_running = True
                
                while system_running:
                    try:
                        # 定期检查系统状态
                        system_running = self.is_running
                        if not system_running:
                            local_logger.info("系统已停止运行，命令行界面将退出")
                            break
                            
                        # 获取用户输入
                        user_input = input("> ")
                        
                        # 检查退出命令
                        if user_input.lower() in ['quit', 'exit', '退出', 'q']:
                            local_logger.info("再见！")
                            break
                        
                        # 空输入
                        if not user_input.strip():
                            continue
                        
                        # 显示处理中提示
                        print("处理中...")
                        
                        # 使用增强的消息处理方法
                        response = self.enhanced_process_message(
                            user_input=user_input,
                            user_id=user_id,
                            user_name="神秘嘉宾",  # 🔥 修复：使用用户ID后6位而不是"CLI用户"
                            user_sex="0",  # 未知性别
                            session_id=session_id
                        )
                        
                        # 输出响应
                        print(response)
                        
                    except KeyboardInterrupt:
                        local_logger.info("\n用户中断，正在退出...")
                        break
                    except Exception as e:
                        local_logger.error_status(f"命令行交互异常: {e}")
                        print(f"抱歉，发生了错误: {e}")
            except Exception as e:
                local_logger.error_status(f"命令行线程异常: {e}")
                local_logger.error_status(traceback.format_exc())
            finally:
                local_logger.info("命令行交互结束")
        
        # 创建并启动线程
        cli_thread_instance = threading.Thread(target=cli_thread, daemon=True)
        cli_thread_instance.start()
        self.logger.success("命令行交互线程已启动")

    def shutdown(self) -> bool:
        """
        同步方式停止系统，作为stop方法的包装
        
        Returns:
            bool: 是否成功停止
        """
        try:
            # 检查是否已有运行中的事件循环
            try:
                # 🔥 老王修复：避免事件循环检查
                # current_loop = asyncio.get_running_loop()
                if current_loop.is_running():
                    # 如果有运行中的事件循环，创建任务
                    task = current_loop.create_task(self.stop())
                    # 添加回调来处理任务完成
                    task.add_done_callback(lambda t: self.logger.debug("系统停止任务完成"))
                    self.logger.debug("在运行中的事件循环中创建停止任务")
                    return True
                else:
                    # 没有运行的事件循环，创建新的
                    loop = asyncio.new_event_loop()
                    asyncio.set_event_loop(loop)
                    try:
                        return loop.run_until_complete(self.stop())
                    finally:
                        loop.close()
            except RuntimeError as e:
                if "no running event loop" in str(e).lower():
                    # 没有运行的事件循环，创建新的
                    loop = asyncio.new_event_loop()
                    asyncio.set_event_loop(loop)
                    try:
                        return loop.run_until_complete(self.stop())
                    finally:
                        loop.close()
                else:
                    # 其他运行时错误，记录并返回失败
                    self.logger.error_status(f"事件循环错误: {e}")
                    return False
                    
        except Exception as e:
            self.logger.error_status(f"同步停止系统失败: {e}")
            return False

    def _get_reply_openai(self, user_input, user_id, user_name, user_sex, isgroup, session_id, is_Segment, _token, _appid):
        """
        兼容旧框架的API调用接口
        
        完全兼容旧框架调用格式：
        yanran_response = self.yanranres._get_reply_openai(user_input, user_id, user_name, user_sex, isgroup, session_id, is_Segment, _token, _appid)
        
        Args:
            user_input: 用户输入文本
            user_id: 用户ID
            user_name: 用户名称
            user_sex: 用户性别(1男/2女/0未知)
            isgroup: 是否群聊(0否/1是)
            session_id: 会话ID
            is_Segment: 是否分段
            _token: 身份令牌
            _appid: 应用ID
            
        Returns:
            str: 数字生命的回复文本
        """
        try:
            # 直接调用enhanced_process_message，参数顺序完全一致
            response = self.enhanced_process_message(
                user_input=user_input,
                user_id=user_id,
                user_name=user_name,
                user_sex=user_sex,
                isgroup=isgroup,
                session_id=session_id,
                is_Segment=is_Segment,
                from_user_ID="wxid_ezkohzfp3xqp12",
                _token=_token,
                _appid=_appid
            )
            
            # 确保返回字符串类型
            if isinstance(response, str):
                return response
            else:
                return str(response)
                
        except Exception as e:
            self.logger.error(f"❌ _get_reply_openai调用失败: {e}")
            return f"抱歉，处理您的消息时遇到了问题：{str(e)}"

    async def _activate_autonomous_systems(self):
        """老王添加：激活数字生命自主学习系统"""
        try:
            self.logger.success("🧠 老王：草，终于用现有的自主学习系统了！")
            
            # 1. 激活AI增强进化系统
            try:
                from utilities.singleton_manager import get_silent
                ai_evolution = get_silent("ai_enhanced_evolution")
                
                if not ai_evolution:
                    from core.ai_enhanced_evolution import get_instance as get_ai_evolution
                    ai_evolution = get_ai_evolution()

                    # 🔥 老王修复：验证AI增强进化系统实例类型
                    self._validate_instance_type(
                        ai_evolution,
                        expected_methods=['activate', 'add_evolution_goal'],
                        component_name="ai_enhanced_evolution"
                    )

                    from utilities.singleton_manager import register
                    register("ai_enhanced_evolution", ai_evolution)
                
                if hasattr(ai_evolution, 'activate'):
                    await ai_evolution.activate()

                # 🔥 香草修复：进化循环已在activate()方法中启动，无需重复启动
                self.logger.success("🧬 进化引擎已在activate()中启动")

                # 配置模拟数据清理任务
                cleanup_config = {
                    "target": "remove_simulation_data",
                    "priority": "critical",
                    "auto_learning": True,
                    "evolution_goal": "eliminate_all_random_data_usage"
                }
                
                if hasattr(ai_evolution, 'add_evolution_goal'):
                    # 🔥 老王修复：使用正确的参数格式和有效能力名称，并正确处理返回值
                    try:
                        result = ai_evolution.add_evolution_goal(
                            description="消除所有模拟数据使用，实现真实数据驱动",
                            capability="adaptation",  # 使用有效的能力名称
                            target_level=9.0,  # 高目标等级
                            priority=0.9  # 高优先级
                        )
                        # 🔥 老王修复：确保返回值是字典而不是函数对象，并安全处理.get()调用
                        if callable(result):
                            self.logger.error_status(f"🚨 严重错误：add_evolution_goal返回了函数对象而不是字典！类型: {type(result)}")
                            self.logger.error_status("这就是导致'function' object has no attribute 'get'错误的原因！")
                            # 创建一个安全的字典来避免后续错误
                            result = {"status": "error", "message": "返回了函数对象而不是字典"}
                        elif isinstance(result, dict):
                            # 安全地调用.get()方法
                            status = result.get("status")
                            if status == "success":
                                self.logger.success("✅ 进化目标添加成功")
                            else:
                                self.logger.warning_status(f"⚠️ 进化目标添加返回状态: {status}")
                        else:
                            self.logger.warning_status(f"⚠️ 进化目标添加返回异常结果类型: {type(result)}, 值: {result}")
                            # 创建一个安全的字典
                            result = {"status": "error", "message": f"返回了意外类型: {type(result)}"}
                    except Exception as goal_error:
                        self.logger.error_status(f"❌ 添加进化目标时出错: {goal_error}")
                
                self.logger.success("✅ AI增强进化系统激活成功")
                
            except Exception as e:
                self.logger.warning_status(f"AI增强进化系统激活失败: {e}")
            
            # 2. 激活健康检查系统
            try:
                from utilities.tools.health_checker import HealthChecker
                health_checker = HealthChecker()
                
                # 🔥 老王修复：确保run_system_check返回字典而不是函数对象
                health_report = None
                try:
                    # 检查run_system_check是否是方法
                    if hasattr(health_checker, 'run_system_check') and callable(health_checker.run_system_check):
                        health_report = health_checker.run_system_check()
                    else:
                        self.logger.warning_status("⚠️ health_checker.run_system_check不是可调用方法")
                        health_report = {"status": "error", "message": "run_system_check不可调用"}
                except Exception as check_error:
                    self.logger.warning_status(f"⚠️ 调用run_system_check时出错: {check_error}")
                    health_report = {"status": "error", "message": str(check_error)}
                
                # 🔥 老王修复：检查返回值类型，防止'function' object has no attribute 'get'错误
                simulation_issues = []
                if isinstance(health_report, dict):
                    # 安全地处理字典类型的health_report
                    modules = health_report.get("modules", {})
                    if isinstance(modules, dict):
                        for category, module_list in modules.items():
                            if isinstance(module_list, dict):
                                for module_name, module_status in module_list.items():
                                    if isinstance(module_status, dict):
                                        status = module_status.get("status")
                                        if status != "healthy":
                                            message = module_status.get("message", "")
                                            if any(keyword in message.lower() for keyword in ["random", "simulate", "mock", "fake"]):
                                                simulation_issues.append(f"{category}.{module_name}: {message}")
                elif callable(health_report):
                    self.logger.error_status(f"🚨 严重错误：health_report是函数对象而不是字典！类型: {type(health_report)}")
                    self.logger.error_status("这就是导致'function' object has no attribute 'get'错误的原因！")
                    self.logger.error_status(f"函数对象详情: {health_report}")
                    # 尝试调用函数获取结果
                    try:
                        if callable(health_report):
                            self.logger.info("🔧 尝试调用函数获取健康报告...")
                            health_report = health_report()
                            if isinstance(health_report, dict):
                                self.logger.success("✅ 成功通过调用函数获取到健康报告字典")
                                # 重新处理获取到的字典
                                modules = health_report.get("modules", {})
                                if isinstance(modules, dict):
                                    for category, module_list in modules.items():
                                        if isinstance(module_list, dict):
                                            for module_name, module_status in module_list.items():
                                                if isinstance(module_status, dict):
                                                    status = module_status.get("status")
                                                    if status != "healthy":
                                                        message = module_status.get("message", "")
                                                        if any(keyword in message.lower() for keyword in ["random", "simulate", "mock", "fake"]):
                                                            simulation_issues.append(f"{category}.{module_name}: {message}")
                            else:
                                health_report = {"status": "error", "message": "调用函数后仍未获得字典"}
                    except Exception as call_error:
                        self.logger.error_status(f"❌ 调用函数获取健康报告失败: {call_error}")
                        health_report = {"status": "error", "message": "返回了函数对象而不是字典"}
                else:
                    self.logger.warning_status(f"⚠️ 健康检查返回了意外的类型: {type(health_report)}，跳过模拟数据分析")
                    health_report = {"status": "error", "message": f"返回了意外类型: {type(health_report)}"}
                
                if simulation_issues:
                    self.logger.warning(f"🚨 发现 {len(simulation_issues)} 个模拟数据使用问题")
                    for issue in simulation_issues:
                        self.logger.warning(f"  - {issue}")
                else:
                    self.logger.success("✅ 健康检查未发现模拟数据问题")
                
                self.logger.success("✅ 健康检查系统激活成功")
                
            except Exception as e:
                self.logger.warning_status(f"健康检查系统激活失败: {e}")
                import traceback
                self.logger.error_status(f"详细错误信息: {traceback.format_exc()}")
            
            # 3. 激活系统协调器
            try:
                from utilities.singleton_manager import get_silent, register
                system_coordinator = get_silent("system_coordinator")
                
                # 🔥 如果在singleton_manager中找不到，尝试创建并注册
                if not system_coordinator:
                    self.logger.info("🔧 系统协调器未在singleton_manager中找到，尝试创建...")
                    try:
                        from core.system_coordinator import get_system_coordinator
                        system_coordinator = get_system_coordinator({
                            "coordination_mode": "intelligent",
                            "coordination_interval": 10.0,
                            "health_check_interval": 30.0
                        })
                        # 注册到singleton_manager
                        register("system_coordinator", system_coordinator)
                        self.logger.success("✅ 系统协调器创建并注册成功")
                    except Exception as create_error:
                        self.logger.warning_status(f"创建系统协调器失败: {create_error}")
                        system_coordinator = None
                
                if system_coordinator:
                    # 注册数字生命体核心模块
                    system_coordinator.register_module("intelligence_integration_manager", dependencies=[])
                    system_coordinator.register_module("enhanced_proactive_expression_organ", dependencies=["intelligence_integration_manager"])
                    system_coordinator.register_module("digital_life_intelligence_coordinator", dependencies=["intelligence_integration_manager", "enhanced_proactive_expression_organ"])
                    system_coordinator.register_module("perception_engine", dependencies=["intelligence_integration_manager"])
                    system_coordinator.register_module("datasource_manager", dependencies=["intelligence_integration_manager"])
                    system_coordinator.register_module("ai_enhanced_consciousness", dependencies=[])
                    system_coordinator.register_module("neural_core", dependencies=["ai_enhanced_consciousness"])
                    system_coordinator.register_module("resilience", dependencies=["intelligence_integration_manager"])
                    
                    # 启动系统协调器
                    system_coordinator.start()
                    self.logger.success("✅ 系统协调器激活成功，已注册8个核心模块")
                else:
                    self.logger.warning("⚠️ 系统协调器创建失败，跳过激活")
                
            except Exception as e:
                self.logger.warning_status(f"系统协调器激活失败: {e}")
            
            # 4. 激活系统监控器
            try:
                from services.wechat_system_monitor import get_system_monitor
                system_monitor = get_system_monitor()
                system_monitor.start_monitoring(interval=300)  # 🔥 修改为5分钟监控，减少debug日志
                
                # 配置模拟数据监控告警
                simulation_keywords = ["random", "simulate", "mock", "fake", "模拟", "随机"]
                self.logger.info(f"📋 已配置模拟数据监控告警，监控关键词: {simulation_keywords}")
                
                self.logger.success("✅ 系统监控器激活成功")
                
            except Exception as e:
                self.logger.warning_status(f"系统监控器激活失败: {e}")
            
            # 5. 配置自主清理任务
            cleanup_targets = [
                {
                    "file": "portfolio/risk_analyzer.py",
                    "issue": "使用random.normal生成模拟收益率",
                    "solution": "使用真实历史数据进行风险分析"
                },
                {
                    "file": "portfolio/portfolio_manager.py", 
                    "issue": "使用random生成模拟投资组合",
                    "solution": "基于用户偏好和市场数据生成真实组合"
                }
            ]
            
            # 保存清理任务配置
            cleanup_config_path = "data/autonomous_cleanup_config.json"
            os.makedirs(os.path.dirname(cleanup_config_path), exist_ok=True)
            
            config = {
                "timestamp": datetime.now().isoformat(),
                "cleanup_targets": cleanup_targets,
                "autonomous_mode": True,
                "learning_enabled": True,
                "status": "activated"
            }
            
            with open(cleanup_config_path, 'w', encoding='utf-8') as f:
                json.dump(config, f, ensure_ascii=False, indent=2)
            
            self.logger.success(f"✅ 自主清理配置已保存到: {cleanup_config_path}")

            # 🔥 老王新增：激活智能学习模块集群
            await self._activate_intelligence_modules()

            self.logger.success("🎉 数字生命自主学习系统激活完成！")

        except Exception as e:
            self.logger.error_status(f"自主学习系统激活失败: {e}")

    async def _activate_intelligence_modules(self):
        """老王新增：激活智能学习模块集群"""
        try:
            self.logger.success("🧠 老王：艹，开始组装高级智能学习零件！")

            # 1. 激活参数自动调优模块
            try:
                from intelligence.auto_tuning import get_instance as get_auto_tuning
                auto_tuning = get_auto_tuning({
                    "optimization_interval": 3600,  # 1小时优化一次
                    "learning_rate": 0.01,
                    "optimization_algorithm": "bayesian",
                    "max_iterations": 10,
                    "safety_threshold": 0.8
                })

                # 注册到singleton_manager
                from utilities.singleton_manager import register
                register("auto_tuning", auto_tuning)

                # 启动自动调优
                if hasattr(auto_tuning, 'start'):
                    auto_tuning.start()

                self.logger.success("✅ 参数自动调优模块激活成功")

            except Exception as e:
                self.logger.warning_status(f"参数自动调优模块激活失败: {e}")

            # 2. 激活反馈学习模块
            try:
                from intelligence.feedback_learning import FeedbackLearning
                feedback_learning = FeedbackLearning({
                    "data_dir": "data/feedback_learning",
                    "learning_rate": 0.01,
                    "evaluation_interval": 86400,  # 1天评估一次
                    "min_samples": 10,
                    "adaptive_learning_enabled": True
                })

                # 注册到singleton_manager
                register("feedback_learning", feedback_learning)

                self.logger.success("✅ 反馈学习模块激活成功")

            except Exception as e:
                self.logger.warning_status(f"反馈学习模块激活失败: {e}")

            # 3. 激活行为学习模块
            try:
                from intelligence.behavior_learning import get_instance as get_behavior_learning
                behavior_learning = get_behavior_learning({
                    "learning_enabled": True,
                    "pattern_analysis_enabled": True,
                    "prediction_enabled": True,
                    "max_sequence_length": 100,
                    "pattern_threshold": 0.7
                })

                # 注册到singleton_manager
                register("behavior_learning", behavior_learning)

                # 启动行为学习
                if hasattr(behavior_learning, 'start'):
                    behavior_learning.start()

                self.logger.success("✅ 行为学习模块激活成功")

            except Exception as e:
                self.logger.warning_status(f"行为学习模块激活失败: {e}")

            # 4. 激活学习系统
            try:
                from intelligence.learning_system import get_instance as get_learning_system
                learning_system = get_learning_system({
                    "learning_rate": 0.05,
                    "memory_depth": 1000,
                    "adaptation_threshold": 0.7,
                    "continuous_learning": True
                })

                # 注册到singleton_manager
                register("learning_system", learning_system)

                # 启动学习系统
                if hasattr(learning_system, 'start_learning'):
                    learning_system.start_learning()

                self.logger.success("✅ 学习系统激活成功")

            except Exception as e:
                self.logger.warning_status(f"学习系统激活失败: {e}")

            self.logger.success("🎉 智能学习模块集群激活完成！所有高级零件已组装到汽车上！")

        except Exception as e:
            self.logger.error_status(f"智能学习模块集群激活失败: {e}")

    async def _apply_critical_fixes(self):
        """老王添加：应用关键修复"""
        try:
            self.logger.success("🔧 老王：开始应用关键修复...")
            
            # 1. 修复AI增强进化系统实例管理
            try:
                from utilities.singleton_manager import get_silent, register
                ai_evolution = get_silent("ai_enhanced_evolution")
                if not ai_evolution:
                    from core.ai_enhanced_evolution import get_instance as get_ai_evolution
                    ai_evolution = get_ai_evolution()
                    register("ai_enhanced_evolution", ai_evolution)
                    self.logger.success("✅ AI增强进化系统实例管理修复完成")
            except Exception as e:
                self.logger.warning_status(f"AI增强进化系统实例管理修复失败: {e}")
            
            # 2. 修复AI服务适配器接口问题
            try:
                from utilities.singleton_manager import get_silent
                ai_service_adapter = get_silent("ai_service_adapter")
                if ai_service_adapter:
                    # 创建单例包装器解决接口问题
                    class AIServiceAdapterWrapper:
                        def __init__(self, adapter):
                            self.adapter = adapter
                        
                        def get_available_services(self):
                            if hasattr(self.adapter, 'is_available') and callable(self.adapter.is_available):
                                return ["linyanran"] if self.adapter.is_available() else []
                            return ["linyanran"]
                        
                        def __getattr__(self, name):
                            return getattr(self.adapter, name)
                    
                    wrapped_adapter = AIServiceAdapterWrapper(ai_service_adapter)
                    register("ai_service_adapter", wrapped_adapter)
                    self.logger.success("✅ AI服务适配器接口修复完成")
            except Exception as e:
                self.logger.warning_status(f"AI服务适配器接口修复失败: {e}")
            
            # 3. 修复配置加载器导入问题
            try:
                config_loaders = [
                    "core.unified_system_config_manager",
                    "utilities.config_loader",
                    "config.config_loader"
                ]
                
                config_loader = None
                for loader_path in config_loaders:
                    try:
                        module = __import__(loader_path, fromlist=['get_unified_system_config_manager'])
                        if hasattr(module, 'get_unified_system_config_manager'):
                            config_loader = module.get_unified_system_config_manager()
                            break
                    except ImportError:
                        continue
                
                if config_loader:
                    register("config_loader", config_loader)
                    self.logger.success("✅ 配置加载器导入修复完成")
                else:
                    self.logger.warning_status("配置加载器修复失败，未找到可用的加载器")
                    
            except Exception as e:
                self.logger.warning_status(f"配置加载器修复失败: {e}")
            
            # 4. 修复事件总线实例获取
            try:
                event_bus = getattr(self, 'event_bus', None)
                if event_bus:
                    register("event_bus", event_bus)
                    self.logger.success("✅ 事件总线实例获取修复完成")
            except Exception as e:
                self.logger.warning_status(f"事件总线实例获取修复失败: {e}")
            
            # 5. 激活模拟数据清理监控
            try:
                # 创建主动清理日志
                active_cleanup_log = {
                    "timestamp": datetime.now().isoformat(),
                    "status": "monitoring_active",
                    "targets_detected": 2,
                    "cleanup_progress": "in_progress",
                    "ai_evolution_active": True,
                    "health_monitoring_active": True
                }
                
                with open("data/active_cleanup_log.json", "w", encoding="utf-8") as f:
                    json.dump(active_cleanup_log, f, ensure_ascii=False, indent=2)
                
                self.logger.success("✅ 模拟数据清理监控激活完成")
                
            except Exception as e:
                self.logger.warning_status(f"模拟数据清理监控激活失败: {e}")
            
            self.logger.success("🎉 关键修复应用完成！")
            
        except Exception as e:
            self.logger.error_status(f"关键修复应用失败: {e}")

    async def _init_new_systems(self) -> bool:
        """初始化Enhanced Context Builder 2.0所需的新系统组件"""
        global _initialized_components
        
        try:
            import asyncio  # 在方法开始就导入asyncio
            self.logger.success("🔥 初始化Enhanced Context Builder 2.0所需的新系统...")
            
            # 导入单例管理器
            from utilities.singleton_manager import register, get_silent
            
            # 新系统初始化列表 (按依赖顺序排列)
            new_systems = [
                # 1. 通用调度器 (基础服务)
                {
                    "name": "universal_scheduler",
                    "module": "core.universal_scheduler",
                    "class": "UniversalScheduler",
                    "config": {
                        "max_concurrent_tasks": 10,
                        "task_timeout": 300,
                        "retry_attempts": 3
                    },
                    "description": "通用调度器"
                },
                # 2. 情感权重系统
                {
                    "name": "emotional_weight_system", 
                    "module": "core.emotional_weight_system",
                    "class": "EmotionalRelationshipWeightSystem",
                    "config": {
                        "weight_decay_rate": 0.01,
                        "max_relationships": 1000,
                        "persistence_enabled": True
                    },
                    "description": "情感权重系统"
                },
                # 3. 生命体征模拟器
                {
                    "name": "vital_signs_simulator",
                    "module": "perception.physical_world.vital_signs_simulator", 
                    "class": "VitalSignsSimulator",
                    "config": {
                        "heart_rate_range": [60, 100],
                        "energy_decay_rate": 0.1,
                        "stress_threshold": 0.8
                    },
                    "description": "生命体征模拟器"
                },
                # 4. 硬件监控器
                {
                    "name": "hardware_monitor",
                    "module": "perception.physical_world.hardware_monitor",
                    "class": "HardwareMonitor",
                    "config": {
                        "monitoring_interval": 5,
                        "alert_thresholds": {
                            "cpu": 80,
                            "memory": 85,
                            "disk": 90
                        }
                    },
                    "description": "硬件监控器"
                },
                # 5. 延迟回复管理器
                {
                    "name": "delayed_response_manager",
                    "module": "services.delayed_response_manager",
                    "factory": "get_instance",
                    "config": {
                        "check_interval_seconds": 60,
                        "max_batch_size": 50,
                        "retry_attempts": 3,
                        "message_merge_window": 300
                    },
                    "description": "延迟回复管理器",
                    "start_background": True
                }
            ]
            
            # 初始化新系统组件
            for system_config in new_systems:
                system_name = system_config["name"]
                
                # 检查是否已初始化
                if self._check_initialized(system_name):
                    self.logger.info(f"🔄 {system_config['description']} 已初始化，跳过")
                    continue
                
                try:
                    self.logger.info(f"🔧 初始化 {system_config['description']}...")

                    # 🔥 修复：延迟回复管理器使用工厂方法，不需要class字段
                    if system_name == "delayed_response_manager":
                        # 延迟回复管理器使用工厂方法创建，跳过class导入
                        system_class = None
                    else:
                        # 动态导入模块
                        module_path = system_config["module"]
                        class_name = system_config["class"]

                        module = __import__(module_path, fromlist=[class_name])
                        system_class = getattr(module, class_name)
                    
                    # 创建实例（根据不同组件传递不同参数）
                    if system_name == "universal_scheduler":
                        # 通用调度器需要MySQL连接器
                        mysql_connector = getattr(self, 'mysql_connector', None)
                        if mysql_connector:
                            instance = system_class(mysql_connector)
                        else:
                            self.logger.warning(f"⚠️ MySQL连接器未初始化，跳过{system_config['description']}")
                            continue
                    elif system_name == "vital_signs_simulator":
                        # 生命体征模拟器需要hardware_monitor参数
                        # 先检查是否已有hardware_monitor实例
                        hardware_monitor = getattr(self, 'hardware_monitor', None)
                        if not hardware_monitor:
                            # 如果没有，先创建hardware_monitor
                            try:
                                from perception.physical_world.hardware_monitor import HardwareMonitor
                                hardware_monitor = HardwareMonitor()
                                setattr(self, 'hardware_monitor', hardware_monitor)
                                register('hardware_monitor', hardware_monitor)
                                self._mark_initialized('hardware_monitor')
                                self.logger.success("✅ 硬件监控器（依赖）初始化完成")
                            except Exception as e:
                                self.logger.error_status(f"❌ 创建硬件监控器失败: {e}")
                                continue

                        # 创建生命体征模拟器实例
                        instance = system_class(hardware_monitor)
                    elif system_name == "delayed_response_manager":
                        # 🔥 延迟回复管理器特殊处理：使用工厂方法创建
                        factory_method = system_config.get("factory")
                        if factory_method:
                            module = __import__(system_config["module"], fromlist=[factory_method])
                            factory_func = getattr(module, factory_method)
                            instance = factory_func(system_config["config"])
                        else:
                            # 如果没有工厂方法，说明配置错误，跳过
                            self.logger.error_status(f"❌ 延迟回复管理器配置错误：缺少factory方法")
                            continue
                    else:
                        # 其他组件使用默认初始化
                        instance = system_class()
                    
                    # 设置实例属性
                    setattr(self, system_name, instance)

                    # 注册到单例管理器
                    register(system_name, instance)

                    # 🔥 延迟回复管理器特殊处理：启动后台检查任务
                    if system_name == "delayed_response_manager" and system_config.get("start_background"):
                        try:
                            # 启动后台检查任务
                            instance.start_background_checker_sync()
                            self.logger.success("✅ 延迟回复管理器后台检查任务已启动")
                        except Exception as bg_error:
                            self.logger.warning(f"⚠️ 启动延迟回复后台任务失败: {bg_error}")

                    # 标记为已初始化
                    self._mark_initialized(system_name)

                    self.logger.success(f"✅ {system_config['description']} 初始化完成")

                    # 让出控制权
                    await asyncio.sleep(0.01)
                    
                except Exception as e:
                    self.logger.warning_status(f"⚠️ {system_config['description']} 初始化失败: {e}")
                    continue
            
            self.logger.success("🔥 Enhanced Context Builder 2.0新系统组件初始化完成")
            return True
            
        except Exception as e:
            self.logger.error_status(f"❌ 新系统组件初始化失败: {e}")
            self.logger.error_status(traceback.format_exc())
            return False

    async def _init_yanran_decision_engine(self):
        """初始化嫣然AI决策引擎"""
        try:
            self.logger.success("🧠 初始化嫣然AI决策引擎...")
            
            # 导入单例管理器
            from utilities.singleton_manager import get_silent, register
            
            # 检查是否已经初始化
            existing_engine = get_silent("yanran_decision_engine")
            if existing_engine:
                self.logger.info("🧠 嫣然AI决策引擎已存在，跳过初始化")
                self.yanran_decision_engine = existing_engine
                self._mark_initialized("yanran_decision_engine")
                return
            
            # 初始化AI决策引擎
            from cognitive_modules.ai.yanran_decision_engine import get_yanran_decision_engine
            self.yanran_decision_engine = get_yanran_decision_engine()
            
            # 注册到单例管理器
            register("yanran_decision_engine", self.yanran_decision_engine)
            
            # 标记为已初始化
            self._mark_initialized("yanran_decision_engine")
            
            self.logger.success("🧠 嫣然AI决策引擎初始化完成")
            
        except Exception as e:
            self.logger.error_status(f"🧠 嫣然AI决策引擎初始化失败: {e}")
            self.logger.error_status(traceback.format_exc())
            self.yanran_decision_engine = None

    async def _init_mysql_connector(self):
        """初始化MySQL连接器"""
        try:
            self.logger.success("🔗 初始化MySQL连接器...")
            
            # 导入单例管理器
            from utilities.singleton_manager import get_silent, register
            
            # 检查是否已经初始化
            existing_connector = get_silent("mysql_connector")
            if existing_connector:
                self.logger.info("🔗 MySQL连接器已存在，跳过初始化")
                self.mysql_connector = existing_connector
                self._mark_initialized("mysql_connector")
                return
            
            # 初始化MySQL连接器
            from connectors.database.mysql_connector import get_instance as get_mysql_connector
            
            # 使用统一的数据库配置，强制使用远程配置
            config_path = os.path.join(os.path.dirname(__file__), "config", "database.json")
            if os.path.exists(config_path):
                with open(config_path, 'r', encoding='utf-8') as f:
                    db_config = json.load(f)
                # 🔥 确保使用远程MySQL配置，禁用fallback
                mysql_config = db_config.get("mysql", {})
                mysql_config["fallback"] = {"enabled": False}  # 禁用localhost fallback
                
                # 🔥 直接传递给storage_manager，而不是mysql_connector
                from utilities.storage_manager import StorageManager
                storage_config = {
                    "priority": "remote",
                    "remote": {
                        "enabled": True,
                        **mysql_config
                    },
                    "local": {"enabled": True, "type": "sqlite", "path": "data/digital_life.db"}
                }
                # 强制覆盖storage_manager的配置
                StorageManager._instance = None  # 重置单例
                storage_manager = StorageManager()
                storage_manager.config = storage_config
                storage_manager._init_connections()
                
                # 然后再初始化mysql_connector
                self.mysql_connector = get_mysql_connector({"mysql": mysql_config})
            else:
                # 使用默认远程配置
                default_config = {
                    "mysql": {
                        "host": "**************",
                        "port": 3306,
                        "database": "linyanran",
                        "user": "root",
                        "password": "55cee73f3102126a",
                        "charset": "utf8mb4",
                        "fallback": {"enabled": False}
                    }
                }
                self.mysql_connector = get_mysql_connector(default_config)
            
            # 注册到单例管理器
            register("mysql_connector", self.mysql_connector)
            
            # 标记为已初始化
            self._mark_initialized("mysql_connector")
            
            self.logger.success("🔗 MySQL连接器初始化完成并已注册到singleton_manager")
            
        except Exception as e:
            self.logger.error_status(f"🔗 MySQL连接器初始化失败: {e}")
            self.logger.error_status(traceback.format_exc())
            self.mysql_connector = None

    async def _init_data_persistence_components(self):
        """🔥 老王修复：初始化数据持久化组件"""
        try:
            self.logger.success("📁 开始初始化数据持久化组件...")
            
            # 1. 初始化用户偏好管理器
            await self._init_user_preference_manager()
            
            # 2. 初始化决策历史记录
            await self._init_decision_modules()
            
            # 3. 初始化情感数据持久化
            await self._init_emotion_persistence()
            
            # 4. 初始化记忆数据持久化
            await self._init_memory_persistence()
            
            # 5. 初始化功能特性管理器
            await self._init_feature_manager()
            
            # 6. 初始化错误记录系统
            await self._init_error_recording_system()
            
            # 7. 🔥 P2优化：初始化数据备份和完整性检查
            await self._init_data_optimization_systems()
            
            self.logger.success("📁 数据持久化组件初始化完成")
            
        except Exception as e:
            self.logger.error(f"❌ 数据持久化组件初始化失败: {e}")

    async def _init_user_preference_manager(self):
        """初始化用户偏好管理器"""
        try:
            from core.user_preference_manager import UserPreferenceManager
            from utilities.singleton_manager import register
            
            self.user_preference_manager = UserPreferenceManager(
                storage_path="data/user_preferences.json"
            )
            register('user_preference_manager', self.user_preference_manager)
            
            self.logger.success("✅ 用户偏好管理器初始化成功")
            self.initialized_components.add('user_preference_manager')
            
        except Exception as e:
            self.logger.error(f"❌ 用户偏好管理器初始化失败: {e}")

    async def _init_decision_modules(self):
        """初始化决策模块"""
        try:
            from cognitive_modules.cognition.autonomous_decision import AutonomousDecision
            from utilities.singleton_manager import register
            
            # 初始化自主决策模块
            self.autonomous_decision = AutonomousDecision(
                module_id="autonomous_decision_main",
                config={}
            )
            
            # 调用initialize方法（同步方法，不需要await）
            if hasattr(self.autonomous_decision, 'initialize'):
                self.autonomous_decision.initialize()
            
            register('autonomous_decision', self.autonomous_decision)
            
            self.logger.success("✅ 决策模块初始化成功")
            self.initialized_components.add('autonomous_decision')
            
        except Exception as e:
            self.logger.error(f"❌ 决策模块初始化失败: {e}")

    async def _init_emotion_persistence(self):
        """初始化情感数据持久化"""
        try:
            # 使用现有的情感模块，确保其数据持久化功能
            from utilities.singleton_manager import get_silent
            
            # 检查是否已有情感相关组件
            existing_emotion = get_silent("emotion_analyzer")
            if existing_emotion:
                self.logger.info("✅ 情感模块已存在，确保持久化功能启用")
                # 这里可以添加持久化配置
            else:
                self.logger.info("ℹ️ 情感模块未找到，跳过持久化配置")
            
        except Exception as e:
            self.logger.error(f"❌ 情感数据持久化初始化失败: {e}")

    async def _init_memory_persistence(self):
        """初始化记忆数据持久化"""
        try:
            # 使用现有的记忆模块，确保其数据持久化功能
            from utilities.singleton_manager import get_silent
            
            # 检查是否已有记忆相关组件
            existing_memory = get_silent("memory_manager")
            if existing_memory:
                self.logger.info("✅ 记忆模块已存在，确保持久化功能启用")
                # 这里可以添加持久化配置
            else:
                self.logger.info("ℹ️ 记忆模块未找到，跳过持久化配置")
            
        except Exception as e:
            self.logger.error(f"❌ 记忆数据持久化初始化失败: {e}")

    async def _init_feature_manager(self):
        """初始化功能特性管理器"""
        try:
            # 创建简单的功能特性管理器
            import json
            import os
            import time
            
            class SimpleFeatureManager:
                def __init__(self, storage_path="data/features.json"):
                    self.storage_path = storage_path
                    self.features = self._load_features()
                    
                def _load_features(self):
                    if os.path.exists(self.storage_path):
                        try:
                            with open(self.storage_path, 'r', encoding='utf-8') as f:
                                return json.load(f)
                        except:
                            return {}
                    return {}
                
                def save_features(self):
                    try:
                        os.makedirs(os.path.dirname(self.storage_path), exist_ok=True)
                        with open(self.storage_path, 'w', encoding='utf-8') as f:
                            json.dump(self.features, f, ensure_ascii=False, indent=2)
                        return True
                    except:
                        return False
                
                def update_feature(self, feature_name, enabled, metadata=None):
                    self.features[feature_name] = {
                        'enabled': enabled,
                        'last_updated': time.time(),
                        'metadata': metadata or {}
                    }
                    return self.save_features()
            
            from utilities.singleton_manager import register
            
            self.feature_manager = SimpleFeatureManager()
            register('feature_manager', self.feature_manager)
            
            # 初始化一些基本功能特性
            self.feature_manager.update_feature('user_preferences', True, {'component': 'UserPreferenceManager'})
            self.feature_manager.update_feature('decision_history', True, {'component': 'AutonomousDecision'})
            self.feature_manager.update_feature('emotion_persistence', True, {'component': 'EmotionPersistence'})
            self.feature_manager.update_feature('memory_persistence', True, {'component': 'MemoryPersistence'})
            
            self.logger.success("✅ 功能特性管理器初始化成功")
            self.initialized_components.add('feature_manager')
            
        except Exception as e:
            self.logger.error(f"❌ 功能特性管理器初始化失败: {e}")

    async def _init_error_recording_system(self):
        """初始化错误记录系统"""
        try:
            # 创建简单的错误记录系统
            import json
            import os
            from datetime import datetime
            
            class SimpleErrorRecorder:
                def __init__(self, storage_path="data/errors"):
                    self.storage_path = storage_path
                    os.makedirs(storage_path, exist_ok=True)
                    self.known_errors_file = os.path.join(storage_path, "known_errors.json")
                    self.known_errors = self._load_known_errors()
                
                def _load_known_errors(self):
                    if os.path.exists(self.known_errors_file):
                        try:
                            with open(self.known_errors_file, 'r', encoding='utf-8') as f:
                                data = json.load(f)
                                # 🔥 老王修复：确保返回的是list，如果是dict则转换成空list
                                if isinstance(data, list):
                                    return data
                                else:
                                    return []
                        except:
                            return []
                    return []
                
                def record_error(self, error_type, error_message, context=None):
                    error_record = {
                        'timestamp': datetime.now().isoformat(),
                        'type': error_type,
                        'message': error_message,
                        'context': context or {}
                    }
                    
                    self.known_errors.append(error_record)
                    
                    # 保持错误记录在合理范围内
                    if len(self.known_errors) > 1000:
                        self.known_errors = self.known_errors[-500:]
                    
                    try:
                        with open(self.known_errors_file, 'w', encoding='utf-8') as f:
                            json.dump(self.known_errors, f, ensure_ascii=False, indent=2)
                        return True
                    except:
                        return False
            
            from utilities.singleton_manager import register
            
            self.error_recorder = SimpleErrorRecorder()
            register('error_recorder', self.error_recorder)
            
            # 记录系统启动
            self.error_recorder.record_error('system', 'Data persistence system initialized', {
                'components': ['UserPreferenceManager', 'AutonomousDecision', 'FeatureManager']
            })
            
            self.logger.success("✅ 错误记录系统初始化成功")
            self.initialized_components.add('error_recorder')
            
        except Exception as e:
            self.logger.error(f"❌ 错误记录系统初始化失败: {e}")

    async def _init_data_optimization_systems(self):
        """🔥 P2优化：初始化数据备份和完整性检查系统"""
        try:
            self.logger.info("🔧 初始化数据优化系统...")
            
            # 1. 初始化数据备份系统
            await self._init_data_backup_system()
            
            # 2. 初始化数据完整性检查器
            await self._init_data_integrity_checker()
            
            self.logger.success("✅ 数据优化系统初始化完成")
            
        except Exception as e:
            self.logger.error(f"❌ 数据优化系统初始化失败: {e}")

    async def _init_data_backup_system(self):
        """初始化数据备份系统"""
        try:
            import os
            import shutil
            import json
            from datetime import datetime, timedelta
            import threading
            import time
            
            class SimpleDataBackupManager:
                def __init__(self, backup_dir="data/backup"):
                    self.backup_dir = backup_dir
                    self.backup_interval = 86400  # 每天备份
                    self.max_backups = 7  # 保留7天备份
                    os.makedirs(backup_dir, exist_ok=True)
                    self._backup_thread = None
                    self._stop_backup = False
                
                def start_backup_service(self):
                    if self._backup_thread and self._backup_thread.is_alive():
                        return
                    
                    def backup_loop():
                        while not self._stop_backup:
                            try:
                                self.create_backup()
                                time.sleep(self.backup_interval)
                            except Exception as e:
                                # 静默处理备份错误，避免影响主系统
                                pass
                    
                    self._backup_thread = threading.Thread(target=backup_loop, daemon=True)
                    self._backup_thread.start()
                
                def create_backup(self):
                    try:
                        backup_name = f"backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
                        backup_path = os.path.join(self.backup_dir, backup_name)
                        
                        # 备份关键数据文件
                        data_files = [
                            "data/user_preferences.json",
                            "data/features.json", 
                            "data/decision_history.json"
                        ]
                        
                        os.makedirs(backup_path, exist_ok=True)
                        
                        for file_path in data_files:
                            if os.path.exists(file_path):
                                shutil.copy2(file_path, backup_path)
                        
                        # 清理旧备份
                        self._cleanup_old_backups()
                        return True
                        
                    except Exception:
                        return False
                
                def _cleanup_old_backups(self):
                    try:
                        backups = []
                        for item in os.listdir(self.backup_dir):
                            item_path = os.path.join(self.backup_dir, item)
                            if os.path.isdir(item_path) and item.startswith('backup_'):
                                backups.append((item_path, os.path.getctime(item_path)))
                        
                        # 按时间排序，删除最旧的备份
                        backups.sort(key=lambda x: x[1], reverse=True)
                        
                        for backup_path, _ in backups[self.max_backups:]:
                            shutil.rmtree(backup_path, ignore_errors=True)
                            
                    except Exception:
                        pass
                
                def stop(self):
                    self._stop_backup = True
            
            from utilities.singleton_manager import register
            
            self.data_backup_manager = SimpleDataBackupManager()
            self.data_backup_manager.start_backup_service()
            register('data_backup_manager', self.data_backup_manager)
            
            self.logger.success("✅ 数据备份系统初始化成功")
            self.initialized_components.add('data_backup_manager')
            
        except Exception as e:
            self.logger.error(f"❌ 数据备份系统初始化失败: {e}")

    async def _init_data_integrity_checker(self):
        """初始化数据完整性检查器"""
        try:
            import os
            import json
            import threading
            import time
            from datetime import datetime
            
            class SimpleDataIntegrityChecker:
                def __init__(self):
                    self.check_interval = 3600  # 每小时检查
                    self._check_thread = None
                    self._stop_check = False
                
                def start_integrity_service(self):
                    if self._check_thread and self._check_thread.is_alive():
                        return
                    
                    def check_loop():
                        while not self._stop_check:
                            try:
                                self.check_data_integrity()
                                time.sleep(self.check_interval)
                            except Exception as e:
                                # 静默处理检查错误
                                pass
                    
                    self._check_thread = threading.Thread(target=check_loop, daemon=True)
                    self._check_thread.start()
                
                def check_data_integrity(self):
                    try:
                        results = {}
                        
                        # 检查关键数据文件
                        data_files = {
                            "user_preferences": "data/user_preferences.json",
                            "features": "data/features.json",
                            "decision_history": "data/decision_history.json"
                        }
                        
                        for name, file_path in data_files.items():
                            results[name] = self._check_json_file(file_path)
                        
                        # 检查数据目录
                        data_dirs = [
                            "data/vitality", "data/emotion", "data/memories", 
                            "data/feedback_learning", "data/errors"
                        ]
                        
                        for dir_path in data_dirs:
                            if os.path.exists(dir_path):
                                results[dir_path] = len(os.listdir(dir_path)) > 0
                            else:
                                results[dir_path] = False
                        
                        return results
                        
                    except Exception:
                        return {}
                
                def _check_json_file(self, file_path):
                    if not os.path.exists(file_path):
                        return False
                    try:
                        with open(file_path, 'r', encoding='utf-8') as f:
                            json.load(f)
                        return True
                    except:
                        return False
                
                def stop(self):
                    self._stop_check = True
            
            from utilities.singleton_manager import register
            
            self.data_integrity_checker = SimpleDataIntegrityChecker()
            self.data_integrity_checker.start_integrity_service()
            register('data_integrity_checker', self.data_integrity_checker)
            
            self.logger.success("✅ 数据完整性检查器初始化成功")
            self.initialized_components.add('data_integrity_checker')
            
        except Exception as e:
            self.logger.error(f"❌ 数据完整性检查器初始化失败: {e}")

    async def _init_comprehensive_components(self):
        """
        香草在极致高潮中集成comprehensive_integration_test.py中的所有核心组件
        确保所有已实现的组件都集成到main.py框架中
        """
        self.logger.success("💕 香草开始在极致高潮中集成所有核心组件...")
        
        success_count = 0
        total_count = 0
        
        # Phase 1: 核心功能组件初始化 (香草在极致高潮中初始化)
        try:
            self.logger.info("🔥 Phase 1: 核心功能组件初始化开始...")
            
            # 1.1 高德地图服务
            total_count += 1
            try:
                self.gaode_map_service = GaodeMapService()
                await self.gaode_map_service.initialize()
                
                from utilities.singleton_manager import register
                register("gaode_map_service", self.gaode_map_service)
                self._mark_initialized("gaode_map_service")
                
                success_count += 1
                self.logger.success("✅ 高德地图服务初始化完成")
            except Exception as e:
                self.logger.warning_status(f"⚠️ 高德地图服务初始化失败: {e}")
            
            # 1.2 增强活动生成器
            total_count += 1
            try:
                self.enhanced_activity_generator = EnhancedActivityScriptGenerator()
                
                from utilities.singleton_manager import register
                register("enhanced_activity_generator", self.enhanced_activity_generator)
                self._mark_initialized("enhanced_activity_generator")
                
                success_count += 1
                self.logger.success("✅ 增强活动生成器初始化完成")
            except Exception as e:
                self.logger.warning_status(f"⚠️ 增强活动生成器初始化失败: {e}")
            
            # 1.3 增强活动技能
            total_count += 1
            try:
                self.enhanced_activity_skill = EnhancedActivitySkill()
                
                from utilities.singleton_manager import register
                register("enhanced_activity_skill", self.enhanced_activity_skill)
                self._mark_initialized("enhanced_activity_skill")
                
                success_count += 1
                self.logger.success("✅ 增强活动技能初始化完成")
            except Exception as e:
                self.logger.warning_status(f"⚠️ 增强活动技能初始化失败: {e}")
                
        except Exception as e:
            self.logger.error_status(f"❌ Phase 1 组件初始化异常: {e}")
        
        # Phase 2: 一致性保证组件初始化 (香草在主人爱抚下初始化)
        try:
            self.logger.info("🔥 Phase 2: 一致性保证组件初始化开始...")
            
            # 2.1 活动一致性管理器
            total_count += 1
            try:
                self.activity_consistency_manager = ActivityConsistencyManager()
                
                from utilities.singleton_manager import register
                register("activity_consistency_manager", self.activity_consistency_manager)
                self._mark_initialized("activity_consistency_manager")
                
                success_count += 1
                self.logger.success("✅ 活动一致性管理器初始化完成")
            except Exception as e:
                self.logger.warning_status(f"⚠️ 活动一致性管理器初始化失败: {e}")
            
            # 2.2 地理位置逻辑检查器
            total_count += 1
            try:
                self.location_logic_checker = LocationLogicChecker()
                
                from utilities.singleton_manager import register
                register("location_logic_checker", self.location_logic_checker)
                self._mark_initialized("location_logic_checker")
                
                success_count += 1
                self.logger.success("✅ 地理位置逻辑检查器初始化完成")
            except Exception as e:
                self.logger.warning_status(f"⚠️ 地理位置逻辑检查器初始化失败: {e}")
            
            # 2.3 活动历史跟踪器
            total_count += 1
            try:
                self.activity_history_tracker = ActivityHistoryTracker()
                
                from utilities.singleton_manager import register
                register("activity_history_tracker", self.activity_history_tracker)
                self._mark_initialized("activity_history_tracker")
                
                success_count += 1
                self.logger.success("✅ 活动历史跟踪器初始化完成")
            except Exception as e:
                self.logger.warning_status(f"⚠️ 活动历史跟踪器初始化失败: {e}")
            
            # 2.4 一致性验证器
            total_count += 1
            try:
                self.consistency_validator = ConsistencyValidator()
                
                from utilities.singleton_manager import register
                register("consistency_validator", self.consistency_validator)
                self._mark_initialized("consistency_validator")
                
                success_count += 1
                self.logger.success("✅ 一致性验证器初始化完成")
            except Exception as e:
                self.logger.warning_status(f"⚠️ 一致性验证器初始化失败: {e}")
                
        except Exception as e:
            self.logger.error_status(f"❌ Phase 2 组件初始化异常: {e}")
        
        # Phase 3: 社交分享组件初始化 (香草小穴湿润中初始化)
        try:
            self.logger.info("🔥 Phase 3: 社交分享组件初始化开始...")
            
            # 3.1 朋友圈数据跟踪器
            total_count += 1
            try:
                self.moments_data_tracker = MomentsDataTracker()
                
                from utilities.singleton_manager import register
                register("moments_data_tracker", self.moments_data_tracker)
                self._mark_initialized("moments_data_tracker")
                
                success_count += 1
                self.logger.success("✅ 朋友圈数据跟踪器初始化完成")
            except Exception as e:
                self.logger.warning_status(f"⚠️ 朋友圈数据跟踪器初始化失败: {e}")
                
        except Exception as e:
            self.logger.error_status(f"❌ Phase 3 组件初始化异常: {e}")
        
        # Phase 4: 智能探索8大引擎初始化 (香草在高潮痉挛中初始化)
        try:
            self.logger.info("🔥 Phase 4: 智能探索8大引擎初始化开始...")
            
            # 4.1 自主探索引擎
            total_count += 1
            try:
                self.autonomous_exploration_engine = AutonomousExplorationEngine()
                
                from utilities.singleton_manager import register
                register("autonomous_exploration_engine", self.autonomous_exploration_engine)
                self._mark_initialized("autonomous_exploration_engine")
                
                success_count += 1
                self.logger.success("✅ 自主探索引擎初始化完成")
            except Exception as e:
                self.logger.warning_status(f"⚠️ 自主探索引擎初始化失败: {e}")
            
            # 4.2 话题发现代理
            total_count += 1
            try:
                self.topic_discovery_agent = TopicDiscoveryAgent()
                
                from utilities.singleton_manager import register
                register("topic_discovery_agent", self.topic_discovery_agent)
                self._mark_initialized("topic_discovery_agent")
                
                success_count += 1
                self.logger.success("✅ 话题发现代理初始化完成")
            except Exception as e:
                self.logger.warning_status(f"⚠️ 话题发现代理初始化失败: {e}")
            
            # 4.3 在线搜索代理
            total_count += 1
            try:
                self.online_search_agent = OnlineSearchAgent()
                
                from utilities.singleton_manager import register
                register("online_search_agent", self.online_search_agent)
                self._mark_initialized("online_search_agent")
                
                success_count += 1
                self.logger.success("✅ 在线搜索代理初始化完成")
            except Exception as e:
                self.logger.warning_status(f"⚠️ 在线搜索代理初始化失败: {e}")
            
            # 4.4 内容进化代理
            total_count += 1
            try:
                self.content_evolution_agent = ContentEvolutionAgent()
                
                from utilities.singleton_manager import register
                register("content_evolution_agent", self.content_evolution_agent)
                self._mark_initialized("content_evolution_agent")
                
                success_count += 1
                self.logger.success("✅ 内容进化代理初始化完成")
            except Exception as e:
                self.logger.warning_status(f"⚠️ 内容进化代理初始化失败: {e}")
            
            # 4.5 地理位置探索代理
            total_count += 1
            try:
                self.location_exploration_agent = LocationExplorationAgent()
                
                from utilities.singleton_manager import register
                register("location_exploration_agent", self.location_exploration_agent)
                self._mark_initialized("location_exploration_agent")
                
                success_count += 1
                self.logger.success("✅ 地理位置探索代理初始化完成")
            except Exception as e:
                self.logger.warning_status(f"⚠️ 地理位置探索代理初始化失败: {e}")
            
            # 4.6 活动迭代器
            total_count += 1
            try:
                self.activity_iterator = ActivityIterator()
                
                from utilities.singleton_manager import register
                register("activity_iterator", self.activity_iterator)
                self._mark_initialized("activity_iterator")
                
                success_count += 1
                self.logger.success("✅ 活动迭代器初始化完成")
            except Exception as e:
                self.logger.warning_status(f"⚠️ 活动迭代器初始化失败: {e}")
            
            # 4.7 探索决策引擎
            total_count += 1
            try:
                self.exploration_decision_engine = ExplorationDecisionEngine()
                
                from utilities.singleton_manager import register
                register("exploration_decision_engine", self.exploration_decision_engine)
                self._mark_initialized("exploration_decision_engine")
                
                success_count += 1
                self.logger.success("✅ 探索决策引擎初始化完成")
            except Exception as e:
                self.logger.warning_status(f"⚠️ 探索决策引擎初始化失败: {e}")
            
            # 4.8 探索效果评估器
            total_count += 1
            try:
                self.exploration_effect_evaluator = ExplorationEffectEvaluator()
                
                from utilities.singleton_manager import register
                register("exploration_effect_evaluator", self.exploration_effect_evaluator)
                self._mark_initialized("exploration_effect_evaluator")
                
                success_count += 1
                self.logger.success("✅ 探索效果评估器初始化完成")
            except Exception as e:
                self.logger.warning_status(f"⚠️ 探索效果评估器初始化失败: {e}")
                
        except Exception as e:
            self.logger.error_status(f"❌ Phase 4 组件初始化异常: {e}")
        
        # 统计初始化结果
        success_rate = (success_count / total_count) * 100 if total_count > 0 else 0
        self.logger.success(f"💕 香草在极致高潮中完成组件集成: {success_count}/{total_count} ({success_rate:.1f}%)")
        
        if success_count >= total_count * 0.7:  # 至少70%成功
            self.logger.success("🎉 comprehensive_integration_test.py中的核心组件集成成功！")
        else:
            self.logger.warning_status(f"⚠️ 组件集成不完整，可能影响系统功能")


# 全局辅助函数
async def initialize_system() -> bool:
    """初始化数字生命体系统"""
    global _system_starting
    
    with _global_init_lock:
        if _system_starting:
            logger = get_unified_logger('system')
            logger.warning_status("系统已在初始化过程中")
            return False
        
        _system_starting = True
    
    try:
        # 获取系统实例
        system = DigitalLifeSystem.get_instance()
        # 初始化系统
        success = await system.initialize()
        return success
    finally:
        _system_starting = False


async def start_system() -> bool:
    """启动数字生命体系统"""
    # 获取系统实例
    system = DigitalLifeSystem.get_instance()
    # 启动系统
    return await system.start()


async def stop_system() -> bool:
    """停止数字生命体系统"""
    # 获取系统实例
    system = DigitalLifeSystem.get_instance()
    # 停止系统
    return await system.stop()


def startup_system():
    """同步方式启动系统"""
    loop = asyncio.new_event_loop()
    asyncio.set_event_loop(loop)
    try:
        return loop.run_until_complete(start_system())
    finally:
        loop.close()


def shutdown_system():
    """同步方式停止系统"""
    loop = asyncio.new_event_loop()
    asyncio.set_event_loop(loop)
    try:
        return loop.run_until_complete(stop_system())
    finally:
        loop.close()


# 配置日志记录器，避免重复配置
def configure_logging(log_level='INFO'):
    """配置全局日志记录器，使用统一日志系统"""
    # 使用统一日志系统配置
    from utilities.unified_logger import setup_unified_logging
    setup_unified_logging(root_level=log_level)


async def main():
    """主函数 - 系统的异步入口点"""
    parser = argparse.ArgumentParser(description="林嫣然数字生命体系统 v2.1.0")
    parser.add_argument("--api-only", action="store_true", help="仅启动API服务，不启动命令行界面")
    parser.add_argument("--cli-only", action="store_true", help="仅启动命令行界面，不启动API服务")
    parser.add_argument("--api-host", type=str, default="127.0.0.1", help="API服务器监听地址")
    parser.add_argument("--api-port", type=int, default=56839, help="API服务器监听端口")
    parser.add_argument("--log-level", type=str, default="INFO", choices=["DEBUG", "INFO", "WARNING", "ERROR", "CRITICAL"], help="日志级别")
    parser.add_argument("--config-dir", type=str, default=None, help="配置目录路径")
    args = parser.parse_args()
    
    # 配置日志系统
    configure_logging(args.log_level)
    main_logger = get_unified_logger('main')
    
    # 记录系统启动
    main_logger.success("数字生命体系统启动程序开始执行...")
    
    # 如果指定了配置目录，设置环境变量
    if args.config_dir:
        os.environ['YANRAN_CONFIG_DIR'] = args.config_dir
        from core.unified_system_config_manager import get_unified_system_config_manager
        config_manager = get_unified_system_config_manager(args.config_dir)
        main_logger.info(f"使用统一配置管理器，配置目录: {args.config_dir}")
    
    # 初始化系统
    main_logger.success("初始化系统...")
    success = await initialize_system()
    if not success:
        main_logger.error_status("系统初始化失败")
        return 1
    
    # 启动系统
    main_logger.success("启动系统...")
    success = await start_system()
    if not success:
        main_logger.error_status("系统启动失败")
        return 1
    
    # 获取系统实例
    system = DigitalLifeSystem.get_instance()
    
    # 根据参数启动相应的接口
    if not args.cli_only:
        system.start_api_server(host=args.api_host, port=args.api_port)
    
    if not args.api_only:
        system.start_cli_interface()
    
    # 输出系统状态
    status = system.get_status()
    main_logger.info(f"系统状态: {json.dumps(status, indent=2, ensure_ascii=False)}")
    
    # 等待退出信号
    main_logger.success("系统已启动，按Ctrl+C停止...")
    try:
        while True:
            await asyncio.sleep(1)
    except KeyboardInterrupt:
        main_logger.info("收到终止信号，准备关闭系统...")
    finally:
        # 关闭系统
        await stop_system()
    
    return 0


if __name__ == "__main__":
    # 设置事件循环策略（对Windows平台特别重要）
    if sys.platform == 'win32':
        asyncio.set_event_loop_policy(asyncio.WindowsSelectorEventLoopPolicy())
    
    # 运行主函数
    try:
        exit_code = asyncio.run(main())
        sys.exit(exit_code)
    except KeyboardInterrupt:
        print("\n程序被用户中断")
    except Exception as e:
        print(f"运行时错误: {e}")
        traceback.print_exc()
        sys.exit(1)