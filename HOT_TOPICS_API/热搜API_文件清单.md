# 热搜API功能 - 文件清单

## 📁 核心文件列表

实现热搜API功能需要以下**4个核心文件**：

### 1. 核心服务文件
**文件路径：** `app/services/hot_topic_service.py`
**作用说明：** 
- 热搜服务的核心业务逻辑
- 包含67个支持平台的完整列表
- 实现与DailyHotApi的HTTP通信
- 处理数据格式转换和错误处理
- 提供异步的热搜数据获取服务

**主要功能：**
- `get_hot_topics()` - 获取指定平台热搜数据
- `get_supported_platforms()` - 获取支持的平台列表
- `SUPPORTED_PLATFORMS` - 67个平台的映射字典

### 2. API路由文件
**文件路径：** `app/apis/v1/hot_topic_api.py`
**作用说明：**
- 定义热搜相关的API接口路由
- 处理HTTP请求和响应
- 实现参数验证和异常处理
- 提供FastAPI的接口文档

**主要接口：**
- `GET /` - 获取热搜数据接口
- `GET /platforms` - 获取支持平台列表接口

### 3. 路由注册文件
**文件路径：** `app/apis/v1/api.py`
**作用说明：**
- 注册热搜API路由到主应用
- 设置API路径前缀为 `/hot-topics`
- 配置API标签为 `hot-topics`

**关键代码：**
```python
from app.apis.v1 import hot_topic_api
api_router.include_router(hot_topic_api.router, prefix="/hot-topics", tags=["hot-topics"])
```

### 4. 主应用文件
**文件路径：** `app/main.py`
**作用说明：**
- FastAPI主应用程序入口
- 集成所有API路由
- 配置应用中间件和设置

## 📋 文件依赖关系

```
app/main.py
    └── app/apis/v1/api.py
        └── app/apis/v1/hot_topic_api.py
            └── app/services/hot_topic_service.py
```

## 🔗 依赖关系说明

1. **main.py** 启动FastAPI应用，加载所有API路由
2. **api.py** 注册hot_topic_api路由，设置URL前缀
3. **hot_topic_api.py** 定义接口端点，调用服务层
4. **hot_topic_service.py** 实现业务逻辑，调用外部API

## 📦 外部依赖

### Python包依赖
```text
fastapi>=0.68.0
httpx>=0.24.0
uvicorn>=0.15.0
pydantic>=1.8.0
```

### 网络依赖
- **DailyHotApi**: `https://api-hot.imsyy.top`
- **网络访问**: 需要能访问互联网

## 🚀 快速部署指南

### 1. 复制文件
将以下4个文件复制到您的FastAPI项目中：
```
app/services/hot_topic_service.py
app/apis/v1/hot_topic_api.py
app/apis/v1/api.py (修改现有文件)
app/main.py (确认包含API路由)
```

### 2. 安装依赖
```bash
pip install fastapi httpx uvicorn pydantic
```

### 3. 启动服务
```bash
uvicorn app.main:app --host 0.0.0.0 --port 8000 --reload
```

### 4. 测试接口
```bash
# 测试获取热搜
curl "http://localhost:8000/hot-topics/?platform=weibo"

# 测试获取平台列表
curl "http://localhost:8000/hot-topics/platforms"
```

## 🔍 代码结构分析

### Service Layer (服务层)
- **职责**: 业务逻辑、数据处理、外部API调用
- **文件**: `hot_topic_service.py`
- **特点**: 异步处理、错误处理、数据验证

### API Layer (接口层)
- **职责**: HTTP接口、参数验证、响应格式化
- **文件**: `hot_topic_api.py`
- **特点**: RESTful设计、异常处理、文档生成

### Router Layer (路由层)
- **职责**: 路由注册、URL前缀、标签管理
- **文件**: `api.py`
- **特点**: 模块化管理、统一配置

### Application Layer (应用层)
- **职责**: 应用启动、全局配置、中间件
- **文件**: `main.py`
- **特点**: 统一入口、配置管理

## 📝 自定义配置

### 修改API前缀
在 `app/apis/v1/api.py` 中修改：
```python
api_router.include_router(hot_topic_api.router, prefix="/your-prefix", tags=["your-tags"])
```

### 添加新平台
在 `app/services/hot_topic_service.py` 的 `SUPPORTED_PLATFORMS` 字典中添加：
```python
SUPPORTED_PLATFORMS = {
    # ... 现有平台
    "new_platform": "新平台名称",
}
```

### 修改超时设置
在 `hot_topic_service.py` 的 `httpx.Timeout()` 中修改：
```python
timeout=httpx.Timeout(30.0, connect=10.0)  # 总超时30秒，连接超时10秒
```

## 🎯 总结

只需要**4个核心文件**就能完整实现67个平台的热搜API功能：

1. ✅ **业务逻辑** - `hot_topic_service.py`
2. ✅ **API接口** - `hot_topic_api.py`  
3. ✅ **路由注册** - `api.py`
4. ✅ **应用启动** - `main.py`

这个架构设计**简洁高效**，**易于维护**，**功能完整**，可以直接用于生产环境！

---

*文件清单最后更新: 2025-06-23* 