# 热搜API接口文档

## 📋 概述

这是一个基于FastAPI开发的热搜数据聚合API，通过集成DailyHotApi提供50+个平台的实时热搜数据。支持微博、知乎、抖音、B站、GitHub等主流平台的热门内容获取。

## 🚀 功能特色

- ✅ **50+平台支持** - 涵盖资讯、科技、社交、游戏、娱乐等各领域
- ✅ **实时数据** - 直接对接DailyHotApi获取最新热搜
- ✅ **统一接口** - 所有平台使用相同的API调用格式
- ✅ **错误处理** - 完善的异常处理和错误提示
- ✅ **平台验证** - 自动验证平台名称有效性
- ✅ **中文友好** - 所有平台都有对应中文名称

## 📡 API接口

### 1. 获取热搜数据

**接口地址：** `GET /hot-topics/`

**请求参数：**
| 参数名 | 类型 | 必填 | 默认值 | 说明 |
|--------|------|------|--------|------|
| platform | string | 否 | weibo | 平台代码，不填默认返回微博热搜 |

**请求示例：**
```bash
# 获取微博热搜（默认）
GET http://localhost:8000/hot-topics/

# 获取指定平台热搜
GET http://localhost:8000/hot-topics/?platform=zhihu
GET http://localhost:8000/hot-topics/?platform=douyin
GET http://localhost:8000/hot-topics/?platform=bilibili
```

**响应格式：**
```json
{
  "code": 200,
  "name": "微博热搜",
  "title": "微博",
  "type": "weibo",
  "total": 51,
  "updateTime": "2025-06-23T10:30:00.000Z",
  "data": [
    {
      "id": 1,
      "title": "热搜标题",
      "desc": "热搜描述",
      "hot": 1234567,
      "url": "https://weibo.com/...",
      "mobileUrl": "https://m.weibo.cn/..."
    }
  ]
}
```

### 2. 获取支持的平台列表

**接口地址：** `GET /hot-topics/platforms`

**请求示例：**
```bash
GET http://localhost:8000/hot-topics/platforms
```

**响应格式：**
```json
{
  "code": 200,
  "message": "获取成功",
  "total": 67,
  "data": {
    "36kr": "36氪",
    "baidu": "百度热搜",
    "weibo": "微博热搜",
    "zhihu": "知乎热榜",
    "douyin": "抖音热搜",
    "bilibili": "哔哩哔哩",
    "github": "GitHub趋势",
    "ithome": "IT之家",
    "...": "..."
  }
}
```

## 🔥 支持的平台（67个）

### 📰 综合资讯 (11个)
| 平台代码 | 中文名称 | 平台代码 | 中文名称 |
|----------|----------|----------|----------|
| 36kr | 36氪 | baidu | 百度热搜 |
| weibo | 微博热搜 | zhihu | 知乎热榜 |
| zhihu-daily | 知乎日报 | toutiao | 今日头条 |
| qq-news | 腾讯新闻 | sina-news | 新浪新闻 |
| netease-news | 网易新闻 | thepaper | 澎湃新闻 |
| nytimes | 纽约时报 |  |  |

### 💻 科技数码 (9个)
| 平台代码 | 中文名称 | 平台代码 | 中文名称 |
|----------|----------|----------|----------|
| ithome | IT之家 | ithome-xijiayi | IT之家喜加一 |
| csdn | CSDN博客 | juejin | 稀土掘金 |
| 51cto | 51CTO博客 | github | GitHub趋势 |
| hackernews | Hacker News | hellogithub | HelloGitHub |
| producthunt | Product Hunt | geekpark | 极客公园 |
| ifanr | 爱范儿 | huxiu | 虎嗅网 |
| dgtle | 数字尾巴 |  |  |

### 📱 社交媒体 (12个)
| 平台代码 | 中文名称 | 平台代码 | 中文名称 |
|----------|----------|----------|----------|
| douyin | 抖音热搜 | kuaishou | 快手热搜 |
| bilibili | 哔哩哔哩 | acfun | AcFun |
| coolapk | 酷安 | v2ex | V2EX |
| nodeseek | NodeSeek | linuxdo | LinuxDo |
| hostloc | 全球主机交流论坛 | newsmth | 水木社区 |
| tieba | 百度贴吧 | ngabbs | NGA游戏论坛 |

### 🛒 购物消费 (1个)
| 平台代码 | 中文名称 |
|----------|----------|
| smzdm | 什么值得买 |

### 📚 文娱生活 (6个)
| 平台代码 | 中文名称 | 平台代码 | 中文名称 |
|----------|----------|----------|----------|
| douban-movie | 豆瓣电影 | douban-group | 豆瓣小组 |
| weread | 微信读书 | jianshu | 简书 |
| sspai | 少数派 | guokr | 果壳网 |

### 🎮 游戏 (5个)
| 平台代码 | 中文名称 | 平台代码 | 中文名称 |
|----------|----------|----------|----------|
| lol | 英雄联盟 | genshin | 原神 |
| honkai | 崩坏星穹铁道 | starrail | 崩坏星穹铁道 |
| miyoushe | 米游社 |  |  |

### 🏃 体育 (1个)
| 平台代码 | 中文名称 |
|----------|----------|
| hupu | 虎扑 |

### 🔓 破解相关 (1个)
| 平台代码 | 中文名称 |
|----------|----------|
| 52pojie | 吾爱破解 |

### 🔧 其他 (4个)
| 平台代码 | 中文名称 | 平台代码 | 中文名称 |
|----------|----------|----------|----------|
| yystv | 游研社 | history | 历史上的今天 |
| earthquake | 地震速报 | weatheralarm | 天气预警 |

### 🧪 测试 (1个)
| 平台代码 | 中文名称 |
|----------|----------|
| mock | 测试数据 |

## ⚠️ 错误处理

### 1. 不支持的平台
**请求：** `GET /hot-topics/?platform=invalid_platform`

**响应：**
```json
{
  "error": "不支持的平台: invalid_platform",
  "supported_platforms": ["36kr", "baidu", "weibo", "..."],
  "details": "请使用支持的平台名称"
}
```

### 2. 网络错误
```json
{
  "error": "Network error",
  "details": "无法连接到DailyHotApi: Connection timeout"
}
```

### 3. API错误
```json
{
  "error": "HTTP error occurred: 500",
  "details": "Internal Server Error"
}
```

## 🛠 技术实现

### 架构设计
- **Framework**: FastAPI + Pydantic
- **HTTP Client**: httpx (异步)
- **Data Source**: DailyHotApi (https://api-hot.imsyy.top)
- **Error Handling**: 完善的异常处理机制

### 性能特点
- ✅ **异步处理** - 使用asyncio提升并发性能
- ✅ **连接复用** - httpx客户端自动管理连接池
- ✅ **超时控制** - 15秒总超时，5秒连接超时
- ✅ **错误重试** - 自动处理临时网络故障

## 📝 使用示例

### Python requests
```python
import requests

# 获取微博热搜
response = requests.get("http://localhost:8000/hot-topics/")
data = response.json()

# 获取B站热门
response = requests.get("http://localhost:8000/hot-topics/?platform=bilibili")
bilibili_data = response.json()

# 获取支持的平台列表
response = requests.get("http://localhost:8000/hot-topics/platforms")
platforms = response.json()
```

### JavaScript fetch
```javascript
// 获取抖音热搜
fetch('http://localhost:8000/hot-topics/?platform=douyin')
  .then(response => response.json())
  .then(data => {
    console.log('抖音热搜:', data);
  });

// 获取GitHub趋势
fetch('http://localhost:8000/hot-topics/?platform=github')
  .then(response => response.json())
  .then(data => {
    console.log('GitHub趋势:', data);
  });
```

### cURL
```bash
# 获取知乎热榜
curl "http://localhost:8000/hot-topics/?platform=zhihu"

# 获取IT之家热门
curl "http://localhost:8000/hot-topics/?platform=ithome"

# 获取平台列表
curl "http://localhost:8000/hot-topics/platforms"
```

## 🔧 部署说明

### 环境要求
- Python 3.9+
- FastAPI
- httpx
- 网络连接（访问api-hot.imsyy.top）

### 快速启动
```bash
# 1. 激活虚拟环境
source venv/bin/activate

# 2. 启动API服务
uvicorn app.main:app --host 0.0.0.0 --port 8000 --reload

# 3. 访问文档
http://localhost:8000/docs
```

### Docker部署
```dockerfile
FROM python:3.9-slim

WORKDIR /app
COPY requirements.txt .
RUN pip install -r requirements.txt

COPY . .
EXPOSE 8000

CMD ["uvicorn", "app.main:app", "--host", "0.0.0.0", "--port", "8000"]
```

## 📞 技术支持

- **数据来源**: [DailyHotApi](https://github.com/imsyy/DailyHotApi)
- **官方展示**: [https://hot.imsyy.top](https://hot.imsyy.top)
- **FastAPI文档**: [https://fastapi.tiangolo.com](https://fastapi.tiangolo.com)

## 🎯 更新日志

### v1.0.0 (2025-06-23)
- ✅ 初始版本发布
- ✅ 支持67个热搜平台
- ✅ 完整的错误处理机制
- ✅ 异步HTTP客户端
- ✅ 平台列表API

---

*最后更新时间: 2025-06-23* 