from typing import Any, List, Dict, Optional

from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session

from app.services.hot_topic_service import hot_topic_service
from app.schemas.utils_schema import MessageResponse # Reusing for simple messages
# If specific schemas are needed for hot topics, they should be created in app/schemas/

router = APIRouter()

@router.get("/", 
            # response_model=List[Dict[str, Any]], # Define a Pydantic model for better validation and docs
            summary="Get Hot Topics",
            description="Fetches hot topics from various platforms via DailyHotApi.")
async def get_hot_topics_list(
    platform: Optional[str] = None # Example: 'zhihu', 'weibo', 'tieba', 'bilibili', 'douyin' etc.
):
    """
    Retrieve a list of hot topics.

    - **platform** (optional): Specify the platform to get hot topics from.
      If not provided, the underlying service might fetch a general list or from a default source.
    """
    try:
        result = await hot_topic_service.get_hot_topics(platform=platform)
        if not result:
            raise HTTPException(status_code=404, detail="No hot topics found or error fetching them.")
        
        # 检查是否有错误
        if isinstance(result, dict) and 'error' in result:
            raise HTTPException(status_code=500, detail=result.get('details', result['error']))
        
        return result
    except HTTPException as e:
        raise e # Re-raise HTTPException to let FastAPI handle it
    except Exception as e:
        # Catch any other unexpected errors from the service or during processing
        raise HTTPException(status_code=500, detail=f"An unexpected error occurred: {str(e)}")

@router.get("/platforms", 
            summary="Get Supported Platforms",
            description="获取所有支持的热搜平台列表")
async def get_supported_platforms():
    """
    获取所有支持的热搜平台列表
    
    Returns:
        Dict[str, str]: 平台代码和中文名称的映射字典
    """
    try:
        platforms = hot_topic_service.get_supported_platforms()
        return {
            "code": 200,
            "message": "获取成功",
            "total": len(platforms),
            "data": platforms
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取平台列表失败: {str(e)}")

# Example of how to add more specific endpoints if needed
# @router.get("/hot-topics/{platform_name}", response_model=List[Dict[str, Any]])
# async def get_platform_hot_topics(platform_name: str):
#     topics = await hot_topic_service.get_hot_topics(platform=platform_name)
#     if not topics or (isinstance(topics[0], dict) and 'error' in topics[0]):
#         error_detail = topics[0]['details'] if topics and isinstance(topics[0], dict) and 'details' in topics[0] else "Error fetching topics."
#         raise HTTPException(status_code=500, detail=error_detail)
#     return topics