import httpx
from typing import Any, Dict, List
import sys
import os

# 导入统一异常处理装饰器
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from core.exception.decorators import network_operation

# 移除不必要的app依赖，直接使用公共API

class HotTopicService:
    # DailyHotApi支持的所有平台列表
    SUPPORTED_PLATFORMS = {
        # 综合资讯
        "36kr": "36氪",
        "baidu": "百度热搜",
        "weibo": "微博热搜",
        "zhihu": "知乎热榜",
        "zhihu-daily": "知乎日报",
        "toutiao": "今日头条",
        "qq-news": "腾讯新闻",
        "sina-news": "新浪新闻",
        "netease-news": "网易新闻",
        "thepaper": "澎湃新闻",
        "nytimes": "纽约时报",
        
        # 科技数码
        "ithome": "IT之家",
        "ithome-xijiayi": "IT之家喜加一",
        "csdn": "CSDN博客",
        "juejin": "稀土掘金",
        "51cto": "51CTO博客", 
        "github": "GitHub趋势",
        "hackernews": "Hacker News",
        "hellogithub": "HelloGitHub",
        "producthunt": "Product Hunt",
        "geekpark": "极客公园",
        "ifanr": "爱范儿",
        "huxiu": "虎嗅网",
        "dgtle": "数字尾巴",
        
        # 社交媒体
        "douyin": "抖音热搜",
        "kuaishou": "快手热搜",
        "bilibili": "哔哩哔哩",
        "acfun": "AcFun",
        "coolapk": "酷安",
        "v2ex": "V2EX",
        "nodeseek": "NodeSeek",
        "linuxdo": "LinuxDo",
        "hostloc": "全球主机交流论坛",
        "newsmth": "水木社区",
        "tieba": "百度贴吧",
        "ngabbs": "NGA游戏论坛",
        
        # 购物消费
        "smzdm": "什么值得买",
        
        # 文娱生活
        "douban-movie": "豆瓣电影",
        "douban-group": "豆瓣小组", 
        "weread": "微信读书",
        "jianshu": "简书",
        "sspai": "少数派",
        "guokr": "果壳网",
        
        # 游戏
        "lol": "英雄联盟",
        "genshin": "原神",
        "honkai": "崩坏星穹铁道",
        "starrail": "崩坏星穹铁道",
        "miyoushe": "米游社",
        
        # 体育
        "hupu": "虎扑",
        
        # 破解相关
        "52pojie": "吾爱破解",
        
        # 其他
        "yystv": "游研社",
        "history": "历史上的今天",
        "earthquake": "地震速报",
        "weatheralarm": "天气预警",
        
        # 测试
        "mock": "测试数据"
    }
    
    @network_operation
    async def get_hot_topics(self, platform: str = None) -> Dict[str, Any]:
        """
        Fetches hot topics from the DailyHotApi.

        Args:
            platform: The specific platform to get hot topics from. 
                      Supported platforms: 36kr, baidu, weibo, zhihu, etc.
                      If None, defaults to weibo.

        Returns:
            A dictionary containing hot topic data.
        """        
        # 提供更丰富的mock数据用于测试
        if platform == "mock":
            return {
                "code": 200,
                "name": "mock",
                "title": "测试热门话题",
                "type": "热搜榜",
                "total": 5,
                "updateTime": "2025-06-23T10:00:00.000Z",
                "data": [
                    {"title": "测试话题1", "desc": "这是第一个测试话题", "hot": 100000, "url": "https://example.com/1"},
                    {"title": "测试话题2", "desc": "这是第二个测试话题", "hot": 90000, "url": "https://example.com/2"},
                    {"title": "测试话题3", "desc": "这是第三个测试话题", "hot": 80000, "url": "https://example.com/3"},
                    {"title": "测试话题4", "desc": "这是第四个测试话题", "hot": 70000, "url": "https://example.com/4"},
                    {"title": "测试话题5", "desc": "这是第五个测试话题", "hot": 60000, "url": "https://example.com/5"}
                ]
            }
        
        # 检查平台是否支持
        if platform and platform not in self.SUPPORTED_PLATFORMS:
            return {
                "error": f"不支持的平台: {platform}",
                "supported_platforms": list(self.SUPPORTED_PLATFORMS.keys()),
                "details": "请使用支持的平台名称"
            }
        
        # 直接使用公共API，避免配置问题
        base_url = "https://api-hot.imsyy.top"
        
        # DailyHotApi的正确格式：/{platform}
        if platform:
            api_url = f"{base_url}/{platform}"
        else:
            api_url = f"{base_url}/weibo"  # 默认微博热搜
        
        try:
            # 配置httpx客户端
            async with httpx.AsyncClient(
                timeout=httpx.Timeout(15.0, connect=5.0),
                verify=False,  # 禁用SSL验证
                follow_redirects=True
            ) as client:
                response = await client.get(api_url)
                response.raise_for_status()
                data = response.json()
                
                # DailyHotApi返回的数据结构：{"code": 200, "data": [...], ...}
                if isinstance(data, dict):
                    if "data" in data and isinstance(data["data"], list):
                        return data  # 返回完整数据结构
                    elif data.get("code") == 200:
                        return data
                    else:
                        return {"error": "API returned error", "details": data}
                elif isinstance(data, list):
                    return {"data": data}  # 包装成标准格式
                else:
                    return {"error": "Unexpected data format from DailyHotApi", "details": str(data)}
                    
        except httpx.HTTPStatusError as e:
            return {"error": f"HTTP error occurred: {e.response.status_code}", "details": str(e)}
        except httpx.RequestError as e:
            return {"error": "Network error", "details": f"无法连接到DailyHotApi: {str(e)}"}
        except Exception as e:
            return {"error": "An unexpected error occurred", "details": str(e)}

    def get_supported_platforms(self) -> Dict[str, str]:
        """
        返回所有支持的平台列表
        
        Returns:
            Dict[str, str]: 平台代码和中文名称的映射
        """
        return self.SUPPORTED_PLATFORMS

hot_topic_service = HotTopicService()