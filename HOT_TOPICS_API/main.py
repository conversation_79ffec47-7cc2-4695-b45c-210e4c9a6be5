from fastapi import <PERSON><PERSON><PERSON>, Request, status
from fastapi.exceptions import RequestValidationError
from fastapi.responses import JSONResponse
from fastapi.middleware.cors import CORSMiddleware
import logging
import os # 新增导入
from fastapi.staticfiles import StaticFiles
from fastapi.openapi.docs import get_swagger_ui_html, get_redoc_html # 新增导入

from app.core.config import settings
from app.db.session import engine, Base #, SessionLocal # SessionLocal not directly used here unless for startup events
from app.apis.v1.api import api_router # Corrected import
from app.core.db import Base, engine # 新增导入
import app.models # 新增导入，确保模型被注册
from app.models import user_model, notification_model, educational_center_models # Make sure all models are imported
# from app.models import user_model # Import models to ensure they are registered with Base
from app.apis.v1 import auth_api, llm_api, notes_api, notifications_api

# Configure logging
# You can customize this further, e.g., to log to a file or use structured logging.
logging.basicConfig(level=settings.LOG_LEVEL.upper()) # Use log level from settings
logger = logging.getLogger(__name__) # Get a logger instance for this module

# Create database tables
# This will create tables based on models imported and registered with Base.
# Ensure all your models (like user_model.User) are imported somewhere before this runs,
# or explicitly import them here.
# For example, if user_model isn't imported by any of the api routers or services yet:
# from app.models import user_model
# user_model.Base.metadata.create_all(bind=engine)
# A better practice is to ensure models are imported when their CRUD/services are defined.
# Let's assume they are registered by virtue of being imported in crud/services that are then imported by APIs.
# If not, explicit create_all with model imports is needed, or use Alembic for migrations.

def create_db_and_tables():
    logging.info("Creating database and tables...")
    # 首先创建users表，因为其他表可能依赖它
    user_model.Base.metadata.create_all(bind=engine)
    # 然后创建其他表
    notification_model.Base.metadata.create_all(bind=engine)
    educational_center_models.Base.metadata.create_all(bind=engine)
    logging.info("Database and tables created successfully.")

app = FastAPI(
    title="Legal Notes API",
    description="Legal Notes Backend API",
    version="1.0.0"
)

# 配置CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Mount static files for Swagger UI
app.mount("/static", StaticFiles(directory=os.path.join(os.getcwd(), "app", "static")), name="static")

# Include the v1 API router
# 创建数据库表（如果尚不存在）
# 在生产环境中，更推荐使用 Alembic 进行数据库迁移
# def create_tables(): # You can wrap this in a function if you prefer
#     Base.metadata.create_all(bind=engine)
# create_tables() # Call it on startup, or integrate with startup events

# 更推荐的方式是使用FastAPI的startup事件
@app.on_event("startup")
def on_startup():
    # This specific call to create_all here might be redundant if create_db_and_tables() is called in startup_event
    # However, to ensure tables are created if startup_event is modified or if create_db_and_tables is not called there for some reason,
    # we can leave it, or ensure create_db_and_tables() is the sole source of truth for table creation on startup.
    # For clarity and to avoid potential double-creation attempts (though create_all is idempotent),
    # let's rely on the startup_event to call create_db_and_tables.
    # Base.metadata.create_all(bind=engine) # Commenting out direct call here, will be handled by startup_event
    pass # The actual table creation is now handled in the startup_event calling create_db_and_tables

app.include_router(api_router, prefix="")  # 移除 /api/v1 前缀

# 注册路由
app.include_router(auth_api.router, prefix="/api/v1/auth", tags=["auth"])
app.include_router(llm_api.router, prefix="/api/v1/llm", tags=["llm"])
app.include_router(notes_api.router, prefix="/api/v1/notes", tags=["notes"])
app.include_router(notifications_api.router, prefix="/api/v1/notifications", tags=["notifications"])

# Custom exception handler for Pydantic validation errors (optional but good for consistent error format)
@app.exception_handler(RequestValidationError)
async def validation_exception_handler(request: Request, exc: RequestValidationError):
    # You can format this error response as you like
    error_messages = []
    for error in exc.errors():
        field_parts = [str(loc) for loc in error["loc"]]
        # Remove 'body' from the path if it's the first part, for cleaner field names
        if field_parts and field_parts[0] == 'body':
            field_parts = field_parts[1:]
        field = ".".join(field_parts)
        message = error["msg"]
        error_messages.append({ "field": field, "message": message, "type": error["type"]})
    
    logger.warning(f"Request Validation Error: {exc.errors()!r}", extra={"errors": error_messages}) # Log detailed error

    return JSONResponse(
        status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
        content={"detail": "Validation Error", "errors": error_messages},
    )

# Root endpoint (optional, good for health checks or simple welcome)
@app.get("/")
async def root():
    return {"message": "Welcome to Legal Notes API"}

# Serve Swagger UI locally
@app.get("/docs", include_in_schema=False)
async def custom_swagger_ui_html():
    return get_swagger_ui_html(
        openapi_url=app.openapi_url,
        title=app.title + " - Swagger UI",
        swagger_js_url="/static/swagger_ui/swagger-ui-bundle.js",
        swagger_css_url="/static/swagger_ui/swagger-ui.css",
    )

@app.get("/redoc", include_in_schema=False)
async def redoc_html():
    return get_redoc_html(
        openapi_url=app.openapi_url,
        title=app.title + " - ReDoc",
        redoc_js_url="/static/swagger_ui/redoc.standalone.js",
    )

# Application startup event (e.g., to create DB tables or connect to other services)
@app.on_event("startup")
async def startup_event():
    logger.info("Application startup sequence initiated...")
    create_db_and_tables() # Create database tables if they don't exist
    # You can add other startup logic here, like:
    # - Connecting to a message queue
    # - Initializing a cache
    # - Seeding initial data (if needed and not handled by migrations)
    logger.info(f'{settings.PROJECT_NAME} started successfully.')

# Application shutdown event (e.g., to clean up resources)
@app.on_event("shutdown")
async def shutdown_event():
    logger.info("Application shutdown sequence initiated...")
    # Add cleanup logic here if needed (e.g., closing database connections if not handled by context managers)
    logger.info(f'{settings.PROJECT_NAME} shutdown complete.')


if __name__ == "__main__":
    # This part is for running with `python app/main.py` directly (less common for FastAPI)
    # Uvicorn is typically used as the ASGI server: `uvicorn app.main:app --reload`
    import uvicorn
    logger.info(f"Starting Uvicorn server for {settings.PROJECT_NAME} directly from main.py (for development only)...")
    uvicorn.run(
        "app.main:app", 
        host="0.0.0.0", 
        port=8000, 
        log_level=settings.LOG_LEVEL.lower(),
        reload=True # Enable reload for development if running this way
    )