from fastapi import APIRouter

from app.apis.v1 import (
    user_api, 
    auth_api, 
    analysis_api, 
    exam_question_api, 
    hot_topic_api, 
    utils_api, 
    knowledge_card_api, 
    llm_api,
    notification_api,
    notes_api
)
from app.apis.v1.educational_center import api as educational_center_api

api_router = APIRouter()

# Include other routers from the v1 module
api_router.include_router(user_api.router, prefix="/users", tags=["users"])
api_router.include_router(auth_api.router, prefix="/auth", tags=["auth"])
api_router.include_router(analysis_api.router, prefix="/analysis", tags=["analysis"])
api_router.include_router(exam_question_api.router, prefix="/exam-questions", tags=["exam-questions"])
api_router.include_router(hot_topic_api.router, prefix="/hot-topics", tags=["hot-topics"])
api_router.include_router(utils_api.utils_api_router, prefix="/utils", tags=["utils"])
api_router.include_router(knowledge_card_api.router, prefix="/knowledge-cards", tags=["knowledge-cards"])
api_router.include_router(llm_api.router, prefix="/llm", tags=["llm"])
api_router.include_router(notification_api.router, prefix="/notifications", tags=["notifications"])
api_router.include_router(notes_api.router, prefix="/notes", tags=["notes"])
api_router.include_router(
    educational_center_api.educational_center_router, 
    prefix="/educational_center", 
    tags=["Educational Center"]
)

# You can add more routers here as your API grows
# For example, if you add a "documents" feature:
# from app.apis.v1 import document_api
# api_router_v1.include_router(document_api.router, prefix="/documents", tags=["Documents"])