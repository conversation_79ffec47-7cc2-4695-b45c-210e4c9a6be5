#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🔥 老王的原子文件写入模块
确保JSON文件写入的原子性，避免并发冲突导致的文件损坏
"""

import os
import json
import tempfile
import threading
from typing import Any, Optional

# 全局文件锁字典
_file_locks = {}
_global_lock = threading.RLock()

def get_file_lock(file_path: str) -> threading.RLock:
    """获取文件专用锁"""
    file_path = os.path.abspath(file_path)
    
    with _global_lock:
        if file_path not in _file_locks:
            _file_locks[file_path] = threading.RLock()
        return _file_locks[file_path]

def safe_json_write(file_path: str, data: Any, default=None) -> bool:
    """安全的JSON文件写入，使用原子操作避免并发冲突"""
    if default is None:
        default = str
    
    file_path = os.path.abspath(file_path)
    file_lock = get_file_lock(file_path)
    
    with file_lock:
        try:
            # 确保目录存在
            os.makedirs(os.path.dirname(file_path), exist_ok=True)
            
            # 使用临时文件写入
            temp_fd, temp_path = tempfile.mkstemp(
                suffix='.tmp',
                prefix=os.path.basename(file_path) + '_',
                dir=os.path.dirname(file_path)
            )
            
            try:
                with os.fdopen(temp_fd, 'w', encoding='utf-8') as f:
                    json.dump(data, f, ensure_ascii=False, indent=2, default=default)
                    f.flush()
                    os.fsync(f.fileno())
                
                # 原子替换
                if os.name == 'nt':  # Windows
                    if os.path.exists(file_path):
                        os.remove(file_path)
                    os.rename(temp_path, file_path)
                else:  # Unix/Linux
                    os.rename(temp_path, file_path)
                
                return True
                
            except Exception as e:
                # 清理临时文件
                try:
                    os.remove(temp_path)
                except:
                    pass
                raise e
                
        except Exception as e:
            print(f"原子写入失败 {file_path}: {e}")
            return False

def write_text_atomic(file_path: str, content: str, encoding: str = 'utf-8') -> bool:
    """原子性地写入文本文件，避免并发冲突"""
    file_path = os.path.abspath(file_path)
    file_lock = get_file_lock(file_path)

    with file_lock:
        try:
            # 确保目录存在
            os.makedirs(os.path.dirname(file_path), exist_ok=True)

            # 使用临时文件写入
            temp_fd, temp_path = tempfile.mkstemp(
                suffix='.tmp',
                prefix=os.path.basename(file_path) + '_',
                dir=os.path.dirname(file_path)
            )

            try:
                with os.fdopen(temp_fd, 'w', encoding=encoding) as f:
                    f.write(content)
                    f.flush()
                    os.fsync(f.fileno())

                # 原子替换
                if os.name == 'nt':  # Windows
                    if os.path.exists(file_path):
                        os.remove(file_path)
                    os.rename(temp_path, file_path)
                else:  # Unix/Linux
                    os.rename(temp_path, file_path)

                return True

            except Exception as e:
                # 清理临时文件
                try:
                    os.remove(temp_path)
                except:
                    pass
                raise e

        except Exception as e:
            print(f"原子文本写入失败 {file_path}: {e}")
            return False

def safe_text_write(file_path: str, content: str, encoding: str = 'utf-8') -> bool:
    """安全的文本文件写入，使用原子操作避免并发冲突"""
    return write_text_atomic(file_path, content, encoding)

# 兼容性别名
write_json_atomic = safe_json_write
