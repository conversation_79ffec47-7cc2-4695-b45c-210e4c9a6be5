#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
配置加载器 - Configuration Loader

该模块提供统一的配置文件加载和处理功能，
支持JSON和YAML格式配置文件，包含环境变量替换和默认值处理。

作者: Claude
创建日期: 2025-06-08
版本: 1.1
"""

import os
import sys
import json
import yaml
from utilities.unified_logger import get_unified_logger, setup_unified_logging
from typing import Dict, Any, Optional, Union, List

# 添加项目根目录到模块搜索路径
current_dir = os.path.dirname(os.path.abspath(__file__))
root_dir = os.path.dirname(current_dir)
if root_dir not in sys.path:
    sys.path.append(root_dir)

# 导入日志记录器
from utilities.unified_logger import get_unified_logger

# 设置日志
logger = get_unified_logger('config_loader')

# 配置目录 - 主配置目录
CONFIG_DIR = os.path.join(root_dir, 'config')

# 备用配置目录（已废弃，仅用于兼容）
LEGACY_CONFIG_DIR = os.path.join(root_dir, 'configs')

# 检查并输出配置目录状态
if os.path.exists(CONFIG_DIR):
    logger.info(f"使用主配置目录: {CONFIG_DIR}")
else:
    logger.warning_status(f"主配置目录不存在: {CONFIG_DIR}")

if os.path.exists(LEGACY_CONFIG_DIR):
    logger.warning_status(f"检测到备用配置目录: {LEGACY_CONFIG_DIR} (已废弃，将迁移至主配置目录)")

def load_config(config_path: str, default_config: Dict[str, Any] = None) -> Dict[str, Any]:
    """
    加载配置文件
    
    Args:
        config_path: 配置文件路径，可以是相对或绝对路径
        default_config: 默认配置，当配置文件不存在或加载失败时使用
        
    Returns:
        配置字典
    """
    # 初始化默认配置
    config = default_config or {}
    
    # 处理相对路径
    if not os.path.isabs(config_path):
        # 修复configs引用为config
        if config_path.startswith('configs/'):
            fixed_path = 'config/' + config_path[8:]
            logger.warning_status(f"配置路径修正: {config_path} -> {fixed_path}")
            config_path = fixed_path
            
        # 先检查主配置目录
        if os.path.exists(CONFIG_DIR):
            full_path = os.path.join(CONFIG_DIR, config_path)
            if os.path.exists(full_path):
                config_path = full_path
                logger.debug(f"从主配置目录加载: {config_path}")
            else:
                # 如果主配置目录中没有，检查备用配置目录（兼容模式）
                legacy_path = os.path.join(LEGACY_CONFIG_DIR, os.path.basename(config_path))
                if os.path.exists(legacy_path):
                    logger.warning_status(f"从备用配置目录加载: {legacy_path} (建议迁移至主配置目录)")
                    config_path = legacy_path
        
        # 如果还是相对路径，则相对于项目根目录
        if not os.path.isabs(config_path):
            config_path = os.path.join(root_dir, config_path)
    
    # 检查文件是否存在
    if not os.path.exists(config_path):
        logger.warning_status(f"配置文件不存在: {config_path}，使用默认配置")
        return config
    
    try:
        # 根据文件扩展名确定加载方法
        ext = os.path.splitext(config_path)[1].lower()
        
        if ext == '.json':
            with open(config_path, 'r', encoding='utf-8') as f:
                loaded_config = json.load(f)
        elif ext in ['.yaml', '.yml']:
            with open(config_path, 'r', encoding='utf-8') as f:
                loaded_config = yaml.safe_load(f)
        else:
            logger.warning_status(f"不支持的配置文件格式: {ext}，使用默认配置")
            return config
        
        # 合并加载的配置和默认配置
        config = deep_merge(config, loaded_config)
        
        # 替换环境变量
        config = replace_env_vars(config)
        
        logger.debug(f"成功加载配置文件: {config_path}")
        
    except Exception as e:
        logger.error_status(f"加载配置文件失败: {config_path}, 错误: {e}")
    
    return config

def deep_merge(base: Dict[str, Any], override: Dict[str, Any]) -> Dict[str, Any]:
    """
    深度合并两个字典
    
    Args:
        base: 基础字典
        override: 覆盖字典
        
    Returns:
        合并后的字典
    """
    result = base.copy()
    
    for k, v in override.items():
        if k in result and isinstance(result[k], dict) and isinstance(v, dict):
            result[k] = deep_merge(result[k], v)
        else:
            result[k] = v
    
    return result

def replace_env_vars(config: Any) -> Any:
    """
    递归替换配置中的环境变量
    
    环境变量格式: ${ENV_NAME:default_value}
    
    Args:
        config: 配置对象
        
    Returns:
        替换环境变量后的配置对象
    """
    if isinstance(config, str):
        # 检查是否包含环境变量格式 ${ENV_NAME:default_value}
        if '${' in config and '}' in config:
            parts = []
            i = 0
            while i < len(config):
                if config[i:i+2] == '${':
                    # 找到环境变量的结束位置
                    end = config.find('}', i)
                    if end > i:
                        # 提取环境变量名和默认值
                        env_spec = config[i+2:end]
                        if ':' in env_spec:
                            env_name, default = env_spec.split(':', 1)
                        else:
                            env_name, default = env_spec, ''
                        
                        # 获取环境变量值
                        env_value = os.environ.get(env_name, default)
                        parts.append(env_value)
                        i = end + 1
                        continue
                
                # 普通字符
                parts.append(config[i])
                i += 1
            
            return ''.join(parts)
        return config
    
    elif isinstance(config, dict):
        return {k: replace_env_vars(v) for k, v in config.items()}
    
    elif isinstance(config, list):
        return [replace_env_vars(item) for item in config]
    
    else:
        return config

def save_config(config: Dict[str, Any], config_path: str) -> bool:
    """
    保存配置到文件
    
    Args:
        config: 配置字典
        config_path: 配置文件路径
        
    Returns:
        是否成功保存
    """
    try:
        # 确保目录存在
        dir_path = os.path.dirname(config_path)
        if dir_path and not os.path.exists(dir_path):
            os.makedirs(dir_path)
        
        # 根据文件扩展名确定保存方法
        ext = os.path.splitext(config_path)[1].lower()
        
        if ext == '.json':
            # 🔥 老王修复：使用原子写入避免并发冲突
            from utilities.atomic_file_writer import safe_json_write
            success = safe_json_write(config_path, config)
            if not success:
                logger.warning_status(f"JSON配置文件保存失败: {config_path}")
                return False
        elif ext in ['.yaml', '.yml']:
            with open(config_path, 'w', encoding='utf-8') as f:
                yaml.safe_dump(config, f, default_flow_style=False, allow_unicode=True)
        else:
            logger.warning_status(f"不支持的配置文件格式: {ext}")
            return False
        
        logger.debug(f"成功保存配置文件: {config_path}")
        return True
        
    except Exception as e:
        logger.error_status(f"保存配置文件失败: {config_path}, 错误: {e}")
        return False

def get_config_value(config: Dict[str, Any], key_path: str, default: Any = None) -> Any:
    """
    获取配置中的值，支持点号分隔的路径
    
    Args:
        config: 配置字典
        key_path: 键路径，如 "database.connection.host"
        default: 默认值
        
    Returns:
        配置值或默认值
    """
    keys = key_path.split('.')
    value = config
    
    for key in keys:
        if isinstance(value, dict) and key in value:
            value = value[key]
        else:
            return default
    
    return value 

def get_config() -> Dict[str, Any]:
    """
    获取完整的配置字典
    
    Returns:
        所有配置的合并字典
    """
    config = {}
    
    # 直接加载数据库配置
    db_config_path = os.path.join(CONFIG_DIR, "database.json")
    if os.path.exists(db_config_path):
        try:
            config = load_config(db_config_path)
            logger.debug(f"成功加载数据库配置: {db_config_path}")
        except Exception as e:
            logger.warning_status(f"加载数据库配置文件失败: {db_config_path}, 错误: {e}")
    else:
        logger.warning_status(f"数据库配置文件不存在: {db_config_path}")
    
    return config