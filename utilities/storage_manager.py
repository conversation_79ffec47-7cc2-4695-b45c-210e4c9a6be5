#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
存储管理器

为数字生命系统提供数据存储和检索功能，支持本地和远程数据库。

作者: <PERSON>
创建日期: 2024-07-08
版本: 1.0
"""

import os
import json
import time
from utilities.unified_logger import get_unified_logger, setup_unified_logging
import threading
import sqlite3
import datetime
from typing import Dict, List, Any, Optional, Union, Tuple
import traceback
import mysql.connector

# 配置日志
setup_unified_logging()
logger = get_unified_logger("utilities.storage_manager")

class StorageManager:
    """存储管理器类，实现单例模式"""
    
    _instance = None
    _lock = threading.Lock()
    
    @classmethod
    def get_instance(cls) -> 'StorageManager':
        """获取存储管理器单例实例"""
        with cls._lock:
            if cls._instance is None:
                cls._instance = StorageManager()
        return cls._instance
    
    def __init__(self, config_path=None):
        """
        初始化存储管理器
        
        Args:
            config_path: 配置文件路径
        """
        self.config = {}
        self.config_path = config_path
        self.local_db_path = None
        self.remote_connected = False
        self.vector_db = None
        self._remote_db = None
        self._local_db = None
        self.connection_lock = threading.Lock()
        self.is_running = True  # 运行状态标志，用于保活线程控制
        self.local_connected = False
        
        # 加载配置
        self._load_config()
        
        # 初始化连接
        self._init_connections()
        
        logger.success("存储管理器已初始化")
    
    def configure(self, config: Dict[str, Any]) -> bool:
        """
        配置存储管理器
        
        Args:
            config: 配置参数
            
        Returns:
            bool: 配置是否成功
        """
        try:
            # 更新配置
            self.config.update(config)
            
            # 重新初始化连接
            self._init_connections()
            
            logger.success("存储管理器配置更新成功")
            return True
            
        except Exception as e:
            logger.error_status(f"存储管理器配置失败: {e}")
            return False
    
    def _load_config(self):
        """加载数据库配置 - 使用统一配置管理器"""
        try:
            # 尝试使用统一配置管理器
            try:
                from core.unified_system_config_manager import get_unified_system_config_manager
                config_manager = get_unified_system_config_manager()
                
                # 🔥 老王修复：从统一配置管理器获取数据库配置，强制使用远程MySQL
                self.config = {
                    "priority": config_manager.get("database.priority", "remote"),
                    "remote": {
                        "enabled": config_manager.get("database.remote.enabled", True),
                        "type": config_manager.get("database.remote.type", "mysql"),
                        "host": config_manager.get("database.remote.host", "**************"),  # 强制远程
                        "port": config_manager.get("database.remote.port", 3306),
                        "user": config_manager.get("database.remote.user", "root"),
                        "password": config_manager.get("database.remote.password", "55cee73f3102126a"),
                        "database": config_manager.get("database.remote.database", "linyanran")
                    },
                    "local": {
                        "enabled": config_manager.get("database.local.enabled", True),
                        "type": config_manager.get("database.local.type", "sqlite"),
                        "path": config_manager.get("database.local.path", "data/digital_life.db")
                    },
                    "backup": {
                        "enabled": config_manager.get("database.backup.enabled", True),
                        "interval": config_manager.get("database.backup.interval", 86400),
                        "keep_days": config_manager.get("database.backup.keep_days", 7),
                        "path": config_manager.get("database.backup.path", "data/backup")
                    }
                }
                
                logger.success(f"从统一配置管理器加载数据库配置，优先级: {self.config['priority']}")
                
            except Exception as e:
                logger.warning_status(f"无法使用统一配置管理器: {e}，回退到传统配置加载")
                
                # 🔥 老王修复：回退到传统配置加载方式，使用远程MySQL服务器
                self.config = {
                    "priority": "remote",
                    "remote": {
                        "enabled": True,
                        "type": "mysql",
                        "host": "**************",  # 使用远程服务器
                        "port": 3306,
                        "user": "root",
                        "password": "55cee73f3102126a",
                        "database": "linyanran"
                    },
                    "local": {
                        "enabled": True,
                        "type": "sqlite",
                        "path": "data/digital_life.db"
                    },
                    "backup": {
                        "enabled": True,
                        "interval": 86400,
                        "keep_days": 7,
                        "path": "data/backup"
                    }
                }
                
                # 🔥 老王修复：从传统配置文件加载，修复配置结构匹配问题
                config_path = os.path.join("config", "database.json")
                if os.path.exists(config_path):
                    with open(config_path, "r", encoding="utf-8") as f:
                        file_config = json.load(f)
                    
                    # 处理MySQL配置 - 修复配置结构
                    if "mysql" in file_config:
                        mysql_config = file_config["mysql"]
                        self.config["remote"].update({
                            "enabled": True,  # 强制启用
                            "type": "mysql",  # 🔥 修复：确保type字段存在
                            "host": mysql_config.get("host", "**************"),
                            "port": mysql_config.get("port", 3306),
                            "user": mysql_config.get("user", "root"),
                            "password": mysql_config.get("password", "55cee73f3102126a"),
                            "database": mysql_config.get("database", "linyanran")
                        })
                        logger.success(f"从database.json加载MySQL配置: {mysql_config.get('host')}:{mysql_config.get('port')}")
                    
                    logger.info("使用传统配置文件加载数据库配置")
                else:
                    logger.warning_status("未找到数据库配置文件，使用远程MySQL默认配置")
                    # 🔥 老王修复：确保默认配置也使用远程MySQL
                    self.config["remote"].update({
                        "host": "**************",
                        "port": 3306,
                        "user": "root",
                        "password": "55cee73f3102126a",
                        "database": "linyanran"
                    })
            
            # 确保必要的目录存在
            local_db_path = self.config["local"]["path"]
            os.makedirs(os.path.dirname(local_db_path), exist_ok=True)
            
            if self.config["backup"]["enabled"]:
                backup_path = self.config["backup"]["path"]
                os.makedirs(backup_path, exist_ok=True)
            
        except Exception as e:
            logger.error_status(f"加载数据库配置失败: {e}")
            logger.error_status(f"异常详情: {traceback.format_exc()}")
            # 使用最基本的默认配置
            self.config = {
                "priority": "local",
                "local": {"enabled": True, "type": "sqlite", "path": "data/digital_life.db"},
                "remote": {"enabled": False},
                "backup": {"enabled": False}
            }
    
    def _init_connections(self):
        """初始化连接"""
        try:
            # 确保配置有效
            if not self.config:
                logger.error_status("存储管理器配置为空")
                return
                
            # 确保本地配置存在
            if "local" not in self.config:
                logger.warning_status("缺少本地存储配置，使用默认配置")
                self.config["local"] = {
                    "enabled": True,
                    "path": "./data/local_storage.db",
                    "type": "sqlite"
                }
                
            # 初始化本地数据库
            if self.config["local"]["enabled"]:
                db_path = self.config["local"]["path"]
                if not os.path.isabs(db_path):
                    # 如果是相对路径，转换为绝对路径
                    base_dir = os.path.dirname(os.path.abspath(__file__))
                    project_root = os.path.dirname(base_dir)
                    db_path = os.path.join(project_root, db_path)
                    
                # 确保目录存在
                os.makedirs(os.path.dirname(db_path), exist_ok=True)
                
                self.local_db_path = db_path
                logger.data_status(f"本地数据库路径: {self.local_db_path}")
                
                try:
                    import sqlite3
                    self._local_db = sqlite3.connect(self.local_db_path, check_same_thread=False)
                    self._create_tables(self._local_db)
                    self.local_connected = True
                    logger.success("已连接到本地SQLite数据库")
                except Exception as e:
                    logger.error_status(f"连接本地数据库失败: {e}")
                    self.local_connected = False
            else:
                logger.info("本地存储未启用")
                self.local_connected = False
            
            # 初始化远程数据库连接
            if "remote" in self.config and self.config["remote"]["enabled"]:
                remote_config = self.config["remote"]
                db_type = remote_config.get("type", "").lower()
                
                if db_type == "mysql":
                    try:
                        logger.debug("MySQL连接器导入成功")
                        
                        # 连接主MySQL数据库
                        connection = self._connect_mysql(remote_config)
                        
                        if connection:
                            self._remote_db = connection
                            self.remote_connected = True
                            logger.success(f"已连接到远程MySQL数据库: {remote_config['host']}:{remote_config['port']}")
                        else:
                            # 主服务器连接失败，尝试备用服务器
                            logger.warning_status(f"主MySQL服务器连接失败，尝试备用服务器")
                            
                            # 检查是否有备用配置
                            if "fallback" in remote_config and remote_config["fallback"]["enabled"]:
                                fallback_config = remote_config["fallback"]
                                logger.success(f"尝试连接备用MySQL服务器: {fallback_config['host']}:{fallback_config['port']}")
                                
                                # 尝试连接备用服务器
                                connection = self._connect_mysql(fallback_config)
                                
                                if connection:
                                    self._remote_db = connection
                                    self.remote_connected = True
                                    logger.success(f"已连接到备用MySQL服务器")
                                else:
                                    logger.error_status("备用MySQL服务器连接也失败")
                                    self.remote_connected = False
                            else:
                                logger.warning_status("没有配置备用MySQL服务器")
                                self.remote_connected = False
                        
                    except Exception as e:
                        logger.error_status(f"连接远程MySQL数据库失败: {e}")
                        traceback.print_exc()
                        self.remote_connected = False
                else:
                    logger.warning_status(f"不支持的远程数据库类型: {db_type}")
                    self.remote_connected = False
            else:
                logger.data_status("远程数据库未启用")
            
            # 初始化向量数据库
            if "vector_db" in self.config and self.config["vector_db"]["enabled"]:
                vector_config = self.config["vector_db"]
                db_type = vector_config.get("type", "").lower()
                
                if db_type == "chroma":
                    try:
                        import chromadb
                        from chromadb.config import Settings
                        
                        # 获取持久化目录
                        persist_directory = vector_config.get("path", "./data/chroma_db")
                        if not os.path.isabs(persist_directory):
                            base_dir = os.path.dirname(os.path.abspath(__file__))
                            project_root = os.path.dirname(base_dir)
                            persist_directory = os.path.join(project_root, persist_directory)
                        
                        # 确保目录存在
                        os.makedirs(persist_directory, exist_ok=True)
                        
                        # 创建客户端
                        client_settings = Settings(
                            chroma_db_impl="duckdb+parquet",
                            persist_directory=persist_directory
                        )
                        
                        # 检查是否为远程Chroma
                        if vector_config.get("host"):
                            # 远程ChromaDB
                            host = vector_config.get("host")
                            port = vector_config.get("port", 8000)
                            self.vector_db = chromadb.HttpClient(host=host, port=port)
                            logger.success(f"已连接到远程ChromaDB: {host}:{port}")
                        else:
                            # 本地ChromaDB
                            self.vector_db = chromadb.Client(client_settings)
                            logger.success(f"已连接到本地ChromaDB: {persist_directory}")
                        
                        # 创建默认集合
                        default_collection = self.vector_db.get_or_create_collection("digital_life_memory")
                        logger.debug(f"已创建/获取默认向量集合: {default_collection.name}")
                        
                    except Exception as e:
                        logger.error_status(f"连接向量数据库失败: {e}")
                        traceback.print_exc()
                        self.vector_db = None
                else:
                    logger.warning_status(f"不支持的向量数据库类型: {db_type}")
                    self.vector_db = None
            else:
                logger.data_status("向量数据库未启用")
                
        except Exception as e:
            logger.error_status(f"初始化连接失败: {e}")
            traceback.print_exc()
            self.remote_connected = False
    
    def _connect_mysql(self, config=None):
        """
        连接MySQL数据库
        
        Args:
            config: MySQL配置信息
            
        Returns:
            连接对象
        """
        if config is None:
            config = self.config["remote"]
            
        if not config:
            logger.error_status("MySQL配置为空，无法连接")
            return None
            
        try:
            # 设置重试参数
            max_retries = 3
            retry_count = 0
            retry_delay = 2  # 初始延迟2秒
            
            while retry_count < max_retries:
                try:
                    # 参考旧框架中的连接参数，确保兼容性
                    connection = mysql.connector.connect(
                        host=config.get("host", "localhost"),
                        port=config.get("port", 3306),
                        user=config.get("user", "root"),
                        password=config.get("password", ""),
                        database=config.get("database", ""),
                        connection_timeout=config.get("connection_timeout", 10),
                        use_pure=True,  # 使用纯Python实现，提高兼容性
                        auth_plugin='mysql_native_password'  # 兼容MySQL 8.0+
                    )
                    
                    # 检查连接是否有效
                    if connection and connection.is_connected():
                        logger.success(f"已成功连接到MySQL数据库: {config.get('host')}:{config.get('port')}/{config.get('database')}")
                        
                        # 配置连接参数
                        cursor = connection.cursor()
                        # 设置更长的会话超时时间
                        cursor.execute("SET SESSION wait_timeout=28800")  # 8小时超时
                        cursor.execute("SET NAMES utf8mb4")  # 使用utf8mb4字符集
                        cursor.execute("SET CHARACTER SET utf8mb4")
                        cursor.close()
                        
                        # 启动定期ping
                        self._start_mysql_keepalive(connection)
                        
                        return connection
                    else:
                        logger.warning_status("MySQL连接对象创建，但连接状态无效")
                        retry_count += 1
                        
                except mysql.connector.Error as err:
                    # 处理特定的错误类型
                    if isinstance(err, mysql.connector.errors.InterfaceError):
                        logger.warning_status(f"MySQL接口错误: {err}")
                    elif isinstance(err, mysql.connector.errors.ProgrammingError):
                        logger.warning_status(f"MySQL编程错误: {err}")
                    elif isinstance(err, mysql.connector.errors.IntegrityError):
                        logger.warning_status(f"MySQL完整性错误: {err}")
                    elif isinstance(err, mysql.connector.errors.NotSupportedError):
                        logger.warning_status(f"MySQL不支持的操作: {err}")
                    else:
                        logger.warning_status(f"MySQL连接错误: {err}")
                        
                    # 重试逻辑
                    retry_count += 1
                    if retry_count < max_retries:
                        logger.success(f"尝试重新连接MySQL (第{retry_count}次重试，延迟{retry_delay}秒)")
                        time.sleep(retry_delay)
                        retry_delay *= 2  # 指数退避
                    else:
                        logger.error_status(f"无法连接MySQL数据库，已达最大重试次数: {max_retries}")
                        
            return None
            
        except Exception as e:
            logger.error_status(f"连接MySQL数据库时发生未知错误: {e}")
            import traceback
            logger.error_status(f"详细错误信息: {traceback.format_exc()}")
            return None
            
    def _start_mysql_keepalive(self, connection):
        """
        启动MySQL保活机制
        
        Args:
            connection: MySQL连接对象
        """
        if not connection:
            return
            
        def ping_mysql():
            """定期ping MySQL连接以保持活跃"""
            while self.is_running and connection:
                try:
                    if connection.is_connected():
                        cursor = connection.cursor()
                        cursor.execute("SELECT 1")
                        # 读取并清除结果集，避免"Unread result found"错误
                        cursor.fetchall()
                        cursor.close()
                        logger.debug("MySQL保活ping成功")
                    else:
                        logger.warning_status("MySQL连接已断开，尝试重新连接")
                        connection.reconnect(attempts=3, delay=1)
                except Exception as e:
                    logger.warning_status(f"MySQL保活失败: {e}")
                    try:
                        connection.reconnect(attempts=3, delay=1)
                    except Exception as reconnect_err:
                        logger.error_status(f"MySQL重新连接失败: {reconnect_err}")
                        
                # 每5分钟ping一次
                time.sleep(300)
                
        # 启动保活线程
        ping_thread = threading.Thread(target=ping_mysql, daemon=True)
        ping_thread.start()
    
    def _create_tables(self, db_conn):
        """创建必要的数据表"""
        try:
            cursor = db_conn.cursor()
            
            # 用户表
            cursor.execute('''
            CREATE TABLE IF NOT EXISTS users (
                id TEXT PRIMARY KEY,
                username TEXT NOT NULL,
                created_at TEXT NOT NULL,
                last_active TEXT,
                preferences TEXT,
                data TEXT
            )
            ''')
            
            # 会话表
            cursor.execute('''
            CREATE TABLE IF NOT EXISTS sessions (
                id TEXT PRIMARY KEY,
                user_id TEXT NOT NULL,
                title TEXT,
                created_at TEXT NOT NULL,
                updated_at TEXT,
                summary TEXT,
                metadata TEXT,
                FOREIGN KEY (user_id) REFERENCES users (id)
            )
            ''')
            
            # 消息表
            cursor.execute('''
            CREATE TABLE IF NOT EXISTS messages (
                id TEXT PRIMARY KEY,
                session_id TEXT NOT NULL,
                user_id TEXT NOT NULL,
                role TEXT NOT NULL,
                content TEXT NOT NULL,
                created_at TEXT NOT NULL,
                metadata TEXT,
                FOREIGN KEY (session_id) REFERENCES sessions (id),
                FOREIGN KEY (user_id) REFERENCES users (id)
            )
            ''')
            
            # 生命体征表
            cursor.execute('''
            CREATE TABLE IF NOT EXISTS vital_signs (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                timestamp TEXT NOT NULL,
                heart_rate REAL,
                emotion TEXT,
                mood REAL,
                energy REAL,
                memory_usage REAL,
                thought_capacity REAL
            )
            ''')
            
            # 系统日志表
            cursor.execute('''
            CREATE TABLE IF NOT EXISTS system_logs (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                timestamp TEXT NOT NULL,
                level TEXT NOT NULL,
                component TEXT NOT NULL,
                message TEXT NOT NULL,
                metadata TEXT
            )
            ''')
            
            # 键值存储表（用于通用数据存储）
            cursor.execute('''
            CREATE TABLE IF NOT EXISTS key_value_store (
                key TEXT PRIMARY KEY,
                value TEXT NOT NULL,
                updated_at TEXT NOT NULL
            )
            ''')
            
            db_conn.commit()
            logger.success("数据表创建或验证成功")
            
        except Exception as e:
            logger.error_status(f"创建数据表失败: {e}")
            db_conn.rollback()
    
    def _get_connection(self):
        """根据优先级获取数据库连接"""
        if self.config["priority"] == "remote" and self.remote_connected:
            return self._remote_db
        elif self.local_connected:
            return self._local_db
        elif self.remote_connected:
            return self._remote_db
        else:
            raise Exception("没有可用的数据库连接")
    
    def _now_iso(self):
        """获取当前ISO格式时间字符串"""
        return datetime.datetime.now().isoformat()
    
    def _backup_database(self):
        """备份本地数据库"""
        if not self.config["backup"]["enabled"] or not self.local_connected:
            return
        
        try:
            backup_path = self.config["backup"]["path"]
            db_path = self.config["local"]["path"]
            
            # 生成备份文件名
            timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
            backup_file = os.path.join(backup_path, f"digital_life_{timestamp}.db")
            
            # 关闭连接，进行备份
            self._local_db.close()
            
            # 复制数据库文件
            import shutil
            shutil.copy2(db_path, backup_file)
            
            # 重新连接
            self._init_local_db()
            
            logger.success(f"数据库备份成功: {backup_file}")
            
            # 清理旧备份
            self._cleanup_old_backups()
            
        except Exception as e:
            logger.error_status(f"数据库备份失败: {e}")
            # 确保重新连接
            self._init_local_db()
    
    def _cleanup_old_backups(self):
        """清理过期的备份文件"""
        try:
            backup_path = self.config["backup"]["path"]
            keep_days = self.config["backup"]["keep_days"]
            
            # 计算截止时间
            cutoff_time = time.time() - (keep_days * 86400)
            
            # 获取所有备份文件
            for filename in os.listdir(backup_path):
                if filename.startswith("digital_life_") and filename.endswith(".db"):
                    file_path = os.path.join(backup_path, filename)
                    
                    # 检查文件修改时间
                    if os.path.getmtime(file_path) < cutoff_time:
                        os.remove(file_path)
                        logger.debug(f"删除过期备份: {filename}")
            
        except Exception as e:
            logger.error_status(f"清理备份文件失败: {e}")
    
    def create_user(self, user_id: str, username: str, preferences: Dict = None) -> bool:
        """
        创建新用户
        
        Args:
            user_id: 用户ID
            username: 用户名
            preferences: 用户偏好设置
            
        Returns:
            是否创建成功
        """
        try:
            conn = self._get_connection()
            cursor = conn.cursor()
            
            # 准备数据
            now = self._now_iso()
            preferences_json = json.dumps(preferences or {})
            
            # 插入用户记录
            cursor.execute(
                "INSERT INTO users (id, username, created_at, preferences, data) VALUES (?, ?, ?, ?, ?)",
                (user_id, username, now, preferences_json, '{}')
            )
            
            conn.commit()
            logger.success(f"创建用户成功: {username} ({user_id})")
            return True
            
        except Exception as e:
            logger.error_status(f"创建用户失败: {e}")
            return False
    
    def get_user(self, user_id: str) -> Optional[Dict[str, Any]]:
        """
        获取用户信息
        
        Args:
            user_id: 用户ID
            
        Returns:
            用户信息字典，不存在则返回None
        """
        try:
            conn = self._get_connection()
            cursor = conn.cursor()
            
            # 查询用户
            cursor.execute("SELECT * FROM users WHERE id = ?", (user_id,))
            row = cursor.fetchone()
            
            if not row:
                return None
            
            # 转换为字典
            user = dict(zip([column[0] for column in cursor.description], row))
            
            # 解析JSON字段
            user["preferences"] = json.loads(user["preferences"])
            user["data"] = json.loads(user["data"])
            
            return user
            
        except Exception as e:
            logger.error_status(f"获取用户失败: {e}")
            return None
    
    def update_user(self, user_id: str, data: Dict[str, Any]) -> bool:
        """
        更新用户信息
        
        Args:
            user_id: 用户ID
            data: 要更新的数据
            
        Returns:
            是否更新成功
        """
        try:
            conn = self._get_connection()
            cursor = conn.cursor()
            
            # 准备更新字段
            updates = []
            params = []
            
            for key, value in data.items():
                if key in ["username", "last_active"]:
                    updates.append(f"{key} = ?")
                    params.append(value)
                elif key in ["preferences", "data"]:
                    updates.append(f"{key} = ?")
                    params.append(json.dumps(value))
            
            if not updates:
                return False
            
            # 添加用户ID
            params.append(user_id)
            
            # 执行更新
            query = f"UPDATE users SET {', '.join(updates)} WHERE id = ?"
            cursor.execute(query, params)
            
            conn.commit()
            logger.success(f"更新用户成功: {user_id}")
            return True
            
        except Exception as e:
            logger.error_status(f"更新用户失败: {e}")
            return False
    
    def create_session(self, user_id: str, title: str = None) -> Optional[str]:
        """
        创建新会话
        
        Args:
            user_id: 用户ID
            title: 会话标题
            
        Returns:
            会话ID，失败则返回None
        """
        try:
            conn = self._get_connection()
            cursor = conn.cursor()
            
            # 生成会话ID
            import uuid
            session_id = str(uuid.uuid4())
            
            # 准备数据
            now = self._now_iso()
            
            # 插入会话记录
            cursor.execute(
                "INSERT INTO sessions (id, user_id, title, created_at, updated_at, summary, metadata) VALUES (?, ?, ?, ?, ?, ?, ?)",
                (session_id, user_id, title or "新会话", now, now, "", "{}")
            )
            
            conn.commit()
            logger.success(f"创建会话成功: {session_id} (用户: {user_id})")
            return session_id
            
        except Exception as e:
            logger.error_status(f"创建会话失败: {e}")
            return None
    
    def get_sessions(self, user_id: str, limit: int = 10) -> List[Dict[str, Any]]:
        """
        获取用户会话列表
        
        Args:
            user_id: 用户ID
            limit: 最大返回数量
            
        Returns:
            会话列表
        """
        try:
            conn = self._get_connection()
            cursor = conn.cursor()
            
            # 查询会话
            cursor.execute(
                "SELECT * FROM sessions WHERE user_id = ? ORDER BY updated_at DESC LIMIT ?",
                (user_id, limit)
            )
            rows = cursor.fetchall()
            
            # 转换为字典列表
            sessions = []
            for row in rows:
                session = dict(zip([column[0] for column in cursor.description], row))
                session["metadata"] = json.loads(session["metadata"])
                sessions.append(session)
            
            return sessions
            
        except Exception as e:
            logger.error_status(f"获取会话列表失败: {e}")
            return []
    
    def add_message(self, session_id: str, user_id: str, role: str, content: str, metadata: Dict = None) -> Optional[str]:
        """
        添加消息
        
        Args:
            session_id: 会话ID
            user_id: 用户ID
            role: 角色（用户/助手）
            content: 消息内容
            metadata: 元数据
            
        Returns:
            消息ID，失败则返回None
        """
        try:
            conn = self._get_connection()
            cursor = conn.cursor()
            
            # 生成消息ID
            import uuid
            message_id = str(uuid.uuid4())
            
            # 准备数据
            now = self._now_iso()
            metadata_json = json.dumps(metadata or {})
            
            # 插入消息记录 - 兼容MySQL messages表结构
            # 注意：这里保持SQLite格式，但字段名与MySQL兼容
            cursor.execute(
                "INSERT INTO messages (id, session_id, user_id, role, content, created_at, metadata) VALUES (?, ?, ?, ?, ?, ?, ?)",
                (message_id, session_id, user_id, role, content, now, metadata_json)
            )
            
            # 更新会话时间
            cursor.execute(
                "UPDATE sessions SET updated_at = ? WHERE id = ?",
                (now, session_id)
            )
            
            conn.commit()
            logger.success(f"添加消息成功: {message_id} (会话: {session_id})")
            return message_id
            
        except Exception as e:
            logger.error_status(f"添加消息失败: {e}")
            return None
    
    def get_messages(self, session_id: str, limit: int = 50) -> List[Dict[str, Any]]:
        """
        获取会话消息列表
        
        Args:
            session_id: 会话ID
            limit: 最大返回数量
            
        Returns:
            消息列表
        """
        try:
            conn = self._get_connection()
            cursor = conn.cursor()
            
            # 查询消息 - 兼容MySQL messages表结构
            cursor.execute(
                "SELECT * FROM messages WHERE session_id = ? ORDER BY created_at ASC LIMIT ?",
                (session_id, limit)
            )
            rows = cursor.fetchall()
            
            # 转换为字典列表
            messages = []
            for row in rows:
                message = dict(zip([column[0] for column in cursor.description], row))
                message["metadata"] = json.loads(message["metadata"])
                messages.append(message)
            
            return messages
            
        except Exception as e:
            logger.error_status(f"获取消息列表失败: {e}")
            return []
    
    def add_vital_signs(self, heart_rate: float = None, emotion: str = None, 
                       mood: float = None, energy: float = None, 
                       memory_usage: float = None, thought_capacity: float = None) -> bool:
        """
        添加生命体征记录
        
        Args:
            heart_rate: 心率
            emotion: 情绪状态
            mood: 情绪值
            energy: 能量水平
            memory_usage: 内存使用率
            thought_capacity: 思维容量
            
        Returns:
            是否添加成功
        """
        try:
            conn = self._get_connection()
            cursor = conn.cursor()
            
            # 准备数据
            now = self._now_iso()
            
            # 插入生命体征记录
            cursor.execute(
                """INSERT INTO vital_signs 
                   (timestamp, heart_rate, emotion, mood, energy, memory_usage, thought_capacity) 
                   VALUES (?, ?, ?, ?, ?, ?, ?)""",
                (now, heart_rate, emotion, mood, energy, memory_usage, thought_capacity)
            )
            
            conn.commit()
            return True
            
        except Exception as e:
            logger.error_status(f"添加生命体征记录失败: {e}")
            return False
    
    def get_vital_signs(self, limit: int = 100) -> List[Dict[str, Any]]:
        """
        获取生命体征记录
        
        Args:
            limit: 最大返回数量
            
        Returns:
            生命体征记录列表
        """
        try:
            conn = self._get_connection()
            cursor = conn.cursor()
            
            # 查询生命体征记录
            cursor.execute(
                "SELECT * FROM vital_signs ORDER BY timestamp DESC LIMIT ?",
                (limit,)
            )
            rows = cursor.fetchall()
            
            # 转换为字典列表
            records = []
            for row in rows:
                record = dict(zip([column[0] for column in cursor.description], row))
                records.append(record)
            
            return records
            
        except Exception as e:
            logger.error_status(f"获取生命体征记录失败: {e}")
            return []
    
    def add_log(self, level: str, component: str, message: str, metadata: Dict = None) -> bool:
        """
        添加系统日志
        
        Args:
            level: 日志级别
            component: 组件名称
            message: 日志消息
            metadata: 元数据
            
        Returns:
            是否添加成功
        """
        try:
            conn = self._get_connection()
            cursor = conn.cursor()
            
            # 准备数据
            now = self._now_iso()
            metadata_json = json.dumps(metadata or {})
            
            # 插入日志记录
            cursor.execute(
                "INSERT INTO system_logs (timestamp, level, component, message, metadata) VALUES (?, ?, ?, ?, ?)",
                (now, level, component, message, metadata_json)
            )
            
            conn.commit()
            return True
            
        except Exception as e:
            logger.error_status(f"添加系统日志失败: {e}")
            return False
    
    def get_logs(self, level: str = None, component: str = None, limit: int = 100) -> List[Dict[str, Any]]:
        """
        获取系统日志
        
        Args:
            level: 过滤的日志级别
            component: 过滤的组件名称
            limit: 最大返回数量
            
        Returns:
            日志记录列表
        """
        try:
            conn = self._get_connection()
            cursor = conn.cursor()
            
            # 构建查询条件
            query = "SELECT * FROM system_logs"
            params = []
            
            conditions = []
            if level:
                conditions.append("level = ?")
                params.append(level)
            
            if component:
                conditions.append("component = ?")
                params.append(component)
            
            if conditions:
                query += " WHERE " + " AND ".join(conditions)
            
            query += " ORDER BY timestamp DESC LIMIT ?"
            params.append(limit)
            
            # 执行查询
            cursor.execute(query, params)
            rows = cursor.fetchall()
            
            # 转换为字典列表
            logs = []
            for row in rows:
                log = dict(zip([column[0] for column in cursor.description], row))
                log["metadata"] = json.loads(log["metadata"])
                logs.append(log)
            
            return logs
            
        except Exception as e:
            logger.error_status(f"获取系统日志失败: {e}")
            return []
    
    def close(self):
        """关闭数据库连接"""
        try:
            if self._local_db:
                self._local_db.close()
                logger.success("本地数据库连接已关闭")
            
            if self._remote_db:
                self._remote_db.close()
                logger.success("远程数据库连接已关闭")
                
        except Exception as e:
            logger.error_status(f"关闭数据库连接失败: {e}")
    
    def load_json(self, file_path: str, default: Any = None) -> Any:
        """
        从JSON文件加载数据
        
        Args:
            file_path: JSON文件路径（相对于项目根目录）
            default: 如果文件不存在或加载失败时返回的默认值
            
        Returns:
            Any: 加载的数据，失败时返回default
        """
        try:
            # 构建绝对路径
            if not os.path.isabs(file_path):
                # 获取项目根目录
                base_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
                file_path = os.path.join(base_dir, file_path)
            
            # 如果文件不存在，返回默认值
            if not os.path.exists(file_path):
                logger.info(f"JSON文件不存在: {file_path}，返回默认值")
                return default
            
            # 读取JSON文件
            with open(file_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
                logger.info(f"成功加载JSON文件: {file_path}")
                return data
                
        except json.JSONDecodeError as e:
            logger.error_status(f"JSON文件格式错误: {file_path}, 错误: {e}")
            return default
        except Exception as e:
            logger.error_status(f"加载JSON文件失败: {file_path}, 错误: {e}")
            return default
    
    def save_json(self, file_path: str, data: Any) -> bool:
        """
        保存数据到JSON文件
        
        Args:
            file_path: JSON文件路径（相对于项目根目录）
            data: 要保存的数据
            
        Returns:
            bool: 保存是否成功
        """
        try:
            # 构建绝对路径
            if not os.path.isabs(file_path):
                # 获取项目根目录
                base_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
                file_path = os.path.join(base_dir, file_path)
            
            # 确保目录存在
            os.makedirs(os.path.dirname(file_path), exist_ok=True)
            
            # 🔥 老王修复：使用原子写入避免并发冲突
            from utilities.atomic_file_writer import safe_json_write
            
            success = safe_json_write(file_path, data)
            if success:
                logger.info(f"成功保存JSON文件: {file_path}")
                return True
            else:
                logger.error(f"保存JSON文件失败: {file_path}")
                return False
                
        except Exception as e:
            logger.error_status(f"保存JSON文件失败: {file_path}, 错误: {e}")
            return False
    
    def get(self, key: str, default: Any = None) -> Any:
        """
        通用数据获取方法（兼容旧代码）
        
        Args:
            key: 数据键
            default: 默认值
            
        Returns:
            Any: 获取的数据
        """
        try:
            # 尝试从数据库获取
            conn = self._get_connection()
            cursor = conn.cursor()
            
            # 查询数据
            cursor.execute("SELECT value FROM key_value_store WHERE key = ?", (key,))
            row = cursor.fetchone()
            
            if row:
                # 尝试解析JSON
                try:
                    return json.loads(row[0])
                except json.JSONDecodeError:
                    return row[0]  # 返回原始字符串
            
            return default
            
        except Exception as e:
            logger.error_status(f"获取数据失败: {key}, 错误: {e}")
            return default
    
    def set(self, key: str, value: Any) -> bool:
        """
        通用数据设置方法（兼容旧代码）
        
        Args:
            key: 数据键
            value: 数据值
            
        Returns:
            bool: 设置是否成功
        """
        try:
            # 序列化数据
            if isinstance(value, (dict, list)):
                value_str = json.dumps(value, ensure_ascii=False)
            else:
                value_str = str(value)
            
            conn = self._get_connection()
            cursor = conn.cursor()
            
            # 插入或更新数据
            cursor.execute(
                "INSERT OR REPLACE INTO key_value_store (key, value, updated_at) VALUES (?, ?, ?)",
                (key, value_str, self._now_iso())
            )
            
            conn.commit()
            return True
            
        except Exception as e:
            logger.error_status(f"设置数据失败: {key}, 错误: {e}")
            return False

# 全局访问点
def get_instance() -> StorageManager:
    """获取存储管理器实例"""
    return StorageManager.get_instance() 