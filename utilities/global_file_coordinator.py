#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🔥 老王的全局文件写入协调器
防止多个线程同时写入同一个文件导致损坏
"""

import threading
import time
from typing import Dict, Set
from collections import defaultdict

class GlobalFileCoordinator:
    """全局文件写入协调器"""
    
    def __init__(self):
        self._file_locks: Dict[str, threading.RLock] = {}
        self._global_lock = threading.RLock()
        self._active_writers: Dict[str, Set[str]] = defaultdict(set)
    
    def get_file_lock(self, file_path: str) -> threading.RLock:
        """获取文件专用锁"""
        file_path = os.path.abspath(file_path)
        
        with self._global_lock:
            if file_path not in self._file_locks:
                self._file_locks[file_path] = threading.RLock()
            return self._file_locks[file_path]
    
    def register_writer(self, file_path: str, writer_id: str):
        """注册文件写入者"""
        file_path = os.path.abspath(file_path)
        with self._global_lock:
            self._active_writers[file_path].add(writer_id)
    
    def unregister_writer(self, file_path: str, writer_id: str):
        """注销文件写入者"""
        file_path = os.path.abspath(file_path)
        with self._global_lock:
            self._active_writers[file_path].discard(writer_id)
            if not self._active_writers[file_path]:
                del self._active_writers[file_path]

# 全局实例
_coordinator = GlobalFileCoordinator()

def get_global_file_coordinator() -> GlobalFileCoordinator:
    """获取全局文件协调器实例"""
    return _coordinator
