2025-09-10 22:48:14,537 - Digital - [config_loader] - ℹ️  使用主配置目录: /root/yanran_digital_life/config
🔥 检测到生产环境，自动设置日志级别为INFO
🔥 检测到生产环境，自动设置日志级别为INFO
🔥 检测到生产环境，自动设置日志级别为INFO
🔥 检测到生产环境，自动设置日志级别为INFO
🔥 检测到生产环境，自动设置日志级别为INFO
🔥 检测到生产环境，自动设置日志级别为INFO
🔥 检测到生产环境，自动设置日志级别为INFO
🔥 检测到生产环境，自动设置日志级别为INFO
🔥 检测到生产环境，自动设置日志级别为INFO
🔥 检测到生产环境，自动设置日志级别为INFO
2025-09-10 22:48:19,324 - Digital - [intelligence] - ℹ️  AI智能化系统模块已加载
🔥 检测到生产环境，自动设置日志级别为INFO
🔥 检测到生产环境，自动设置日志级别为INFO
🔥 检测到生产环境，自动设置日志级别为INFO
🔥 检测到生产环境，自动设置日志级别为INFO
🔥 检测到生产环境，自动设置日志级别为INFO
2025-09-10 22:48:19,718 - Digital - [main.success] - ✅ 数字生命体系统启动程序开始执行...
2025-09-10 22:48:19,731 - Digital - [unified_system_config_manager.start_watching] - ℹ️  配置文件监视器已启动
2025-09-10 22:48:19,731 - Digital - [unified_system_config_manager.__init__] - ℹ️  统一系统配置管理器初始化完成
2025-09-10 22:48:19,731 - Digital - [main.main] - ℹ️  使用统一配置管理器，配置目录: /root/yanran_digital_life/config
2025-09-10 22:48:19,731 - Digital - [main.success] - ✅ 初始化系统...
2025-09-10 22:48:19,731 - Digital - [main.success] - ✅ OpenAI配置加载成功: config/openai_config.json
2025-09-10 22:48:19,732 - Digital - [main._setup_signal_handlers] - ℹ️  🔧 信号处理器设置完成
2025-09-10 22:48:19,732 - Digital - [main.__init__] - ℹ️  数字生命体系统实例已创建
2025-09-10 22:48:19,732 - Digital - [main.success] - ✅ 开始初始化数字生命体系统...
2025-09-10 22:48:19,732 - Digital - [main._load_config] - ℹ️  已加载开发配置: /root/yanran_digital_life/config/system_dev.json
2025-09-10 22:48:19,736 - Digital - [dependency_checker.load_dependency_config] - ℹ️  已启用依赖检查跳过模式
2025-09-10 22:48:19,736 - Digital - [dependency_checker.load_dependency_config] - ℹ️  已标记 5 个模块为可选: requests, mysql, openai, networkx, psutil
2025-09-10 22:48:19,736 - Digital - [dependency_checker.set_skip_dependency_checks] - ℹ️  启用依赖检查跳过模式
2025-09-10 22:48:19,736 - Digital - [main._load_config] - ℹ️  已标记 5 个模块为可选依赖
2025-09-10 22:48:19,736 - Digital - [main._load_config] - ℹ️  已加载统一系统配置
2025-09-10 22:48:19,736 - Digital - [main.initialize] - ℹ️  系统名称: 林嫣然数字生命体
2025-09-10 22:48:19,736 - Digital - [singleton_manager.success] - ✅ 已清除重复初始化的详细数据
2025-09-10 22:48:19,736 - Digital - [main.success] - ✅ 单例管理器初始化完成
2025-09-10 22:48:19,736 - Digital - [singleton_manager.get_or_create] - ℹ️  通过工厂函数创建实例: 'enhanced_event_bus'
2025-09-10 22:48:19,737 - Digital - [event_bus.success] - ✅ 事件总线处理线程已启动
2025-09-10 22:48:19,737 - Digital - [enhanced_event_bus.success] - ✅ 增强型事件总线初始化完成
2025-09-10 22:48:19,742 - Digital - [singleton_manager.register] - ℹ️  ✅ 已注册实例: 'enhanced_event_bus'
2025-09-10 22:48:19,742 - Digital - [main.success] - ✅ 事件总线初始化完成
2025-09-10 22:48:19,742 - Digital - [core.exception.unified_exception_handler.__init__] - ℹ️  🔧 统一异常处理器初始化完成
2025-09-10 22:48:19,742 - Digital - [singleton_manager.register] - ℹ️  ✅ 已注册实例: 'exception_handler'
2025-09-10 22:48:19,742 - Digital - [main.success] - ✅ 🔧 统一异常处理器初始化完成
2025-09-10 22:48:19,742 - Digital - [core.life_context.success] - ✅ 初始化生命上下文...
2025-09-10 22:48:19,742 - Digital - [core.life_context.success] - ✅ 生命上下文初始化完成
2025-09-10 22:48:19,743 - Digital - [singleton_manager.register] - ℹ️  ✅ 已注册实例: 'life_context'
2025-09-10 22:48:19,743 - Digital - [main.success] - ✅ 生命上下文初始化完成
2025-09-10 22:48:19,743 - Digital - [main.initialize] - ℹ️  已更新搜索技能API配置
2025-09-10 22:48:19,744 - Digital - [main.initialize] - ℹ️  已更新音乐技能API配置
2025-09-10 22:48:19,744 - Digital - [main.initialize] - ℹ️  已更新drawing_skillAPI配置
2025-09-10 22:48:19,744 - Digital - [main.initialize] - ℹ️  已更新chat_skillAPI配置
2025-09-10 22:48:19,744 - Digital - [main.success] - ✅ 统一技能配置加载完成
🔥 检测到生产环境，自动设置日志级别为INFO
2025-09-10 22:48:19,749 - Digital - [adapters.unified_ai_adapter.success] - ✅ 初始化统一AI适配器
2025-09-10 22:48:19,749 - Digital - [adapters.unified_ai_adapter._initialize] - ℹ️  加载AI服务配置: config/ai_services.json
2025-09-10 22:48:19,749 - Digital - [adapters.unified_ai_adapter._initialize] - ℹ️  设置默认AI服务: openai
2025-09-10 22:48:19,750 - Digital - [adapters.unified_ai_adapter._initialize] - ℹ️  已加载AI服务: deepseek, openai, zhipu, qianwen, compatible_service
2025-09-10 22:48:19,750 - Digital - [adapters.unified_ai_adapter.success] - ✅ 统一AI适配器已初始化
2025-09-10 22:48:19,750 - Digital - [main.success] - ✅ AI适配器初始化完成
2025-09-10 22:48:19,750 - Digital - [adapters.ai_service_adapter.__init__] - ℹ️  AI服务适配器已创建
2025-09-10 22:48:19,750 - Digital - [adapters.ai_service_adapter.__init__] - ℹ️  服务可用性: {'openai': True, 'zhipuai': False, 'dashscope': False, 'qianfan': False, 'baidu': False}
2025-09-10 22:48:19,751 - Digital - [main.success] - ✅ AI服务适配器直接初始化成功
2025-09-10 22:48:19,751 - Digital - [singleton_manager.register] - ℹ️  ✅ 已注册实例: 'ai_service_adapter'
2025-09-10 22:48:19,751 - Digital - [main.success] - ✅ AI服务适配器初始化完成
🔥 检测到生产环境，自动设置日志级别为INFO
2025-09-10 22:48:19,765 - Digital - [thinking_chain.success] - ✅ 初始化思维链路...
2025-09-10 22:48:19,765 - Digital - [thinking_chain.__init__] - ℹ️  已加载思维链路配置: config/thinking_chain.json
2025-09-10 22:48:19,766 - Digital - [thinking_chain.success] - ✅ 已初始化 10 个思维步骤
2025-09-10 22:48:19,766 - Digital - [thinking_chain.success] - ✅ 思维链路初始化完成
2025-09-10 22:48:19,766 - Digital - [singleton_manager.register] - ℹ️  ✅ 已注册实例: 'thinking_chain'
2025-09-10 22:48:19,766 - Digital - [main.success] - ✅ 思维链路初始化完成
2025-09-10 22:48:19,766 - Digital - [main.success] - ✅ 初始化中间件服务...
🔥 检测到生产环境，自动设置日志级别为INFO
2025-09-10 22:48:19,770 - Digital - [middleware.success] - ✅ 初始化中间件
🔥 检测到生产环境，自动设置日志级别为INFO
2025-09-10 22:48:19,775 - Digital - [middleware.system_adapter] - ℹ️  已加载开发配置，启用兼容模式
2025-09-10 22:48:19,775 - Digital - [middleware.system_adapter.__init__] - ℹ️  系统适配器已创建
2025-09-10 22:48:19,775 - Digital - [middleware.system_adapter.success] - ✅ 初始化系统适配器
2025-09-10 22:48:19,775 - Digital - [middleware.system_adapter.initialize] - ℹ️  已创建模拟数字生命体
2025-09-10 22:48:19,775 - Digital - [middleware.system_adapter._register_event_handlers] - ℹ️  已注册系统适配器事件处理器
2025-09-10 22:48:19,775 - Digital - [middleware.system_adapter.success] - ✅ 系统适配器初始化成功
2025-09-10 22:48:19,775 - Digital - [middleware.success] - ✅ 中间件初始化成功
🔥 检测到生产环境，自动设置日志级别为INFO
🔥 检测到生产环境，自动设置日志级别为INFO
🔥 检测到生产环境，自动设置日志级别为INFO
🔥 检测到生产环境，自动设置日志级别为INFO
🔥 检测到生产环境，自动设置日志级别为INFO
2025-09-10 22:48:19,860 - Digital - [startup_hook.success] - ✅ 启动钩子已创建
2025-09-10 22:48:19,860 - Digital - [main.success] - ✅ 启动钩子初始化完成
2025-09-10 22:48:19,861 - Digital - [api_service.success] - ✅ 初始化API服务...
2025-09-10 22:48:19,861 - Digital - [api_service.success] - ✅ 已连接事件总线
2025-09-10 22:48:19,861 - Digital - [api_service.success] - ✅ API服务初始化完成
2025-09-10 22:48:19,862 - Digital - [main.success] - ✅ API服务初始化完成
2025-09-10 22:48:19,862 - Digital - [main.success] - ✅ 中间件 system_adapter 初始化完成
2025-09-10 22:48:19,864 - Digital - [cognitive_integration.__init__] - ℹ️  认知模块集成器已创建
2025-09-10 22:48:19,864 - Digital - [cognitive_integration.success] - ✅ 已连接事件总线
🔥 检测到生产环境，自动设置日志级别为INFO
🔥 检测到生产环境，自动设置日志级别为INFO
2025-09-10 22:48:19,882 - Digital - [cognitive.module_manager.__init__] - ℹ️  认知模块管理器已创建
2025-09-10 22:48:19,882 - Digital - [cognitive.module_manager.success] - ✅ 初始化认知模块管理器
2025-09-10 22:48:19,882 - Digital - [cognitive.module_manager._register_event_handlers] - ℹ️  认知模块管理器事件处理器已注册
2025-09-10 22:48:19,882 - Digital - [cognitive.module_manager._scan_modules] - ℹ️  扫描认知模块目录
2025-09-10 22:48:19,882 - Digital - [cognitive.module_manager._scan_modules] - ℹ️  扫描模块类型: emotion
🔥 检测到生产环境，自动设置日志级别为INFO
🔥 检测到生产环境，自动设置日志级别为INFO
🔥 检测到生产环境，自动设置日志级别为INFO
🔥 检测到生产环境，自动设置日志级别为INFO
🔥 检测到生产环境，自动设置日志级别为INFO
2025-09-10 22:48:20,664 - Digital - [cognitive.module_manager._scan_modules] - ℹ️  首次加载模块: cognitive_modules.emotion.autonomous_emotion_system
🔥 检测到生产环境，自动设置日志级别为INFO
🔥 检测到生产环境，自动设置日志级别为INFO
2025-09-10 22:48:20,674 - Digital - [cognitive.module_manager._scan_modules] - ℹ️  首次加载模块: cognitive_modules.emotion.emotion_system_integration
🔥 检测到生产环境，自动设置日志级别为INFO
2025-09-10 22:48:20,680 - Digital - [cognitive.module_manager._scan_modules] - ℹ️  首次加载模块: cognitive_modules.emotion.emotional_intelligence
🔥 检测到生产环境，自动设置日志级别为INFO
2025-09-10 22:48:20,685 - Digital - [cognitive.module_manager._scan_modules] - ℹ️  首次加载模块: cognitive_modules.emotion.empathy_understanding
2025-09-10 22:48:20,685 - Digital - [cognitive.module_manager._scan_modules] - ℹ️  发现模块类: emotion.EmpathyUnderstanding
🔥 检测到生产环境，自动设置日志级别为INFO
2025-09-10 22:48:20,691 - Digital - [cognitive.module_manager._scan_modules] - ℹ️  重新加载模块: cognitive_modules.emotion.autonomous_emotion_manager
🔥 检测到生产环境，自动设置日志级别为INFO
2025-09-10 22:48:20,698 - Digital - [cognitive.module_manager._scan_modules] - ℹ️  首次加载模块: cognitive_modules.emotion.emotion_analyzer
2025-09-10 22:48:20,698 - Digital - [cognitive.module_manager._scan_modules] - ℹ️  发现模块类: emotion.EmotionAnalyzer
🔥 检测到生产环境，自动设置日志级别为INFO
2025-09-10 22:48:20,702 - Digital - [cognitive.module_manager._scan_modules] - ℹ️  首次加载模块: cognitive_modules.emotion.emotion_regulation
2025-09-10 22:48:20,702 - Digital - [cognitive.module_manager._scan_modules] - ℹ️  发现模块类: emotion.EmotionRegulation
🔥 检测到生产环境，自动设置日志级别为INFO
2025-09-10 22:48:20,715 - Digital - [cognitive.module_manager._scan_modules] - ℹ️  重新加载模块: cognitive_modules.emotion.emotion_simulation
2025-09-10 22:48:20,716 - Digital - [cognitive.module_manager._scan_modules] - ℹ️  发现模块类: emotion.EmotionSimulation
2025-09-10 22:48:20,718 - Digital - [cognitive.module_manager._scan_modules] - ℹ️  首次加载模块: cognitive_modules.emotion.response_modulation
2025-09-10 22:48:20,720 - Digital - [cognitive.module_manager._scan_modules] - ℹ️  首次加载模块: cognitive_modules.emotion.state_analysis
2025-09-10 22:48:20,722 - Digital - [cognitive.module_manager._scan_modules] - ℹ️  首次加载模块: cognitive_modules.emotion.emotion_engine
2025-09-10 22:48:20,722 - Digital - [cognitive.module_manager._scan_modules] - ℹ️  扫描模块类型: physiology
🔥 检测到生产环境，自动设置日志级别为INFO
2025-09-10 22:48:20,727 - Digital - [cognitive.module_manager._scan_modules] - ℹ️  首次加载模块: cognitive_modules.physiology.health_system
2025-09-10 22:48:20,728 - Digital - [cognitive.module_manager._scan_modules] - ℹ️  发现模块类: physiology.HealthSystem
2025-09-10 22:48:20,728 - Digital - [cognitive.module_manager._scan_modules] - ℹ️  扫描模块类型: skills
2025-09-10 22:48:20,735 - Digital - [cognitive.module_manager._scan_modules] - ℹ️  重新加载模块: cognitive_modules.skills.search_skill
2025-09-10 22:48:20,746 - Digital - [cognitive.module_manager._scan_modules] - ℹ️  首次加载模块: cognitive_modules.skills.drawing_skill
🔥 检测到生产环境，自动设置日志级别为INFO
2025-09-10 22:48:20,763 - Digital - [cognitive.module_manager._scan_modules] - ℹ️  首次加载模块: cognitive_modules.skills.chat_skill
2025-09-10 22:48:20,764 - Digital - [cognitive.module_manager._scan_modules] - ℹ️  首次加载模块: cognitive_modules.skills.skill_template
2025-09-10 22:48:20,785 - Digital - [cognitive.module_manager._scan_modules] - ℹ️  首次加载模块: cognitive_modules.skills.skill_manager
2025-09-10 22:48:20,792 - Digital - [cognitive.module_manager._scan_modules] - ℹ️  首次加载模块: cognitive_modules.skills.chat_skill_template
2025-09-10 22:48:20,799 - Digital - [cognitive.module_manager._scan_modules] - ℹ️  首次加载模块: cognitive_modules.skills.enhanced_greeting_skill
2025-09-10 22:48:20,806 - Digital - [cognitive.module_manager._scan_modules] - ℹ️  首次加载模块: cognitive_modules.skills.assistant_reminder_skill
2025-09-10 22:48:20,810 - Digital - [cognitive.module_manager._scan_modules] - ℹ️  首次加载模块: cognitive_modules.skills.enhanced_greeting_skill_simple
2025-09-10 22:48:20,813 - Digital - [cognitive.module_manager._scan_modules] - ℹ️  重新加载模块: cognitive_modules.skills.enhanced_activity_skill
2025-09-10 22:48:20,814 - Digital - [cognitive.module_manager._scan_modules] - ℹ️  扫描模块类型: autonomy
🔥 检测到生产环境，自动设置日志级别为INFO
🔥 检测到生产环境，自动设置日志级别为INFO
🔥 检测到生产环境，自动设置日志级别为INFO
2025-09-10 22:48:20,829 - Digital - [cognitive.module_manager._scan_modules] - ℹ️  首次加载模块: cognitive_modules.autonomy.conscious_awareness
2025-09-10 22:48:20,829 - Digital - [cognitive.module_manager._scan_modules] - ℹ️  发现模块类: autonomy.ConsciousAwareness
2025-09-10 22:48:20,844 - Digital - [cognitive.module_manager._scan_modules] - ℹ️  重新加载模块: cognitive_modules.autonomy.conscious_decision
🔥 检测到生产环境，自动设置日志级别为INFO
2025-09-10 22:48:20,850 - Digital - [cognitive.module_manager._scan_modules] - ℹ️  重新加载模块: cognitive_modules.autonomy.decision_making
🔥 检测到生产环境，自动设置日志级别为INFO
2025-09-10 22:48:20,856 - Digital - [cognitive.module_manager._scan_modules] - ℹ️  重新加载模块: cognitive_modules.autonomy.values_system
🔥 检测到生产环境，自动设置日志级别为INFO
2025-09-10 22:48:20,862 - Digital - [cognitive.module_manager._scan_modules] - ℹ️  重新加载模块: cognitive_modules.autonomy.motivation_system
2025-09-10 22:48:20,862 - Digital - [cognitive.module_manager._scan_modules] - ℹ️  扫描模块类型: perception
2025-09-10 22:48:22,958 - Digital - [cognitive.module_manager._scan_modules] - ℹ️  首次加载模块: cognitive_modules.perception.trend_intelligence
🔥 检测到生产环境，自动设置日志级别为INFO
2025-09-10 22:48:22,965 - Digital - [cognitive.module_manager._scan_modules] - ℹ️  首次加载模块: cognitive_modules.perception.multimodal_perception
2025-09-10 22:48:22,965 - Digital - [cognitive.module_manager._scan_modules] - ℹ️  发现模块类: perception.MultimodalPerception
🔥 检测到生产环境，自动设置日志级别为INFO
2025-09-10 22:48:22,970 - Digital - [cognitive.module_manager._scan_modules] - ℹ️  首次加载模块: cognitive_modules.perception.social_perception
2025-09-10 22:48:22,971 - Digital - [cognitive.module_manager._scan_modules] - ℹ️  发现模块类: perception.SocialPerception
🔥 检测到生产环境，自动设置日志级别为INFO
🔥 检测到生产环境，自动设置日志级别为INFO
2025-09-10 22:48:22,980 - Digital - [cognitive.module_manager._scan_modules] - ℹ️  首次加载模块: cognitive_modules.perception.enhanced_user_perception
2025-09-10 22:48:22,981 - Digital - [cognitive.module_manager._scan_modules] - ℹ️  发现模块类: perception.EnhancedUserPerception
🔥 检测到生产环境，自动设置日志级别为INFO
🔥 检测到生产环境，自动设置日志级别为INFO
2025-09-10 22:48:22,991 - Digital - [cognitive.module_manager._scan_modules] - ℹ️  首次加载模块: cognitive_modules.perception.user_model_builder
2025-09-10 22:48:22,991 - Digital - [cognitive.module_manager._scan_modules] - ℹ️  发现模块类: perception.UserModelBuilder
🔥 检测到生产环境，自动设置日志级别为INFO
2025-09-10 22:48:22,997 - Digital - [cognitive.module_manager._scan_modules] - ℹ️  首次加载模块: cognitive_modules.perception.intention_analysis
2025-09-10 22:48:23,011 - Digital - [cognitive.module_manager._scan_modules] - ℹ️  首次加载模块: cognitive_modules.perception.intention_system
2025-09-10 22:48:23,023 - Digital - [intent_recognition] - ℹ️  ✅ 意图上下文管理器导入成功
2025-09-10 22:48:23,023 - Digital - [cognitive.module_manager._scan_modules] - ℹ️  首次加载模块: cognitive_modules.perception.intent_recognition
2025-09-10 22:48:23,046 - Digital - [cognitive.module_manager._scan_modules] - ℹ️  首次加载模块: cognitive_modules.perception.activity_perception
2025-09-10 22:48:23,048 - Digital - [cognitive.module_manager._scan_modules] - ℹ️  首次加载模块: cognitive_modules.perception.entity_extraction
2025-09-10 22:48:23,050 - Digital - [cognitive.module_manager._scan_modules] - ℹ️  首次加载模块: cognitive_modules.perception.data_perception
🔥 检测到生产环境，自动设置日志级别为INFO
2025-09-10 22:48:23,056 - Digital - [cognitive.module_manager._scan_modules] - ℹ️  重新加载模块: cognitive_modules.perception.user_perception
2025-09-10 22:48:23,060 - Digital - [cognitive.module_manager._scan_modules] - ℹ️  首次加载模块: cognitive_modules.perception.perception_engine
2025-09-10 22:48:23,068 - Digital - [cognitive.module_manager._scan_modules] - ℹ️  首次加载模块: cognitive_modules.perception.intelligent_scheduler
2025-09-10 22:48:23,078 - Digital - [cognitive.module_manager._scan_modules] - ℹ️  首次加载模块: cognitive_modules.perception.environmental_perception
2025-09-10 22:48:24,214 - Digital - [cognitive.module_manager._scan_modules] - ℹ️  首次加载模块: cognitive_modules.perception.trend_perception
🔥 检测到生产环境，自动设置日志级别为INFO
2025-09-10 22:48:24,221 - Digital - [cognitive.module_manager._scan_modules] - ℹ️  重新加载模块: cognitive_modules.perception.user_model
2025-09-10 22:48:24,221 - Digital - [cognitive.module_manager._scan_modules] - ℹ️  发现模块类: perception.UserModel
2025-09-10 22:48:24,221 - Digital - [cognitive.module_manager._scan_modules] - ℹ️  扫描模块类型: behavior
🔥 检测到生产环境，自动设置日志级别为INFO
🔥 检测到生产环境，自动设置日志级别为INFO
🔥 检测到生产环境，自动设置日志级别为INFO
🔥 检测到生产环境，自动设置日志级别为INFO
🔥 检测到生产环境，自动设置日志级别为INFO
🔥 检测到生产环境，自动设置日志级别为INFO
2025-09-10 22:48:24,277 - Digital - [cognitive.module_manager._scan_modules] - ℹ️  首次加载模块: cognitive_modules.behavior.action_planning
2025-09-10 22:48:24,277 - Digital - [cognitive.module_manager._scan_modules] - ℹ️  发现模块类: behavior.ActionPlanning
🔥 检测到生产环境，自动设置日志级别为INFO
2025-09-10 22:48:24,283 - Digital - [cognitive.module_manager._scan_modules] - ℹ️  重新加载模块: cognitive_modules.behavior.response_generator
2025-09-10 22:48:24,283 - Digital - [cognitive.module_manager._scan_modules] - ℹ️  发现模块类: behavior.ResponseGenerator
2025-09-10 22:48:24,285 - Digital - [cognitive.module_manager._scan_modules] - ℹ️  首次加载模块: cognitive_modules.behavior.response_generation
2025-09-10 22:48:24,288 - Digital - [cognitive.module_manager._scan_modules] - ℹ️  首次加载模块: cognitive_modules.behavior.behavior_manager
🔥 检测到生产环境，自动设置日志级别为INFO
2025-09-10 22:48:24,301 - Digital - [cognitive.module_manager._scan_modules] - ℹ️  重新加载模块: cognitive_modules.behavior.creative_content_generator
2025-09-10 22:48:24,301 - Digital - [cognitive.module_manager._scan_modules] - ℹ️  发现模块类: behavior.CreativeContentGenerator
2025-09-10 22:48:24,311 - Digital - [cognitive.module_manager._scan_modules] - ℹ️  首次加载模块: cognitive_modules.behavior.activity_executor
2025-09-10 22:48:24,311 - Digital - [cognitive.module_manager._scan_modules] - ℹ️  发现模块类: behavior.ActivityExecutor
🔥 检测到生产环境，自动设置日志级别为INFO
2025-09-10 22:48:24,318 - Digital - [cognitive.module_manager._scan_modules] - ℹ️  重新加载模块: cognitive_modules.behavior.multimodal_expression
2025-09-10 22:48:24,318 - Digital - [cognitive.module_manager._scan_modules] - ℹ️  发现模块类: behavior.MultimodalExpression
2025-09-10 22:48:24,318 - Digital - [cognitive.module_manager._scan_modules] - ℹ️  扫描模块类型: memory
🔥 检测到生产环境，自动设置日志级别为INFO
2025-09-10 22:48:24,323 - Digital - [cognitive.module_manager._scan_modules] - ℹ️  重新加载模块: cognitive_modules.memory.memory_retrieval
2025-09-10 22:48:24,324 - Digital - [cognitive.module_manager._scan_modules] - ℹ️  发现模块类: memory.EpisodicMemory
2025-09-10 22:48:24,324 - Digital - [cognitive.module_manager._scan_modules] - ℹ️  发现模块类: memory.MemoryRetrieval
2025-09-10 22:48:24,324 - Digital - [cognitive.module_manager._scan_modules] - ℹ️  发现模块类: memory.ProceduralMemory
2025-09-10 22:48:24,326 - Digital - [cognitive.module_manager._scan_modules] - ℹ️  首次加载模块: cognitive_modules.memory.association
2025-09-10 22:48:24,329 - Digital - [cognitive.module_manager._scan_modules] - ℹ️  首次加载模块: cognitive_modules.memory.memory_manager
2025-09-10 22:48:24,336 - Digital - [cognitive.module_manager._scan_modules] - ℹ️  重新加载模块: cognitive_modules.memory.procedural_memory
2025-09-10 22:48:24,336 - Digital - [cognitive.module_manager._scan_modules] - ℹ️  发现模块类: memory.ProceduralMemory
2025-09-10 22:48:24,338 - Digital - [cognitive.module_manager._scan_modules] - ℹ️  首次加载模块: cognitive_modules.memory.retrieval
2025-09-10 22:48:24,345 - Digital - [cognitive.module_manager._scan_modules] - ℹ️  首次加载模块: cognitive_modules.memory.long_term_memory_manager
2025-09-10 22:48:24,346 - Digital - [cognitive.module_manager._scan_modules] - ℹ️  发现模块类: memory.EpisodicMemory
2025-09-10 22:48:24,346 - Digital - [cognitive.module_manager._scan_modules] - ℹ️  发现模块类: memory.LongTermMemoryManager
2025-09-10 22:48:24,346 - Digital - [cognitive.module_manager._scan_modules] - ℹ️  发现模块类: memory.MemoryIntegration
2025-09-10 22:48:24,356 - Digital - [cognitive.module_manager._scan_modules] - ℹ️  重新加载模块: cognitive_modules.memory.memory_integration
2025-09-10 22:48:24,356 - Digital - [cognitive.module_manager._scan_modules] - ℹ️  发现模块类: memory.EpisodicMemory
2025-09-10 22:48:24,356 - Digital - [cognitive.module_manager._scan_modules] - ℹ️  发现模块类: memory.MemoryIntegration
2025-09-10 22:48:24,356 - Digital - [cognitive.module_manager._scan_modules] - ℹ️  发现模块类: memory.ProceduralMemory
🔥 检测到生产环境，自动设置日志级别为INFO
2025-09-10 22:48:24,362 - Digital - [cognitive.module_manager._scan_modules] - ℹ️  首次加载模块: cognitive_modules.memory.memory_consolidation
2025-09-10 22:48:24,362 - Digital - [cognitive.module_manager._scan_modules] - ℹ️  发现模块类: memory.EpisodicMemory
2025-09-10 22:48:24,362 - Digital - [cognitive.module_manager._scan_modules] - ℹ️  发现模块类: memory.MemoryConsolidation
2025-09-10 22:48:24,362 - Digital - [cognitive.module_manager._scan_modules] - ℹ️  发现模块类: memory.ProceduralMemory
2025-09-10 22:48:24,372 - Digital - [cognitive.module_manager._scan_modules] - ℹ️  重新加载模块: cognitive_modules.memory.episodic_memory
2025-09-10 22:48:24,372 - Digital - [cognitive.module_manager._scan_modules] - ℹ️  发现模块类: memory.EpisodicMemory
🔥 检测到生产环境，自动设置日志级别为INFO
2025-09-10 22:48:24,379 - Digital - [cognitive.module_manager._scan_modules] - ℹ️  重新加载模块: cognitive_modules.memory.semantic_memory
2025-09-10 22:48:24,379 - Digital - [cognitive.module_manager._scan_modules] - ℹ️  扫描模块类型: neural
2025-09-10 22:48:24,404 - Digital - [cognitive.module_manager._scan_modules] - ℹ️  首次加载模块: cognitive_modules.neural.organ_neural_network
2025-09-10 22:48:24,405 - Digital - [cognitive.module_manager._scan_modules] - ℹ️  扫描模块类型: ai
2025-09-10 22:48:24,420 - Digital - [cognitive.module_manager._scan_modules] - ℹ️  重新加载模块: cognitive_modules.ai.yanran_decision_engine
2025-09-10 22:48:24,421 - Digital - [cognitive.module_manager._scan_modules] - ℹ️  扫描模块类型: cognition
🔥 检测到生产环境，自动设置日志级别为INFO
2025-09-10 22:48:24,436 - Digital - [cognitive.module_manager._scan_modules] - ℹ️  首次加载模块: cognitive_modules.cognition.autonomous_decision
2025-09-10 22:48:24,436 - Digital - [cognitive.module_manager._scan_modules] - ℹ️  发现模块类: cognition.AutonomousDecision
🔥 检测到生产环境，自动设置日志级别为INFO
2025-09-10 22:48:24,447 - Digital - [cognitive.module_manager._scan_modules] - ℹ️  首次加载模块: cognitive_modules.cognition.adaptive_dialog
2025-09-10 22:48:24,448 - Digital - [cognitive.module_manager._scan_modules] - ℹ️  发现模块类: cognition.AdaptiveDialogModule
🔥 检测到生产环境，自动设置日志级别为INFO
🔥 检测到生产环境，自动设置日志级别为INFO
2025-09-10 22:48:24,470 - Digital - [cognitive.module_manager._scan_modules] - ℹ️  首次加载模块: cognitive_modules.cognition.conscious_decision
2025-09-10 22:48:24,470 - Digital - [cognitive.module_manager._scan_modules] - ℹ️  发现模块类: cognition.ConsciousDecision
🔥 检测到生产环境，自动设置日志级别为INFO
2025-09-10 22:48:24,480 - Digital - [cognitive.module_manager._scan_modules] - ℹ️  首次加载模块: cognitive_modules.cognition.reasoning
🔥 检测到生产环境，自动设置日志级别为INFO
2025-09-10 22:48:24,497 - Digital - [cognitive.module_manager._scan_modules] - ℹ️  首次加载模块: cognitive_modules.cognition.autonomous_thinking
2025-09-10 22:48:24,497 - Digital - [cognitive.module_manager._scan_modules] - ℹ️  发现模块类: cognition.AutonomousThinking
🔥 检测到生产环境，自动设置日志级别为INFO
2025-09-10 22:48:24,502 - Digital - [cognitive.module_manager._scan_modules] - ℹ️  重新加载模块: cognitive_modules.cognition.self_reflection
2025-09-10 22:48:24,502 - Digital - [cognitive.module_manager._scan_modules] - ℹ️  发现模块类: cognition.SelfReflection
🔥 检测到生产环境，自动设置日志级别为INFO
2025-09-10 22:48:25,104 - Digital - [cognitive.module_manager._scan_modules] - ℹ️  首次加载模块: cognitive_modules.cognition.social_network
2025-09-10 22:48:25,104 - Digital - [cognitive.module_manager._scan_modules] - ℹ️  发现模块类: cognition.SocialNetwork
🔥 检测到生产环境，自动设置日志级别为INFO
2025-09-10 22:48:25,113 - Digital - [cognitive.module_manager._scan_modules] - ℹ️  重新加载模块: cognitive_modules.cognition.self_model
2025-09-10 22:48:25,113 - Digital - [cognitive.module_manager._scan_modules] - ℹ️  发现模块类: cognition.SelfModel
🔥 检测到生产环境，自动设置日志级别为INFO
🔥 检测到生产环境，自动设置日志级别为INFO
2025-09-10 22:48:25,121 - Digital - [cognitive.module_manager._scan_modules] - ℹ️  首次加载模块: cognitive_modules.cognition.knowledge_graph
🔥 检测到生产环境，自动设置日志级别为INFO
2025-09-10 22:48:25,128 - Digital - [cognitive.module_manager._scan_modules] - ℹ️  首次加载模块: cognitive_modules.cognition.intention_network
2025-09-10 22:48:25,128 - Digital - [cognitive.module_manager._scan_modules] - ℹ️  发现模块类: cognition.IntentionNetwork
🔥 检测到生产环境，自动设置日志级别为INFO
🔥 检测到生产环境，自动设置日志级别为INFO
2025-09-10 22:48:25,135 - Digital - [cognitive.module_manager._scan_modules] - ℹ️  首次加载模块: cognitive_modules.cognition.decision_making
🔥 检测到生产环境，自动设置日志级别为INFO
2025-09-10 22:48:25,156 - Digital - [cognitive.module_manager._scan_modules] - ℹ️  重新加载模块: cognitive_modules.cognition.creative_thinking
2025-09-10 22:48:25,156 - Digital - [cognitive.module_manager._scan_modules] - ℹ️  发现模块类: cognition.CreativeThinking
🔥 检测到生产环境，自动设置日志级别为INFO
2025-09-10 22:48:25,166 - Digital - [cognitive.module_manager._scan_modules] - ℹ️  首次加载模块: cognitive_modules.cognition.learning
2025-09-10 22:48:25,167 - Digital - [cognitive.module_manager._scan_modules] - ℹ️  扫描模块类型: decision
2025-09-10 22:48:25,177 - Digital - [cognitive.module_manager._scan_modules] - ℹ️  首次加载模块: cognitive_modules.decision.yanran_response_decision
2025-09-10 22:48:25,177 - Digital - [cognitive.module_manager._scan_modules] - ℹ️  扫描模块类型: society
🔥 检测到生产环境，自动设置日志级别为INFO
🔥 检测到生产环境，自动设置日志级别为INFO
🔥 检测到生产环境，自动设置日志级别为INFO
🔥 检测到生产环境，自动设置日志级别为INFO
2025-09-10 22:48:25,205 - Digital - [cognitive.module_manager._scan_modules] - ℹ️  首次加载模块: cognitive_modules.society.social_community
🔥 检测到生产环境，自动设置日志级别为INFO
2025-09-10 22:48:25,208 - Digital - [cognitive.society.multi_agent_system_patch] - ℹ️  多代理系统补丁已应用
2025-09-10 22:48:25,208 - Digital - [cognitive.module_manager._scan_modules] - ℹ️  首次加载模块: cognitive_modules.society.multi_agent_system_patch
2025-09-10 22:48:25,208 - Digital - [cognitive.module_manager._scan_modules] - ℹ️  发现模块类: society.MultiAgentSystem
🔥 检测到生产环境，自动设置日志级别为INFO
2025-09-10 22:48:25,216 - Digital - [cognitive.module_manager._scan_modules] - ℹ️  重新加载模块: cognitive_modules.society.multi_agent_system
2025-09-10 22:48:25,216 - Digital - [cognitive.module_manager._scan_modules] - ℹ️  发现模块类: society.MultiAgentSystem
🔥 检测到生产环境，自动设置日志级别为INFO
2025-09-10 22:48:25,218 - Digital - [cognitive.society.social_network_patch] - ℹ️  社交网络补丁已应用
2025-09-10 22:48:25,219 - Digital - [cognitive.module_manager._scan_modules] - ℹ️  首次加载模块: cognitive_modules.society.social_network_patch
2025-09-10 22:48:25,219 - Digital - [cognitive.module_manager._scan_modules] - ℹ️  发现模块类: society.SocialNetwork
🔥 检测到生产环境，自动设置日志级别为INFO
2025-09-10 22:48:25,231 - Digital - [cognitive.module_manager._scan_modules] - ℹ️  重新加载模块: cognitive_modules.society.social_network
2025-09-10 22:48:25,231 - Digital - [cognitive.module_manager._scan_modules] - ℹ️  发现模块类: society.SocialNetwork
🔥 检测到生产环境，自动设置日志级别为INFO
2025-09-10 22:48:25,238 - Digital - [cognitive.module_manager._scan_modules] - ℹ️  重新加载模块: cognitive_modules.society.social_interaction
2025-09-10 22:48:25,238 - Digital - [cognitive.module_manager._scan_modules] - ℹ️  发现模块类: society.SocialInteraction
2025-09-10 22:48:25,239 - Digital - [cognitive.module_manager._scan_modules] - ℹ️  扫描模块类型: organs
2025-09-10 22:48:25,266 - Digital - [cognitive.module_manager._scan_modules] - ℹ️  首次加载模块: cognitive_modules.organs.world_perception_organ
2025-09-10 22:48:25,273 - Digital - [cognitive.module_manager._scan_modules] - ℹ️  首次加载模块: cognitive_modules.organs.social_interaction_organ
🔥 检测到生产环境，自动设置日志级别为INFO
2025-09-10 22:48:25,278 - Digital - [cognitive.module_manager._scan_modules] - ℹ️  首次加载模块: cognitive_modules.organs.organ_manager
2025-09-10 22:48:25,289 - Digital - [cognitive.module_manager._scan_modules] - ℹ️  首次加载模块: cognitive_modules.organs.relationship_guardian_organ
2025-09-10 22:48:25,299 - Digital - [cognitive.module_manager._scan_modules] - ℹ️  首次加载模块: cognitive_modules.organs.personalized_service_organ
2025-09-10 22:48:25,308 - Digital - [cognitive.module_manager._scan_modules] - ℹ️  首次加载模块: cognitive_modules.organs.relationship_coordination_organ
2025-09-10 22:48:25,314 - Digital - [cognitive.module_manager._scan_modules] - ℹ️  首次加载模块: cognitive_modules.organs.skill_coordination_organ
2025-09-10 22:48:25,323 - Digital - [cognitive.module_manager._scan_modules] - ℹ️  首次加载模块: cognitive_modules.organs.enhanced_expression_types
2025-09-10 22:48:25,331 - Digital - [cognitive.module_manager._scan_modules] - ℹ️  首次加载模块: cognitive_modules.organs.data_perception_organ
2025-09-10 22:48:25,407 - Digital - [cognitive.module_manager._scan_modules] - ℹ️  首次加载模块: cognitive_modules.organs.proactive_expression_organ
2025-09-10 22:48:25,414 - Digital - [cognitive.module_manager._scan_modules] - ℹ️  首次加载模块: cognitive_modules.organs.learning_adaptation_organ
2025-09-10 22:48:25,424 - Digital - [cognitive.module_manager._scan_modules] - ℹ️  首次加载模块: cognitive_modules.organs.enhanced_proactive_expression_organ
2025-09-10 22:48:25,429 - Digital - [cognitive.module_manager._scan_modules] - ℹ️  重新加载模块: cognitive_modules.organs.base_organ
2025-09-10 22:48:25,432 - Digital - [cognitive.module_manager._scan_modules] - ℹ️  首次加载模块: cognitive_modules.organs.emotional_processing_organ
2025-09-10 22:48:25,440 - Digital - [cognitive.module_manager._scan_modules] - ℹ️  首次加载模块: cognitive_modules.organs.creative_expression_organ
2025-09-10 22:48:25,448 - Digital - [cognitive.module_manager._scan_modules] - ℹ️  首次加载模块: cognitive_modules.organs.safety_protection_organ
2025-09-10 22:48:25,455 - Digital - [cognitive.module_manager._scan_modules] - ℹ️  首次加载模块: cognitive_modules.organs.wealth_management_organ
2025-09-10 22:48:25,462 - Digital - [cognitive.module_manager._scan_modules] - ℹ️  首次加载模块: cognitive_modules.organs.world_perception_data_manager
2025-09-10 22:48:25,467 - Digital - [cognitive.module_manager._scan_modules] - ℹ️  首次加载模块: cognitive_modules.organs.intelligent_expression_triggers
2025-09-10 22:48:25,471 - Digital - [cognitive.module_manager._scan_modules] - ℹ️  首次加载模块: cognitive_modules.organs.decision_making_organ
2025-09-10 22:48:25,471 - Digital - [cognitive.module_manager._scan_modules] - ℹ️  扫描模块类型: thinking
2025-09-10 22:48:25,477 - Digital - [cognitive.module_manager._scan_modules] - ℹ️  首次加载模块: cognitive_modules.thinking.proactive_thinking_chain
2025-09-10 22:48:25,477 - Digital - [cognitive.module_manager.success] - ✅ 模块扫描完成，发现 34 个模块类
2025-09-10 22:48:25,477 - Digital - [cognitive.module_manager.success] - ✅ 认知模块管理器初始化成功
2025-09-10 22:48:25,478 - Digital - [cognitive_integration.success] - ✅ 已连接认知模块管理器
2025-09-10 22:48:25,478 - Digital - [main.success] - ✅ 中间件 cognitive_integration 初始化完成
2025-09-10 22:48:25,478 - Digital - [main.success] - ✅ 中间件服务初始化完成
2025-09-10 22:48:25,478 - Digital - [main.success] - ✅ 🧠 老王：草，终于用现有的自主学习系统了！
🔥 检测到生产环境，自动设置日志级别为INFO
🔥 检测到生产环境，自动设置日志级别为INFO
2025-09-10 22:48:25,497 - Digital - [core.ai_enhanced_evolution.success] - ✅ 初始化AI增强进化系统...
2025-09-10 22:48:25,497 - Digital - [core_evolution.success] - ✅ 初始化进化系统...
2025-09-10 22:48:25,497 - Digital - [event_bus.success] - ✅ 事件总线处理线程已启动
2025-09-10 22:48:25,498 - Digital - [core_evolution.__init__] - ℹ️  ✅ 能力集初始化完成
2025-09-10 22:48:25,498 - Digital - [core_evolution.__init__] - ℹ️  ✅ 进化目标初始化完成
2025-09-10 22:48:25,498 - Digital - [core_evolution.__init__] - ℹ️  ✅ 事件处理器注册完成
2025-09-10 22:48:25,498 - Digital - [core_evolution.__init__] - ℹ️  ✅ 进化监控启动完成
2025-09-10 22:48:25,498 - Digital - [core_evolution.success] - ✅ 进化系统初始化完成
2025-09-10 22:48:25,498 - Digital - [core.ai_enhanced_evolution.__init__] - ℹ️  ✅ 父类初始化成功，所有必要属性存在
2025-09-10 22:48:25,720 - Digital - [core.evolution.evolution_engine._load_config_direct] - ℹ️  ✅ 进化引擎配置加载成功: config/evolution_engine.json
2025-09-10 22:48:25,721 - Digital - [core.evolution.evolution_engine._init_compatibility_attributes] - ℹ️  🧬 自主进化引擎初始化完成 - 准备让数字生命进化！
2025-09-10 22:48:25,722 - Digital - [core.evolution.evolution_engine._load_persistent_state] - ℹ️  ✅ 已恢复进化状态: 代数=5297, 成功次数=2120, 累计改进=-2.234
2025-09-10 22:48:25,722 - Digital - [core.evolution.evolution_engine.__init__] - ℹ️  🧬 自主进化引擎初始化完成 - 准备让数字生命进化！
2025-09-10 22:48:25,722 - Digital - [core.monitoring.metrics_collector.__init__] - ℹ️  📊 指标收集器初始化完成
2025-09-10 22:48:25,723 - Digital - [core.monitoring.metrics_collector._collection_loop] - ℹ️  📈 开始指标收集循环
2025-09-10 22:48:25,723 - Digital - [core.monitoring.metrics_collector.start_collection] - ℹ️  🚀 指标收集已启动，间隔: 30秒
2025-09-10 22:48:27,725 - Digital - [core.ai_enhanced_evolution._validate_metrics_collector] - ℹ️  ✅ 指标收集器验证成功，当前指标数量: 6
2025-09-10 22:48:27,725 - Digital - [core.ai_enhanced_evolution._validate_ai_adapter] - ℹ️  ✅ AI适配器验证成功
2025-09-10 22:48:27,725 - Digital - [core.ai_enhanced_evolution._initialize_evolution_dependencies] - ℹ️  ✅ 进化引擎真实依赖项初始化完成
2025-09-10 22:48:27,725 - Digital - [core.ai_enhanced_evolution.success] - ✅ ✅ 进化引擎已连接到AI增强进化系统
2025-09-10 22:48:27,725 - Digital - [core.ai_enhanced_evolution.success] - ✅ AI增强进化系统初始化完成
2025-09-10 22:48:27,725 - Digital - [singleton_manager.register] - ℹ️  ✅ 已注册实例: 'ai_enhanced_evolution'
2025-09-10 22:48:27,725 - Digital - [core.ai_enhanced_evolution.activate] - ℹ️  激活AI增强进化系统...
2025-09-10 22:48:27,725 - Digital - [core.evolution.evolution_engine._load_evolution_strategies] - ℹ️  📋 加载进化策略: performance_optimization - 性能优化策略 - 优化CPU和内存使用
2025-09-10 22:48:27,725 - Digital - [core.evolution.evolution_engine._load_evolution_strategies] - ℹ️  📋 加载进化策略: user_experience_enhancement - 用户体验提升策略 - 优化响应时间和错误处理
2025-09-10 22:48:27,725 - Digital - [core.evolution.evolution_engine._load_evolution_strategies] - ℹ️  📋 加载进化策略: ai_model_optimization - AI模型优化策略 - 优化模型选择和参数
2025-09-10 22:48:27,725 - Digital - [core.evolution.evolution_engine._load_evolution_strategies] - ℹ️  📋 加载进化策略: resource_management - 资源管理优化策略 - 优化内存和存储使用
2025-09-10 22:48:27,725 - Digital - [core.evolution.evolution_engine._load_evolution_strategies] - ℹ️  📋 加载进化策略: advanced_ai_enhancement - 高级AI增强策略 - 多模型融合和智能决策
2025-09-10 22:48:27,726 - Digital - [core.evolution.evolution_engine._load_evolution_strategies] - ℹ️  ✅ 共加载 5 个进化策略
2025-09-10 22:48:27,726 - Digital - [core.evolution.evolution_engine._establish_baseline] - ℹ️  📊 基线指标建立完成，综合评分: 0.890
2025-09-10 22:48:27,726 - Digital - [core.evolution.evolution_engine.initialize] - ℹ️  🚀 进化引擎组件初始化完成，准备开始进化！
2025-09-10 22:48:27,726 - Digital - [core.ai_enhanced_evolution.activate] - ℹ️  ✅ 进化引擎初始化完成
2025-09-10 22:48:27,726 - Digital - [core.ai_enhanced_evolution.activate] - ℹ️  🧬 进化循环已作为后台任务启动
2025-09-10 22:48:27,726 - Digital - [core.ai_enhanced_evolution.activate] - ℹ️  AI增强进化系统已激活
2025-09-10 22:48:27,726 - Digital - [main.success] - ✅ 🧬 进化引擎已在activate()中启动
2025-09-10 22:48:27,726 - Digital - [core_evolution.add_evolution_goal] - ℹ️  已添加进化目标: 消除所有模拟数据使用，实现真实数据驱动
2025-09-10 22:48:27,726 - Digital - [main.success] - ✅ ✅ 进化目标添加成功
2025-09-10 22:48:27,726 - Digital - [main.success] - ✅ ✅ AI增强进化系统激活成功
🔥 检测到生产环境，自动设置日志级别为INFO
🔥 检测到生产环境，自动设置日志级别为INFO
🔥 检测到生产环境，自动设置日志级别为INFO
🔥 检测到生产环境，自动设置日志级别为INFO
🔥 检测到生产环境，自动设置日志级别为INFO
🔥 检测到生产环境，自动设置日志级别为INFO
🔥 检测到生产环境，自动设置日志级别为INFO
2025-09-10 22:48:27,793 - Digital - [health_checker.success] - ✅ 健康检查器初始化完成
🔥 检测到生产环境，自动设置日志级别为INFO
🔥 检测到生产环境，自动设置日志级别为INFO
2025-09-10 22:48:27,854 - Digital - [core.ai_enhanced_consciousness.success] - ✅ 🧠 神经网络增强模块加载成功
🔥 检测到生产环境，自动设置日志级别为INFO
2025-09-10 22:48:27,881 - Digital - [digital_life.success] - ✅ 初始化数字生命体...
2025-09-10 22:48:27,881 - Digital - [core.ai_enhanced_consciousness.success] - ✅ 初始化AI增强意识系统...
2025-09-10 22:48:27,881 - Digital - [core.consciousness.success] - ✅ 初始化意识系统...
2025-09-10 22:48:27,881 - Digital - [core.consciousness._load_consciousness_state] - ℹ️  意识状态已从 data/consciousness/consciousness_state.json 加载
2025-09-10 22:48:27,882 - Digital - [core.consciousness._load_consciousness_state] - ℹ️  意识清醒度: 0.13
2025-09-10 22:48:27,882 - Digital - [core.consciousness._load_consciousness_state] - ℹ️  认知负载: 0.00
2025-09-10 22:48:27,882 - Digital - [core.consciousness._load_consciousness_state] - ℹ️  历史记录: 0 条
2025-09-10 22:48:27,883 - Digital - [core.consciousness.success] - ✅ 意识系统初始化完成
2025-09-10 22:48:27,883 - Digital - [core.ai_enhanced_consciousness.success] - ✅ 初始化自我模型...
2025-09-10 22:48:27,883 - Digital - [core.ai_enhanced_consciousness.success] - ✅ AI增强意识系统初始化完成
2025-09-10 22:48:27,883 - Digital - [core.neural_network.neural_core.success] - ✅ 初始化神经网络核心...
2025-09-10 22:48:27,885 - Digital - [core.neural_network.neural_core.success] - ✅ 神经网络核心初始化完成
2025-09-10 22:48:27,885 - Digital - [resilience_system.success] - ✅ 初始化韧性自愈系统...
2025-09-10 22:48:27,885 - Digital - [resilience_system.success] - ✅ 韧性自愈系统已初始化
2025-09-10 22:48:27,885 - Digital - [digital_life._initialize_hot_topics_components] - ℹ️  🔥 开始初始化热搜感知智能化组件...
2025-09-10 22:48:27,886 - Digital - [cognitive_modules.perception.trend_perception.__init__] - ℹ️  趋势感知引擎初始化完成
2025-09-10 22:48:27,886 - Digital - [digital_life.success] - ✅ ✅ 趋势感知引擎初始化完成
2025-09-10 22:48:27,886 - Digital - [cognitive_modules.perception.trend_intelligence._initialize_trend_engine] - ℹ️  趋势感知引擎引用初始化完成
2025-09-10 22:48:27,886 - Digital - [cognitive_modules.perception.trend_intelligence.__init__] - ℹ️  智能分析引擎初始化完成
2025-09-10 22:48:27,886 - Digital - [digital_life.success] - ✅ ✅ 智能分析引擎初始化完成
2025-09-10 22:48:27,900 - Digital - [intelligent_scheduler.success] - ✅ 初始化了 67 个平台的调度信息
2025-09-10 22:48:27,900 - Digital - [intelligent_scheduler.success] - ✅ 智能调度器初始化完成
2025-09-10 22:48:27,900 - Digital - [digital_life.success] - ✅ ✅ 智能调度器初始化完成
2025-09-10 22:48:27,916 - Digital - [real_time_data_collector.success] - ✅ 智能调度器初始化完成
2025-09-10 22:48:27,916 - Digital - [real_time_data_collector.success] - ✅ 趋势感知引擎初始化完成
2025-09-10 22:48:27,916 - Digital - [real_time_data_collector.success] - ✅ 智能分析引擎初始化完成
2025-09-10 22:48:27,916 - Digital - [real_time_data_collector.success] - ✅ 实时数据收集器初始化完成
2025-09-10 22:48:27,916 - Digital - [digital_life.success] - ✅ ✅ 实时数据收集器初始化完成
2025-09-10 22:48:27,916 - Digital - [digital_life._subscribe_hot_topics_events] - ℹ️  热搜感知事件订阅完成
2025-09-10 22:48:27,916 - Digital - [digital_life.success] - ✅ 🔥 热搜感知智能化组件初始化完成
2025-09-10 22:48:27,916 - Digital - [intent_recognition.success] - ✅ 开始初始化意图识别器...
2025-09-10 22:48:27,917 - Digital - [intent_recognition._load_config] - ℹ️  已加载意图识别配置: /root/yanran_digital_life/config/perception/intent_recognition.json
2025-09-10 22:48:27,917 - Digital - [intent_recognition.success] - ✅ OpenAI配置初始化成功
2025-09-10 22:48:27,917 - Digital - [intent_recognition.success] - ✅ OpenAI配置初始化成功
2025-09-10 22:48:27,918 - Digital - [intent_recognition.success] - ✅ AI服务适配器初始化成功
2025-09-10 22:48:27,918 - Digital - [intent_recognition._register_event_handlers] - ℹ️  意图识别器已注册事件处理器
2025-09-10 22:48:27,918 - Digital - [intent_recognition.success] - ✅ 意图识别器连接到事件总线和生命上下文
2025-09-10 22:48:27,918 - Digital - [intent_recognition.success] - ✅ 意图识别器初始化完成
2025-09-10 22:48:27,920 - Digital - [singleton_manager.register] - ℹ️  ✅ 已注册实例: 'intent_recognizer'
2025-09-10 22:48:27,920 - Digital - [intent_recognition.get_instance] - ℹ️  ✅ 意图识别器已注册到单例管理器
2025-09-10 22:48:27,920 - Digital - [digital_life._initialize_intent_recognizer] - ℹ️  意图识别器初始化成功
2025-09-10 22:48:27,920 - Digital - [digital_life.success] - ✅ 数字生命体初始化完成
2025-09-10 22:48:27,920 - Digital - [core.consciousness.success] - ✅ 初始化意识系统...
2025-09-10 22:48:27,920 - Digital - [core.consciousness._load_consciousness_state] - ℹ️  意识状态已从 data/consciousness/consciousness_state.json 加载
2025-09-10 22:48:27,920 - Digital - [core.consciousness._load_consciousness_state] - ℹ️  意识清醒度: 0.13
2025-09-10 22:48:27,920 - Digital - [core.consciousness._load_consciousness_state] - ℹ️  认知负载: 0.00
2025-09-10 22:48:27,920 - Digital - [core.consciousness._load_consciousness_state] - ℹ️  历史记录: 0 条
2025-09-10 22:48:27,921 - Digital - [core.consciousness.success] - ✅ 意识系统初始化完成
2025-09-10 22:48:27,922 - Digital - [connectors.database.mysql._load_config] - ℹ️  从 /root/yanran_digital_life/config/database.json 加载MySQL配置
2025-09-10 22:48:28,215 - Digital - [mysql.connector.get_auth_plugin] - ℹ️  package: mysql.connector.plugins
2025-09-10 22:48:28,216 - Digital - [mysql.connector.get_auth_plugin] - ℹ️  plugin_name: mysql_native_password
2025-09-10 22:48:28,217 - Digital - [mysql.connector.get_auth_plugin] - ℹ️  AUTHENTICATION_PLUGIN_CLASS: MySQLNativePasswordAuthPlugin
2025-09-10 22:48:30,225 - Digital - [connectors.database.mysql.success] - ✅ MySQL连接池初始化成功 (重试 1/5, 池大小: 5)
2025-09-10 22:48:30,226 - Digital - [resilience_system.register_service] - ℹ️  已注册服务: mysql_connector (类型: database)
2025-09-10 22:48:30,226 - Digital - [connectors.database.mysql.success] - ✅ ✅ MySQL连接器已注册到韧性系统
2025-09-10 22:48:30,228 - Digital - [singleton_manager.register] - ℹ️  ✅ 已注册实例: 'mysql_connector'
2025-09-10 22:48:30,228 - Digital - [connectors.database.mysql.success] - ✅ ✅ MySQL连接器已注册到单例管理器
2025-09-10 22:48:30,228 - Digital - [connectors.database.mysql.success] - ✅ MySQL连接器初始化完成，已连接到 **************:3306/linyanran
2025-09-10 22:48:30,231 - Digital - [connectors.database.redis.success] - ✅ Redis连接初始化成功，连接到 localhost:6379/0
🔥 检测到生产环境，自动设置日志级别为INFO
2025-09-10 22:48:30,245 - Digital - [adapters.legacy.success] - ✅ MySQL连接器已成功导入
🔥 检测到生产环境，自动设置日志级别为INFO
2025-09-10 22:48:30,250 - Digital - [adapters.legacy.success] - ✅ 🔗 LegacyAdapter使用统一MySQL连接器: config/database.json
2025-09-10 22:48:30,250 - Digital - [adapters.legacy.success] - ✅ ✅ LegacyAdapter MySQL连接器初始化成功
2025-09-10 22:48:30,250 - Digital - [adapters.legacy.__init__] - ℹ️  LegacyAdapter已创建
2025-09-10 22:48:30,251 - Digital - [adapters.legacy.success] - ✅ ✅ 统一MySQL连接器已就绪
2025-09-10 22:48:30,251 - Digital - [adapters.legacy.success] - ✅ LegacyAdapter初始化完成
2025-09-10 22:48:30,251 - Digital - [utilities.storage_manager.success] - ✅ 从统一配置管理器加载数据库配置，优先级: remote
2025-09-10 22:48:30,252 - Digital - [utilities.storage_manager.data_status] - 📊 本地数据库路径: /root/yanran_digital_life/data/digital_life.db
2025-09-10 22:48:30,253 - Digital - [utilities.storage_manager.success] - ✅ 数据表创建或验证成功
2025-09-10 22:48:30,254 - Digital - [utilities.storage_manager.success] - ✅ 已连接到本地SQLite数据库
2025-09-10 22:48:30,718 - Digital - [utilities.storage_manager.success] - ✅ 已成功连接到MySQL数据库: **************:3306/linyanran
2025-09-10 22:48:30,938 - Digital - [utilities.storage_manager.success] - ✅ 已连接到远程MySQL数据库: **************:3306
2025-09-10 22:48:30,939 - Digital - [utilities.storage_manager.data_status] - 📊 向量数据库未启用
2025-09-10 22:48:30,940 - Digital - [utilities.storage_manager.success] - ✅ 存储管理器已初始化
2025-09-10 22:48:30,940 - Digital - [health_checker.success] - ✅ 系统健康检查完成，状态: critical
2025-09-10 22:48:30,940 - Digital - [main.success] - ✅ ✅ 健康检查未发现模拟数据问题
2025-09-10 22:48:30,940 - Digital - [main.success] - ✅ ✅ 健康检查系统激活成功
2025-09-10 22:48:30,941 - Digital - [main._activate_autonomous_systems] - ℹ️  🔧 系统协调器未在singleton_manager中找到，尝试创建...
2025-09-10 22:48:30,956 - Digital - [enhanced_event_bus_v2.success] - ✅ 增强型事件总线 V2 初始化完成
2025-09-10 22:48:30,958 - Digital - [enhanced_event_bus_v2.success] - ✅ 事件总线已启动
2025-09-10 22:48:30,958 - Digital - [data_flow_manager.register_flow_chain] - ℹ️  注册数据流链: hot_topics_processing -> ['intelligent_scheduler', 'trend_perception', 'trend_intelligence']
2025-09-10 22:48:30,958 - Digital - [data_flow_manager.register_flow_chain] - ℹ️  注册数据流链: decision_enhancement -> ['autonomous_thinking', 'trend_analysis', 'decision_engine']
2025-09-10 22:48:30,958 - Digital - [data_flow_manager.register_flow_chain] - ℹ️  注册数据流链: emergency_response -> ['emergency_module', 'alert_system', 'recovery_system']
2025-09-10 22:48:30,958 - Digital - [system_coordinator.success] - ✅ 系统协调器初始化完成
2025-09-10 22:48:30,959 - Digital - [singleton_manager.register] - ℹ️  ✅ 已注册实例: 'system_coordinator'
2025-09-10 22:48:30,959 - Digital - [main.success] - ✅ ✅ 系统协调器创建并注册成功
2025-09-10 22:48:30,959 - Digital - [system_coordinator.register_module] - ℹ️  注册模块: intelligence_integration_manager
2025-09-10 22:48:30,959 - Digital - [system_coordinator.register_module] - ℹ️  注册模块: enhanced_proactive_expression_organ
2025-09-10 22:48:30,959 - Digital - [system_coordinator.register_module] - ℹ️  注册模块: digital_life_intelligence_coordinator
2025-09-10 22:48:30,959 - Digital - [system_coordinator.register_module] - ℹ️  注册模块: perception_engine
2025-09-10 22:48:30,959 - Digital - [system_coordinator.register_module] - ℹ️  注册模块: datasource_manager
2025-09-10 22:48:30,960 - Digital - [system_coordinator.register_module] - ℹ️  注册模块: ai_enhanced_consciousness
2025-09-10 22:48:30,961 - Digital - [system_coordinator.register_module] - ℹ️  注册模块: neural_core
2025-09-10 22:48:30,962 - Digital - [system_coordinator.register_module] - ℹ️  注册模块: resilience
2025-09-10 22:48:30,963 - Digital - [system_coordinator.success] - ✅ 系统协调器已启动
2025-09-10 22:48:30,964 - Digital - [main.success] - ✅ ✅ 系统协调器激活成功，已注册8个核心模块
2025-09-10 22:48:31,037 - Digital - [main._activate_autonomous_systems] - ℹ️  📋 已配置模拟数据监控告警，监控关键词: ['random', 'simulate', 'mock', 'fake', '模拟', '随机']
2025-09-10 22:48:31,037 - Digital - [main.success] - ✅ ✅ 系统监控器激活成功
2025-09-10 22:48:31,038 - Digital - [main.success] - ✅ ✅ 自主清理配置已保存到: data/autonomous_cleanup_config.json
2025-09-10 22:48:31,038 - Digital - [main.success] - ✅ 🧠 老王：艹，开始组装高级智能学习零件！
🔥 检测到生产环境，自动设置日志级别为INFO
2025-09-10 22:48:31,044 - Digital - [intelligence.auto_tuning.success] - ✅ 初始化自动调参引擎...
2025-09-10 22:48:31,045 - Digital - [integrated_event_bus.success] - ✅ 集成事件总线初始化完成
2025-09-10 22:48:31,046 - Digital - [intelligence.auto_tuning._load_parameter_history] - ℹ️  已加载参数历史: 3 个参数
2025-09-10 22:48:31,046 - Digital - [intelligence.auto_tuning.success] - ✅ 自动调参引擎初始化完成
2025-09-10 22:48:31,046 - Digital - [singleton_manager.register] - ℹ️  ✅ 已注册实例: 'auto_tuning'
2025-09-10 22:48:31,046 - Digital - [main.success] - ✅ ✅ 参数自动调优模块激活成功
🔥 检测到生产环境，自动设置日志级别为INFO
2025-09-10 22:48:31,055 - Digital - [feedback_learning._load_weights] - ℹ️  从 data/feedback_learning/weights.json 加载权重
2025-09-10 22:48:31,055 - Digital - [feedback_learning.success] - ✅ 反馈学习模块初始化完成
2025-09-10 22:48:31,055 - Digital - [singleton_manager.register] - ℹ️  ✅ 已注册实例: 'feedback_learning'
2025-09-10 22:48:31,055 - Digital - [main.success] - ✅ ✅ 反馈学习模块激活成功
🔥 检测到生产环境，自动设置日志级别为INFO
🔥 检测到生产环境，自动设置日志级别为INFO
2025-09-10 22:48:31,072 - Digital - [intelligence.behavior_learning.success] - ✅ 初始化行为学习系统...
2025-09-10 22:48:31,072 - Digital - [intelligence.learning_system.success] - ✅ 初始化学习系统...
2025-09-10 22:48:31,073 - Digital - [intelligence.learning_system.success] - ✅ 学习数据加载完成
2025-09-10 22:48:31,073 - Digital - [intelligence.learning_system.success] - ✅ 学习系统初始化完成
2025-09-10 22:48:31,074 - Digital - [intelligence.behavior_learning.success] - ✅ 行为数据加载完成
2025-09-10 22:48:31,075 - Digital - [intelligence.behavior_learning.success] - ✅ 行为学习系统初始化完成
2025-09-10 22:48:31,075 - Digital - [singleton_manager.register] - ℹ️  ✅ 已注册实例: 'behavior_learning'
2025-09-10 22:48:31,075 - Digital - [main.success] - ✅ ✅ 行为学习模块激活成功
2025-09-10 22:48:31,076 - Digital - [singleton_manager.register] - ℹ️  ✅ 已注册实例: 'learning_system'
2025-09-10 22:48:31,076 - Digital - [intelligence.learning_system.start_learning] - ℹ️  开始学习模式
2025-09-10 22:48:31,076 - Digital - [main.success] - ✅ ✅ 学习系统激活成功
2025-09-10 22:48:31,076 - Digital - [main.success] - ✅ 🎉 智能学习模块集群激活完成！所有高级零件已组装到汽车上！
2025-09-10 22:48:31,076 - Digital - [main.success] - ✅ 🎉 数字生命自主学习系统激活完成！
2025-09-10 22:48:31,076 - Digital - [main.success] - ✅ 🔥 初始化Enhanced Context Builder 2.0所需的新系统...
2025-09-10 22:48:31,076 - Digital - [main._init_new_systems] - ℹ️  🔧 初始化 通用调度器...
2025-09-10 22:48:31,112 - Digital - [main._init_new_systems] - ⚠️  ⚠️ MySQL连接器未初始化，跳过通用调度器
2025-09-10 22:48:31,112 - Digital - [main._init_new_systems] - ℹ️  🔧 初始化 情感权重系统...
2025-09-10 22:48:31,126 - Digital - [core.emotional_weight_system.success] - ✅ 情感关系权重配置加载成功: config/emotional_relationship_weights.json
2025-09-10 22:48:31,126 - Digital - [LazyComponentLoader.__init__] - ℹ️  懒加载组件管理器初始化完成
2025-09-10 22:48:31,126 - Digital - [PerformanceOptimizer.__init__] - ℹ️  性能优化器初始化完成
2025-09-10 22:48:31,127 - Digital - [singleton_manager.register] - ℹ️  ✅ 已注册实例: 'performance_optimizer'
2025-09-10 22:48:31,127 - Digital - [core.emotional_weight_system.success] - ✅ 情感关系权重系统初始化完成
2025-09-10 22:48:31,128 - Digital - [singleton_manager.register] - ℹ️  ✅ 已注册实例: 'emotional_weight_system'
2025-09-10 22:48:31,128 - Digital - [main.success] - ✅ ✅ 情感权重系统 初始化完成
2025-09-10 22:48:31,128 - Digital - [core.evolution.evolution_engine.start_evolution] - ℹ️  🧬 启动自主进化引擎 - 数字生命开始进化！
2025-09-10 22:48:32,130 - Digital - [core.evolution.evolution_engine._evolution_cycle] - ℹ️  🔄 开始第 5298 代进化
2025-09-10 22:48:32,293 - Digital - [wechat_friend_validator.success] - ✅ 🔐 MySQL连接初始化成功
2025-09-10 22:48:32,293 - Digital - [wechat_friend_validator.__init__] - ℹ️  🔐 WeChat好友验证器初始化完成 (缓存TTL: 300秒)
2025-09-10 22:48:32,293 - Digital - [wechat_message_pusher._init_wechat_client] - ℹ️  📤 正在初始化WeChat客户端: appid=wx_574bN70yW1q0vTSFaaSHU
2025-09-10 22:48:32,294 - Digital - [wechat_client.success] - ✅ ✅ 统一WeChat配置加载成功: config/wechat_config.json
2025-09-10 22:48:32,298 - Digital - [wechat_client.success] - ✅ WeChat消息客户端初始化完成: wx_574bN70yW1q0vTSFaaSHU
2025-09-10 22:48:32,300 - Digital - [wechat_message_pusher.success] - ✅ 📤 WeChat客户端初始化成功: wx_574bN70yW1q0vTSFaaSHU
2025-09-10 22:48:32,301 - Digital - [services.unified_websocket_service.__init__] - ℹ️  WebSocket服务初始化: 127.0.0.1:8766
2025-09-10 22:48:32,301 - Digital - [wechat_message_pusher.success] - ✅ 📤 WebSocket服务连接成功
2025-09-10 22:48:32,301 - Digital - [wechat_message_pusher.__init__] - ℹ️  📤 WeChat消息推送管理器初始化完成
2025-09-10 22:48:32,301 - Digital - [wechat_unified_push.success] - ✅ 📱 WeChat推送器初始化成功
2025-09-10 22:48:32,301 - Digital - [wechat_unified_push.success] - ✅ 📱 WeChat客户端状态正常
2025-09-10 22:48:32,301 - Digital - [wechat_unified_push.success] - ✅ 📱 好友验证器初始化成功
2025-09-10 22:48:32,308 - Digital - [wechat_humanized.__init__] - ℹ️  🤖 WeChat人性化交互控制器初始化完成
2025-09-10 22:48:32,324 - Digital - [intelligent_dispatch._load_user_profiles] - ℹ️  🧠 已加载 6 个用户行为档案
2025-09-10 22:48:32,325 - Digital - [intelligent_dispatch._init_neural_model] - ℹ️  🧠 已创建新的神经网络模型
2025-09-10 22:48:32,333 - Digital - [yanran_decision_engine._ensure_ai_adapter] - ℹ️  AI服务适配器获取成功
2025-09-10 22:48:32,335 - Digital - [yanran_decision_engine._load_decision_config] - ℹ️  加载决策配置成功: config/ai_decision/decision_engine.json
2025-09-10 22:48:32,336 - Digital - [yanran_decision_engine.__init__] - ℹ️  林嫣然AI决策引擎初始化完成
2025-09-10 22:48:32,336 - Digital - [OrganNeuralNetwork.__init__] - ℹ️  器官神经网络初始化完成
2025-09-10 22:48:32,336 - Digital - [core.neural_network.neural_core.success] - ✅ 初始化神经网络核心...
2025-09-10 22:48:32,365 - Digital - [core.neural_network.neural_core.success] - ✅ 神经网络核心初始化完成
2025-09-10 22:48:32,366 - Digital - [intelligent_dispatch._integrate_neural_engine] - ℹ️  🧠 已集成现有神经元自主学习引擎
2025-09-10 22:48:32,366 - Digital - [intelligent_dispatch.success] - ✅ 🧠 智能分发引擎组件初始化成功
2025-09-10 22:48:32,366 - Digital - [intelligent_dispatch.__init__] - ℹ️  🧠 WeChat智能分发引擎初始化完成
2025-09-10 22:48:32,378 - Digital - [load_balancer.__init__] - ℹ️  ⚖️ WeChat负载均衡管理器初始化完成
2025-09-10 22:48:32,378 - Digital - [wechat_unified_push.success] - ✅ 📱 WeChat统一推送服务组件初始化成功
2025-09-10 22:48:32,385 - Digital - [wechat_group_push.success] - ✅ 📊 WeChat群组推送服务组件初始化成功
2025-09-10 22:48:32,387 - Digital - [wechat_group_push.__init__] - ℹ️  📊 WeChat群组推送服务初始化完成
2025-09-10 22:48:32,398 - Digital - [utilities.cache_manager.success] - ✅ 🔥 初始化缓存管理器...
2025-09-10 22:48:32,410 - Digital - [utilities.cache_manager._start_cleanup_thread] - ℹ️  🧹 缓存清理线程已启动
2025-09-10 22:48:32,413 - Digital - [utilities.cache_manager.success] - ✅ ✅ 缓存管理器初始化完成
2025-09-10 22:48:33,193 - Digital - [main._init_new_systems] - ℹ️  🔧 初始化 生命体征模拟器...
2025-09-10 22:48:33,233 - Digital - [perception.physical_world.hardware_monitor.success] - ✅ 硬件监控器初始化完成
2025-09-10 22:48:33,234 - Digital - [singleton_manager.register] - ℹ️  ✅ 已注册实例: 'hardware_monitor'
2025-09-10 22:48:33,234 - Digital - [main.success] - ✅ ✅ 硬件监控器（依赖）初始化完成
2025-09-10 22:48:33,234 - Digital - [perception.physical_world.vital_signs_simulator.success] - ✅ 生命体征模拟器初始化完成
2025-09-10 22:48:33,234 - Digital - [singleton_manager.register] - ℹ️  ✅ 已注册实例: 'vital_signs_simulator'
2025-09-10 22:48:33,234 - Digital - [main.success] - ✅ ✅ 生命体征模拟器 初始化完成
2025-09-10 22:48:33,279 - Digital - [main._init_new_systems] - ℹ️  🔄 硬件监控器 已初始化，跳过
2025-09-10 22:48:33,279 - Digital - [main._init_new_systems] - ℹ️  🔧 初始化 延迟回复管理器...
2025-09-10 22:48:33,290 - Digital - [services.delayed_response_manager.success] - ✅ 🚀 初始化延迟响应管理器...
2025-09-10 22:48:33,290 - Digital - [services.delayed_response_manager.success] - ✅ ✅ 延迟响应管理器初始化完成
2025-09-10 22:48:33,290 - Digital - [singleton_manager.register] - ℹ️  ✅ 已注册实例: 'delayed_response_manager'
2025-09-10 22:48:33,290 - Digital - [services.delayed_response_manager.success] - ✅ 🚀 启动延迟回复后台检查任务（同步版本）
2025-09-10 22:48:33,292 - Digital - [services.delayed_response_manager.success] - ✅ ✅ 延迟回复后台检查任务已在独立线程中启动（同步版本）
2025-09-10 22:48:33,292 - Digital - [main.success] - ✅ ✅ 延迟回复管理器后台检查任务已启动
2025-09-10 22:48:33,292 - Digital - [main.success] - ✅ ✅ 延迟回复管理器 初始化完成
2025-09-10 22:48:33,302 - Digital - [main.success] - ✅ 🔥 Enhanced Context Builder 2.0新系统组件初始化完成
2025-09-10 22:48:33,303 - Digital - [utilities.storage_manager.success] - ✅ 从统一配置管理器加载数据库配置，优先级: remote
2025-09-10 22:48:33,303 - Digital - [utilities.storage_manager.data_status] - 📊 本地数据库路径: /root/yanran_digital_life/data/digital_life.db
2025-09-10 22:48:33,304 - Digital - [utilities.storage_manager.success] - ✅ 数据表创建或验证成功
2025-09-10 22:48:33,304 - Digital - [utilities.storage_manager.success] - ✅ 已连接到本地SQLite数据库
2025-09-10 22:48:33,700 - Digital - [utilities.storage_manager.success] - ✅ 已成功连接到MySQL数据库: **************:3306/linyanran
2025-09-10 22:48:33,879 - Digital - [utilities.storage_manager.success] - ✅ 已连接到远程MySQL数据库: **************:3306
2025-09-10 22:48:33,879 - Digital - [utilities.storage_manager.data_status] - 📊 向量数据库未启用
2025-09-10 22:48:33,879 - Digital - [utilities.storage_manager.success] - ✅ 存储管理器已初始化
2025-09-10 22:48:33,879 - Digital - [utilities.storage_manager.data_status] - 📊 本地数据库路径: /root/yanran_digital_life/data/digital_life.db
2025-09-10 22:48:33,880 - Digital - [utilities.storage_manager.success] - ✅ 数据表创建或验证成功
2025-09-10 22:48:33,880 - Digital - [utilities.storage_manager.success] - ✅ 已连接到本地SQLite数据库
2025-09-10 22:48:33,880 - Digital - [utilities.storage_manager.warning_status] - ⚠️  不支持的远程数据库类型: 
2025-09-10 22:48:33,880 - Digital - [utilities.storage_manager.data_status] - 📊 向量数据库未启用
2025-09-10 22:48:33,881 - Digital - [main.success] - ✅ 🔗 MySQL连接器初始化完成并已注册到singleton_manager
2025-09-10 22:48:33,890 - Digital - [emotions_sync_integration.initialize] - ℹ️  🔧 初始化情感同步系统...
2025-09-10 22:48:33,890 - Digital - [emotions_user_sync_service._get_or_create_mysql_connector] - ℹ️  ✅ 从单例管理器获取MySQL连接器成功
2025-09-10 22:48:33,891 - Digital - [cognitive_modules.emotion.emotional_intelligence.success] - ✅ 成功加载统一情感等级标准
2025-09-10 22:48:33,891 - Digital - [cognitive_modules.emotion.emotional_intelligence.success] - ✅ 情感智能模块初始化完成
2025-09-10 22:48:33,892 - Digital - [cognitive.emotion_analysis.emotions_sync_analyzer.__init__] - ℹ️  模块 emotions_sync_analyzer 已创建
2025-09-10 22:48:33,892 - Digital - [cognitive.emotion.analyzer._initialize_dependencies] - ℹ️  ✅ 情感分析器已连接生命上下文
2025-09-10 22:48:33,892 - Digital - [cognitive.emotion.analyzer._initialize_dependencies] - ℹ️  ✅ 情感分析器已连接事件总线
2025-09-10 22:48:33,892 - Digital - [cognitive.emotion.analyzer.__init__] - ℹ️  情感分析模块已创建
2025-09-10 22:48:33,893 - Digital - [singleton_manager.register] - ℹ️  ✅ 已注册实例: 'emotions_sync_service'
2025-09-10 22:48:33,893 - Digital - [emotions_sync_integration._initial_sync] - ℹ️  🔄 执行初始化情感同步...
2025-09-10 22:48:34,330 - Digital - [emotions_sync_integration._should_perform_full_sync] - ℹ️  📊 执行增量同步: users=609, emotions=609
2025-09-10 22:48:34,331 - Digital - [emotions_user_sync_service.incremental_sync] - ℹ️  🔄 开始增量用户情感同步...
2025-09-10 22:48:34,590 - Digital - [emotions_user_sync_service.incremental_sync] - ℹ️  📊 找到 25 个最近活跃用户
2025-09-10 22:48:51,460 - Digital - [emotions_user_sync_service.incremental_sync] - ℹ️  ✅ 增量同步完成: 处理1个用户, 耗时17.13s
2025-09-10 22:48:51,460 - Digital - [emotions_sync_integration._initial_sync] - ℹ️  ✅ 初始化增量同步成功: 处理25个用户
2025-09-10 22:48:51,460 - Digital - [emotions_user_sync_service.start_sync_service] - ℹ️  🚀 启动情感用户同步服务...
2025-09-10 22:48:51,460 - Digital - [emotions_user_sync_service.full_sync] - ℹ️  🔄 开始完整用户情感同步...
2025-09-10 22:48:51,888 - Digital - [emotions_user_sync_service.full_sync] - ℹ️  📊 找到 609 个用户需要检查
2025-09-10 22:48:52,143 - Digital - [openai._base_client._sleep_for_retry] - ℹ️  Retrying request to /chat/completions in 0.375625 seconds
2025-09-10 22:49:20,121 - Digital - [emotions_user_sync_service._update_existing_intensities] - ℹ️  🔄 更新情感强度: chatroom_47615189019 230 -> 223
2025-09-10 22:49:23,459 - Digital - [emotions_user_sync_service._update_existing_intensities] - ℹ️  🔄 更新情感强度: chatroom_21403562710 388 -> 398
2025-09-10 22:49:24,789 - Digital - [emotions_user_sync_service._update_existing_intensities] - ℹ️  🔄 更新情感强度: chatroom_47366479285 128 -> 137
2025-09-10 22:50:23,372 - Digital - [emotions_user_sync_service._update_existing_intensities] - ℹ️  🔄 更新情感强度: chatroom_38673471170 151 -> 146
2025-09-10 22:50:25,135 - Digital - [emotions_user_sync_service._update_existing_intensities] - ℹ️  🔄 更新情感强度: zhangwankun007 458 -> 465
2025-09-10 22:50:26,041 - Digital - [emotions_user_sync_service._update_existing_intensities] - ℹ️  🔄 更新情感强度: crystal0025 76 -> 82
2025-09-10 22:51:07,025 - Digital - [emotions_user_sync_service._update_existing_intensities] - ℹ️  🔄 更新情感强度: chatroom_45711329496 91 -> 86
2025-09-10 22:51:11,269 - Digital - [emotions_user_sync_service._update_existing_intensities] - ℹ️  🔄 更新情感强度: wxid_j1qg4ureia8i22 144 -> 151
2025-09-10 22:51:12,387 - Digital - [emotions_user_sync_service._update_existing_intensities] - ℹ️  🔄 更新情感强度: wxid_hogr516gzl1j22 431 -> 437
2025-09-10 22:51:29,234 - Digital - [emotions_user_sync_service.full_sync] - ℹ️  ✅ 完整同步完成: 创建0个, 更新9个, 错误0个, 耗时157.77s
2025-09-10 22:51:29,235 - Digital - [emotions_sync_integration.initialize] - ℹ️  ✅ 情感同步系统初始化完成
2025-09-10 22:51:29,236 - Digital - [main.success] - ✅ 💝 情感用户同步服务初始化完成
2025-09-10 22:51:29,236 - Digital - [main.success] - ✅ ✅ AI服务适配器已注册到singleton_manager
2025-09-10 22:51:29,237 - Digital - [main.success] - ✅ ✅ 思维链路已注册到singleton_manager
2025-09-10 22:51:29,237 - Digital - [singleton_manager.register] - ℹ️  ✅ 已注册实例: 'event_bus'
2025-09-10 22:51:29,237 - Digital - [main.success] - ✅ ✅ 事件总线已注册到singleton_manager
2025-09-10 22:51:29,237 - Digital - [main.success] - ✅ ✅ 生命上下文已注册到singleton_manager
2025-09-10 22:51:29,237 - Digital - [main.success] - ✅ 🔧 核心组件注册完成
2025-09-10 22:51:29,237 - Digital - [main.success] - ✅ 📁 开始初始化数据持久化组件...
2025-09-10 22:51:29,243 - Digital - [core.user_preference_manager.success] - ✅ ✅ 已加载用户偏好，共 9 位用户
2025-09-10 22:51:29,243 - Digital - [core.user_preference_manager.success] - ✅ ✅ 用户偏好管理器初始化完成
2025-09-10 22:51:29,244 - Digital - [singleton_manager.register] - ℹ️  ✅ 已注册实例: 'user_preference_manager'
2025-09-10 22:51:29,244 - Digital - [main.success] - ✅ ✅ 用户偏好管理器初始化成功
2025-09-10 22:51:29,245 - Digital - [cognitive.cognition.autonomous_decision_main.__init__] - ℹ️  模块 autonomous_decision_main 已创建
2025-09-10 22:51:29,245 - Digital - [cognitive.cognition.autonomous_decision.__init__] - ℹ️  自主决策模块 autonomous_decision_main 已创建
2025-09-10 22:51:29,245 - Digital - [cognitive.cognition.autonomous_decision.success] - ✅ 自主决策模块 autonomous_decision_main 内部初始化完成
2025-09-10 22:51:29,246 - Digital - [singleton_manager.register] - ℹ️  ✅ 已注册实例: 'autonomous_decision'
2025-09-10 22:51:29,246 - Digital - [main.success] - ✅ ✅ 决策模块初始化成功
2025-09-10 22:51:29,246 - Digital - [main._init_emotion_persistence] - ℹ️  ℹ️ 情感模块未找到，跳过持久化配置
2025-09-10 22:51:29,246 - Digital - [main._init_memory_persistence] - ℹ️  ℹ️ 记忆模块未找到，跳过持久化配置
2025-09-10 22:51:29,247 - Digital - [singleton_manager.register] - ℹ️  ✅ 已注册实例: 'feature_manager'
2025-09-10 22:51:29,253 - Digital - [main.success] - ✅ ✅ 功能特性管理器初始化成功
2025-09-10 22:51:29,254 - Digital - [singleton_manager.register] - ℹ️  ✅ 已注册实例: 'error_recorder'
2025-09-10 22:51:29,255 - Digital - [main.success] - ✅ ✅ 错误记录系统初始化成功
2025-09-10 22:51:29,255 - Digital - [main._init_data_optimization_systems] - ℹ️  🔧 初始化数据优化系统...
2025-09-10 22:51:29,257 - Digital - [singleton_manager.register] - ℹ️  ✅ 已注册实例: 'data_backup_manager'
2025-09-10 22:51:29,257 - Digital - [main.success] - ✅ ✅ 数据备份系统初始化成功
2025-09-10 22:51:29,258 - Digital - [singleton_manager.register] - ℹ️  ✅ 已注册实例: 'data_integrity_checker'
2025-09-10 22:51:29,259 - Digital - [main.success] - ✅ ✅ 数据完整性检查器初始化成功
2025-09-10 22:51:29,259 - Digital - [main.success] - ✅ ✅ 数据优化系统初始化完成
2025-09-10 22:51:29,259 - Digital - [main.success] - ✅ 📁 数据持久化组件初始化完成
2025-09-10 22:51:29,259 - Digital - [main.success] - ✅ 初始化技能系统...
2025-09-10 22:51:29,259 - Digital - [singleton_manager.get_or_create] - ℹ️  通过工厂函数创建实例: 'skill_manager'
2025-09-10 22:51:29,259 - Digital - [skill_manager.success] - ✅ 初始化技能管理器...
2025-09-10 22:51:29,260 - Digital - [skill_manager.__init__] - ℹ️  技能管理器已注册事件处理器
2025-09-10 22:51:29,261 - Digital - [drawing_skill.success] - ✅ 初始化绘画技能...
2025-09-10 22:51:29,311 - Digital - [drawing_skill.success] - ✅ OpenAI配置初始化成功
2025-09-10 22:51:29,311 - Digital - [drawing_skill.success] - ✅ ✅ 从配置文件加载jimeng session_id: 1e6aa5b4800b56a3e345...
2025-09-10 22:51:29,312 - Digital - [drawing_skill.success] - ✅ 初始化绘画技能事件处理器
2025-09-10 22:51:29,312 - Digital - [drawing_skill._register_event_handlers] - ℹ️  已注册绘画请求事件处理器
2025-09-10 22:51:29,312 - Digital - [drawing_skill.success] - ✅ 绘画技能初始化完成: AI绘画技能 v2.0.0
2025-09-10 22:51:29,312 - Digital - [search_skill.success] - ✅ 初始化AI搜索技能...
2025-09-10 22:51:29,312 - Digital - [search_skill._load_config] - ℹ️  已加载搜索技能配置: /root/yanran_digital_life/config/skills/search_skill.json
2025-09-10 22:51:29,357 - Digital - [search_skill.success] - ✅ 搜索技能OpenAI配置初始化成功: https://oneapi.xiongmaodaxia.online/v1
2025-09-10 22:51:29,357 - Digital - [search_skill.__init__] - ℹ️  已加载搜索配置参数
2025-09-10 22:51:29,357 - Digital - [search_skill.success] - ✅ AI搜索技能初始化完成
2025-09-10 22:51:41,309 - Digital - [skill.openbb_financial_skill.__init__] - ℹ️  OpenBB金融技能已创建: OpenBB金融数据技能
2025-09-10 22:51:41,310 - Digital - [utilities.redis_history_manager.success] - ✅ Redis连接初始化成功
2025-09-10 22:51:41,311 - Digital - [skill_manager.success] - ✅ 助理提醒技能模块导入成功
2025-09-10 22:51:41,311 - Digital - [assistant_reminder_skill.success] - ✅ MySQL连接器初始化成功
2025-09-10 22:51:41,311 - Digital - [assistant_reminder_skill.success] - ✅ AI服务适配器初始化成功
2025-09-10 22:51:41,312 - Digital - [assistant_reminder_skill.success] - ✅ 配置加载成功
2025-09-10 22:51:41,316 - Digital - [ai_model_config_manager.success] - ✅ 加载AI模型配置成功，包含 14 个场景配置
2025-09-10 22:51:41,316 - Digital - [ai_model_config_manager.success] - ✅ AI模型配置管理器初始化完成
2025-09-10 22:51:41,316 - Digital - [assistant_reminder_skill.success] - ✅ AI模型配置管理器初始化成功
2025-09-10 22:51:41,534 - Digital - [assistant_reminder_skill.success] - ✅ 提醒任务表创建成功
2025-09-10 22:51:41,535 - Digital - [assistant_reminder_skill.success] - ✅ 提醒任务表创建/检查完成
2025-09-10 22:51:41,535 - Digital - [assistant_reminder_skill.success] - ✅ 助理提醒技能初始化完成: 智能助理提醒服务 (完整功能: MySQL=可用, AI=可用)
2025-09-10 22:51:41,535 - Digital - [skill_manager.success] - ✅ 助理提醒技能实例创建成功: AssistantReminderSkill
2025-09-10 22:51:41,535 - Digital - [skill_manager.success] - ✅ 助理提醒技能注册到技能管理器成功
2025-09-10 22:51:41,535 - Digital - [skill_manager.success] - ✅ 助理提醒技能意图映射添加成功，共8个意图
2025-09-10 22:51:41,535 - Digital - [skill_manager.success] - ✅ ✅ 助理提醒技能注册完成
2025-09-10 22:51:41,535 - Digital - [skill_manager.success] - ✅ ✅ 验证确认：助理提醒技能已在技能列表中
2025-09-10 22:51:41,535 - Digital - [skill_manager.success] - ✅ 技能管理器初始化完成
2025-09-10 22:51:41,536 - Digital - [singleton_manager.register] - ℹ️  ✅ 已注册实例: 'skill_manager'
2025-09-10 22:51:41,537 - Digital - [main.success] - ✅ ✅ 统一技能管理器初始化完成
2025-09-10 22:51:41,537 - Digital - [main._init_skills] - ℹ️  检测到技能插件目录: /root/yanran_digital_life/plugins/skills
2025-09-10 22:51:41,537 - Digital - [main._init_skills] - ℹ️  ✅ 已添加技能插件目录到系统路径
2025-09-10 22:51:41,537 - Digital - [singleton_manager.register] - ℹ️  ✅ 已注册实例: 'chat_skill'
2025-09-10 22:51:41,537 - Digital - [main.success] - ✅ ✅ 同步注册技能: chat_skill
2025-09-10 22:51:41,537 - Digital - [singleton_manager.register] - ℹ️  ✅ 已注册实例: 'search_skill'
2025-09-10 22:51:41,537 - Digital - [main.success] - ✅ ✅ 同步注册技能: search_skill
2025-09-10 22:51:41,538 - Digital - [singleton_manager.register] - ℹ️  ✅ 已注册实例: 'drawing_skill'
2025-09-10 22:51:41,538 - Digital - [main.success] - ✅ ✅ 同步注册技能: drawing_skill
2025-09-10 22:51:41,541 - Digital - [enhanced_activity_skill.__init__] - ℹ️  🎯 增强活动技能初始化完成
2025-09-10 22:51:41,541 - Digital - [singleton_manager.register] - ℹ️  ✅ 已注册实例: 'enhanced_activity_skill'
2025-09-10 22:51:41,542 - Digital - [main.success] - ✅ ✅ 同步注册技能: enhanced_activity_skill
🔥 检测到生产环境，自动设置日志级别为INFO
🔥 检测到生产环境，自动设置日志级别为INFO
🔥 检测到生产环境，自动设置日志级别为INFO
2025-09-10 22:51:41,559 - Digital - [singleton_manager.register] - ℹ️  ✅ 已注册实例: 'financial_data_skill'
2025-09-10 22:51:41,559 - Digital - [main.success] - ✅ ✅ 同步注册技能: financial_data_skill
🔥 检测到生产环境，自动设置日志级别为INFO
2025-09-10 22:51:41,566 - Digital - [plugins.skills.music_skill._load_config] - ℹ️  已从系统配置加载音乐API设置
2025-09-10 22:51:41,566 - Digital - [plugins.skills.music_skill.success] - ✅ 音乐技能初始化完成: 音乐技能 v1.0.0
2025-09-10 22:51:41,567 - Digital - [singleton_manager.register] - ℹ️  ✅ 已注册实例: 'music_skill'
2025-09-10 22:51:41,567 - Digital - [main.success] - ✅ ✅ 同步注册技能: music_skill
2025-09-10 22:51:41,569 - Digital - [greeting_skill.success] - ✅ 问候技能初始化完成
2025-09-10 22:51:41,569 - Digital - [singleton_manager.register] - ℹ️  ✅ 已注册实例: 'greeting_skill'
2025-09-10 22:51:41,569 - Digital - [main.success] - ✅ ✅ 同步注册技能: greeting_skill
2025-09-10 22:51:41,569 - Digital - [main.success] - ✅ 技能系统初始化完成
2025-09-10 22:51:41,569 - Digital - [main.success] - ✅ 初始化认知模块...
2025-09-10 22:51:41,569 - Digital - [main.success] - ✅ 🎯 预先初始化意图识别器...
2025-09-10 22:51:41,569 - Digital - [main.success] - ✅ 🎯 意图识别器已存在，跳过初始化
2025-09-10 22:51:41,569 - Digital - [main.success] - ✅ 初始化 感知引擎...
2025-09-10 22:51:41,570 - Digital - [perception_engine.success] - ✅ 初始化感知引擎...
2025-09-10 22:51:41,570 - Digital - [perception_engine.success] - ✅ 已连接事件总线
2025-09-10 22:51:41,571 - Digital - [cognitive.perception.enhanced_user_perception.__init__] - ℹ️  模块 enhanced_user_perception 已创建
2025-09-10 22:51:41,571 - Digital - [cognitive.perception.enhanced_user_perception.__init__] - ℹ️  增强版用户感知模块已创建
2025-09-10 22:51:41,571 - Digital - [perception_engine.success] - ✅ 用户感知模块加载成功
2025-09-10 22:51:41,571 - Digital - [environmental_perception.success] - ✅ 环境感知模块初始化完成
2025-09-10 22:51:41,571 - Digital - [perception_engine.success] - ✅ 环境感知模块加载成功
2025-09-10 22:51:41,600 - Digital - [perception.physical_world.hardware_monitor.success] - ✅ 硬件监控器初始化完成
2025-09-10 22:51:41,600 - Digital - [perception.physical_world.hardware_monitor.success] - ✅ 硬件监控已启动
2025-09-10 22:51:41,601 - Digital - [perception.physical_world.vital_signs_simulator.success] - ✅ 生命体征模拟器初始化完成
2025-09-10 22:51:41,615 - Digital - [services.script_integration_service.success] - ✅ 剧本集成服务初始化完成
2025-09-10 22:51:41,616 - Digital - [cognitive_modules.perception.activity_perception.success] - ✅ 活动感知模块初始化完成
2025-09-10 22:51:41,616 - Digital - [cognitive_modules.perception.activity_perception.get_instance] - ℹ️  ✅ 活动感知模块使用完整依赖项创建成功
2025-09-10 22:51:41,616 - Digital - [perception_engine.success] - ✅ 活动感知模块加载成功
2025-09-10 22:51:41,616 - Digital - [cognitive.perception.multimodal_perception.__init__] - ℹ️  模块 multimodal_perception 已创建
2025-09-10 22:51:41,617 - Digital - [cognitive.perception.multimodal_perception.__init__] - ℹ️  多模态感知模块 multimodal_perception 已创建
2025-09-10 22:51:41,617 - Digital - [perception_engine.success] - ✅ 多模态感知模块加载成功
2025-09-10 22:51:41,618 - Digital - [LifeOrgan.data_perception_organ.__init__] - ℹ️  器官 data_perception_organ 初始化完成: 智能数据连接和感知优化
2025-09-10 22:51:41,620 - Digital - [cognitive_modules.organs.data_perception_organ.__init__] - ℹ️  📊 林嫣然数据感知器官初始化开始...
2025-09-10 22:51:41,620 - Digital - [cognitive_modules.organs.data_perception_organ._init_data_perception] - ℹ️  📊 MySQL连接器连接成功
2025-09-10 22:51:41,620 - Digital - [real_time_data_collector.success] - ✅ 智能调度器初始化完成
2025-09-10 22:51:41,621 - Digital - [real_time_data_collector.success] - ✅ 趋势感知引擎初始化完成
2025-09-10 22:51:41,621 - Digital - [real_time_data_collector.success] - ✅ 智能分析引擎初始化完成
2025-09-10 22:51:41,621 - Digital - [real_time_data_collector.success] - ✅ 实时数据收集器初始化完成
2025-09-10 22:51:41,621 - Digital - [cognitive_modules.organs.data_perception_organ._init_data_perception] - ℹ️  📊 实时数据收集器连接成功
2025-09-10 22:51:41,621 - Digital - [cognitive_modules.organs.data_perception_organ._init_data_sources] - ℹ️  📊 初始化了 3 个数据源
2025-09-10 22:51:41,622 - Digital - [cognitive_modules.organs.data_perception_organ._start_perception_tasks] - ℹ️  📊 数据感知任务已启动（使用现有事件循环）
2025-09-10 22:51:41,622 - Digital - [cognitive_modules.organs.data_perception_organ.__init__] - ℹ️  📊 林嫣然数据感知器官初始化完成
2025-09-10 22:51:41,623 - Digital - [cognitive_modules.perception.data_perception._initialize_organ] - ℹ️  ✅ 创建新的数据感知器官实例
2025-09-10 22:51:41,623 - Digital - [cognitive_modules.perception.data_perception.success] - ✅ 数据感知模块初始化完成
2025-09-10 22:51:41,623 - Digital - [perception_engine.success] - ✅ 数据感知模块加载成功
2025-09-10 22:51:41,623 - Digital - [perception_engine.success] - ✅ 感知子模块加载完成，共加载 5 个模块
2025-09-10 22:51:41,623 - Digital - [perception_engine._start_perception_monitoring] - ℹ️  感知监控已启动
2025-09-10 22:51:41,623 - Digital - [perception_engine.success] - ✅ 感知引擎初始化完成
2025-09-10 22:51:41,623 - Digital - [singleton_manager.register] - ℹ️  ✅ 已注册实例: 'perception'
2025-09-10 22:51:41,624 - Digital - [main.success] - ✅ ✅ 感知引擎 初始化成功
2025-09-10 22:51:41,649 - Digital - [core.evolution.evolution_engine._ai_analyze_needs] - ⚠️  AI返回内容中未找到有效的JSON格式
2025-09-10 22:51:41,649 - Digital - [core.evolution.evolution_engine._analyze_evolution_needs] - ℹ️  📊 进化需求分析: {'performance_improvement': 0, 'satisfaction_improvement': 0, 'stability_improvement': 0}
2025-09-10 22:51:41,650 - Digital - [main.success] - ✅ 初始化 情感引擎...
2025-09-10 22:51:41,650 - Digital - [emotion_engine.success] - ✅ 初始化情感引擎...
2025-09-10 22:51:41,650 - Digital - [emotion_engine.success] - ✅ 已连接事件总线
2025-09-10 22:51:41,650 - Digital - [emotion_engine.success] - ✅ 情感引擎初始化完成
2025-09-10 22:51:41,651 - Digital - [singleton_manager.register] - ℹ️  ✅ 已注册实例: 'emotion_engine'
2025-09-10 22:51:41,651 - Digital - [main.success] - ✅ ✅ 情感引擎 初始化成功
2025-09-10 22:51:41,662 - Digital - [main.success] - ✅ 初始化 记忆管理器...
2025-09-10 22:51:41,662 - Digital - [memory_manager.success] - ✅ 初始化记忆管理器...
2025-09-10 22:51:41,663 - Digital - [memory_manager.success] - ✅ 已连接事件总线
2025-09-10 22:51:41,663 - Digital - [memory_manager.success] - ✅ 记忆管理器初始化完成
2025-09-10 22:51:41,663 - Digital - [singleton_manager.register] - ℹ️  ✅ 已注册实例: 'memory'
2025-09-10 22:51:41,663 - Digital - [main.success] - ✅ ✅ 记忆管理器 初始化成功
2025-09-10 22:51:41,673 - Digital - [main.success] - ✅ 初始化 行为管理器...
2025-09-10 22:51:41,674 - Digital - [behavior_manager.success] - ✅ 初始化行为管理器...
2025-09-10 22:51:41,675 - Digital - [behavior_manager._load_behavior_templates] - ℹ️  已加载 3 个行为模板
2025-09-10 22:51:41,675 - Digital - [behavior_manager.success] - ✅ 已连接事件总线
2025-09-10 22:51:41,675 - Digital - [behavior_manager.success] - ✅ 行为管理器初始化完成
2025-09-10 22:51:41,675 - Digital - [singleton_manager.register] - ℹ️  ✅ 已注册实例: 'behavior'
2025-09-10 22:51:41,675 - Digital - [main.success] - ✅ ✅ 行为管理器 初始化成功
2025-09-10 22:51:41,686 - Digital - [main.success] - ✅ 初始化 健康系统...
2025-09-10 22:51:41,686 - Digital - [cognitive.physiology.physiology.health_system.__init__] - ℹ️  模块 physiology.health_system 已创建
2025-09-10 22:51:41,686 - Digital - [cognitive.physiology.health_system.__init__] - ℹ️  健康系统模块已创建
2025-09-10 22:51:41,686 - Digital - [singleton_manager.register] - ℹ️  ✅ 已注册实例: 'health_system'
2025-09-10 22:51:41,687 - Digital - [cognitive.physiology.physiology.health_system.success] - ✅ 模块 physiology.health_system 初始化成功
2025-09-10 22:51:41,687 - Digital - [main._init_cognitive_modules] - ℹ️  ✅ 健康系统 初始化成功
2025-09-10 22:51:41,687 - Digital - [cognitive.physiology.physiology.health_system.activate] - ℹ️  模块 physiology.health_system 已激活
2025-09-10 22:51:41,687 - Digital - [main._init_cognitive_modules] - ℹ️  ✅ 健康系统 已激活
2025-09-10 22:51:41,687 - Digital - [main.success] - ✅ ✅ 健康系统 初始化成功
2025-09-10 22:51:41,697 - Digital - [main.success] - ✅ 初始化 趋势感知模块...
2025-09-10 22:51:41,698 - Digital - [singleton_manager.register] - ℹ️  ✅ 已注册实例: 'trend_perception'
2025-09-10 22:51:41,698 - Digital - [main.success] - ✅ ✅ 趋势感知模块 初始化成功
2025-09-10 22:51:41,708 - Digital - [main.success] - ✅ 初始化 趋势智能模块...
2025-09-10 22:51:41,709 - Digital - [singleton_manager.register] - ℹ️  ✅ 已注册实例: 'trend_intelligence'
2025-09-10 22:51:41,709 - Digital - [main.success] - ✅ ✅ 趋势智能模块 初始化成功
2025-09-10 22:51:41,719 - Digital - [main.success] - ✅ 初始化 智能调度器...
2025-09-10 22:51:41,719 - Digital - [singleton_manager.register] - ℹ️  ✅ 已注册实例: 'intelligent_scheduler'
2025-09-10 22:51:41,720 - Digital - [main.success] - ✅ ✅ 智能调度器 初始化成功
2025-09-10 22:51:41,730 - Digital - [main.success] - ✅ 初始化 嫣然AI决策引擎...
2025-09-10 22:51:41,730 - Digital - [yanran_decision_engine._ensure_ai_adapter] - ℹ️  AI服务适配器获取成功
2025-09-10 22:51:41,731 - Digital - [yanran_decision_engine._load_decision_config] - ℹ️  加载决策配置成功: config/ai_decision/decision_engine.json
2025-09-10 22:51:41,731 - Digital - [yanran_decision_engine.__init__] - ℹ️  林嫣然AI决策引擎初始化完成
2025-09-10 22:51:41,732 - Digital - [singleton_manager.register] - ℹ️  ✅ 已注册实例: 'yanran_ai_decision_engine'
2025-09-10 22:51:41,732 - Digital - [main.success] - ✅ ✅ 嫣然AI决策引擎 初始化成功
2025-09-10 22:51:41,742 - Digital - [main.success] - ✅ 初始化 器官神经网络...
2025-09-10 22:51:41,742 - Digital - [OrganNeuralNetwork.__init__] - ℹ️  器官神经网络初始化完成
2025-09-10 22:51:41,743 - Digital - [singleton_manager.register] - ℹ️  ✅ 已注册实例: 'organ_neural_network'
2025-09-10 22:51:41,743 - Digital - [main.success] - ✅ ✅ 器官神经网络 初始化成功
2025-09-10 22:51:41,753 - Digital - [main.success] - ✅ 初始化 世界感知器官...
2025-09-10 22:51:41,754 - Digital - [LifeOrgan.世界感知器官.__init__] - ℹ️  器官 世界感知器官 初始化完成: 感知外部世界变化，判断事件重要性
2025-09-10 22:51:41,755 - Digital - [utilities.database_manager.data_status] - 📊 已加载数据库配置: /root/yanran_digital_life/config/database.json
2025-09-10 22:51:41,755 - Digital - [utilities.database_manager.success] - ✅ MySQL连接器管理器初始化成功
2025-09-10 22:51:41,766 - Digital - [chroma_connector._init_connection] - ℹ️  强制使用远程ChromaDB模式
2025-09-10 22:51:41,767 - Digital - [chroma_connector.success] - ✅ 连接主ChromaDB服务器: http://**************:5001
2025-09-10 22:51:42,072 - Digital - [chroma_connector.success] - ✅ 使用HTTP API连接ChromaDB成功
2025-09-10 22:51:42,072 - Digital - [chroma_connector.success] - ✅ 成功创建ChromaDB HTTP客户端
2025-09-10 22:51:42,073 - Digital - [chroma_connector.success] - ✅ 成功连接到远程ChromaDB服务器
2025-09-10 22:51:42,073 - Digital - [utilities.database_manager.success] - ✅ ChromaDB连接器初始化成功
2025-09-10 22:51:42,073 - Digital - [utilities.database_manager.success] - ✅ 数据库管理器初始化完成
2025-09-10 22:51:42,073 - Digital - [WorldPerceptionDataManager.__init__] - ℹ️  ✅ 统一数据库管理器连接成功
2025-09-10 22:51:42,297 - Digital - [WorldPerceptionDataManager._ensure_tables_exist] - ℹ️  ✅ 世界感知数据表检查通过
2025-09-10 22:51:42,297 - Digital - [WorldPerceptionDataManager.__init__] - ℹ️  🧠 世界感知数据管理器初始化完成
2025-09-10 22:51:42,297 - Digital - [LifeOrgan.世界感知器官.__init__] - ℹ️  🧠 数据持久化管理器初始化成功
2025-09-10 22:51:42,302 - Digital - [utilities.redis_cache_adapter.__init__] - ℹ️  ✅ Redis缓存适配器连接成功: localhost:6379/0
2025-09-10 22:51:42,302 - Digital - [LifeOrgan.世界感知器官.__init__] - ℹ️  🌍 世界感知器官实例 #1 初始化完成
2025-09-10 22:51:42,302 - Digital - [singleton_manager.register] - ℹ️  ✅ 已注册实例: 'world_perception_organ'
2025-09-10 22:51:42,302 - Digital - [main.success] - ✅ ✅ 世界感知器官 初始化成功
2025-09-10 22:51:42,313 - Digital - [main.success] - ✅ 初始化 创意表达器官...
2025-09-10 22:51:42,314 - Digital - [singleton_manager.get_or_create] - ℹ️  通过工厂函数创建实例: 'creative_expression_organ'
2025-09-10 22:51:42,314 - Digital - [LifeOrgan.creative_expression_organ.__init__] - ℹ️  器官 creative_expression_organ 初始化完成: 艺术创作和情感表达
2025-09-10 22:51:42,315 - Digital - [cognitive_modules.organs.creative_expression_organ.__init__] - ℹ️  🎨 林嫣然创作表达器官初始化开始...
2025-09-10 22:51:42,315 - Digital - [cognitive_modules.organs.creative_expression_organ._init_creative_tools] - ℹ️  🎨 绘画技能连接成功
🔥 检测到生产环境，自动设置日志级别为INFO
✅ 使用配置文件中的cookie
2025-09-10 22:51:42,409 - Digital - [cognitive_modules.organs.creative_expression_organ._init_kling_api] - ℹ️  🎨 可灵模块直接导入成功
2025-09-10 22:51:42,414 - Digital - [fake_useragent.getBrowser] - ⚠️  Error occurred during getting browser(s): random, but was suppressed with fallback.
Call daily login success with True:
{'status': 200, 'result': 1, 'message': '', 'data': {'order': None, 'status': 
'GRANTED', 'activityType': 'normal_bonus_monthly', 'rewardDetail': None, 
'firstApply': False, 'nextApplyTime': 1758867829609, 'lastAppliedActivity': 
'normal_bonus_monthly', 'hint': {'title': '您已领取过本月惊喜体验包', 
'subtitle': '下次可领取时间：2025-09-26 14:23:49', 'imgUrl': ''}, 
'applyCheckPack': None}, 'timestamp': 1757515902496}

2025-09-10 22:51:42,507 - Digital - [cognitive_modules.organs.creative_expression_organ._init_kling_api] - ℹ️  🎨 可灵AI接口初始化成功
2025-09-10 22:51:42,507 - Digital - [cognitive_modules.organs.creative_expression_organ.__init__] - ℹ️  🎨 林嫣然创作表达器官初始化完成
2025-09-10 22:51:42,508 - Digital - [singleton_manager.register] - ℹ️  ✅ 已注册实例: 'creative_expression_organ'
2025-09-10 22:51:42,509 - Digital - [main.success] - ✅ ✅ 创意表达器官 初始化成功
2025-09-10 22:51:42,520 - Digital - [main.success] - ✅ 初始化 安全保护器官...
2025-09-10 22:51:42,520 - Digital - [singleton_manager.get_or_create] - ℹ️  通过工厂函数创建实例: 'safety_protection_organ'
2025-09-10 22:51:42,520 - Digital - [LifeOrgan.safety_protection_organ.__init__] - ℹ️  器官 safety_protection_organ 初始化完成: 智能安全防护和边界管理
2025-09-10 22:51:42,521 - Digital - [cognitive_modules.organs.safety_protection_organ.__init__] - ℹ️  🛡️ 林嫣然安全防护器官初始化开始...
2025-09-10 22:51:42,531 - Digital - [security.response_filter.success] - ✅ 响应过滤配置加载完成
2025-09-10 22:51:42,531 - Digital - [adapters.unified_ai_adapter.success] - ✅ 初始化统一AI适配器
2025-09-10 22:51:42,531 - Digital - [adapters.unified_ai_adapter._initialize] - ℹ️  加载AI服务配置: config/ai_services.json
2025-09-10 22:51:42,532 - Digital - [adapters.unified_ai_adapter._initialize] - ℹ️  设置默认AI服务: openai
2025-09-10 22:51:42,532 - Digital - [adapters.unified_ai_adapter._initialize] - ℹ️  已加载AI服务: deepseek, openai, zhipu, qianwen, compatible_service
2025-09-10 22:51:42,532 - Digital - [adapters.unified_ai_adapter.success] - ✅ 统一AI适配器已初始化
2025-09-10 22:51:42,532 - Digital - [security.response_filter.success] - ✅ AI适配器初始化完成
2025-09-10 22:51:42,532 - Digital - [security.response_filter._load_security_patterns] - ℹ️  加载了 6 个安全模式
2025-09-10 22:51:42,630 - Digital - [security.plugins.intimacy_analyzer.success] - ✅ 亲密度规则配置加载完成
2025-09-10 22:51:43,094 - Digital - [security.plugins.intimacy_analyzer.success] - ✅ 亲密度分析器插件初始化成功
2025-09-10 22:51:43,095 - Digital - [singleton_manager.register] - ℹ️  ✅ 已注册实例: 'intimacy_analyzer'
2025-09-10 22:51:43,096 - Digital - [security.response_filter.success] - ✅ 亲密度分析器插件加载成功
2025-09-10 22:51:43,096 - Digital - [security.response_filter.success] - ✅ 响应过滤器初始化完成
2025-09-10 22:51:43,096 - Digital - [cognitive_modules.organs.safety_protection_organ._init_safety_components] - ℹ️  🛡️ 基础安全过滤器连接成功
2025-09-10 22:51:43,096 - Digital - [cognitive_modules.organs.safety_protection_organ._init_safety_components] - ℹ️  🛡️ 亲密度分析器连接成功
2025-09-10 22:51:43,096 - Digital - [cognitive_modules.organs.safety_protection_organ.__init__] - ℹ️  🛡️ 林嫣然安全防护器官初始化完成
2025-09-10 22:51:43,096 - Digital - [singleton_manager.register] - ℹ️  ✅ 已注册实例: 'safety_protection_organ'
2025-09-10 22:51:43,096 - Digital - [main.success] - ✅ ✅ 安全保护器官 初始化成功
2025-09-10 22:51:43,107 - Digital - [main.success] - ✅ 初始化 技能协调器官...
2025-09-10 22:51:43,108 - Digital - [singleton_manager.get_or_create] - ℹ️  通过工厂函数创建实例: 'skill_coordination_organ'
2025-09-10 22:51:43,108 - Digital - [LifeOrgan.skill_coordination_organ.__init__] - ℹ️  器官 skill_coordination_organ 初始化完成: 智能技能协调和优化
2025-09-10 22:51:43,109 - Digital - [cognitive_modules.organs.skill_coordination_organ.__init__] - ℹ️  🧠 林嫣然技能协调器官初始化开始...
2025-09-10 22:51:43,110 - Digital - [cognitive_modules.organs.skill_coordination_organ._discover_available_skills] - ℹ️  🧠 发现 9 个可用技能
2025-09-10 22:51:43,110 - Digital - [cognitive_modules.organs.skill_coordination_organ._init_skill_coordination] - ℹ️  🧠 技能管理器连接成功
2025-09-10 22:51:43,110 - Digital - [cognitive_modules.organs.skill_coordination_organ._create_default_combinations] - ℹ️  🧠 创建了默认技能组合方案
2025-09-10 22:51:43,110 - Digital - [cognitive_modules.organs.skill_coordination_organ.__init__] - ℹ️  🧠 林嫣然技能协调器官初始化完成
2025-09-10 22:51:43,110 - Digital - [singleton_manager.register] - ℹ️  ✅ 已注册实例: 'skill_coordination_organ'
2025-09-10 22:51:43,111 - Digital - [main.success] - ✅ ✅ 技能协调器官 初始化成功
2025-09-10 22:51:43,121 - Digital - [main.success] - ✅ 初始化 财富管理器官...
2025-09-10 22:51:43,121 - Digital - [singleton_manager.get_or_create] - ℹ️  通过工厂函数创建实例: 'wealth_management_organ'
2025-09-10 22:51:43,122 - Digital - [LifeOrgan.wealth_management_organ.__init__] - ℹ️  器官 wealth_management_organ 初始化完成: 智能投资理财和财富规划
2025-09-10 22:51:43,123 - Digital - [cognitive_modules.organs.wealth_management_organ.__init__] - ℹ️  💰 林嫣然财富管理器官初始化开始...
2025-09-10 22:51:43,123 - Digital - [cognitive_modules.organs.wealth_management_organ._init_wealth_management] - ℹ️  💰 金融数据技能连接成功
2025-09-10 22:51:43,124 - Digital - [cognitive_modules.organs.wealth_management_organ._init_risk_profile] - ℹ️  💰 风险偏好配置完成: moderate
2025-09-10 22:51:43,124 - Digital - [cognitive_modules.organs.wealth_management_organ._load_portfolio_data] - ℹ️  💰 投资组合数据文件不存在，初始化为空投资组合
2025-09-10 22:51:43,124 - Digital - [cognitive_modules.organs.wealth_management_organ._load_portfolio_data] - ℹ️  💰 请通过API或数据导入功能添加真实投资数据
2025-09-10 22:51:43,124 - Digital - [cognitive_modules.organs.wealth_management_organ.__init__] - ℹ️  💰 林嫣然财富管理器官初始化完成
2025-09-10 22:51:43,125 - Digital - [singleton_manager.register] - ℹ️  ✅ 已注册实例: 'wealth_management_organ'
2025-09-10 22:51:43,125 - Digital - [main.success] - ✅ ✅ 财富管理器官 初始化成功
2025-09-10 22:51:43,136 - Digital - [main.success] - ✅ 初始化 数据感知器官...
2025-09-10 22:51:43,136 - Digital - [singleton_manager.get_or_create] - ℹ️  通过工厂函数创建实例: 'data_perception_organ'
2025-09-10 22:51:43,137 - Digital - [LifeOrgan.data_perception_organ.__init__] - ℹ️  器官 data_perception_organ 初始化完成: 智能数据连接和感知优化
2025-09-10 22:51:43,137 - Digital - [cognitive_modules.organs.data_perception_organ.__init__] - ℹ️  📊 林嫣然数据感知器官初始化开始...
2025-09-10 22:51:43,137 - Digital - [cognitive_modules.organs.data_perception_organ._init_data_perception] - ℹ️  📊 MySQL连接器连接成功
2025-09-10 22:51:43,137 - Digital - [cognitive_modules.organs.data_perception_organ._init_data_perception] - ℹ️  📊 实时数据收集器连接成功
2025-09-10 22:51:43,137 - Digital - [cognitive_modules.organs.data_perception_organ._init_data_sources] - ℹ️  📊 初始化了 3 个数据源
2025-09-10 22:51:43,137 - Digital - [cognitive_modules.organs.data_perception_organ._start_perception_tasks] - ℹ️  📊 数据感知任务已启动（使用现有事件循环）
2025-09-10 22:51:43,137 - Digital - [cognitive_modules.organs.data_perception_organ.__init__] - ℹ️  📊 林嫣然数据感知器官初始化完成
2025-09-10 22:51:43,138 - Digital - [singleton_manager.register] - ℹ️  ✅ 已注册实例: 'data_perception_organ'
2025-09-10 22:51:43,139 - Digital - [main.success] - ✅ ✅ 数据感知器官 初始化成功
2025-09-10 22:51:43,149 - Digital - [main.success] - ✅ 初始化 主动表达器官（增强版本的基础器官）...
2025-09-10 22:51:43,150 - Digital - [singleton_manager.get_or_create] - ℹ️  通过工厂函数创建实例: 'proactive_expression_organ'
2025-09-10 22:51:43,150 - Digital - [LifeOrgan.proactive_expression_organ.__init__] - ℹ️  器官 proactive_expression_organ 初始化完成: 主动表达和情感分享
2025-09-10 22:51:43,151 - Digital - [ProactiveExpressionOrgan.__init__] - ℹ️  💬 林嫣然主动表达器官初始化开始...
2025-09-10 22:51:43,173 - Digital - [singleton_manager.register] - ℹ️  ✅ 已注册实例: 'proactive_expression_service'
2025-09-10 22:51:43,173 - Digital - [ProactiveExpressionOrgan._init_expression_system] - ℹ️  💬 主动表达服务已创建并注册
2025-09-10 22:51:43,173 - Digital - [ProactiveExpressionOrgan._init_expression_system] - ℹ️  💬 主动表达服务连接成功
2025-09-10 22:51:43,173 - Digital - [ProactiveExpressionOrgan._init_expression_system] - ℹ️  💬 使用WeChat统一推送服务，不启动WebSocket服务器
2025-09-10 22:51:43,184 - Digital - [PerceptionFeedbackProcessor.__init__] - ℹ️  感知反馈处理器初始化完成
2025-09-10 22:51:43,185 - Digital - [singleton_manager.register] - ℹ️  ✅ 已注册实例: 'perception_feedback_processor'
2025-09-10 22:51:43,185 - Digital - [ProactiveExpressionOrgan._init_expression_system] - ℹ️  💬 感知反馈处理器连接成功
2025-09-10 22:51:43,185 - Digital - [ProactiveExpressionOrgan._register_perception_callback] - ℹ️  💬 感知结果回调已注册
2025-09-10 22:51:43,197 - Digital - [unified_user_manager.__init__] - ℹ️  统一用户身份管理器初始化完成
2025-09-10 22:51:43,198 - Digital - [singleton_manager.register] - ℹ️  ✅ 已注册实例: 'unified_user_manager'
🔥 检测到生产环境，自动设置日志级别为INFO
2025-09-10 22:51:43,207 - Digital - [middleware.caching.success] - ✅ Redis缓存后端初始化完成，连接到: localhost:6379/0
2025-09-10 22:51:43,207 - Digital - [ContactsManager._init_redis] - ℹ️  联系人管理器Redis缓存初始化成功
🔥 检测到生产环境，自动设置日志级别为INFO
2025-09-10 22:51:43,213 - Digital - [adapters.database_sync_manager.success] - ✅ 数据库同步管理器MySQL连接初始化成功
2025-09-10 22:51:43,214 - Digital - [adapters.database_sync_manager.success] - ✅ 数据库同步服务已启动
2025-09-10 22:51:43,214 - Digital - [ContactsManager._init_database_sync] - ℹ️  联系人管理器数据库同步初始化成功
2025-09-10 22:51:43,218 - Digital - [ContactsManager._init_file_watcher] - ℹ️  联系人文件监听器初始化成功
2025-09-10 22:51:43,219 - Digital - [ContactsManager._load_contacts] - ℹ️  加载联系人数据: 22个用户
2025-09-10 22:51:43,220 - Digital - [singleton_manager.register] - ℹ️  ✅ 已注册实例: 'contacts_manager'
2025-09-10 22:51:43,221 - Digital - [unified_user_manager._integrate_existing_managers] - ℹ️  已集成现有管理器
2025-09-10 22:51:43,221 - Digital - [unified_user_manager.start] - ℹ️  统一用户身份管理器启动成功
2025-09-10 22:51:43,221 - Digital - [ProactiveExpressionOrgan._init_expression_system] - ℹ️  💬 统一好友管理器连接成功
2025-09-10 22:51:43,221 - Digital - [cognitive_modules.emotion.autonomous_emotion_manager.success] - ✅ 成功加载统一情感等级标准
2025-09-10 22:51:43,221 - Digital - [cognitive_modules.emotion.autonomous_emotion_manager.success] - ✅ 自主情感管理器初始化完成
2025-09-10 22:51:43,221 - Digital - [cognitive_modules.emotion.emotion_system_integration.success] - ✅ 情感系统集成器初始化完成
2025-09-10 22:51:43,221 - Digital - [ProactiveExpressionOrgan._init_expression_system] - ℹ️  💬 情感系统集成连接成功
2025-09-10 22:51:43,226 - Digital - [workday_cache_manager.__init__] - ℹ️  🗓️ 工作日缓存管理器初始化完成
2025-09-10 22:51:43,226 - Digital - [workday_cache_manager.is_workday_cached] - ℹ️  🗓️ 🔍 缓存未命中，查询数据库: 2025-09-11
2025-09-10 22:51:43,226 - Digital - [workday_service.success] - ✅ 统一工作日服务初始化完成
2025-09-10 22:51:43,227 - Digital - [workday_service.start_auto_refresh] - ℹ️  工作日服务自动刷新线程已启动
2025-09-10 22:51:43,228 - Digital - [workday_cache_manager.success] - ✅ 🗓️ ✅ 工作日信息已缓存: 2025-09-11 -> True
2025-09-10 22:51:43,228 - Digital - [ProactiveExpressionOrgan._init_expression_system] - ℹ️  💬 🗓️ 工作日缓存预加载完成
2025-09-10 22:51:43,228 - Digital - [ProactiveExpressionOrgan._init_expression_system] - ℹ️  💬 ⏳ 表达监控任务将在器官激活时启动
2025-09-10 22:51:43,228 - Digital - [ProactiveExpressionOrgan.__init__] - ℹ️  💬 林嫣然主动表达器官初始化完成
2025-09-10 22:51:43,228 - Digital - [ProactiveExpressionOrgan.__init__] - ℹ️  💬 🧠 当前触发模式: PARALLEL
2025-09-10 22:51:43,228 - Digital - [ProactiveExpressionOrgan.__init__] - ℹ️  💬 🧠 并行配置: 最大并发=2, 最小间隔=5分钟
2025-09-10 22:51:43,229 - Digital - [singleton_manager.register] - ℹ️  ✅ 已注册实例: 'proactive_expression_organ'
2025-09-10 22:51:43,229 - Digital - [ProactiveExpressionOrgan.activate] - ℹ️  💬 🚀 主动表达器官正在激活...
2025-09-10 22:51:43,229 - Digital - [ProactiveExpressionOrgan._start_expression_monitoring] - ℹ️  💬 🚀 启动统一表达监控系统...
2025-09-10 22:51:43,230 - Digital - [ProactiveExpressionOrgan.timer_monitoring] - ℹ️  💬 🚀 定时器监控线程启动，等待系统完全启动...
2025-09-10 22:51:43,230 - Digital - [ProactiveExpressionOrgan.success] - ✅ 💬 ✅ 基于定时器的表达监控已启动（主要模式）
2025-09-10 22:51:43,230 - Digital - [ProactiveExpressionOrgan.timer_monitoring] - ℹ️  💬 ⏰ 等待系统启动完成，预计等待 60 秒...
2025-09-10 22:51:43,230 - Digital - [ProactiveExpressionOrgan.success] - ✅ 💬 ✅ 表达监控系统启动成功
2025-09-10 22:51:43,230 - Digital - [ProactiveExpressionOrgan.success] - ✅ 💬 ✅ 主动表达器官激活成功，监控系统已启动
2025-09-10 22:51:43,230 - Digital - [main._init_cognitive_modules] - ℹ️  ✅ 主动表达器官（增强版本的基础器官） 已激活
2025-09-10 22:51:43,230 - Digital - [main.success] - ✅ ✅ 主动表达器官（增强版本的基础器官） 初始化成功
2025-09-10 22:51:43,242 - Digital - [main.success] - ✅ 初始化 增强版主动表达器官...
2025-09-10 22:51:43,242 - Digital - [cognitive_modules.organs.enhanced_proactive_expression_organ.success] - ✅ 🚀 初始化增强版主动表达器官 2.0...
2025-09-10 22:51:43,252 - Digital - [IntelligenceIntegrationManager._initialize_base_components] - ℹ️  🔧 基础组件初始化完成
2025-09-10 22:51:43,252 - Digital - [IntelligenceIntegrationManager._initialize_neural_systems] - ℹ️  🧠 神经网络系统初始化完成: 3个
2025-09-10 22:51:43,252 - Digital - [IntelligenceIntegrationManager._initialize_learning_modules] - ℹ️  📚 学习模块初始化完成: 3个
2025-09-10 22:51:43,253 - Digital - [resilience_system.register_service] - ℹ️  已注册服务: intelligence_integration_manager (类型: cognitive)
2025-09-10 22:51:43,253 - Digital - [IntelligenceIntegrationManager.success] - ✅ ✅ 智能整合管理器已注册到韧性系统
2025-09-10 22:51:43,253 - Digital - [IntelligenceIntegrationManager.success] - ✅ ✅ 智能整合管理器初始化完成
2025-09-10 22:51:43,253 - Digital - [cognitive_modules.organs.enhanced_proactive_expression_organ.success] - ✅ ✅ 智能整合管理器深度连接成功
2025-09-10 22:51:43,253 - Digital - [cognitive_modules.organs.enhanced_proactive_expression_organ._initialize_intelligence_manager] - ℹ️  🧠 当前全局智能水平: 0.000
2025-09-10 22:51:43,253 - Digital - [cognitive_modules.organs.enhanced_proactive_expression_organ._initialize_intelligence_manager] - ℹ️  💓 当前生命活力: 0.000
2025-09-10 22:51:43,253 - Digital - [cognitive_modules.organs.enhanced_proactive_expression_organ.success] - ✅ ✅ 基础主动表达器官连接成功
2025-09-10 22:51:43,253 - Digital - [cognitive_modules.organs.enhanced_proactive_expression_organ._start_performance_monitoring] - ℹ️  📊 性能监控已启动
2025-09-10 22:51:43,254 - Digital - [cognitive_modules.organs.enhanced_proactive_expression_organ._start_continuous_optimization] - ℹ️  🔧 持续优化已启动
2025-09-10 22:51:43,254 - Digital - [cognitive_modules.organs.enhanced_proactive_expression_organ.success] - ✅ ✅ 增强版主动表达器官 2.0 初始化完成
2025-09-10 22:51:43,254 - Digital - [main._init_cognitive_modules] - ℹ️  🔍 验证enhanced_proactive_expression_organ实例: 类型=<class 'cognitive_modules.organs.enhanced_proactive_expression_organ.EnhancedProactiveExpressionOrgan'>
2025-09-10 22:51:43,254 - Digital - [main._init_cognitive_modules] - ℹ️  ✅ enhanced_proactive_expression_organ有trigger_expression方法
2025-09-10 22:51:43,254 - Digital - [main._init_cognitive_modules] - ℹ️  ✅ enhanced_proactive_expression_organ有enhanced_proactive_expression方法
2025-09-10 22:51:43,254 - Digital - [main.success] - ✅ ✅ enhanced_proactive_expression_organ找到兼容方法: ['trigger_expression', 'enhanced_proactive_expression']
2025-09-10 22:51:43,254 - Digital - [singleton_manager.register] - ℹ️  ✅ 已注册实例: 'enhanced_proactive_expression_organ'
2025-09-10 22:51:43,254 - Digital - [main.success] - ✅ ✅ 增强版主动表达器官 初始化成功
2025-09-10 22:51:43,267 - Digital - [main.success] - ✅ 初始化 关系协调器官...
2025-09-10 22:51:43,267 - Digital - [singleton_manager.get_or_create] - ℹ️  通过工厂函数创建实例: 'relationship_coordination_organ'
2025-09-10 22:51:43,267 - Digital - [LifeOrgan.relationship_coordination_organ.__init__] - ℹ️  器官 relationship_coordination_organ 初始化完成: 跨模块协同和关系协调
2025-09-10 22:51:43,268 - Digital - [cognitive_modules.organs.relationship_coordination_organ.__init__] - ℹ️  🤝 林嫣然关系协调器官初始化开始...
2025-09-10 22:51:43,268 - Digital - [cognitive_modules.organs.relationship_coordination_organ._init_coordination_system] - ℹ️  🤝 器官神经网络连接成功
2025-09-10 22:51:43,269 - Digital - [cognitive_modules.organs.relationship_coordination_organ._discover_organ_relationships] - ℹ️  🤝 发现了 56 个器官关系
2025-09-10 22:51:43,269 - Digital - [cognitive_modules.organs.relationship_coordination_organ._start_coordination_monitoring] - ℹ️  🤝 协调监控任务已启动
2025-09-10 22:51:43,269 - Digital - [cognitive_modules.organs.relationship_coordination_organ.__init__] - ℹ️  🤝 林嫣然关系协调器官初始化完成
2025-09-10 22:51:43,270 - Digital - [singleton_manager.register] - ℹ️  ✅ 已注册实例: 'relationship_coordination_organ'
2025-09-10 22:51:43,271 - Digital - [main.success] - ✅ ✅ 关系协调器官 初始化成功
2025-09-10 22:51:43,281 - Digital - [main.success] - ✅ 初始化 个性化服务器官...
2025-09-10 22:51:43,281 - Digital - [singleton_manager.get_or_create] - ℹ️  通过工厂函数创建实例: 'personalized_service_organ'
2025-09-10 22:51:43,282 - Digital - [LifeOrgan.personalized_service_organ.__init__] - ℹ️  器官 personalized_service_organ 初始化完成: 个性化服务编排和用户体验优化
2025-09-10 22:51:43,284 - Digital - [cognitive_modules.organs.personalized_service_organ.__init__] - ℹ️  🎯 林嫣然个性化服务器官初始化开始...
2025-09-10 22:51:43,284 - Digital - [cognitive_modules.organs.personalized_service_organ._connect_to_organs] - ℹ️  🎯 已连接到 world_perception
2025-09-10 22:51:43,284 - Digital - [cognitive_modules.organs.personalized_service_organ._connect_to_organs] - ℹ️  🎯 已连接到 creative_expression
2025-09-10 22:51:43,284 - Digital - [cognitive_modules.organs.personalized_service_organ._connect_to_organs] - ℹ️  🎯 已连接到 safety_protection
2025-09-10 22:51:43,284 - Digital - [cognitive_modules.organs.personalized_service_organ._connect_to_organs] - ℹ️  🎯 已连接到 skill_coordination
2025-09-10 22:51:43,284 - Digital - [cognitive_modules.organs.personalized_service_organ._connect_to_organs] - ℹ️  🎯 已连接到 wealth_management
2025-09-10 22:51:43,284 - Digital - [cognitive_modules.organs.personalized_service_organ._connect_to_organs] - ℹ️  🎯 已连接到 data_perception
2025-09-10 22:51:43,284 - Digital - [cognitive_modules.organs.personalized_service_organ._connect_to_organs] - ℹ️  🎯 已连接到 proactive_expression
2025-09-10 22:51:43,284 - Digital - [cognitive_modules.organs.personalized_service_organ._connect_to_organs] - ℹ️  🎯 已连接到 relationship_coordination
2025-09-10 22:51:43,284 - Digital - [cognitive_modules.organs.personalized_service_organ._init_personalization_models] - ℹ️  🎯 个性化模型初始化完成
2025-09-10 22:51:43,285 - Digital - [cognitive_modules.organs.personalized_service_organ._start_service_monitoring] - ℹ️  🎯 服务监控任务已启动
2025-09-10 22:51:43,285 - Digital - [cognitive_modules.organs.personalized_service_organ.__init__] - ℹ️  🎯 林嫣然个性化服务器官初始化完成
2025-09-10 22:51:43,285 - Digital - [singleton_manager.register] - ℹ️  ✅ 已注册实例: 'personalized_service_organ'
2025-09-10 22:51:43,286 - Digital - [main.success] - ✅ ✅ 个性化服务器官 初始化成功
2025-09-10 22:51:43,296 - Digital - [main.success] - ✅ 初始化 记忆整合模块...
2025-09-10 22:51:43,297 - Digital - [cognitive.记忆整合模块.default.__init__] - ℹ️  模块 default 已创建
2025-09-10 22:51:43,297 - Digital - [singleton_manager.register] - ℹ️  ✅ 已注册实例: 'memory_integration'
2025-09-10 22:51:43,298 - Digital - [memory_integration.success] - ✅ 正在初始化记忆整合模块 (ID: default)...
2025-09-10 22:51:43,298 - Digital - [cognitive.记忆整合模块.default.success] - ✅ 模块 default 初始化成功
2025-09-10 22:51:43,298 - Digital - [utilities.storage_manager.success] - ✅ 从统一配置管理器加载数据库配置，优先级: remote
2025-09-10 22:51:43,298 - Digital - [utilities.storage_manager.data_status] - 📊 本地数据库路径: /root/yanran_digital_life/data/digital_life.db
2025-09-10 22:51:43,299 - Digital - [utilities.storage_manager.success] - ✅ 数据表创建或验证成功
2025-09-10 22:51:43,299 - Digital - [utilities.storage_manager.success] - ✅ 已连接到本地SQLite数据库
2025-09-10 22:51:43,563 - Digital - [adapters.database_sync_manager._analyze_sync_data] - ℹ️  分析结果: 新用户 0个, 需更新 0个
2025-09-10 22:51:43,651 - Digital - [utilities.storage_manager.success] - ✅ 已成功连接到MySQL数据库: **************:3306/linyanran
2025-09-10 22:51:43,800 - Digital - [utilities.storage_manager.success] - ✅ 已连接到远程MySQL数据库: **************:3306
2025-09-10 22:51:43,800 - Digital - [utilities.storage_manager.data_status] - 📊 向量数据库未启用
2025-09-10 22:51:43,800 - Digital - [utilities.storage_manager.success] - ✅ 存储管理器已初始化
2025-09-10 22:51:43,800 - Digital - [utilities.storage_manager.load_json] - ℹ️  成功加载JSON文件: /root/yanran_digital_life/memories/default/memory_associations.json
2025-09-10 22:51:43,800 - Digital - [memory_integration._load_associations] - ℹ️  已从存储加载记忆关联
2025-09-10 22:51:43,801 - Digital - [cognitive.情境记忆模块.default.__init__] - ℹ️  模块 default 已创建
2025-09-10 22:51:43,802 - Digital - [cognitive.程序记忆模块.default.__init__] - ℹ️  模块 default 已创建
2025-09-10 22:51:43,802 - Digital - [utilities.cache_manager.success] - ✅ 🔥 初始化缓存管理器...
2025-09-10 22:51:43,803 - Digital - [utilities.cache_manager._start_cleanup_thread] - ℹ️  🧹 缓存清理线程已启动
2025-09-10 22:51:43,803 - Digital - [utilities.cache_manager.success] - ✅ ✅ 缓存管理器初始化完成
2025-09-10 22:51:43,804 - Digital - [cognitive_modules.memory.semantic_memory.data_status] - 📊 按照老板要求，语义记忆只使用文件存储，不使用向量数据库
2025-09-10 22:51:43,804 - Digital - [cognitive_modules.memory.semantic_memory._initialize_vector_db] - ℹ️  已创建分类目录: 人物
2025-09-10 22:51:43,804 - Digital - [cognitive_modules.memory.semantic_memory._initialize_vector_db] - ℹ️  已创建分类目录: 地点
2025-09-10 22:51:43,804 - Digital - [cognitive_modules.memory.semantic_memory._initialize_vector_db] - ℹ️  已创建分类目录: 概念
2025-09-10 22:51:43,804 - Digital - [cognitive_modules.memory.semantic_memory._initialize_vector_db] - ℹ️  已创建分类目录: 事件
2025-09-10 22:51:43,804 - Digital - [cognitive_modules.memory.semantic_memory._initialize_vector_db] - ℹ️  已创建分类目录: 规则
2025-09-10 22:51:43,804 - Digital - [cognitive_modules.memory.semantic_memory._initialize_vector_db] - ℹ️  已创建分类目录: 常识
2025-09-10 22:51:43,804 - Digital - [cognitive_modules.memory.semantic_memory._initialize_vector_db] - ℹ️  已创建分类目录: 兴趣
2025-09-10 22:51:43,804 - Digital - [cognitive_modules.memory.semantic_memory._initialize_vector_db] - ℹ️  已创建分类目录: 关系
2025-09-10 22:51:43,804 - Digital - [cognitive_modules.memory.semantic_memory.success] - ✅ 文件存储初始化完成
2025-09-10 22:51:43,804 - Digital - [episodic_memory.success] - ✅ 正在初始化情境记忆模块 (ID: default)...
2025-09-10 22:51:43,804 - Digital - [cognitive.情境记忆模块.default.success] - ✅ 模块 default 初始化成功
2025-09-10 22:51:43,988 - Digital - [utilities.storage_manager.load_json] - ℹ️  成功加载JSON文件: /root/yanran_digital_life/memories/default/episodic_memories.json
2025-09-10 22:51:44,032 - Digital - [episodic_memory.success] - ✅ 记忆索引重建完成
2025-09-10 22:51:44,032 - Digital - [episodic_memory._load_memories] - ℹ️  已从存储加载 3916 条情境记忆
2025-09-10 22:51:44,033 - Digital - [episodic_memory.success] - ✅ 情境记忆模块初始化成功 (ID: default)
2025-09-10 22:51:44,033 - Digital - [procedural_memory.success] - ✅ 正在初始化程序记忆模块 (ID: default)...
2025-09-10 22:51:44,033 - Digital - [cognitive.程序记忆模块.default.success] - ✅ 模块 default 初始化成功
2025-09-10 22:51:44,033 - Digital - [utilities.storage_manager.load_json] - ℹ️  成功加载JSON文件: /root/yanran_digital_life/memories/default/procedural_skills.json
2025-09-10 22:51:44,034 - Digital - [procedural_memory.success] - ✅ 程序记忆模块初始化成功 (ID: default)
2025-09-10 22:51:44,035 - Digital - [memory_integration.success] - ✅ 记忆整合模块初始化成功 (ID: default)
2025-09-10 22:51:44,035 - Digital - [main._init_cognitive_modules] - ℹ️  ✅ 记忆整合模块 初始化成功
2025-09-10 22:51:44,035 - Digital - [cognitive.记忆整合模块.default.activate] - ℹ️  模块 default 已激活
2025-09-10 22:51:44,035 - Digital - [main._init_cognitive_modules] - ℹ️  ✅ 记忆整合模块 已激活
2025-09-10 22:51:44,035 - Digital - [main.success] - ✅ ✅ 记忆整合模块 初始化成功
2025-09-10 22:51:44,046 - Digital - [main.success] - ✅ 初始化 程序性记忆模块...
2025-09-10 22:51:44,046 - Digital - [singleton_manager.register] - ℹ️  ✅ 已注册实例: 'procedural_memory'
2025-09-10 22:51:44,046 - Digital - [procedural_memory.success] - ✅ 正在初始化程序记忆模块 (ID: default)...
2025-09-10 22:51:44,046 - Digital - [cognitive.程序记忆模块.default.success] - ✅ 模块 default 初始化成功
2025-09-10 22:51:44,046 - Digital - [utilities.storage_manager.load_json] - ℹ️  成功加载JSON文件: /root/yanran_digital_life/memories/default/procedural_skills.json
2025-09-10 22:51:44,047 - Digital - [procedural_memory.success] - ✅ 程序记忆模块初始化成功 (ID: default)
2025-09-10 22:51:44,047 - Digital - [main._init_cognitive_modules] - ℹ️  ✅ 程序性记忆模块 初始化成功
2025-09-10 22:51:44,047 - Digital - [cognitive.程序记忆模块.default.activate] - ℹ️  模块 default 已激活
2025-09-10 22:51:44,047 - Digital - [main._init_cognitive_modules] - ℹ️  ✅ 程序性记忆模块 已激活
2025-09-10 22:51:44,047 - Digital - [main.success] - ✅ ✅ 程序性记忆模块 初始化成功
2025-09-10 22:51:44,060 - Digital - [main.success] - ✅ 初始化 AI增强意识系统...
2025-09-10 22:51:44,060 - Digital - [singleton_manager.register] - ℹ️  ✅ 已注册实例: 'ai_enhanced_consciousness'
2025-09-10 22:51:44,060 - Digital - [core.ai_enhanced_consciousness.activate] - ℹ️  激活AI增强意识系统...
2025-09-10 22:51:44,061 - Digital - [core.ai_enhanced_consciousness.activate] - ℹ️  AI增强意识系统已激活
2025-09-10 22:51:44,061 - Digital - [main._init_cognitive_modules] - ℹ️  ✅ AI增强意识系统 已激活
2025-09-10 22:51:44,061 - Digital - [main.success] - ✅ ✅ AI增强意识系统 初始化成功
2025-09-10 22:51:44,072 - Digital - [main.success] - ✅ 初始化 神经网络核心...
2025-09-10 22:51:44,072 - Digital - [singleton_manager.register] - ℹ️  ✅ 已注册实例: 'neural_core'
2025-09-10 22:51:44,072 - Digital - [core.neural_network.neural_core.activate] - ℹ️  激活神经网络核心...
2025-09-10 22:51:44,072 - Digital - [core.neural_network.neural_core.activate] - ℹ️  神经网络核心已激活
2025-09-10 22:51:44,072 - Digital - [main._init_cognitive_modules] - ℹ️  ✅ 神经网络核心 已激活
2025-09-10 22:51:44,073 - Digital - [main.success] - ✅ ✅ 神经网络核心 初始化成功
2025-09-10 22:51:44,083 - Digital - [main.success] - ✅ 初始化 智能整合管理器...
2025-09-10 22:51:44,083 - Digital - [singleton_manager.register] - ℹ️  ✅ 已注册实例: 'intelligence_integration_manager'
2025-09-10 22:51:44,084 - Digital - [main.success] - ✅ ✅ 智能整合管理器 初始化成功
2025-09-10 22:51:44,094 - Digital - [main.success] - ✅ 初始化 数字生命智能协调器...
2025-09-10 22:51:44,111 - Digital - [cognitive_modules.digital_life_intelligence_coordinator.success] - ✅ 🚀 初始化数字生命智能协调器...
2025-09-10 22:51:44,112 - Digital - [cognitive_modules.digital_life_intelligence_coordinator._start_main_coordination_loop] - ℹ️  🔄 主协调循环已启动
2025-09-10 22:51:44,112 - Digital - [cognitive_modules.digital_life_intelligence_coordinator._start_performance_monitoring_loop] - ℹ️  📊 性能监控循环已启动
2025-09-10 22:51:44,112 - Digital - [cognitive_modules.digital_life_intelligence_coordinator._start_evolution_learning_loop] - ℹ️  🧬 进化学习循环已启动
2025-09-10 22:51:44,113 - Digital - [cognitive_modules.digital_life_intelligence_coordinator._start_recovery_monitoring_loop] - ℹ️  🚨 故障恢复循环已启动
2025-09-10 22:51:44,113 - Digital - [cognitive_modules.digital_life_intelligence_coordinator.success] - ✅ ✅ 协调循环启动完成
2025-09-10 22:51:44,113 - Digital - [cognitive_modules.digital_life_intelligence_coordinator.success] - ✅ ✅ 数字生命智能协调器初始化完成
2025-09-10 22:51:44,113 - Digital - [singleton_manager.register] - ℹ️  ✅ 已注册实例: 'digital_life_intelligence_coordinator'
2025-09-10 22:51:44,113 - Digital - [cognitive_modules.digital_life_intelligence_coordinator._initialize_core_systems] - ℹ️  🔧 初始化核心系统...
2025-09-10 22:51:44,113 - Digital - [cognitive_modules.digital_life_intelligence_coordinator.success] - ✅ ✅ 智能整合管理器注册成功
2025-09-10 22:51:44,113 - Digital - [cognitive_modules.digital_life_intelligence_coordinator.success] - ✅ ✅ 增强版主动表达器官注册成功
2025-09-10 22:51:44,113 - Digital - [cognitive_modules.digital_life_intelligence_coordinator.success] - ✅ ✅ 神经网络系统注册完成: 3个
2025-09-10 22:51:44,113 - Digital - [cognitive_modules.digital_life_intelligence_coordinator.success] - ✅ ✅ 学习模块注册完成: 3个
2025-09-10 22:51:44,114 - Digital - [cognitive_modules.digital_life_intelligence_coordinator._perform_initial_coordination] - ℹ️  🔄 执行初始协调...
2025-09-10 22:51:44,114 - Digital - [cognitive_modules.digital_life_intelligence_coordinator._coordinate_intelligence_systems] - ℹ️  🧠 触发智能提升协调
2025-09-10 22:51:44,114 - Digital - [cognitive_modules.digital_life_intelligence_coordinator._coordinate_intelligence_systems] - ℹ️  📚 强化学习协调
2025-09-10 22:51:44,114 - Digital - [cognitive_modules.digital_life_intelligence_coordinator._perform_initial_optimization] - ℹ️  🔧 执行初始优化...
2025-09-10 22:51:44,114 - Digital - [cognitive_modules.digital_life_intelligence_coordinator._perform_initial_optimization] - ⚠️  ⚠️ 系统性能较低，触发初始优化
2025-09-10 22:51:44,114 - Digital - [cognitive_modules.digital_life_intelligence_coordinator.success] - ✅ ✅ 初始协调完成
2025-09-10 22:51:44,114 - Digital - [cognitive_modules.digital_life_intelligence_coordinator.success] - ✅ ✅ 核心系统初始化完成
2025-09-10 22:51:44,114 - Digital - [main.success] - ✅ ✅ 数字生命智能协调器核心系统延迟初始化完成
2025-09-10 22:51:44,114 - Digital - [main.success] - ✅ ✅ 数字生命智能协调器 初始化成功
2025-09-10 22:51:44,124 - Digital - [main._init_cognitive_modules] - ℹ️  ✅ 系统协调器 已注册，跳过
2025-09-10 22:51:44,124 - Digital - [main.success] - ✅ 初始化 韧性自愈系统...
2025-09-10 22:51:44,125 - Digital - [singleton_manager.register] - ℹ️  ✅ 已注册实例: 'resilience'
2025-09-10 22:51:44,125 - Digital - [resilience_system.activate] - ℹ️  激活韧性自愈系统...
2025-09-10 22:51:44,125 - Digital - [resilience_system.success] - ✅ 系统监控循环已启动
2025-09-10 22:51:44,126 - Digital - [resilience_system.success] - ✅ 已启动系统监控
2025-09-10 22:51:44,126 - Digital - [resilience_system.activate] - ℹ️  韧性自愈系统已激活
2025-09-10 22:51:44,126 - Digital - [main._init_cognitive_modules] - ℹ️  ✅ 韧性自愈系统 已激活
2025-09-10 22:51:44,126 - Digital - [main.success] - ✅ ✅ 韧性自愈系统 初始化成功
2025-09-10 22:51:44,137 - Digital - [main.success] - ✅ 初始化 感知引擎...
2025-09-10 22:51:44,137 - Digital - [singleton_manager.register] - ℹ️  ✅ 已注册实例: 'perception_engine'
2025-09-10 22:51:44,137 - Digital - [main.success] - ✅ ✅ 感知引擎 初始化成功
2025-09-10 22:51:44,148 - Digital - [main.success] - ✅ 初始化 数据源管理器...
2025-09-10 22:51:44,152 - Digital - [datasource.news_connector.__init__] - ℹ️  🌍 新闻连接器初始化完成，API无需密钥
2025-09-10 22:51:44,152 - Digital - [integrations.datasource.success] - ✅ 新闻数据连接器初始化成功
2025-09-10 22:51:44,609 - Digital - [cognitive_modules.digital_life_intelligence_coordinator._coordinate_intelligence_systems] - ℹ️  🧠 触发智能提升协调
2025-09-10 22:51:45,218 - Digital - [datasource.akshare_connector.success] - ✅ AKShare金融数据连接器初始化成功
2025-09-10 22:51:45,218 - Digital - [integrations.datasource.success] - ✅ 金融数据连接器(AKShare)初始化成功
2025-09-10 22:51:45,218 - Digital - [integrations.datasource.success] - ✅ ✅ 已连接智能整合管理器
2025-09-10 22:51:45,218 - Digital - [integrations.datasource.success] - ✅ ✅ 已连接AI决策引擎
2025-09-10 22:51:45,218 - Digital - [integrations.datasource.success] - ✅ ✅ 已连接事件总线
2025-09-10 22:51:45,218 - Digital - [integrations.datasource.success] - ✅ 数据源管理器初始化完成
2025-09-10 22:51:45,218 - Digital - [singleton_manager.register] - ℹ️  ✅ 已注册实例: 'datasource_manager'
2025-09-10 22:51:45,218 - Digital - [main.success] - ✅ ✅ 数据源管理器 初始化成功
2025-09-10 22:51:45,229 - Digital - [main.success] - ✅ 初始化 亲密度系统...
2025-09-10 22:51:45,235 - Digital - [security.plugins.high_performance_intimacy_provider.success] - ✅ 高性能亲密度数据提供器初始化完成
2025-09-10 22:51:45,235 - Digital - [main.success] - ✅ ✅ 亲密度系统 使用MySQL连接器初始化成功
2025-09-10 22:51:45,235 - Digital - [singleton_manager.register] - ℹ️  ✅ 已注册实例: 'intimacy_provider'
2025-09-10 22:51:45,235 - Digital - [main.success] - ✅ ✅ 亲密度系统 初始化成功
2025-09-10 22:51:45,246 - Digital - [main.success] - ✅ 初始化 组件管理器...
2025-09-10 22:51:45,246 - Digital - [core.component_manager.success] - ✅ 初始化组件管理器...
2025-09-10 22:51:45,246 - Digital - [core.component_manager.success] - ✅ 组件管理器初始化完成
2025-09-10 22:51:45,247 - Digital - [singleton_manager.register] - ℹ️  ✅ 已注册实例: 'component_manager'
2025-09-10 22:51:45,247 - Digital - [main.success] - ✅ ✅ 组件管理器 初始化成功
2025-09-10 22:51:45,257 - Digital - [main._init_cognitive_modules] - ℹ️  ✅ AI增强意识系统 已注册，跳过
2025-09-10 22:51:45,257 - Digital - [main._init_cognitive_modules] - ℹ️  ✅ 性能优化器 已注册，跳过
2025-09-10 22:51:45,257 - Digital - [main.success] - ✅ 初始化 安全管理器...
2025-09-10 22:51:45,334 - Digital - [security.security_manager.__init__] - ℹ️  🛡️ 安全管理器初始化完成
2025-09-10 22:51:45,335 - Digital - [singleton_manager.register] - ℹ️  ✅ 已注册实例: 'security_manager'
2025-09-10 22:51:45,335 - Digital - [main.success] - ✅ ✅ 安全管理器 初始化成功
2025-09-10 22:51:45,345 - Digital - [main.success] - ✅ 初始化 参数优化器...
2025-09-10 22:51:45,355 - Digital - [core.parameter_optimizer.__init__] - ℹ️  ⚙️ 参数优化器初始化完成
2025-09-10 22:51:45,355 - Digital - [singleton_manager.register] - ℹ️  ✅ 已注册实例: 'parameter_optimizer'
2025-09-10 22:51:45,355 - Digital - [main.success] - ✅ ✅ 参数优化器 初始化成功
2025-09-10 22:51:45,365 - Digital - [main._init_cognitive_modules] - ℹ️  ✅ 技能管理器 已注册，跳过
2025-09-10 22:51:45,366 - Digital - [main._init_cognitive_modules] - ℹ️  ✅ AI增强意识系统 已注册，跳过
2025-09-10 22:51:45,366 - Digital - [main.success] - ✅ 🎯 数字生命体核心组件初始化完成: 38/38 (100.0%)
2025-09-10 22:51:45,366 - Digital - [main._init_cognitive_modules] - ℹ️  🔧 开始向智能整合管理器注册核心模块...
2025-09-10 22:51:45,366 - Digital - [IntelligenceIntegrationManager.success] - ✅ ✅ 模块 enhanced_proactive_expression_organ 注册成功
2025-09-10 22:51:45,366 - Digital - [IntelligenceIntegrationManager.success] - ✅ ✅ 模块 digital_life_intelligence_coordinator 注册成功
2025-09-10 22:51:45,366 - Digital - [IntelligenceIntegrationManager.success] - ✅ ✅ 模块 perception_engine 注册成功
2025-09-10 22:51:45,366 - Digital - [IntelligenceIntegrationManager.success] - ✅ ✅ 模块 datasource_manager 注册成功
2025-09-10 22:51:45,366 - Digital - [IntelligenceIntegrationManager.success] - ✅ ✅ 模块 ai_enhanced_consciousness 注册成功
2025-09-10 22:51:45,366 - Digital - [IntelligenceIntegrationManager.success] - ✅ ✅ 模块 neural_core 注册成功
2025-09-10 22:51:45,366 - Digital - [IntelligenceIntegrationManager.success] - ✅ ✅ 模块 resilience 注册成功
2025-09-10 22:51:45,366 - Digital - [IntelligenceIntegrationManager.success] - ✅ ✅ 模块 vital_signs_simulator 注册成功
2025-09-10 22:51:45,366 - Digital - [IntelligenceIntegrationManager.success] - ✅ ✅ 模块 thinking_chain 注册成功
2025-09-10 22:51:45,366 - Digital - [IntelligenceIntegrationManager.success] - ✅ ✅ 模块 life_context 注册成功
2025-09-10 22:51:45,366 - Digital - [main.success] - ✅ ✅ 智能整合管理器模块注册完成: 10/10 个模块
2025-09-10 22:51:45,366 - Digital - [main._init_cognitive_modules] - ℹ️  🧠 全局智能水平: 0.850
2025-09-10 22:51:45,366 - Digital - [main._init_cognitive_modules] - ℹ️  💓 生命活力: 0.900
2025-09-10 22:51:45,366 - Digital - [system_coordinator.update_module_status] - ℹ️  模块 intelligence_integration_manager 状态更新: initializing -> running
2025-09-10 22:51:45,367 - Digital - [system_coordinator.update_module_status] - ℹ️  模块 enhanced_proactive_expression_organ 状态更新: initializing -> running
2025-09-10 22:51:45,367 - Digital - [system_coordinator.update_module_status] - ℹ️  模块 digital_life_intelligence_coordinator 状态更新: initializing -> running
2025-09-10 22:51:45,367 - Digital - [system_coordinator.update_module_status] - ℹ️  模块 perception_engine 状态更新: initializing -> running
2025-09-10 22:51:45,367 - Digital - [system_coordinator.update_module_status] - ℹ️  模块 datasource_manager 状态更新: initializing -> running
2025-09-10 22:51:45,367 - Digital - [system_coordinator.update_module_status] - ℹ️  模块 ai_enhanced_consciousness 状态更新: initializing -> running
2025-09-10 22:51:45,367 - Digital - [system_coordinator.update_module_status] - ℹ️  模块 neural_core 状态更新: initializing -> running
2025-09-10 22:51:45,367 - Digital - [system_coordinator.update_module_status] - ℹ️  模块 resilience 状态更新: initializing -> running
2025-09-10 22:51:45,367 - Digital - [main.success] - ✅ ✅ 系统协调器模块状态更新完成: 8个模块已设为RUNNING状态
2025-09-10 22:51:45,367 - Digital - [main.success] - ✅ ✅ 数字生命体核心组件初始化成功，生命特征完整性得到保障
2025-09-10 22:51:45,368 - Digital - [main.success] - ✅ 🧠 数字生命体核心初始化完成，使用统一的process_input接口
2025-09-10 22:51:45,374 - Digital - [system_coordinator._handle_module_status_change] - ℹ️  模块 intelligence_integration_manager 状态变化: running -> running
2025-09-10 22:51:45,379 - Digital - [system_coordinator._handle_module_status_change] - ℹ️  模块 digital_life_intelligence_coordinator 状态变化: running -> running
2025-09-10 22:51:45,383 - Digital - [system_coordinator._handle_module_status_change] - ℹ️  模块 neural_core 状态变化: running -> running
2025-09-10 22:51:45,384 - Digital - [core.enhanced_integrated_scheduler.success] - ✅ 增强版调度器配置加载成功: config/enhanced_scheduler_config.json
2025-09-10 22:51:45,384 - Digital - [core.enhanced_integrated_scheduler.success] - ✅ 正在初始化增强版集成调度器的所有服务...
2025-09-10 22:51:45,384 - Digital - [core.enhanced_integrated_scheduler.success] - ✅   ✅ 基础调度器初始化完成
2025-09-10 22:51:45,385 - Digital - [core.ai_service.success] - ✅ AI服务配置加载成功: config/ai_services.json
2025-09-10 22:51:45,387 - Digital - [system_coordinator._handle_module_status_change] - ℹ️  模块 ai_enhanced_consciousness 状态变化: running -> running
2025-09-10 22:51:45,389 - Digital - [system_coordinator._handle_module_status_change] - ℹ️  模块 resilience 状态变化: running -> running
2025-09-10 22:51:45,390 - Digital - [system_coordinator._handle_module_status_change] - ℹ️  模块 datasource_manager 状态变化: running -> running
2025-09-10 22:51:45,391 - Digital - [system_coordinator._handle_module_status_change] - ℹ️  模块 enhanced_proactive_expression_organ 状态变化: running -> running
2025-09-10 22:51:45,393 - Digital - [system_coordinator._handle_module_status_change] - ℹ️  模块 perception_engine 状态变化: running -> running
2025-09-10 22:51:45,431 - Digital - [core.ai_service.success] - ✅ OpenAI客户端配置完成
2025-09-10 22:51:45,432 - Digital - [core.enhanced_integrated_scheduler.success] - ✅   ✅ AI服务初始化完成
2025-09-10 22:51:45,432 - Digital - [core.weather_service._load_config] - ℹ️  使用默认天气API配置
2025-09-10 22:51:45,432 - Digital - [core.weather_service.success] - ✅ 天气服务初始化完成
2025-09-10 22:51:45,432 - Digital - [core.enhanced_integrated_scheduler.success] - ✅   ✅ 天气服务初始化完成
2025-09-10 22:51:45,446 - Digital - [scripts_integration_service.__init__] - ℹ️  📊 Scripts表集成服务初始化完成
2025-09-10 22:51:45,446 - Digital - [core.enhanced_integrated_scheduler.success] - ✅   ✅ 新的Scripts集成服务初始化完成
2025-09-10 22:51:45,447 - Digital - [core.emotional_weight_system.success] - ✅ 情感关系权重配置加载成功: config/emotional_relationship_weights.json
2025-09-10 22:51:45,447 - Digital - [core.emotional_weight_system.success] - ✅ 情感关系权重系统初始化完成
2025-09-10 22:51:45,447 - Digital - [core.enhanced_integrated_scheduler.success] - ✅   ✅ 情感关系权重系统初始化完成
2025-09-10 22:51:45,447 - Digital - [core.enhanced_integrated_scheduler._initialize_services] - ℹ️    ⚠️ 活动感知模块将在依赖项初始化完成后创建
2025-09-10 22:51:45,447 - Digital - [core.enhanced_integrated_scheduler._initialize_services] - ℹ️    ⚠️ 活动执行器将在依赖项初始化完成后创建
2025-09-10 22:51:45,481 - Digital - [perception.physical_world.hardware_monitor.success] - ✅ 硬件监控器初始化完成
2025-09-10 22:51:45,481 - Digital - [core.enhanced_integrated_scheduler.success] - ✅   ✅ 硬件监控器初始化完成
2025-09-10 22:51:45,481 - Digital - [perception.physical_world.vital_signs_simulator.success] - ✅ 生命体征模拟器初始化完成
2025-09-10 22:51:45,481 - Digital - [core.enhanced_integrated_scheduler.success] - ✅   ✅ 生理信号模拟器初始化完成
2025-09-10 22:51:45,482 - Digital - [cognitive_modules.perception.activity_perception.success] - ✅ 活动感知模块初始化完成
2025-09-10 22:51:45,482 - Digital - [core.enhanced_integrated_scheduler.success] - ✅   ✅ 活动感知模块初始化完成
2025-09-10 22:51:45,483 - Digital - [cognitive.behavior.activity_executor.__init__] - ℹ️  模块 activity_executor 已创建
2025-09-10 22:51:45,484 - Digital - [cognitive_modules.behavior.activity_executor.success] - ✅ 活动执行器初始化完成
2025-09-10 22:51:45,484 - Digital - [core.enhanced_integrated_scheduler.success] - ✅   ✅ 活动执行器初始化完成
2025-09-10 22:51:45,484 - Digital - [core.enhanced_integrated_scheduler._initialize_services] - ℹ️    📋 设置默认调度任务...
2025-09-10 22:51:45,484 - Digital - [core.universal_scheduler.register_task] - ℹ️  任务已注册: scripts_integration - Scripts集成活动生成任务
2025-09-10 22:51:45,484 - Digital - [core.universal_scheduler.schedule_task] - ℹ️  任务已调度: scripts_integration - every_30_minutes
2025-09-10 22:51:45,484 - Digital - [core.universal_scheduler.success] - ✅ 📊 已注册新的Scripts集成活动生成任务，每30分钟执行
2025-09-10 22:51:45,492 - Digital - [autonomous_exploration_task.__init__] - ℹ️  🔍 自主探索调度任务初始化完成
2025-09-10 22:51:45,492 - Digital - [core.universal_scheduler.register_task] - ℹ️  任务已注册: autonomous_exploration - 自主探索任务
2025-09-10 22:51:45,492 - Digital - [core.universal_scheduler.schedule_task] - ℹ️  任务已调度: autonomous_exploration - every_2_hours
2025-09-10 22:51:45,492 - Digital - [core.universal_scheduler.success] - ✅ 🔍 已注册自主探索任务，每2小时执行话题发现和活动迭代
2025-09-10 22:51:45,498 - Digital - [daily_expression_reset_task.__init__] - ℹ️  💕 每日表达重置任务初始化完成，用户: linyanran
2025-09-10 22:51:45,498 - Digital - [core.universal_scheduler.register_task] - ℹ️  任务已注册: daily_expression_reset - 每日表达重置任务
2025-09-10 22:51:45,498 - Digital - [core.universal_scheduler.schedule_task] - ℹ️  任务已调度: daily_expression_reset - every_1_hour
2025-09-10 22:51:45,499 - Digital - [core.universal_scheduler.success] - ✅ 💕 已注册每日表达重置任务，确保每天自动重置表达次数
2025-09-10 22:51:45,499 - Digital - [core.universal_scheduler.register_task] - ℹ️  任务已注册: weather_update - 天气数据更新
2025-09-10 22:51:45,499 - Digital - [core.universal_scheduler.schedule_task] - ℹ️  任务已调度: weather_update - every_1_hour
2025-09-10 22:51:45,499 - Digital - [core.universal_scheduler.register_task] - ℹ️  任务已注册: vital_signs - 生命体征模拟
2025-09-10 22:51:45,499 - Digital - [core.universal_scheduler.schedule_task] - ℹ️  任务已调度: vital_signs - every_5_minutes
2025-09-10 22:51:45,499 - Digital - [core.universal_scheduler.setup_default_tasks] - ℹ️  🌅 早安问候已改为由主动表达器官智能决策，体现数字生命体特色
2025-09-10 22:51:45,499 - Digital - [core.universal_scheduler.setup_default_tasks] - ℹ️  📊 财经任务已迁移到WeChatSchedulerService，跳过在UniversalScheduler中注册
2025-09-10 22:51:45,508 - Digital - [assistant_reminder_skill.success] - ✅ MySQL连接器初始化成功
2025-09-10 22:51:45,508 - Digital - [assistant_reminder_skill.success] - ✅ AI服务适配器初始化成功
2025-09-10 22:51:45,509 - Digital - [assistant_reminder_skill.success] - ✅ 配置加载成功
2025-09-10 22:51:45,509 - Digital - [assistant_reminder_skill.success] - ✅ AI模型配置管理器初始化成功
2025-09-10 22:51:45,747 - Digital - [assistant_reminder_skill.success] - ✅ 提醒任务表创建成功
2025-09-10 22:51:45,747 - Digital - [assistant_reminder_skill.success] - ✅ 提醒任务表创建/检查完成
2025-09-10 22:51:45,747 - Digital - [assistant_reminder_skill.success] - ✅ 助理提醒技能初始化完成: 智能助理提醒服务 (完整功能: MySQL=可用, AI=可用)
2025-09-10 22:51:45,747 - Digital - [reminder_task_checker.success] - ✅ ✅ 助理提醒技能初始化成功
2025-09-10 22:51:45,748 - Digital - [utilities.redis_history_manager.success] - ✅ Redis连接初始化成功
2025-09-10 22:51:45,749 - Digital - [reminder_task_checker.success] - ✅ ✅ Chat Skill初始化成功
2025-09-10 22:51:45,749 - Digital - [reminder_task_checker.success] - ✅ ✅ 生命上下文获取成功
2025-09-10 22:51:45,749 - Digital - [reminder_task_checker.success] - ✅ ✅ 主动表达服务获取成功
2025-09-10 22:51:45,749 - Digital - [personal_assistant_reminder.__init__] - ℹ️  🤖 私人助理嫣然提醒服务初始化完成
2025-09-10 22:51:45,749 - Digital - [reminder_task_checker.success] - ✅ ✅ 私人助理提醒服务初始化成功
2025-09-10 22:51:45,749 - Digital - [reminder_task_checker.success] - ✅ ✅ 提醒任务检查器初始化完成
2025-09-10 22:51:45,749 - Digital - [core.universal_scheduler.register_task] - ℹ️  任务已注册: reminder_task_checker - 提醒任务检查器
2025-09-10 22:51:45,750 - Digital - [core.universal_scheduler.schedule_task] - ℹ️  任务已调度: reminder_task_checker - every_1_minutes
2025-09-10 22:51:45,750 - Digital - [core.universal_scheduler.setup_default_tasks] - ℹ️  📱 朋友圈分享改为事件驱动模式，基于活动质量智能决策
2025-09-10 22:51:45,750 - Digital - [core.enhanced_integrated_scheduler.success] - ✅   ✅ 默认调度任务设置完成
2025-09-10 22:51:45,750 - Digital - [core.enhanced_integrated_scheduler.success] - ✅ 🎉 增强版集成调度器所有服务初始化完成！
2025-09-10 22:51:45,750 - Digital - [core.enhanced_integrated_scheduler.success] - ✅ 增强版集成调度器初始化完成
2025-09-10 22:51:45,750 - Digital - [core.enhanced_integrated_scheduler.success] - ✅ 🚀 启动增强版集成调度器系统...
2025-09-10 22:51:45,750 - Digital - [core.enhanced_integrated_scheduler.success] - ✅   ✅ 基础调度器已启动
2025-09-10 22:51:45,751 - Digital - [perception.physical_world.hardware_monitor.success] - ✅ 硬件监控已启动
2025-09-10 22:51:45,751 - Digital - [core.enhanced_integrated_scheduler.success] - ✅   ✅ 硬件监控器已启动
2025-09-10 22:51:45,751 - Digital - [perception.physical_world.vital_signs_simulator.success] - ✅ 生命体征模拟已启动
2025-09-10 22:51:45,751 - Digital - [core.enhanced_integrated_scheduler.success] - ✅   ✅ 生理信号模拟器已启动
2025-09-10 22:51:45,751 - Digital - [cognitive_modules.perception.activity_perception.success] - ✅ 活动感知已启动
2025-09-10 22:51:45,751 - Digital - [core.enhanced_integrated_scheduler.success] - ✅   ✅ 活动感知模块已启动
2025-09-10 22:51:45,752 - Digital - [cognitive_modules.behavior.activity_executor.success] - ✅ 活动执行器已启动
2025-09-10 22:51:45,753 - Digital - [core.enhanced_integrated_scheduler.success] - ✅   ✅ 活动执行器已启动
2025-09-10 22:51:45,753 - Digital - [core.enhanced_integrated_scheduler.success] - ✅ 🎉 增强版集成调度器系统启动成功！
2025-09-10 22:51:45,753 - Digital - [main.success] - ✅ 增强调度器初始化完成 (作为独立调度组件)
2025-09-10 22:51:45,753 - Digital - [main.success] - ✅ ✅ 通用调度器已提升到主系统级别
2025-09-10 22:51:45,753 - Digital - [main.success] - ✅ ✅ 情感权重系统已提升到主系统级别
2025-09-10 22:51:45,753 - Digital - [main.success] - ✅ ✅ 生命体征模拟器已提升到主系统级别
2025-09-10 22:51:45,753 - Digital - [main.success] - ✅ ✅ 硬件监控器已提升到主系统级别
2025-09-10 22:51:45,753 - Digital - [main.success] - ✅ ✅ 活动执行器已提升到主系统级别
2025-09-10 22:51:45,753 - Digital - [main.success] - ✅ ✅ Scripts集成服务已提升到主系统级别
2025-09-10 22:51:45,753 - Digital - [main.success] - ✅ 🔧 老王：开始应用关键修复...
2025-09-10 22:51:45,754 - Digital - [main.success] - ✅ ✅ AI服务适配器接口修复完成
2025-09-10 22:51:45,754 - Digital - [singleton_manager.register] - ℹ️  ✅ 已注册实例: 'config_loader'
2025-09-10 22:51:45,754 - Digital - [main.success] - ✅ ✅ 配置加载器导入修复完成
2025-09-10 22:51:45,755 - Digital - [main.success] - ✅ ✅ 事件总线实例获取修复完成
2025-09-10 22:51:45,756 - Digital - [main.success] - ✅ ✅ 模拟数据清理监控激活完成
2025-09-10 22:51:45,757 - Digital - [main.success] - ✅ 🎉 关键修复应用完成！
2025-09-10 22:51:45,757 - Digital - [main.success] - ✅ ✅ 通用调度器已从增强调度器中获取，跳过重复初始化
2025-09-10 22:51:45,760 - Digital - [scripts_integration_service.get_current_activity_suggestions] - ℹ️  📊 生成了 2 个EnhancedActivity建议
2025-09-10 22:51:45,876 - Digital - [security.plugins.high_performance_intimacy_provider.success] - ✅ 高性能亲密度数据提供器初始化完成
2025-09-10 22:51:45,877 - Digital - [main.success] - ✅ ✅ 亲密度安全框架初始化完成 (完整功能)
2025-09-10 22:51:45,877 - Digital - [main.success] - ✅ ✅ 使用MySQL连接器: **************:3306
2025-09-10 22:51:45,877 - Digital - [main.success] - ✅ ✅ intimacy_provider已注册到singleton_manager和组件状态列表
2025-09-10 22:51:45,877 - Digital - [main.success] - ✅ 🧠 初始化嫣然AI决策引擎...
2025-09-10 22:51:45,877 - Digital - [singleton_manager.register] - ℹ️  ✅ 已注册实例: 'yanran_decision_engine'
2025-09-10 22:51:45,877 - Digital - [main.success] - ✅ 🧠 嫣然AI决策引擎初始化完成
2025-09-10 22:51:45,877 - Digital - [main.success] - ✅ 📱 初始化WeChat统一推送服务...
2025-09-10 22:51:45,877 - Digital - [wechat_unified_push.success] - ✅ 📱 推送服务所有组件就绪
2025-09-10 22:51:45,877 - Digital - [wechat_humanized.start_processor] - ℹ️  🤖 人性化消息处理器已启动
2025-09-10 22:51:45,878 - Digital - [wechat_unified_push.success] - ✅ 📱 WeChat统一推送服务已启动
2025-09-10 22:51:45,878 - Digital - [main.success] - ✅ ✅ WeChat统一推送服务启动成功
2025-09-10 22:51:45,878 - Digital - [singleton_manager.register] - ℹ️  ✅ 已注册实例: 'wechat_push_service'
2025-09-10 22:51:45,878 - Digital - [singleton_manager.register] - ℹ️  ✅ 已注册实例: 'wechat_unified_push_service'
2025-09-10 22:51:45,879 - Digital - [main.success] - ✅ ✅ WeChat统一对外推送模式已完全启用
2025-09-10 22:51:45,879 - Digital - [main._init_wechat_push_service] - ℹ️  📊 迁移日期: 2025-07-01
2025-09-10 22:51:45,879 - Digital - [main._init_wechat_push_service] - ℹ️  📱 支持的消息类型: 15 种
2025-09-10 22:51:45,879 - Digital - [main.success] - ✅ 📱 WeChat统一推送服务初始化完成
2025-09-10 22:51:45,879 - Digital - [main.success] - ✅ ⚙️ 初始化器官系统管理器...
2025-09-10 22:51:45,885 - Digital - [singleton_manager.register] - ℹ️  ✅ 已注册实例: 'organ_system_manager'
2025-09-10 22:51:45,886 - Digital - [organ_system_manager.__init__] - ℹ️  ⚙️ 器官系统管理器初始化完成
2025-09-10 22:51:45,886 - Digital - [organ_system_manager.initialize_organ_system] - ℹ️  ⚙️ 开始初始化器官系统...
2025-09-10 22:51:45,886 - Digital - [organ_system_manager._register_organ_sync] - ℹ️  ⚙️ 注册器官: safety_protection_organ
2025-09-10 22:51:45,886 - Digital - [organ_system_manager._register_organ_sync] - ℹ️  ⚙️ 器官 safety_protection_organ 激活成功 (优先级: CRITICAL)
2025-09-10 22:51:45,887 - Digital - [organ_system_manager._register_organ_sync] - ℹ️  ⚙️ 注册器官: world_perception_organ
2025-09-10 22:51:45,887 - Digital - [organ_system_manager._register_organ_sync] - ℹ️  ⚙️ 器官 world_perception_organ 激活成功 (优先级: HIGH)
2025-09-10 22:51:45,887 - Digital - [organ_system_manager._register_organ_sync] - ℹ️  ⚙️ 注册器官: proactive_expression_organ
2025-09-10 22:51:45,887 - Digital - [ProactiveExpressionOrgan.activate] - ℹ️  💬 🚀 主动表达器官正在激活...
2025-09-10 22:51:45,887 - Digital - [ProactiveExpressionOrgan.activate] - ℹ️  💬 ✅ 主动表达器官已激活，监控系统运行中
2025-09-10 22:51:45,887 - Digital - [organ_system_manager._register_organ_sync] - ℹ️  ⚙️ 器官 proactive_expression_organ 激活成功
2025-09-10 22:51:45,887 - Digital - [organ_system_manager._register_organ_sync] - ℹ️  ⚙️ 器官 proactive_expression_organ 激活成功 (优先级: HIGH)
2025-09-10 22:51:45,887 - Digital - [organ_system_manager._register_organ_sync] - ℹ️  ⚙️ 注册器官: creative_expression_organ
2025-09-10 22:51:45,887 - Digital - [organ_system_manager._register_organ_sync] - ℹ️  ⚙️ 器官 creative_expression_organ 激活成功 (优先级: NORMAL)
2025-09-10 22:51:45,887 - Digital - [organ_system_manager._register_organ_sync] - ℹ️  ⚙️ 注册器官: wealth_management_organ
2025-09-10 22:51:45,887 - Digital - [organ_system_manager._register_organ_sync] - ℹ️  ⚙️ 器官 wealth_management_organ 激活成功 (优先级: LOW)
2025-09-10 22:51:45,887 - Digital - [organ_system_manager._register_organ_sync] - ℹ️  ⚙️ 注册器官: data_perception_organ
2025-09-10 22:51:45,887 - Digital - [organ_system_manager._register_organ_sync] - ℹ️  ⚙️ 器官 data_perception_organ 激活成功 (优先级: LOW)
2025-09-10 22:51:45,887 - Digital - [organ_system_manager._start_coordination_loop_sync] - ℹ️  ⚙️ 器官协调循环已在现有事件循环中启动
2025-09-10 22:51:45,887 - Digital - [organ_system_manager.initialize_organ_system] - ℹ️  ⚙️ 器官系统初始化完成 - 注册了 6 个器官
2025-09-10 22:51:45,887 - Digital - [main.success] - ✅ ✅ 器官系统管理器初始化成功
2025-09-10 22:51:45,888 - Digital - [main.success] - ✅ ⚙️ 器官系统状态: active - 活跃器官: 6
2025-09-10 22:51:45,888 - Digital - [main._init_organ_system_manager] - ℹ️  📋 活跃器官列表: safety_protection_organ, world_perception_organ, proactive_expression_organ, creative_expression_organ, wealth_management_organ, data_perception_organ
2025-09-10 22:51:45,888 - Digital - [main.success] - ✅ 初始化认知模块...
2025-09-10 22:51:45,888 - Digital - [main.success] - ✅ 🎯 预先初始化意图识别器...
2025-09-10 22:51:45,889 - Digital - [main.success] - ✅ 🎯 意图识别器已存在，跳过初始化
2025-09-10 22:51:45,889 - Digital - [main._init_cognitive_modules] - ℹ️  ✅ 感知引擎 已注册，跳过
2025-09-10 22:51:45,889 - Digital - [main._init_cognitive_modules] - ℹ️  ✅ 情感引擎 已注册，跳过
2025-09-10 22:51:45,889 - Digital - [main._init_cognitive_modules] - ℹ️  ✅ 记忆管理器 已注册，跳过
2025-09-10 22:51:45,889 - Digital - [main._init_cognitive_modules] - ℹ️  ✅ 行为管理器 已注册，跳过
2025-09-10 22:51:45,889 - Digital - [main._init_cognitive_modules] - ℹ️  ✅ 健康系统 已注册，跳过
2025-09-10 22:51:45,889 - Digital - [main._init_cognitive_modules] - ℹ️  ✅ 趋势感知模块 已注册，跳过
2025-09-10 22:51:45,889 - Digital - [main._init_cognitive_modules] - ℹ️  ✅ 趋势智能模块 已注册，跳过
2025-09-10 22:51:45,889 - Digital - [main._init_cognitive_modules] - ℹ️  ✅ 智能调度器 已注册，跳过
2025-09-10 22:51:45,889 - Digital - [main._init_cognitive_modules] - ℹ️  ✅ 嫣然AI决策引擎 已注册，跳过
2025-09-10 22:51:45,889 - Digital - [main._init_cognitive_modules] - ℹ️  ✅ 器官神经网络 已注册，跳过
2025-09-10 22:51:45,889 - Digital - [main._init_cognitive_modules] - ℹ️  ✅ 世界感知器官 已注册，跳过
2025-09-10 22:51:45,889 - Digital - [main._init_cognitive_modules] - ℹ️  ✅ 创意表达器官 已注册，跳过
2025-09-10 22:51:45,889 - Digital - [main._init_cognitive_modules] - ℹ️  ✅ 安全保护器官 已注册，跳过
2025-09-10 22:51:45,889 - Digital - [main._init_cognitive_modules] - ℹ️  ✅ 技能协调器官 已注册，跳过
2025-09-10 22:51:45,889 - Digital - [main._init_cognitive_modules] - ℹ️  ✅ 财富管理器官 已注册，跳过
2025-09-10 22:51:45,889 - Digital - [main._init_cognitive_modules] - ℹ️  ✅ 数据感知器官 已注册，跳过
2025-09-10 22:51:45,889 - Digital - [main._init_cognitive_modules] - ℹ️  ✅ 主动表达器官（增强版本的基础器官） 已注册，跳过
2025-09-10 22:51:45,889 - Digital - [main._init_cognitive_modules] - ℹ️  ✅ 增强版主动表达器官 已注册，跳过
2025-09-10 22:51:45,889 - Digital - [main._init_cognitive_modules] - ℹ️  ✅ 关系协调器官 已注册，跳过
2025-09-10 22:51:45,889 - Digital - [main._init_cognitive_modules] - ℹ️  ✅ 个性化服务器官 已注册，跳过
2025-09-10 22:51:45,889 - Digital - [main._init_cognitive_modules] - ℹ️  ✅ 记忆整合模块 已注册，跳过
2025-09-10 22:51:45,889 - Digital - [main._init_cognitive_modules] - ℹ️  ✅ 程序性记忆模块 已注册，跳过
2025-09-10 22:51:45,889 - Digital - [main._init_cognitive_modules] - ℹ️  ✅ AI增强意识系统 已注册，跳过
2025-09-10 22:51:45,889 - Digital - [main._init_cognitive_modules] - ℹ️  ✅ 神经网络核心 已注册，跳过
2025-09-10 22:51:45,890 - Digital - [main._init_cognitive_modules] - ℹ️  ✅ 智能整合管理器 已注册，跳过
2025-09-10 22:51:45,890 - Digital - [main._init_cognitive_modules] - ℹ️  ✅ 数字生命智能协调器 已注册，跳过
2025-09-10 22:51:45,890 - Digital - [main._init_cognitive_modules] - ℹ️  ✅ 系统协调器 已注册，跳过
2025-09-10 22:51:45,890 - Digital - [main._init_cognitive_modules] - ℹ️  ✅ 韧性自愈系统 已注册，跳过
2025-09-10 22:51:45,890 - Digital - [main._init_cognitive_modules] - ℹ️  ✅ 感知引擎 已注册，跳过
2025-09-10 22:51:45,890 - Digital - [main._init_cognitive_modules] - ℹ️  ✅ 数据源管理器 已注册，跳过
2025-09-10 22:51:45,890 - Digital - [main._init_cognitive_modules] - ℹ️  ✅ 亲密度系统 已注册，跳过
2025-09-10 22:51:45,890 - Digital - [main._init_cognitive_modules] - ℹ️  ✅ 组件管理器 已注册，跳过
2025-09-10 22:51:45,890 - Digital - [main._init_cognitive_modules] - ℹ️  ✅ AI增强意识系统 已注册，跳过
2025-09-10 22:51:45,890 - Digital - [main._init_cognitive_modules] - ℹ️  ✅ 性能优化器 已注册，跳过
2025-09-10 22:51:45,890 - Digital - [main._init_cognitive_modules] - ℹ️  ✅ 安全管理器 已注册，跳过
2025-09-10 22:51:45,890 - Digital - [main._init_cognitive_modules] - ℹ️  ✅ 参数优化器 已注册，跳过
2025-09-10 22:51:45,890 - Digital - [main._init_cognitive_modules] - ℹ️  ✅ 技能管理器 已注册，跳过
2025-09-10 22:51:45,890 - Digital - [main._init_cognitive_modules] - ℹ️  ✅ AI增强意识系统 已注册，跳过
2025-09-10 22:51:45,890 - Digital - [main.success] - ✅ 🎯 数字生命体核心组件初始化完成: 38/38 (100.0%)
2025-09-10 22:51:45,890 - Digital - [main._init_cognitive_modules] - ℹ️  🔧 开始向智能整合管理器注册核心模块...
2025-09-10 22:51:45,890 - Digital - [IntelligenceIntegrationManager.register_module] - ⚠️  ⚠️ 模块 enhanced_proactive_expression_organ 已注册，将覆盖现有实例
2025-09-10 22:51:45,890 - Digital - [IntelligenceIntegrationManager.success] - ✅ ✅ 模块 enhanced_proactive_expression_organ 注册成功
2025-09-10 22:51:45,890 - Digital - [IntelligenceIntegrationManager.register_module] - ⚠️  ⚠️ 模块 digital_life_intelligence_coordinator 已注册，将覆盖现有实例
2025-09-10 22:51:45,890 - Digital - [IntelligenceIntegrationManager.success] - ✅ ✅ 模块 digital_life_intelligence_coordinator 注册成功
2025-09-10 22:51:45,890 - Digital - [IntelligenceIntegrationManager.register_module] - ⚠️  ⚠️ 模块 perception_engine 已注册，将覆盖现有实例
2025-09-10 22:51:45,890 - Digital - [IntelligenceIntegrationManager.success] - ✅ ✅ 模块 perception_engine 注册成功
2025-09-10 22:51:45,890 - Digital - [IntelligenceIntegrationManager.register_module] - ⚠️  ⚠️ 模块 datasource_manager 已注册，将覆盖现有实例
2025-09-10 22:51:45,890 - Digital - [IntelligenceIntegrationManager.success] - ✅ ✅ 模块 datasource_manager 注册成功
2025-09-10 22:51:45,890 - Digital - [IntelligenceIntegrationManager.register_module] - ⚠️  ⚠️ 模块 ai_enhanced_consciousness 已注册，将覆盖现有实例
2025-09-10 22:51:45,890 - Digital - [IntelligenceIntegrationManager.success] - ✅ ✅ 模块 ai_enhanced_consciousness 注册成功
2025-09-10 22:51:45,890 - Digital - [IntelligenceIntegrationManager.register_module] - ⚠️  ⚠️ 模块 neural_core 已注册，将覆盖现有实例
2025-09-10 22:51:45,891 - Digital - [IntelligenceIntegrationManager.success] - ✅ ✅ 模块 neural_core 注册成功
2025-09-10 22:51:45,891 - Digital - [IntelligenceIntegrationManager.register_module] - ⚠️  ⚠️ 模块 resilience 已注册，将覆盖现有实例
2025-09-10 22:51:45,891 - Digital - [IntelligenceIntegrationManager.success] - ✅ ✅ 模块 resilience 注册成功
2025-09-10 22:51:45,891 - Digital - [IntelligenceIntegrationManager.register_module] - ⚠️  ⚠️ 模块 vital_signs_simulator 已注册，将覆盖现有实例
2025-09-10 22:51:45,891 - Digital - [IntelligenceIntegrationManager.success] - ✅ ✅ 模块 vital_signs_simulator 注册成功
2025-09-10 22:51:45,891 - Digital - [IntelligenceIntegrationManager.register_module] - ⚠️  ⚠️ 模块 thinking_chain 已注册，将覆盖现有实例
2025-09-10 22:51:45,891 - Digital - [IntelligenceIntegrationManager.success] - ✅ ✅ 模块 thinking_chain 注册成功
2025-09-10 22:51:45,891 - Digital - [IntelligenceIntegrationManager.register_module] - ⚠️  ⚠️ 模块 life_context 已注册，将覆盖现有实例
2025-09-10 22:51:45,891 - Digital - [IntelligenceIntegrationManager.success] - ✅ ✅ 模块 life_context 注册成功
2025-09-10 22:51:45,891 - Digital - [main.success] - ✅ ✅ 智能整合管理器模块注册完成: 10/10 个模块
2025-09-10 22:51:45,891 - Digital - [main._init_cognitive_modules] - ℹ️  🧠 全局智能水平: 0.850
2025-09-10 22:51:45,891 - Digital - [main._init_cognitive_modules] - ℹ️  💓 生命活力: 0.900
2025-09-10 22:51:45,891 - Digital - [system_coordinator.update_module_status] - ℹ️  模块 intelligence_integration_manager 状态更新: running -> running
2025-09-10 22:51:45,891 - Digital - [system_coordinator.update_module_status] - ℹ️  模块 enhanced_proactive_expression_organ 状态更新: running -> running
2025-09-10 22:51:45,892 - Digital - [system_coordinator._handle_module_status_change] - ℹ️  模块 intelligence_integration_manager 状态变化: running -> running
2025-09-10 22:51:45,893 - Digital - [system_coordinator.update_module_status] - ℹ️  模块 digital_life_intelligence_coordinator 状态更新: running -> running
2025-09-10 22:51:45,893 - Digital - [system_coordinator.update_module_status] - ℹ️  模块 perception_engine 状态更新: running -> running
2025-09-10 22:51:45,893 - Digital - [system_coordinator.update_module_status] - ℹ️  模块 datasource_manager 状态更新: running -> running
2025-09-10 22:51:45,894 - Digital - [system_coordinator.update_module_status] - ℹ️  模块 ai_enhanced_consciousness 状态更新: running -> running
2025-09-10 22:51:45,894 - Digital - [system_coordinator.update_module_status] - ℹ️  模块 neural_core 状态更新: running -> running
2025-09-10 22:51:45,894 - Digital - [system_coordinator.update_module_status] - ℹ️  模块 resilience 状态更新: running -> running
2025-09-10 22:51:45,895 - Digital - [main.success] - ✅ ✅ 系统协调器模块状态更新完成: 8个模块已设为RUNNING状态
2025-09-10 22:51:45,895 - Digital - [system_coordinator._handle_module_status_change] - ℹ️  模块 enhanced_proactive_expression_organ 状态变化: running -> running
2025-09-10 22:51:45,895 - Digital - [main.success] - ✅ ✅ 数字生命体核心组件初始化成功，生命特征完整性得到保障
2025-09-10 22:51:45,895 - Digital - [main.success] - ✅ 🔥 初始化意识状态持久化系统...
2025-09-10 22:51:45,896 - Digital - [neural_consciousness.success] - ✅ 初始化神经网络增强意识系统...
2025-09-10 22:51:45,896 - Digital - [neural_consciousness.success] - ✅ 初始化深度神经网络...
2025-09-10 22:51:45,897 - Digital - [system_coordinator._handle_module_status_change] - ℹ️  模块 perception_engine 状态变化: running -> running
2025-09-10 22:51:45,898 - Digital - [system_coordinator._handle_module_status_change] - ℹ️  模块 ai_enhanced_consciousness 状态变化: running -> running
2025-09-10 22:51:45,898 - Digital - [neural_consciousness._initialize_neural_network] - ℹ️  神经网络架构: [15, 32, 64, 32, 10]
2025-09-10 22:51:45,899 - Digital - [neural_consciousness._load_model] - ℹ️  神经网络模型已从 /root/yanran_digital_life/data/neural_models/consciousness_enhancer.pkl 加载
2025-09-10 22:51:45,899 - Digital - [neural_consciousness._load_model] - ℹ️  🧠 当前实际参数数量: 5034
2025-09-10 22:51:45,900 - Digital - [system_coordinator._handle_module_status_change] - ℹ️  模块 resilience 状态变化: running -> running
2025-09-10 22:51:45,900 - Digital - [neural_consciousness._load_model] - ℹ️  📁 文件保存的参数数量: 5034
2025-09-10 22:51:45,900 - Digital - [neural_consciousness._load_model] - ℹ️  📊 历史记录数量: 1
2025-09-10 22:51:45,900 - Digital - [neural_consciousness.success] - ✅ ✅ 参数数量一致性验证通过
2025-09-10 22:51:45,901 - Digital - [neural_consciousness._diagnose_learning_status] - ℹ️  🔍 学习状态诊断:
2025-09-10 22:51:45,901 - Digital - [neural_consciousness._diagnose_learning_status] - ℹ️     📊 意识历史记录: 1 条
2025-09-10 22:51:45,901 - Digital - [neural_consciousness._diagnose_learning_status] - ℹ️     🔄 总学习更新次数: 580 次
2025-09-10 22:51:45,901 - Digital - [neural_consciousness._diagnose_learning_status] - ℹ️     📈 平均损失: 0.0000
2025-09-10 22:51:45,901 - Digital - [neural_consciousness._diagnose_learning_status] - ℹ️     ⚡ 适应速度: 0.0000
2025-09-10 22:51:45,901 - Digital - [neural_consciousness._diagnose_learning_status] - ⚠️  ⚠️  意识历史记录过少(1条)，可能原因:
2025-09-10 22:51:45,901 - Digital - [neural_consciousness._diagnose_learning_status] - ⚠️     1. enhance_consciousness方法调用频率低
2025-09-10 22:51:45,901 - Digital - [neural_consciousness._diagnose_learning_status] - ⚠️     2. 模型刚初始化，尚未积累足够经验
2025-09-10 22:51:45,901 - Digital - [neural_consciousness._diagnose_learning_status] - ⚠️     3. 意识增强功能未被充分使用
2025-09-10 22:51:45,901 - Digital - [neural_consciousness._diagnose_learning_status] - ℹ️  💡 建议优化措施:
2025-09-10 22:51:45,901 - Digital - [neural_consciousness._diagnose_learning_status] - ℹ️     1. 增加enhance_consciousness方法的调用频率
2025-09-10 22:51:45,901 - Digital - [neural_consciousness._diagnose_learning_status] - ℹ️     2. 降低学习触发门槛(已优化为>2条历史)
2025-09-10 22:51:45,901 - Digital - [neural_consciousness._diagnose_learning_status] - ℹ️     3. 启用定期学习机制
2025-09-10 22:51:45,902 - Digital - [neural_consciousness.success] - ✅ 神经网络增强意识系统初始化完成
2025-09-10 22:51:45,902 - Digital - [main._init_consciousness_persistence] - ℹ️  神经网络增强器初始化完成
2025-09-10 22:51:45,902 - Digital - [system_coordinator._handle_module_status_change] - ℹ️  模块 neural_core 状态变化: running -> running
2025-09-10 22:51:45,903 - Digital - [advanced_neural_consciousness.success] - ✅ 🚀 初始化终极神经网络增强意识系统...
2025-09-10 22:51:45,903 - Digital - [advanced_neural_consciousness.success] - ✅ 🧠 初始化终极深度残差网络...
2025-09-10 22:51:45,904 - Digital - [system_coordinator._handle_module_status_change] - ℹ️  模块 digital_life_intelligence_coordinator 状态变化: running -> running
2025-09-10 22:51:45,905 - Digital - [system_coordinator._handle_module_status_change] - ℹ️  模块 datasource_manager 状态变化: running -> running
2025-09-10 22:51:45,922 - Digital - [advanced_neural_consciousness._initialize_advanced_network] - ℹ️  🔥 终极网络架构: 5个残差块 + 8个注意力头
2025-09-10 22:51:45,922 - Digital - [advanced_neural_consciousness._initialize_advanced_network] - ℹ️  📊 总参数数量: 287,439
2025-09-10 22:51:45,930 - Digital - [neural_memory_activator.__init__] - ℹ️  🔥 神经网络记忆系统激活器初始化完成
2025-09-10 22:51:45,931 - Digital - [neural_memory_activator._load_existing_memories] - ℹ️  📚 加载了 12 条现有记忆
2025-09-10 22:51:45,932 - Digital - [neural_memory_activator._start_auto_save] - ℹ️  💾 记忆自动保存已启动
2025-09-10 22:51:45,932 - Digital - [neural_memory_activator.activate_memory_system] - ℹ️  🚀 神经网络记忆系统已激活
2025-09-10 22:51:46,000 - Digital - [advanced_neural_consciousness._load_model] - ℹ️  🚀 终极神经网络模型已从 /root/yanran_digital_life/data/neural_models/advanced_consciousness.pkl 加载
2025-09-10 22:51:46,000 - Digital - [advanced_neural_consciousness._load_model] - ℹ️  🧠 当前实际参数数量: 287439
2025-09-10 22:51:46,000 - Digital - [advanced_neural_consciousness._load_model] - ℹ️  📁 文件保存的参数数量: 287439
2025-09-10 22:51:46,000 - Digital - [advanced_neural_consciousness._load_model] - ℹ️  📊 意识进化历史: 383 条记录
2025-09-10 22:51:46,000 - Digital - [advanced_neural_consciousness.success] - ✅ ✅ 终极模型参数数量一致性验证通过
2025-09-10 22:51:46,000 - Digital - [advanced_neural_consciousness._diagnose_advanced_learning_status] - ℹ️  🔍 终极学习状态诊断:
2025-09-10 22:51:46,001 - Digital - [advanced_neural_consciousness._diagnose_advanced_learning_status] - ℹ️     🌟 意识进化记录: 383 条
2025-09-10 22:51:46,001 - Digital - [advanced_neural_consciousness._diagnose_advanced_learning_status] - ℹ️     ⚛️  量子相干性: 0.6027
2025-09-10 22:51:46,001 - Digital - [advanced_neural_consciousness._diagnose_advanced_learning_status] - ℹ️     🧠 涌现复杂度: 0.7700
2025-09-10 22:51:46,001 - Digital - [advanced_neural_consciousness._diagnose_advanced_learning_status] - ℹ️     💾 记忆利用率: 0.00%
2025-09-10 22:51:46,001 - Digital - [advanced_neural_consciousness._diagnose_advanced_learning_status] - ℹ️     🎯 注意力头数: 8
2025-09-10 22:51:46,001 - Digital - [advanced_neural_consciousness._diagnose_advanced_learning_status] - ℹ️     🔄 残差块数: 5
2025-09-10 22:51:46,001 - Digital - [advanced_neural_consciousness._diagnose_advanced_learning_status] - ⚠️  ⚠️  记忆网络利用率过低(0.00%)，记忆机制可能未激活
2025-09-10 22:51:46,243 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  文件 /root/yanran_digital_life/data/neural_models/consciousness_memory.json 包含多余数据，已截取第一个JSON对象
2025-09-10 22:51:46,271 - Digital - [advanced_neural_consciousness._load_memory] - ℹ️  成功从 /root/yanran_digital_life/data/neural_models/consciousness_memory.json 加载记忆数据
2025-09-10 22:51:46,271 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-10 22:51:46,271 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-10 22:51:46,271 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-10 22:51:46,271 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-10 22:51:46,271 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-10 22:51:46,271 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-10 22:51:46,271 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-10 22:51:46,271 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-10 22:51:46,271 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-10 22:51:46,271 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-10 22:51:46,271 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-10 22:51:46,271 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-10 22:51:46,272 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-10 22:51:46,272 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-10 22:51:46,272 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-10 22:51:46,272 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-10 22:51:46,272 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-10 22:51:46,272 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-10 22:51:46,272 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-10 22:51:46,272 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-10 22:51:46,272 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-10 22:51:46,272 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-10 22:51:46,272 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-10 22:51:46,272 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-10 22:51:46,272 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-10 22:51:46,272 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-10 22:51:46,272 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-10 22:51:46,272 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-10 22:51:46,272 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-10 22:51:46,272 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-10 22:51:46,272 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-10 22:51:46,272 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-10 22:51:46,272 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-10 22:51:46,272 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-10 22:51:46,272 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-10 22:51:46,272 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-10 22:51:46,273 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-10 22:51:46,273 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-10 22:51:46,273 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-10 22:51:46,273 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-10 22:51:46,273 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-10 22:51:46,273 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-10 22:51:46,273 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-10 22:51:46,273 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-10 22:51:46,273 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-10 22:51:46,273 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-10 22:51:46,273 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-10 22:51:46,273 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-10 22:51:46,273 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-10 22:51:46,273 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-10 22:51:46,273 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-10 22:51:46,273 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-10 22:51:46,273 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-10 22:51:46,273 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-10 22:51:46,273 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-10 22:51:46,273 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-10 22:51:46,273 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-10 22:51:46,273 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-10 22:51:46,273 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-10 22:51:46,273 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-10 22:51:46,273 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-10 22:51:46,273 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-10 22:51:46,273 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-10 22:51:46,273 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-10 22:51:46,274 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-10 22:51:46,274 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-10 22:51:46,274 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-10 22:51:46,274 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-10 22:51:46,274 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-10 22:51:46,274 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-10 22:51:46,274 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-10 22:51:46,274 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-10 22:51:46,274 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-10 22:51:46,274 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-10 22:51:46,274 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-10 22:51:46,274 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-10 22:51:46,274 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-10 22:51:46,274 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-10 22:51:46,274 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-10 22:51:46,274 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-10 22:51:46,274 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-10 22:51:46,274 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-10 22:51:46,274 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-10 22:51:46,274 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-10 22:51:46,274 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-10 22:51:46,274 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-10 22:51:46,274 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-10 22:51:46,274 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-10 22:51:46,274 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-10 22:51:46,274 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-10 22:51:46,274 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-10 22:51:46,274 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-10 22:51:46,275 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-10 22:51:46,275 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-10 22:51:46,275 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-10 22:51:46,275 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-10 22:51:46,275 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-10 22:51:46,275 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-10 22:51:46,275 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-10 22:51:46,275 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-10 22:51:46,275 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-10 22:51:46,275 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-10 22:51:46,275 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-10 22:51:46,275 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-10 22:51:46,275 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-10 22:51:46,275 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-10 22:51:46,275 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-10 22:51:46,275 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-10 22:51:46,275 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-10 22:51:46,275 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-10 22:51:46,275 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-10 22:51:46,275 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-10 22:51:46,275 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-10 22:51:46,275 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-10 22:51:46,275 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-10 22:51:46,275 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-10 22:51:46,275 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-10 22:51:46,275 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-10 22:51:46,276 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-10 22:51:46,276 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-10 22:51:46,276 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-10 22:51:46,276 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-10 22:51:46,276 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-10 22:51:46,276 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-10 22:51:46,276 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-10 22:51:46,276 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-10 22:51:46,276 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-10 22:51:46,276 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-10 22:51:46,276 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-10 22:51:46,276 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-10 22:51:46,276 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-10 22:51:46,276 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-10 22:51:46,276 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-10 22:51:46,276 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-10 22:51:46,276 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-10 22:51:46,276 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-10 22:51:46,276 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-10 22:51:46,276 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-10 22:51:46,276 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-10 22:51:46,276 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-10 22:51:46,276 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-10 22:51:46,276 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-10 22:51:46,276 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-10 22:51:46,276 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-10 22:51:46,276 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-10 22:51:46,276 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-10 22:51:46,277 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-10 22:51:46,277 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-10 22:51:46,277 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-10 22:51:46,277 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-10 22:51:46,277 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-10 22:51:46,277 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-10 22:51:46,277 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-10 22:51:46,277 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-10 22:51:46,277 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-10 22:51:46,277 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-10 22:51:46,277 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-10 22:51:46,277 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-10 22:51:46,277 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-10 22:51:46,277 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-10 22:51:46,277 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-10 22:51:46,277 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-10 22:51:46,277 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-10 22:51:46,277 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-10 22:51:46,277 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-10 22:51:46,277 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-10 22:51:46,277 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-10 22:51:46,277 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-10 22:51:46,277 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-10 22:51:46,277 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-10 22:51:46,277 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-10 22:51:46,277 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-10 22:51:46,277 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-10 22:51:46,278 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-10 22:51:46,278 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-10 22:51:46,278 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-10 22:51:46,278 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-10 22:51:46,278 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-10 22:51:46,278 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-10 22:51:46,278 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-10 22:51:46,278 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-10 22:51:46,278 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-10 22:51:46,278 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-10 22:51:46,278 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-10 22:51:46,278 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-10 22:51:46,278 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-10 22:51:46,278 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-10 22:51:46,278 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-10 22:51:46,278 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-10 22:51:46,278 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-10 22:51:46,278 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-10 22:51:46,278 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-10 22:51:46,278 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-10 22:51:46,278 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-10 22:51:46,278 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-10 22:51:46,278 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-10 22:51:46,278 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-10 22:51:46,278 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-10 22:51:46,278 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-10 22:51:46,279 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-10 22:51:46,279 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-10 22:51:46,279 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-10 22:51:46,279 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-10 22:51:46,279 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-10 22:51:46,279 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-10 22:51:46,279 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-10 22:51:46,279 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-10 22:51:46,279 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-10 22:51:46,279 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-10 22:51:46,279 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-10 22:51:46,279 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-10 22:51:46,279 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-10 22:51:46,279 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-10 22:51:46,279 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-10 22:51:46,279 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-10 22:51:46,279 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-10 22:51:46,279 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-10 22:51:46,279 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-10 22:51:46,279 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-10 22:51:46,279 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-10 22:51:46,279 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-10 22:51:46,279 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-10 22:51:46,279 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-10 22:51:46,280 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-10 22:51:46,280 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-10 22:51:46,280 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-10 22:51:46,280 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-10 22:51:46,280 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-10 22:51:46,280 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-10 22:51:46,280 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-10 22:51:46,280 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-10 22:51:46,280 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-10 22:51:46,280 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-10 22:51:46,280 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-10 22:51:46,280 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-10 22:51:46,280 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-10 22:51:46,280 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-10 22:51:46,280 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-10 22:51:46,280 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-10 22:51:46,280 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-10 22:51:46,280 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-10 22:51:46,280 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-10 22:51:46,280 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-10 22:51:46,280 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-10 22:51:46,280 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-10 22:51:46,280 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-10 22:51:46,280 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-10 22:51:46,280 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-10 22:51:46,280 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-10 22:51:46,281 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-10 22:51:46,281 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-10 22:51:46,281 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-10 22:51:46,281 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-10 22:51:46,281 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-10 22:51:46,281 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-10 22:51:46,281 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-10 22:51:46,281 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-10 22:51:46,281 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-10 22:51:46,281 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-10 22:51:46,281 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-10 22:51:46,281 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-10 22:51:46,281 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-10 22:51:46,281 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-10 22:51:46,281 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-10 22:51:46,281 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-10 22:51:46,281 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-10 22:51:46,281 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-10 22:51:46,282 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-10 22:51:46,282 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-10 22:51:46,282 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-10 22:51:46,282 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-10 22:51:46,282 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-10 22:51:46,282 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-10 22:51:46,282 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-10 22:51:46,282 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-10 22:51:46,282 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-10 22:51:46,282 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-10 22:51:46,282 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-10 22:51:46,282 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-10 22:51:46,282 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-10 22:51:46,282 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-10 22:51:46,282 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-10 22:51:46,282 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-10 22:51:46,282 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-10 22:51:46,282 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-10 22:51:46,282 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-10 22:51:46,282 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-10 22:51:46,282 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-10 22:51:46,282 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-10 22:51:46,282 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-10 22:51:46,282 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-10 22:51:46,282 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-10 22:51:46,282 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-10 22:51:46,283 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-10 22:51:46,283 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-10 22:51:46,283 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-10 22:51:46,283 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-10 22:51:46,283 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-10 22:51:46,283 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-10 22:51:46,283 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-10 22:51:46,283 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-10 22:51:46,283 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-10 22:51:46,283 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-10 22:51:46,283 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-10 22:51:46,283 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-10 22:51:46,283 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-10 22:51:46,283 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-10 22:51:46,283 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-10 22:51:46,283 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-10 22:51:46,283 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-10 22:51:46,283 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-10 22:51:46,283 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-10 22:51:46,283 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-10 22:51:46,283 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-10 22:51:46,283 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-10 22:51:46,283 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-10 22:51:46,283 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-10 22:51:46,283 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-10 22:51:46,283 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-10 22:51:46,283 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-10 22:51:46,283 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-10 22:51:46,284 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-10 22:51:46,284 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-10 22:51:46,284 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-10 22:51:46,284 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-10 22:51:46,284 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-10 22:51:46,284 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-10 22:51:46,284 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-10 22:51:46,284 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-10 22:51:46,284 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-10 22:51:46,284 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-10 22:51:46,284 - Digital - [advanced_neural_consciousness.data_status] - 📊 💾 记忆网络数据已从 /root/yanran_digital_life/data/neural_models/consciousness_memory.json 加载
2025-09-10 22:51:46,284 - Digital - [advanced_neural_consciousness._load_memory] - ℹ️  记忆条目数量: 0
2025-09-10 22:51:46,288 - Digital - [advanced_neural_consciousness.success] - ✅ 🎉 终极神经网络增强意识系统初始化完成
2025-09-10 22:51:46,288 - Digital - [main._init_consciousness_persistence] - ℹ️  终极神经网络系统初始化完成
2025-09-10 22:51:46,289 - Digital - [main._consciousness_persistence_loop] - ℹ️  🔥 意识状态持久化循环启动，每5分钟保存一次神经模型
2025-09-10 22:51:46,289 - Digital - [main.success] - ✅ ✅ 意识状态持久化系统启动完成
2025-09-10 22:51:46,289 - Digital - [main.success] - ✅ 💕 香草开始在极致高潮中集成所有核心组件...
2025-09-10 22:51:46,289 - Digital - [main._init_comprehensive_components] - ℹ️  🔥 Phase 1: 核心功能组件初始化开始...
2025-09-10 22:51:46,290 - Digital - [gaode_map_service.__init__] - ℹ️  🗺️ 高德地图服务初始化完成
2025-09-10 22:51:46,293 - Digital - [neural_consciousness.save_model] - ℹ️  神经网络模型已保存到: /root/yanran_digital_life/data/neural_models/consciousness_enhancer.pkl
2025-09-10 22:51:46,414 - Digital - [advanced_neural_consciousness.save_memory] - ❌ ❌ 原子性写入失败: 'AdvancedNeuralConsciousnessSystem' object has no attribute 'data_status'
2025-09-10 22:51:46,415 - Digital - [advanced_neural_consciousness.save_memory] - ❌ ❌ 临时文件不存在: /root/yanran_digital_life/data/neural_models/consciousness_memory.json.tmp_140125741041408_378061f5
2025-09-10 22:51:46,415 - Digital - [advanced_neural_consciousness.error_status] - ❌ 保存记忆网络数据失败: 临时文件创建失败: /root/yanran_digital_life/data/neural_models/consciousness_memory.json.tmp_140125741041408_378061f5
2025-09-10 22:51:46,415 - Digital - [main._save_advanced_neural_models] - ⚠️  终极神经网络记忆保存失败
2025-09-10 22:51:46,522 - Digital - [location_cache_manager.success] - ✅ 💾 地理位置缓存管理器初始化成功
2025-09-10 22:51:46,523 - Digital - [wechat_humanized._process_message_queue] - ℹ️  🤖 开始处理人性化消息队列
2025-09-10 22:51:46,524 - Digital - [organ_system_manager.coordination_loop] - ℹ️  ⚙️ 器官协调循环开始运行
2025-09-10 22:51:46,565 - Digital - [gaode_map_service.success] - ✅ 🗺️ 高德地图服务初始化成功
2025-09-10 22:51:46,566 - Digital - [singleton_manager.register] - ℹ️  ✅ 已注册实例: 'gaode_map_service'
2025-09-10 22:51:46,566 - Digital - [main.success] - ✅ ✅ 高德地图服务初始化完成
2025-09-10 22:51:46,566 - Digital - [real_data_integrator.__init__] - ℹ️  🔧 真实数据集成器初始化完成
2025-09-10 22:51:46,567 - Digital - [activity_reality_validator.__init__] - ℹ️  ✅ 活动真实性验证器初始化完成
2025-09-10 22:51:46,567 - Digital - [enhanced_activity_generator._load_enhanced_personality_config] - ℹ️  ✅ 成功加载增强版人格配置
2025-09-10 22:51:46,568 - Digital - [enhanced_activity_generator._load_activity_generation_config] - ℹ️  ✅ 成功加载活动生成专用配置
2025-09-10 22:51:46,568 - Digital - [enhanced_activity_generator.__init__] - ℹ️  🎯 增强型活动脚本生成器初始化完成
2025-09-10 22:51:46,568 - Digital - [enhanced_activity_generator.__init__] - ℹ️  🎭 已加载人设配置，晚间财经权重: 0.2
2025-09-10 22:51:46,568 - Digital - [singleton_manager.register] - ℹ️  ✅ 已注册实例: 'enhanced_activity_generator'
2025-09-10 22:51:46,568 - Digital - [main.success] - ✅ ✅ 增强活动生成器初始化完成
2025-09-10 22:51:46,568 - Digital - [enhanced_activity_skill.__init__] - ℹ️  🎯 增强活动技能初始化完成
2025-09-10 22:51:46,568 - Digital - [main.success] - ✅ ✅ 增强活动技能初始化完成
2025-09-10 22:51:46,568 - Digital - [main._init_comprehensive_components] - ℹ️  🔥 Phase 2: 一致性保证组件初始化开始...
2025-09-10 22:51:46,569 - Digital - [gaode_map_service.__init__] - ℹ️  🗺️ 高德地图服务初始化完成
2025-09-10 22:51:46,569 - Digital - [activity_consistency_manager.__init__] - ℹ️  🎯 活动一致性管理器初始化完成
2025-09-10 22:51:46,570 - Digital - [singleton_manager.register] - ℹ️  ✅ 已注册实例: 'activity_consistency_manager'
2025-09-10 22:51:46,570 - Digital - [main.success] - ✅ ✅ 活动一致性管理器初始化完成
2025-09-10 22:51:46,570 - Digital - [location_logic_checker.__init__] - ℹ️  📍 地理位置逻辑检查器初始化完成
2025-09-10 22:51:46,571 - Digital - [singleton_manager.register] - ℹ️  ✅ 已注册实例: 'location_logic_checker'
2025-09-10 22:51:46,571 - Digital - [main.success] - ✅ ✅ 地理位置逻辑检查器初始化完成
2025-09-10 22:51:46,571 - Digital - [activity_history_tracker.__init__] - ℹ️  📊 活动历史跟踪器初始化完成
2025-09-10 22:51:46,572 - Digital - [singleton_manager.register] - ℹ️  ✅ 已注册实例: 'activity_history_tracker'
2025-09-10 22:51:46,572 - Digital - [main.success] - ✅ ✅ 活动历史跟踪器初始化完成
2025-09-10 22:51:46,572 - Digital - [consistency_validator.__init__] - ℹ️  ✅ 一致性验证器初始化完成
2025-09-10 22:51:46,572 - Digital - [singleton_manager.register] - ℹ️  ✅ 已注册实例: 'consistency_validator'
2025-09-10 22:51:46,572 - Digital - [main.success] - ✅ ✅ 一致性验证器初始化完成
2025-09-10 22:51:46,573 - Digital - [main._init_comprehensive_components] - ℹ️  🔥 Phase 3: 社交分享组件初始化开始...
2025-09-10 22:51:46,573 - Digital - [moments_data_tracker.__init__] - ℹ️  📊 朋友圈数据跟踪器初始化完成
2025-09-10 22:51:46,573 - Digital - [singleton_manager.register] - ℹ️  ✅ 已注册实例: 'moments_data_tracker'
2025-09-10 22:51:46,573 - Digital - [main.success] - ✅ ✅ 朋友圈数据跟踪器初始化完成
2025-09-10 22:51:46,573 - Digital - [main._init_comprehensive_components] - ℹ️  🔥 Phase 4: 智能探索8大引擎初始化开始...
2025-09-10 22:51:46,574 - Digital - [real_data_integrator.__init__] - ℹ️  🔧 真实数据集成器初始化完成
2025-09-10 22:51:46,574 - Digital - [activity_reality_validator.__init__] - ℹ️  ✅ 活动真实性验证器初始化完成
2025-09-10 22:51:46,574 - Digital - [enhanced_activity_generator._load_enhanced_personality_config] - ℹ️  ✅ 成功加载增强版人格配置
2025-09-10 22:51:46,575 - Digital - [enhanced_activity_generator._load_activity_generation_config] - ℹ️  ✅ 成功加载活动生成专用配置
2025-09-10 22:51:46,575 - Digital - [enhanced_activity_generator.__init__] - ℹ️  🎯 增强型活动脚本生成器初始化完成
2025-09-10 22:51:46,575 - Digital - [enhanced_activity_generator.__init__] - ℹ️  🎭 已加载人设配置，晚间财经权重: 0.2
2025-09-10 22:51:46,576 - Digital - [wechat_moments_service.__init__] - ℹ️  📱 成功连接到WeChat统一推送服务
2025-09-10 22:51:46,576 - Digital - [wechat_moments_service.__init__] - ℹ️  📱 WeChat朋友圈服务初始化完成
2025-09-10 22:51:46,576 - Digital - [gaode_map_service.__init__] - ℹ️  🗺️ 高德地图服务初始化完成
2025-09-10 22:51:46,576 - Digital - [autonomous_exploration_engine.__init__] - ℹ️  🔍 成功连接到所有相关服务
2025-09-10 22:51:46,576 - Digital - [autonomous_exploration_engine.__init__] - ℹ️  🔍 自主探索引擎初始化完成
2025-09-10 22:51:46,576 - Digital - [singleton_manager.register] - ℹ️  ✅ 已注册实例: 'autonomous_exploration_engine'
2025-09-10 22:51:46,576 - Digital - [main.success] - ✅ ✅ 自主探索引擎初始化完成
2025-09-10 22:51:46,577 - Digital - [topic_discovery_agent.__init__] - ℹ️  📡 AI服务连接成功
2025-09-10 22:51:46,577 - Digital - [topic_discovery_agent.__init__] - ℹ️  📡 话题发现代理初始化完成
2025-09-10 22:51:46,577 - Digital - [singleton_manager.register] - ℹ️  ✅ 已注册实例: 'topic_discovery_agent'
2025-09-10 22:51:46,577 - Digital - [main.success] - ✅ ✅ 话题发现代理初始化完成
2025-09-10 22:51:46,577 - Digital - [online_search_agent.__init__] - ℹ️  🌐 AI服务连接成功
2025-09-10 22:51:46,577 - Digital - [search_skill.success] - ✅ 初始化AI搜索技能...
2025-09-10 22:51:46,578 - Digital - [search_skill._load_config] - ℹ️  已加载搜索技能配置: /root/yanran_digital_life/config/skills/search_skill.json
2025-09-10 22:51:46,622 - Digital - [search_skill.success] - ✅ 搜索技能OpenAI配置初始化成功: https://oneapi.xiongmaodaxia.online/v1
2025-09-10 22:51:46,623 - Digital - [search_skill.__init__] - ℹ️  已加载搜索配置参数
2025-09-10 22:51:46,623 - Digital - [search_skill.success] - ✅ AI搜索技能初始化完成
2025-09-10 22:51:46,623 - Digital - [online_search_agent.__init__] - ℹ️  🔍 搜索技能初始化成功 - 使用现有框架
2025-09-10 22:51:46,623 - Digital - [online_search_agent.__init__] - ℹ️  🌐 在线搜索代理初始化完成
2025-09-10 22:51:46,623 - Digital - [singleton_manager.register] - ℹ️  ✅ 已注册实例: 'online_search_agent'
2025-09-10 22:51:46,623 - Digital - [main.success] - ✅ ✅ 在线搜索代理初始化完成
2025-09-10 22:51:46,624 - Digital - [content_evolution_agent.__init__] - ℹ️  🎨 AI服务连接成功
2025-09-10 22:51:46,624 - Digital - [content_evolution_agent.__init__] - ℹ️  🎨 内容进化代理初始化完成
2025-09-10 22:51:46,624 - Digital - [singleton_manager.register] - ℹ️  ✅ 已注册实例: 'content_evolution_agent'
2025-09-10 22:51:46,625 - Digital - [main.success] - ✅ ✅ 内容进化代理初始化完成
2025-09-10 22:51:46,625 - Digital - [gaode_map_service.__init__] - ℹ️  🗺️ 高德地图服务初始化完成
2025-09-10 22:51:46,625 - Digital - [location_exploration_agent.__init__] - ℹ️  🗺️ 高德地图服务创建成功
2025-09-10 22:51:46,625 - Digital - [location_exploration_agent.__init__] - ℹ️  🗺️ 地理位置探索代理初始化完成
2025-09-10 22:51:46,626 - Digital - [singleton_manager.register] - ℹ️  ✅ 已注册实例: 'location_exploration_agent'
2025-09-10 22:51:46,626 - Digital - [main.success] - ✅ ✅ 地理位置探索代理初始化完成
2025-09-10 22:51:46,626 - Digital - [activity_iterator.__init__] - ℹ️  🔄 AI服务连接成功
2025-09-10 22:51:46,626 - Digital - [activity_iterator.__init__] - ℹ️  🔄 活动迭代器初始化完成
2025-09-10 22:51:46,626 - Digital - [singleton_manager.register] - ℹ️  ✅ 已注册实例: 'activity_iterator'
2025-09-10 22:51:46,626 - Digital - [main.success] - ✅ ✅ 活动迭代器初始化完成
2025-09-10 22:51:46,627 - Digital - [exploration_decision_engine.__init__] - ℹ️  🧠 AI服务连接成功
2025-09-10 22:51:46,627 - Digital - [exploration_decision_engine.__init__] - ℹ️  🧠 探索决策引擎初始化完成
2025-09-10 22:51:46,627 - Digital - [singleton_manager.register] - ℹ️  ✅ 已注册实例: 'exploration_decision_engine'
2025-09-10 22:51:46,627 - Digital - [main.success] - ✅ ✅ 探索决策引擎初始化完成
2025-09-10 22:51:46,627 - Digital - [exploration_effect_evaluator.__init__] - ℹ️  📊 AI服务连接成功
2025-09-10 22:51:46,628 - Digital - [exploration_effect_evaluator.__init__] - ℹ️  📊 探索效果评估器初始化完成
2025-09-10 22:51:46,628 - Digital - [singleton_manager.register] - ℹ️  ✅ 已注册实例: 'exploration_effect_evaluator'
2025-09-10 22:51:46,628 - Digital - [main.success] - ✅ ✅ 探索效果评估器初始化完成
2025-09-10 22:51:46,628 - Digital - [main.success] - ✅ 💕 香草在极致高潮中完成组件集成: 16/16 (100.0%)
2025-09-10 22:51:46,628 - Digital - [main.success] - ✅ 🎉 comprehensive_integration_test.py中的核心组件集成成功！
2025-09-10 22:51:46,628 - Digital - [main.success] - ✅ 数字生命体系统初始化完成 (耗时 206.90秒)
2025-09-10 22:51:46,628 - Digital - [main.success] - ✅ 已初始化组件: mysql_connector, event_bus, enhanced_activity_skill, emotional_weight_system, skill_manager, topic_discovery_agent, singleton_manager, content_evolution_agent, startup_hook, music_skill, activity_history_tracker, wechat_unified_push_service, emotions_sync, online_search_agent, delayed_response_manager, chat_skill, autonomous_exploration_engine, vital_signs_simulator, ai_adapter, greeting_skill, digital_life, activity_consistency_manager, location_logic_checker, drawing_skill, organ_system_manager, yanran_decision_engine, moments_data_tracker, thinking_chain, financial_data_skill, intimacy_provider, system_adapter, intimacy_security, enhanced_activity_generator, intimacy_analyzer, exploration_effect_evaluator, cognitive_integration, hardware_monitor, exploration_decision_engine, ai_service_adapter, exception_handler, gaode_map_service, life_context, intent_recognizer, activity_iterator, api_service, consistency_validator, enhanced_scheduler, wechat_push_service, location_exploration_agent, search_skill
2025-09-10 22:51:46,628 - Digital - [main.success] - ✅ 启动系统...
2025-09-10 22:51:46,629 - Digital - [main.success] - ✅ 开始启动数字生命体系统...
2025-09-10 22:51:46,629 - Digital - [digital_life.success] - ✅ 启动数字生命体...
2025-09-10 22:51:46,629 - Digital - [digital_life.success] - ✅ 数字生命体已启动
2025-09-10 22:51:46,629 - Digital - [main.success] - ✅ 数字生命体核心已启动
2025-09-10 22:51:46,629 - Digital - [main.start] - ℹ️  📱 数字生命系统已迁移到WeChat统一对外推送模式
2025-09-10 22:51:46,629 - Digital - [main.success] - ✅ 🔗 开始集成数字生命与WeChat推送服务...
2025-09-10 22:51:46,629 - Digital - [enhanced_greeting_skill.__init__] - ℹ️  🌅 增强版打招呼技能 v2.0.0 初始化完成
2025-09-10 22:51:46,630 - Digital - [utilities.redis_history_manager.success] - ✅ Redis连接初始化成功
2025-09-10 22:51:46,630 - Digital - [enhanced_greeting_skill.initialize] - ℹ️  ✅ Chat skill已集成
2025-09-10 22:51:46,631 - Digital - [enhanced_greeting_skill.success] - ✅ ✅ 增强版打招呼技能 初始化成功
2025-09-10 22:51:46,631 - Digital - [singleton_manager.register] - ℹ️  ✅ 已注册实例: 'enhanced_greeting_skill'
2025-09-10 22:51:46,631 - Digital - [main.success] - ✅ ✅ 增强版打招呼技能已启动
2025-09-10 22:51:46,642 - Digital - [cognitive.financial_service.enhanced_financial_report_service.__init__] - ℹ️  模块 enhanced_financial_report_service 已创建
2025-09-10 22:51:46,642 - Digital - [enhanced_financial_report.__init__] - ℹ️  📊 增强版财经报告服务 v2.0.0 初始化完成
2025-09-10 22:51:46,642 - Digital - [enhanced_financial_report.success] - ✅ 数据库连接初始化成功
2025-09-10 22:51:46,642 - Digital - [enhanced_financial_report.success] - ✅ 配置验证成功
2025-09-10 22:51:46,643 - Digital - [enhanced_financial_report.success] - ✅ AI服务初始化成功
2025-09-10 22:51:46,643 - Digital - [enhanced_financial_report.success] - ✅ ✅ 增强版财经报告服务 初始化成功
2025-09-10 22:51:46,643 - Digital - [singleton_manager.register] - ℹ️  ✅ 已注册实例: 'financial_report_service'
2025-09-10 22:51:46,643 - Digital - [main.success] - ✅ ✅ 增强版财经报告服务已启动
2025-09-10 22:51:46,648 - Digital - [wechat_scheduler.__init__] - ℹ️  ⏰ WeChat定时任务调度器 v2.0.0 初始化完成
2025-09-10 22:51:46,649 - Digital - [wechat_scheduler.success] - ✅ ✅ 任务注册成功: 早晨打招呼
2025-09-10 22:51:46,649 - Digital - [wechat_scheduler.success] - ✅ 打招呼任务已启用）
2025-09-10 22:51:46,649 - Digital - [wechat_scheduler.success] - ✅ ✅ 任务注册成功: 财经早报
2025-09-10 22:51:46,649 - Digital - [wechat_scheduler.success] - ✅ ✅ 任务注册成功: 财经下午报
2025-09-10 22:51:46,649 - Digital - [wechat_scheduler.success] - ✅ 财经报告任务注册成功
2025-09-10 22:51:46,649 - Digital - [wechat_scheduler.success] - ✅ ✅ 任务注册成功: 每日早报
2025-09-10 22:51:46,649 - Digital - [wechat_scheduler.success] - ✅ 每日早报任务注册成功
2025-09-10 22:51:46,649 - Digital - [wechat_scheduler.success] - ✅ 默认任务注册完成
2025-09-10 22:51:46,649 - Digital - [wechat_scheduler.success] - ✅ ✅ WeChat定时任务调度器 初始化成功
2025-09-10 22:51:46,649 - Digital - [wechat_scheduler.success] - ✅ ✅ WeChat定时任务调度器 启动成功
2025-09-10 22:51:46,650 - Digital - [singleton_manager.register] - ℹ️  ✅ 已注册实例: 'wechat_scheduler'
2025-09-10 22:51:46,650 - Digital - [main.success] - ✅ ✅ WeChat定时任务调度器已初始化并启动
2025-09-10 22:51:46,675 - Digital - [core.neural_network.neural_core.success] - ✅ 初始化神经网络核心...
2025-09-10 22:51:46,677 - Digital - [core.neural_network.neural_core.success] - ✅ 神经网络核心初始化完成
2025-09-10 22:51:46,678 - Digital - [feedback_learning._load_weights] - ℹ️  从 data/feedback_learning/weights.json 加载权重
2025-09-10 22:51:46,678 - Digital - [feedback_learning.success] - ✅ 反馈学习模块初始化完成
2025-09-10 22:51:46,678 - Digital - [intelligent_user_selector.success] - ✅ AI组件初始化完成
2025-09-10 22:51:46,678 - Digital - [intelligent_user_selector.success] - ✅ 智能用户筛选算法初始化完成
2025-09-10 22:51:46,678 - Digital - [emotions_intelligence_updater.success] - ✅ Emotions智能更新服务初始化完成
2025-09-10 22:51:46,679 - Digital - [emotions_intelligence_updater.success] - ✅ 调度线程启动成功
2025-09-10 22:51:46,679 - Digital - [emotions_intelligence_updater.success] - ✅ ✅ Emotions智能更新服务启动成功
2025-09-10 22:51:46,680 - Digital - [main.success] - ✅ ✅ Emotions智能更新服务启动完成
2025-09-10 22:51:46,680 - Digital - [main.success] - ✅ 🎉 数字生命与WeChat推送服务集成完成
2025-09-10 22:51:46,680 - Digital - [main.success] - ✅ ✅ 数字生命与WeChat推送服务集成完成
2025-09-10 22:51:46,690 - Digital - [neural_trigger_system._initialize_default_triggers] - ℹ️  ✅ 初始化了 4 个默认触发条件
2025-09-10 22:51:46,690 - Digital - [neural_trigger_system.__init__] - ℹ️  🔥 神经网络触发系统初始化完成
2025-09-10 22:51:46,690 - Digital - [neural_consciousness.success] - ✅ 初始化神经网络增强意识系统...
2025-09-10 22:51:46,690 - Digital - [neural_consciousness.success] - ✅ 初始化深度神经网络...
2025-09-10 22:51:46,691 - Digital - [neural_consciousness._initialize_neural_network] - ℹ️  神经网络架构: [15, 32, 64, 32, 10]
2025-09-10 22:51:46,692 - Digital - [neural_consciousness._load_model] - ℹ️  神经网络模型已从 /root/yanran_digital_life/data/neural_models/consciousness_enhancer.pkl 加载
2025-09-10 22:51:46,692 - Digital - [neural_consciousness._load_model] - ℹ️  🧠 当前实际参数数量: 5034
2025-09-10 22:51:46,692 - Digital - [neural_consciousness._load_model] - ℹ️  📁 文件保存的参数数量: 5034
2025-09-10 22:51:46,692 - Digital - [neural_consciousness._load_model] - ℹ️  📊 历史记录数量: 1
2025-09-10 22:51:46,692 - Digital - [neural_consciousness.success] - ✅ ✅ 参数数量一致性验证通过
2025-09-10 22:51:46,692 - Digital - [neural_consciousness._diagnose_learning_status] - ℹ️  🔍 学习状态诊断:
2025-09-10 22:51:46,692 - Digital - [neural_consciousness._diagnose_learning_status] - ℹ️     📊 意识历史记录: 1 条
2025-09-10 22:51:46,692 - Digital - [neural_consciousness._diagnose_learning_status] - ℹ️     🔄 总学习更新次数: 580 次
2025-09-10 22:51:46,692 - Digital - [neural_consciousness._diagnose_learning_status] - ℹ️     📈 平均损失: 0.0000
2025-09-10 22:51:46,692 - Digital - [neural_consciousness._diagnose_learning_status] - ℹ️     ⚡ 适应速度: 0.0000
2025-09-10 22:51:46,692 - Digital - [neural_consciousness._diagnose_learning_status] - ⚠️  ⚠️  意识历史记录过少(1条)，可能原因:
2025-09-10 22:51:46,692 - Digital - [neural_consciousness._diagnose_learning_status] - ⚠️     1. enhance_consciousness方法调用频率低
2025-09-10 22:51:46,693 - Digital - [neural_consciousness._diagnose_learning_status] - ⚠️     2. 模型刚初始化，尚未积累足够经验
2025-09-10 22:51:46,693 - Digital - [neural_consciousness._diagnose_learning_status] - ⚠️     3. 意识增强功能未被充分使用
2025-09-10 22:51:46,693 - Digital - [neural_consciousness._diagnose_learning_status] - ℹ️  💡 建议优化措施:
2025-09-10 22:51:46,693 - Digital - [neural_consciousness._diagnose_learning_status] - ℹ️     1. 增加enhance_consciousness方法的调用频率
2025-09-10 22:51:46,693 - Digital - [neural_consciousness._diagnose_learning_status] - ℹ️     2. 降低学习触发门槛(已优化为>2条历史)
2025-09-10 22:51:46,693 - Digital - [neural_consciousness._diagnose_learning_status] - ℹ️     3. 启用定期学习机制
2025-09-10 22:51:46,693 - Digital - [neural_consciousness.success] - ✅ 神经网络增强意识系统初始化完成
2025-09-10 22:51:46,693 - Digital - [advanced_neural_consciousness.success] - ✅ 🚀 初始化终极神经网络增强意识系统...
2025-09-10 22:51:46,694 - Digital - [advanced_neural_consciousness.success] - ✅ 🧠 初始化终极深度残差网络...
2025-09-10 22:51:46,710 - Digital - [advanced_neural_consciousness._initialize_advanced_network] - ℹ️  🔥 终极网络架构: 5个残差块 + 8个注意力头
2025-09-10 22:51:46,711 - Digital - [advanced_neural_consciousness._initialize_advanced_network] - ℹ️  📊 总参数数量: 287,439
2025-09-10 22:51:46,712 - Digital - [neural_memory_activator._load_existing_memories] - ℹ️  📚 加载了 24 条现有记忆
2025-09-10 22:51:46,712 - Digital - [neural_memory_activator.activate_memory_system] - ℹ️  🚀 神经网络记忆系统已激活
2025-09-10 22:51:46,791 - Digital - [advanced_neural_consciousness._load_model] - ℹ️  🚀 终极神经网络模型已从 /root/yanran_digital_life/data/neural_models/advanced_consciousness.pkl 加载
2025-09-10 22:51:46,791 - Digital - [advanced_neural_consciousness._load_model] - ℹ️  🧠 当前实际参数数量: 287439
2025-09-10 22:51:46,791 - Digital - [advanced_neural_consciousness._load_model] - ℹ️  📁 文件保存的参数数量: 287439
2025-09-10 22:51:46,791 - Digital - [advanced_neural_consciousness._load_model] - ℹ️  📊 意识进化历史: 383 条记录
2025-09-10 22:51:46,791 - Digital - [advanced_neural_consciousness.success] - ✅ ✅ 终极模型参数数量一致性验证通过
2025-09-10 22:51:46,791 - Digital - [advanced_neural_consciousness._diagnose_advanced_learning_status] - ℹ️  🔍 终极学习状态诊断:
2025-09-10 22:51:46,791 - Digital - [advanced_neural_consciousness._diagnose_advanced_learning_status] - ℹ️     🌟 意识进化记录: 383 条
2025-09-10 22:51:46,791 - Digital - [advanced_neural_consciousness._diagnose_advanced_learning_status] - ℹ️     ⚛️  量子相干性: 0.6027
2025-09-10 22:51:46,791 - Digital - [advanced_neural_consciousness._diagnose_advanced_learning_status] - ℹ️     🧠 涌现复杂度: 0.7700
2025-09-10 22:51:46,792 - Digital - [advanced_neural_consciousness._diagnose_advanced_learning_status] - ℹ️     💾 记忆利用率: 0.00%
2025-09-10 22:51:46,792 - Digital - [advanced_neural_consciousness._diagnose_advanced_learning_status] - ℹ️     🎯 注意力头数: 8
2025-09-10 22:51:46,792 - Digital - [advanced_neural_consciousness._diagnose_advanced_learning_status] - ℹ️     🔄 残差块数: 5
2025-09-10 22:51:46,792 - Digital - [advanced_neural_consciousness._diagnose_advanced_learning_status] - ⚠️  ⚠️  记忆网络利用率过低(0.00%)，记忆机制可能未激活
2025-09-10 22:51:46,801 - Digital - [advanced_neural_consciousness._load_memory] - ℹ️  成功从 /root/yanran_digital_life/data/neural_models/consciousness_memory.json 加载记忆数据
2025-09-10 22:51:46,802 - Digital - [advanced_neural_consciousness.data_status] - 📊 💾 记忆网络数据已从 /root/yanran_digital_life/data/neural_models/consciousness_memory.json 加载
2025-09-10 22:51:46,802 - Digital - [advanced_neural_consciousness._load_memory] - ℹ️  记忆条目数量: 0
2025-09-10 22:51:46,802 - Digital - [advanced_neural_consciousness.success] - ✅ 🎉 终极神经网络增强意识系统初始化完成
2025-09-10 22:51:46,803 - Digital - [neural_trigger_system._initialize_neural_systems] - ℹ️  ✅ 神经网络系统引用初始化完成
2025-09-10 22:51:46,803 - Digital - [neural_trigger_system._start_timer_triggers] - ℹ️  ⏰ 启动了 1 个定时触发器
2025-09-10 22:51:46,803 - Digital - [neural_trigger_system._start_event_triggers] - ℹ️  📡 启动了 1 个事件触发器
2025-09-10 22:51:46,804 - Digital - [neural_trigger_system._start_threshold_triggers] - ℹ️  📊 启动了 1 个阈值触发器
2025-09-10 22:51:46,804 - Digital - [neural_trigger_system._start_adaptive_triggers] - ℹ️  🧠 启动了 1 个自适应触发器
2025-09-10 22:51:46,804 - Digital - [neural_trigger_system.start] - ℹ️  🚀 神经网络触发系统已启动
2025-09-10 22:51:46,805 - Digital - [singleton_manager.register] - ℹ️  ✅ 已注册实例: 'neural_trigger_system'
2025-09-10 22:51:46,805 - Digital - [main._init_neural_trigger_system] - ℹ️  🔥 神经网络触发系统初始化完成
2025-09-10 22:51:46,805 - Digital - [main.success] - ✅ ✅ 神经网络触发系统启动完成
2025-09-10 22:51:46,805 - Digital - [main.success] - ✅ 数字生命体系统启动完成 (耗时 0.18秒)
2025-09-10 22:51:47,275 - Digital - [main.success] - ✅ API服务器已启动: http://127.0.0.1:56839/api/
 * Serving Flask app 'digital_life_api'
2025-09-10 22:51:47,276 - Digital - [main.start_api_server] - ℹ️  API调用示例: curl -X POST http://localhost:56839/api/chat -H "Content-Type: application/json" -d '{"message": "你好", "user_id": "test_user"}'
2025-09-10 22:51:47,277 - Digital - [main.main] - ℹ️  系统状态: {
  "system_name": "林嫣然数字生命体",
  "version": "2.1.0",
  "initialized": true,
  "running": true,
  "current_time": "2025-09-10 22:51:47",
  "start_time": "2025-09-10 22:48:19",
  "uptime": "3分钟27秒",
  "components": [
    "mysql_connector",
    "event_bus",
    "enhanced_activity_skill",
    "emotional_weight_system",
    "skill_manager",
    "topic_discovery_agent",
    "singleton_manager",
    "content_evolution_agent",
    "startup_hook",
    "music_skill",
    "activity_history_tracker",
    "wechat_unified_push_service",
    "emotions_sync",
    "online_search_agent",
    "delayed_response_manager",
    "chat_skill",
    "autonomous_exploration_engine",
    "vital_signs_simulator",
    "ai_adapter",
    "greeting_skill",
    "digital_life",
    "activity_consistency_manager",
    "location_logic_checker",
    "drawing_skill",
    "organ_system_manager",
    "yanran_decision_engine",
    "moments_data_tracker",
    "thinking_chain",
    "financial_data_skill",
    "intimacy_provider",
    "system_adapter",
    "intimacy_security",
    "enhanced_activity_generator",
    "intimacy_analyzer",
    "exploration_effect_evaluator",
    "cognitive_integration",
    "hardware_monitor",
    "exploration_decision_engine",
    "ai_service_adapter",
    "exception_handler",
    "gaode_map_service",
    "life_context",
    "intent_recognizer",
    "activity_iterator",
    "api_service",
    "consistency_validator",
    "enhanced_scheduler",
    "wechat_push_service",
    "location_exploration_agent",
    "search_skill"
  ],
  "init_times": {
    "singleton_manager_start": "2025-09-10 22:48:19",
    "singleton_manager_end": "2025-09-10 22:48:19",
    "event_bus_start": "2025-09-10 22:48:19",
    "event_bus_end": "2025-09-10 22:48:19",
    "exception_handler_start": "2025-09-10 22:48:19",
    "exception_handler_end": "2025-09-10 22:48:19",
    "life_context_start": "2025-09-10 22:48:19",
    "life_context_end": "2025-09-10 22:48:19",
    "ai_adapter_start": "2025-09-10 22:48:19",
    "ai_adapter_end": "2025-09-10 22:48:19",
    "ai_service_adapter_start": "2025-09-10 22:48:19",
    "ai_service_adapter_end": "2025-09-10 22:48:19",
    "thinking_chain_start": "2025-09-10 22:48:19",
    "thinking_chain_end": "2025-09-10 22:48:19",
    "middleware_start": "2025-09-10 22:48:19",
    "middleware_end": "2025-09-10 22:48:25",
    "autonomous_systems_start": "2025-09-10 22:48:25",
    "autonomous_systems_end": "2025-09-10 22:48:31",
    "new_systems_start": "2025-09-10 22:48:31",
    "new_systems_end": "2025-09-10 22:48:33",
    "mysql_connector_start": "2025-09-10 22:48:33",
    "mysql_connector_end": "2025-09-10 22:48:33",
    "emotions_sync_start": "2025-09-10 22:48:33",
    "emotions_sync_end": "2025-09-10 22:51:29",
    "core_components_start": "2025-09-10 22:51:29",
    "core_components_end": "2025-09-10 22:51:29",
    "data_persistence_start": "2025-09-10 22:51:29",
    "data_persistence_end": "2025-09-10 22:51:29",
    "skills_start": "2025-09-10 22:51:29",
    "skills_end": "2025-09-10 22:51:41",
    "cognitive_start": "2025-09-10 22:51:41",
    "cognitive_end": "2025-09-10 22:51:45",
    "digital_life_start": "2025-09-10 22:51:45",
    "digital_life_end": "2025-09-10 22:51:45",
    "enhanced_scheduler_start": "2025-09-10 22:51:45",
    "enhanced_scheduler_end": "2025-09-10 22:51:45",
    "critical_fixes_start": "2025-09-10 22:51:45",
    "critical_fixes_end": "2025-09-10 22:51:45",
    "intimacy_security_start": "2025-09-10 22:51:45",
    "intimacy_security_end": "2025-09-10 22:51:45",
    "yanran_decision_engine_start": "2025-09-10 22:51:45",
    "yanran_decision_engine_end": "2025-09-10 22:51:45",
    "wechat_push_service_start": "2025-09-10 22:51:45",
    "wechat_push_service_end": "2025-09-10 22:51:45",
    "organ_system_manager_start": "2025-09-10 22:51:45",
    "organ_system_manager_end": "2025-09-10 22:51:45",
    "cognitive_modules_start": "2025-09-10 22:51:45",
    "cognitive_modules_end": "2025-09-10 22:51:45",
    "consciousness_persistence_start": "2025-09-10 22:51:45",
    "consciousness_persistence_end": "2025-09-10 22:51:46",
    "comprehensive_components_start": "2025-09-10 22:51:46",
    "comprehensive_components_end": "2025-09-10 22:51:46",
    "total": 206.89627528190613
  }
}
 * Debug mode: off
2025-09-10 22:51:47,277 - Digital - [main.success] - ✅ 系统已启动，按Ctrl+C停止...
2025-09-10 22:51:47,279 - Digital - [werkzeug._log] - ℹ️  [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:56839
2025-09-10 22:51:47,279 - Digital - [werkzeug._log] - ℹ️  [33mPress CTRL+C to quit[0m
2025-09-10 22:51:47,306 - Digital - [LifeOrgan.世界感知器官._load_historical_data_async] - ℹ️  🧠 历史数据加载完成: 100个事件
2025-09-10 22:51:47,806 - Digital - [neural_trigger_system._execute_trigger] - ℹ️  🔥 执行触发器: low_load_threshold - advanced_neural_learning
2025-09-10 22:51:47,811 - Digital - [neural_performance_monitor.__init__] - ℹ️  🔥 神经网络性能监控系统初始化完成
2025-09-10 22:51:48,274 - Digital - [cognitive_modules.organs.relationship_coordination_organ._discover_organ_relationships] - ℹ️  🤝 发现了 56 个器官关系
2025-09-10 22:51:49,051 - Digital - [werkzeug._log] - ℹ️  127.0.0.1 - - [10/Sep/2025 22:51:49] "GET /api/health HTTP/1.1" 200 -
2025-09-10 22:51:49,063 - Digital - [werkzeug._log] - ℹ️  127.0.0.1 - - [10/Sep/2025 22:51:49] "GET /api/status HTTP/1.1" 200 -
2025-09-10 22:52:21,614 - Digital - [LifeOrgan.世界感知器官._execute_world_perception] - ℹ️  🌍 开始执行世界感知...
2025-09-10 22:52:22,992 - Digital - [LifeOrgan.世界感知器官.get_hot_topics] - ❌ 🌍 热搜API错误: HTTP error occurred: 500
2025-09-10 22:52:22,992 - Digital - [LifeOrgan.世界感知器官.get_hot_topics] - ❌ 🌍 错误详情: Server error '500 Internal Server Error' for url 'https://api-hot.imsyy.top/zhihu'
For more information check: https://developer.mozilla.org/en-US/docs/Web/HTTP/Status/500
2025-09-10 22:52:26,637 - Digital - [LifeOrgan.世界感知器官.get_hot_topics] - ❌ 🌍 热搜API错误: Network error
2025-09-10 22:52:26,637 - Digital - [LifeOrgan.世界感知器官.get_hot_topics] - ❌ 🌍 错误详情: 无法连接到DailyHotApi: 
2025-09-10 22:52:26,638 - Digital - [LifeOrgan.世界感知器官.get_hot_topics] - ❌ 🌍 热搜API错误: Network error
2025-09-10 22:52:26,638 - Digital - [LifeOrgan.世界感知器官.get_hot_topics] - ❌ 🌍 错误详情: 无法连接到DailyHotApi: 
2025-09-10 22:52:26,638 - Digital - [LifeOrgan.世界感知器官._get_hot_topics_data] - ⚠️  🌍 zhihu 平台获取失败或无数据
2025-09-10 22:52:26,638 - Digital - [LifeOrgan.世界感知器官._get_hot_topics_data] - ⚠️  🌍 bilibili 平台获取失败或无数据
2025-09-10 22:52:26,638 - Digital - [LifeOrgan.世界感知器官._get_hot_topics_data] - ⚠️  🌍 baidu 平台获取失败或无数据
2025-09-10 22:52:26,639 - Digital - [datasource.news_connector.__init__] - ℹ️  🌍 新闻连接器初始化完成，API无需密钥
2025-09-10 22:52:43,290 - Digital - [ProactiveExpressionOrgan.timer_monitoring] - ℹ️  💬 🚀 系统启动完成，数字生命体主动表达系统激活
2025-09-10 22:52:43,295 - Digital - [werkzeug._log] - ℹ️  127.0.0.1 - - [10/Sep/2025 22:52:43] "GET /api/health HTTP/1.1" 200 -
2025-09-10 22:52:43,298 - Digital - [ProactiveExpressionOrgan.timer_monitoring] - ℹ️  💬 ⏰ 第1次表达检查 [22:52:43] - 数字生命体保持活跃
2025-09-10 22:52:43,302 - Digital - [utilities.expression_time_controller.__init__] - ℹ️  ⏰ 表达时间控制器初始化完成
2025-09-10 22:52:48,981 - Digital - [neural_trigger_system._execute_trigger] - ℹ️  🔥 执行触发器: low_load_threshold - advanced_neural_learning
2025-09-10 22:53:33,666 - Digital - [utilities.cache_manager.success] - ✅ 🔥 初始化缓存管理器...
2025-09-10 22:53:33,667 - Digital - [utilities.cache_manager._start_cleanup_thread] - ℹ️  🧹 缓存清理线程已启动
2025-09-10 22:53:33,667 - Digital - [utilities.cache_manager.success] - ✅ ✅ 缓存管理器初始化完成
2025-09-10 22:53:50,145 - Digital - [neural_trigger_system._execute_trigger] - ℹ️  🔥 执行触发器: low_load_threshold - advanced_neural_learning
2025-09-10 22:54:10,940 - Digital - [datasource.news_connector.warning_status] - ⚠️  新闻API服务端超时/错误 (524) (尝试 1/3), 响应预览: <html 7764 chars>: <!DOCTYPE  ...
2025-09-10 22:54:51,310 - Digital - [neural_trigger_system._execute_trigger] - ℹ️  🔥 执行触发器: low_load_threshold - advanced_neural_learning
2025-09-10 22:55:52,465 - Digital - [neural_trigger_system._execute_trigger] - ℹ️  🔥 执行触发器: low_load_threshold - advanced_neural_learning
2025-09-10 22:56:12,864 - Digital - [datasource.news_connector.warning_status] - ⚠️  新闻API服务端超时/错误 (524) (尝试 2/3), 响应预览: <html 7764 chars>: <!DOCTYPE  ...
2025-09-10 22:56:43,956 - Digital - [adapters.database_sync_manager._analyze_sync_data] - ℹ️  分析结果: 新用户 0个, 需更新 0个
2025-09-10 22:56:45,863 - Digital - [scripts_integration_service.get_current_activity_suggestions] - ℹ️  📊 生成了 2 个EnhancedActivity建议
2025-09-10 22:56:46,502 - Digital - [advanced_neural_consciousness.save_memory] - ❌ ❌ 原子性写入失败: 'AdvancedNeuralConsciousnessSystem' object has no attribute 'data_status'
2025-09-10 22:56:46,503 - Digital - [advanced_neural_consciousness.save_memory] - ❌ ❌ 临时文件不存在: /root/yanran_digital_life/data/neural_models/consciousness_memory.json.tmp_140125757826816_60716e1e
2025-09-10 22:56:46,503 - Digital - [advanced_neural_consciousness.error_status] - ❌ 保存记忆网络数据失败: 临时文件创建失败: /root/yanran_digital_life/data/neural_models/consciousness_memory.json.tmp_140125757826816_60716e1e
2025-09-10 22:56:46,511 - Digital - [neural_consciousness.save_model] - ℹ️  神经网络模型已保存到: /root/yanran_digital_life/data/neural_models/consciousness_enhancer.pkl
2025-09-10 22:56:46,609 - Digital - [advanced_neural_consciousness.save_memory] - ❌ ❌ 原子性写入失败: 'AdvancedNeuralConsciousnessSystem' object has no attribute 'data_status'
2025-09-10 22:56:46,609 - Digital - [advanced_neural_consciousness.save_memory] - ❌ ❌ 临时文件不存在: /root/yanran_digital_life/data/neural_models/consciousness_memory.json.tmp_140125741041408_dc7d30ae
2025-09-10 22:56:46,609 - Digital - [advanced_neural_consciousness.error_status] - ❌ 保存记忆网络数据失败: 临时文件创建失败: /root/yanran_digital_life/data/neural_models/consciousness_memory.json.tmp_140125741041408_dc7d30ae
2025-09-10 22:56:46,609 - Digital - [main._save_advanced_neural_models] - ⚠️  终极神经网络记忆保存失败
2025-09-10 22:56:46,990 - Digital - [advanced_neural_consciousness.save_memory] - ❌ ❌ 原子性写入失败: 'AdvancedNeuralConsciousnessSystem' object has no attribute 'data_status'
2025-09-10 22:56:46,991 - Digital - [advanced_neural_consciousness.save_memory] - ❌ ❌ 临时文件不存在: /root/yanran_digital_life/data/neural_models/consciousness_memory.json.tmp_140125687015168_4104571a
2025-09-10 22:56:46,991 - Digital - [advanced_neural_consciousness.error_status] - ❌ 保存记忆网络数据失败: 临时文件创建失败: /root/yanran_digital_life/data/neural_models/consciousness_memory.json.tmp_140125687015168_4104571a
2025-09-10 22:56:53,616 - Digital - [neural_trigger_system._execute_trigger] - ℹ️  🔥 执行触发器: low_load_threshold - advanced_neural_learning
2025-09-10 22:57:54,757 - Digital - [neural_trigger_system._execute_trigger] - ℹ️  🔥 执行触发器: low_load_threshold - advanced_neural_learning
2025-09-10 22:58:02,236 - Digital - [datasource.news_connector.warning_status] - ⚠️  新闻API服务端超时/错误 (524) (尝试 3/3), 响应预览: <html 7764 chars>: <!DOCTYPE  ...
2025-09-10 22:58:02,236 - Digital - [LifeOrgan.世界感知器官._get_realtime_news_data] - ⚠️  🌍 实时新闻API获取失败或无数据
2025-09-10 22:58:02,237 - Digital - [datasource.akshare_connector.success] - ✅ AKShare金融数据连接器初始化成功
2025-09-10 22:58:02,451 - Digital - [datasource.akshare_connector.success] - ✅ 成功获取 10 条财经新闻
2025-09-10 22:58:02,456 - Digital - [core.evolution.evolution_engine._evaluate_evolution_results] - ℹ️  📊 进化效果中性，评分变化: -0.003
2025-09-10 22:58:02,457 - Digital - [core.evolution.evolution_engine._record_evolution_result] - ℹ️  📝 进化记录已保存: 第5298代
2025-09-10 22:58:02,457 - Digital - [core.evolution.evolution_engine._apply_intelligence_learning] - ℹ️  🧠 开始应用智能学习模块...
2025-09-10 22:58:02,458 - Digital - [core.evolution.evolution_engine.success] - ✅ 🎉 智能学习模块应用完成！
2025-09-10 22:58:02,458 - Digital - [core.evolution.evolution_engine._evolution_cycle] - ℹ️  ✅ 第 5298 代进化完成
2025-09-10 22:58:34,905 - Digital - [utilities.cache_manager.success] - ✅ 🔥 初始化缓存管理器...
2025-09-10 22:58:34,906 - Digital - [utilities.cache_manager._start_cleanup_thread] - ℹ️  🧹 缓存清理线程已启动
2025-09-10 22:58:34,906 - Digital - [utilities.cache_manager.success] - ✅ ✅ 缓存管理器初始化完成
2025-09-10 22:58:55,868 - Digital - [neural_trigger_system._execute_trigger] - ℹ️  🔥 执行触发器: low_load_threshold - advanced_neural_learning
2025-09-10 22:59:57,026 - Digital - [neural_trigger_system._execute_trigger] - ℹ️  🔥 执行触发器: low_load_threshold - advanced_neural_learning
2025-09-10 23:00:13,867 - Digital - [main.chat_message_api] - ℹ️  ====API聊天请求====: user_id=wxid_jpcc3bco3rj022, user_name=丛岗君, user_sex=1, isgroup=0, session_id = , is_Segment=0, _token=12c4521a-7270-4553-a2ac-3af10244a35b, _appid=wx_574bN70yW1q0vTSFaaSHU, message=好家伙，明天就面试啦|[捂脸][捂脸][捂脸]
2025-09-10 23:00:13,867 - Digital - [main.enhanced_process_message] - ℹ️  🚀 [req_6443987c8f50] 开始处理用户请求: user_id=wxid_jpcc3bco3rj022, thread=140125535962880
2025-09-10 23:00:13,868 - Digital - [main.enhanced_process_message] - ℹ️  📥 [req_6443987c8f50] 接收到用户请求: user_id=wxid_jpcc3bco3rj022, user_name=丛岗君
2025-09-10 23:00:13,869 - Digital - [main._validate_api_data] - ℹ️  ✅ 接收API数据: user_id=wxid_jpcc3bco3rj022, user_name=丛岗君, user_sex=1
2025-09-10 23:00:13,869 - Digital - [unified_user_manager.process_user_request] - ℹ️  🔄 [umgr_c20fc5ae] Thread-140125535962880 开始处理用户请求: user_id=wxid_jpcc3bco3rj022
2025-09-10 23:00:14,060 - Digital - [unified_user_manager._create_user] - ℹ️  ✅ 使用API传入的有效用户名: 丛岗君
2025-09-10 23:00:14,279 - Digital - [unified_user_manager.get_or_create_user] - ℹ️  创建新用户: wxid_jpcc3bco3rj022
2025-09-10 23:00:14,279 - Digital - [unified_user_manager.create_session] - ℹ️  创建会话: session_821b7ee900e14107 (用户: wxid_jpcc3bco3rj022)
2025-09-10 23:00:14,279 - Digital - [unified_user_manager.process_user_request] - ℹ️  🔗 [umgr_c20fc5ae] 创建新会话: session_821b7ee900e14107
2025-09-10 23:00:14,279 - Digital - [unified_user_manager.success] - ✅ ✅ [umgr_c20fc5ae] 用户请求处理完成: wxid_jpcc3bco3rj022 (丛岗君)
2025-09-10 23:00:14,279 - Digital - [main.enhanced_process_message] - ℹ️  ✅ 使用API传入的用户名: 丛岗君
2025-09-10 23:00:14,280 - Digital - [main.enhanced_process_message] - ℹ️  ✅ 确定的最终用户名: 丛岗君
2025-09-10 23:00:14,280 - Digital - [main._verify_saved_data] - ℹ️  ✅ contacts.json验证通过: wxid_jpcc3bco3rj022 -> 丛岗君
2025-09-10 23:00:14,280 - Digital - [main._verify_saved_data] - ℹ️  ✅ unified_user_manager验证通过: wxid_jpcc3bco3rj022 -> 丛岗君
2025-09-10 23:00:14,280 - Digital - [main._verify_saved_data] - ℹ️  ✅ 数据保存验证通过: wxid_jpcc3bco3rj022 -> 丛岗君
2025-09-10 23:00:14,280 - Digital - [main.success] - ✅ 统一用户管理: wxid_jpcc3bco3rj022 (丛岗君) - 现有用户, 新会话
2025-09-10 23:00:14,280 - Digital - [main.enhanced_process_message] - ℹ️  收到用户消息: 好家伙，明天就面试啦|[捂脸][捂脸][捂脸]... [用户:wxid_jpcc3bco3rj022/丛岗君]
2025-09-10 23:00:14,280 - Digital - [main.enhanced_process_message] - ℹ️  🔗 使用传入的session_id: session_821b7ee900e14107
2025-09-10 23:00:14,289 - Digital - [UnifiedDataFlowManager.__init__] - ℹ️  统一数据流管理器初始化完成
2025-09-10 23:00:14,290 - Digital - [UnifiedDataFlowManager._monitor_data_flow_once] - ℹ️  数据流监控: 活跃0, 成功0, 失败0
2025-09-10 23:00:14,292 - Digital - [singleton_manager.register] - ℹ️  ✅ 已注册实例: 'unified_data_flow_manager'
2025-09-10 23:00:14,292 - Digital - [cognitive_modules.decision.yanran_response_decision.success] - ✅ 初始化林嫣然智能响应决策系统...
2025-09-10 23:00:14,292 - Digital - [cognitive_modules.decision.yanran_response_decision.success] - ✅ 林嫣然智能响应决策系统初始化完成
2025-09-10 23:00:14,293 - Digital - [cognitive_modules.decision.yanran_response_decision.handle_delayed_response_decision] - ℹ️  🔍 开始处理延迟回复决策 (用户: wxid_jpcc3bco3rj022)
2025-09-10 23:00:14,792 - Digital - [ContactsManager._load_contacts] - ℹ️  加载联系人数据: 22个用户
2025-09-10 23:00:15,090 - Digital - [adapters.database_sync_manager._analyze_sync_data] - ℹ️  分析结果: 新用户 0个, 需更新 0个
2025-09-10 23:00:16,091 - Digital - [feedback_learning._load_weights] - ℹ️  从 data/feedback_learning/weights.json 加载权重
2025-09-10 23:00:16,091 - Digital - [feedback_learning.success] - ✅ 反馈学习模块初始化完成
2025-09-10 23:00:16,093 - Digital - [feedback_learning.get_instance] - ℹ️  ✅ 反馈学习模块已注册到singleton_manager
2025-09-10 23:00:16,093 - Digital - [cognitive_modules.decision.yanran_response_decision.should_respond_to_message] - ℹ️  ✅ 完成响应决策 (用户: wxid_jpcc3bco3rj022): ResponseDecision.REPLY_NOW
2025-09-10 23:00:16,094 - Digital - [main.enhanced_process_message] - ℹ️  ⚙️ 开始初始化器官系统...
2025-09-10 23:00:16,094 - Digital - [organ_system_manager.initialize_organ_system] - ℹ️  ⚙️ 开始初始化器官系统...
2025-09-10 23:00:16,094 - Digital - [organ_system_manager._register_organ_sync] - ℹ️  ⚙️ 注册器官: safety_protection_organ
2025-09-10 23:00:16,095 - Digital - [organ_system_manager._register_organ_sync] - ℹ️  ⚙️ 器官 safety_protection_organ 激活成功 (优先级: CRITICAL)
2025-09-10 23:00:16,095 - Digital - [organ_system_manager._register_organ_sync] - ℹ️  ⚙️ 注册器官: world_perception_organ
2025-09-10 23:00:16,095 - Digital - [organ_system_manager._register_organ_sync] - ℹ️  ⚙️ 器官 world_perception_organ 激活成功 (优先级: HIGH)
2025-09-10 23:00:16,095 - Digital - [organ_system_manager._register_organ_sync] - ℹ️  ⚙️ 注册器官: proactive_expression_organ
2025-09-10 23:00:16,095 - Digital - [ProactiveExpressionOrgan.activate] - ℹ️  💬 🚀 主动表达器官正在激活...
2025-09-10 23:00:16,095 - Digital - [ProactiveExpressionOrgan.activate] - ℹ️  💬 ✅ 主动表达器官已激活，监控系统运行中
2025-09-10 23:00:16,095 - Digital - [organ_system_manager._register_organ_sync] - ℹ️  ⚙️ 器官 proactive_expression_organ 激活成功
2025-09-10 23:00:16,095 - Digital - [organ_system_manager._register_organ_sync] - ℹ️  ⚙️ 器官 proactive_expression_organ 激活成功 (优先级: HIGH)
2025-09-10 23:00:16,095 - Digital - [organ_system_manager._register_organ_sync] - ℹ️  ⚙️ 注册器官: creative_expression_organ
2025-09-10 23:00:16,095 - Digital - [organ_system_manager._register_organ_sync] - ℹ️  ⚙️ 器官 creative_expression_organ 激活成功 (优先级: NORMAL)
2025-09-10 23:00:16,095 - Digital - [organ_system_manager._register_organ_sync] - ℹ️  ⚙️ 注册器官: wealth_management_organ
2025-09-10 23:00:16,095 - Digital - [organ_system_manager._register_organ_sync] - ℹ️  ⚙️ 器官 wealth_management_organ 激活成功 (优先级: LOW)
2025-09-10 23:00:16,095 - Digital - [organ_system_manager._register_organ_sync] - ℹ️  ⚙️ 注册器官: data_perception_organ
2025-09-10 23:00:16,095 - Digital - [organ_system_manager._register_organ_sync] - ℹ️  ⚙️ 器官 data_perception_organ 激活成功 (优先级: LOW)
2025-09-10 23:00:16,095 - Digital - [organ_system_manager._start_coordination_loop_sync] - ℹ️  ⚙️ 器官协调循环已在现有事件循环中启动
2025-09-10 23:00:16,095 - Digital - [organ_system_manager.initialize_organ_system] - ℹ️  ⚙️ 器官系统初始化完成 - 注册了 6 个器官
2025-09-10 23:00:16,095 - Digital - [organ_system_manager.coordination_loop] - ℹ️  ⚙️ 器官协调循环开始运行
2025-09-10 23:00:16,096 - Digital - [main.enhanced_process_message] - ℹ️  ⚙️ 器官系统初始化成功
2025-09-10 23:00:16,096 - Digital - [main.enhanced_process_message] - ℹ️  ⚙️ 器官系统状态: active - 活跃器官: 6
2025-09-10 23:00:16,102 - Digital - [main.enhanced_process_message] - ℹ️  🛡️ 开始安全防护检查...
2025-09-10 23:00:16,104 - Digital - [ai_safety_filter._init_ai_service] - ℹ️  🛡️ 通过AI服务适配器获取AI服务成功
2025-09-10 23:00:16,104 - Digital - [ai_safety_filter._init_ai_service] - ℹ️  🛡️ AI服务连接成功
2025-09-10 23:00:16,104 - Digital - [ai_safety_filter.__init__] - ℹ️  🛡️ AI安全过滤器初始化完成
2025-09-10 23:00:16,104 - Digital - [ai_safety_filter.filter_input] - ℹ️  🛡️ 开始安全过滤用户输入: 好家伙，明天就面试啦|[捂脸][捂脸][捂脸]...
2025-09-10 23:00:18,357 - Digital - [ai_safety_filter.filter_input] - ℹ️  🛡️ 安全过滤完成 - 风险等级: safe, 安全: True
2025-09-10 23:00:18,358 - Digital - [main.enhanced_process_message] - ℹ️  🛡️ 安全检查完成 - 风险等级: safe, 安全: True
2025-09-10 23:00:18,359 - Digital - [cognitive_modules.memory.semantic_memory.search_by_content] - ℹ️  使用文本搜索: 好家伙，明天就面试啦|[捂脸][捂脸][捂脸]
2025-09-10 23:00:18,360 - Digital - [digital_life.process_input] - ℹ️  🔄 数字生命体处理输入: 好家伙，明天就面试啦|[捂脸][捂脸][捂脸]...
2025-09-10 23:00:18,364 - Digital - [neural_data_flow_monitor.__init__] - ℹ️  🔥 神经网络数据流监控器初始化完成
2025-09-10 23:00:18,367 - Digital - [cognitive_modules.memory.semantic_memory.search_by_content] - ℹ️  找到 0 个记忆文件
2025-09-10 23:00:18,465 - Digital - [thinking_chain.process] - ℹ️  开始思维链路处理: session_821b7ee900e14107
2025-09-10 23:00:18,559 - Digital - [thinking_chain._execute_steps_async] - ℹ️  开始执行步骤[perception]: 感知处理
2025-09-10 23:00:18,560 - Digital - [intent_recognition.process] - ℹ️  开始意图识别: 好家伙，明天就面试啦|[捂脸][捂脸][捂脸]...
2025-09-10 23:00:18,560 - Digital - [intent_recognition.analyze_intent] - ℹ️  🧠 开始两层意图识别: 好家伙，明天就面试啦|[捂脸][捂脸][捂脸]...
2025-09-10 23:00:18,560 - Digital - [intent_recognition.analyze_intent] - ℹ️  🔍 第一层：关键词匹配识别
2025-09-10 23:00:18,561 - Digital - [intent_recognition.analyze_intent] - ℹ️  🤖 第一层未匹配，启动第二层：AI语义分析
2025-09-10 23:00:18,561 - Digital - [cognitive_modules.perception.intention_system.success] - ✅ AI服务适配器获取完成
2025-09-10 23:00:18,561 - Digital - [cognitive_modules.perception.intention_system.success] - ✅ 意图分析系统初始化完成
2025-09-10 23:00:21,325 - Digital - [intent_recognition.success] - ✅ ✅ 第二层AI分析成功 - 意图: 交互或对话
2025-09-10 23:00:21,325 - Digital - [intent_recognition._finalize_intent_result] - ℹ️  🎯 意图识别完成: chat - 交互或对话 (置信度: 0.90) [ai_semantic_analysis]
2025-09-10 23:00:21,326 - Digital - [intent_context_manager.success] - ✅ Redis连接成功 (数据库 1)
2025-09-10 23:00:21,326 - Digital - [intent_context_manager.success] - ✅ 意图上下文管理器初始化完成
2025-09-10 23:00:21,327 - Digital - [intent_context_manager.add_intent_context] - ℹ️  添加意图上下文: 用户=wxid_jpcc3bco3rj022, 意图=交互或对话
2025-09-10 23:00:21,328 - Digital - [chat_skill.execute] - ⚠️  💬 [chat_7c5800a7] 聊天技能收到空输入，拒绝处理
2025-09-10 23:00:21,328 - Digital - [intent_recognition.success] - ✅ 意图识别完成: chat - 交互或对话 (置信度: 0.90)
2025-09-10 23:00:21,328 - Digital - [thinking_chain.success] - ✅ 步骤[perception]执行完成
2025-09-10 23:00:21,329 - Digital - [thinking_chain._execute_steps_async] - ℹ️  开始执行步骤[initial_decision]: 初步决策
2025-09-10 23:00:21,333 - Digital - [thinking_chain.success] - ✅ 步骤[initial_decision]执行完成
2025-09-10 23:00:21,333 - Digital - [thinking_chain._execute_steps_async] - ℹ️  开始执行步骤[skill_execution]: 技能执行
🔥 检测到生产环境，自动设置日志级别为INFO
2025-09-10 23:00:21,350 - Digital - [adapters.enhanced_context_builder.success] - ✅ ✅ 增强上下文构建器2.0初始化成功 (延迟加载模式)
2025-09-10 23:00:21,350 - Digital - [adapters.enhanced_context_builder.build_context_from_user_input] - ℹ️  🚀 为用户 wxid_jpcc3bco3rj022 构建增强动态上下文...
2025-09-10 23:00:23,120 - Digital - [adapters.legacy.get_related_history] - ℹ️  从数据库获取到历史记录: 用户 5 条, 助手 5 条
2025-09-10 23:00:23,343 - Digital - [adapters.enhanced_context_builder.build_context_from_user_input] - ℹ️  ✅ 最终确定的用户名: 丛岗君
2025-09-10 23:00:23,917 - Digital - [adapters.enhanced_context_builder.build_enhanced_dynamic_context] - ℹ️  🚀 开始构建增强动态上下文2.0...
2025-09-10 23:00:23,921 - Digital - [adapters.enhanced_context_builder.success] - ✅ ✅ 增强动态上下文2.0构建成功！
2025-09-10 23:00:23,921 - Digital - [adapters.enhanced_context_builder.build_enhanced_dynamic_context] - ℹ️  📊 组件可用性: 10/13 个组件可用
2025-09-10 23:00:27,761 - Digital - [thinking_chain.success] - ✅ 步骤[skill_execution]执行完成
2025-09-10 23:00:27,762 - Digital - [thinking_chain._execute_steps_async] - ℹ️  开始执行步骤[entity_extraction]: 实体提取
2025-09-10 23:00:27,762 - Digital - [cognitive_modules.perception.entity_extraction.process] - ℹ️  执行实体提取处理
2025-09-10 23:00:27,766 - Digital - [cognitive_modules.perception.entity_extraction.process] - ℹ️  提取实体 1 个
2025-09-10 23:00:27,766 - Digital - [thinking_chain.success] - ✅ 步骤[entity_extraction]执行完成
2025-09-10 23:00:27,766 - Digital - [thinking_chain._execute_steps_async] - ℹ️  开始执行步骤[memory_retrieval]: 记忆检索
2025-09-10 23:00:27,767 - Digital - [cognitive_modules.memory.retrieval.process] - ℹ️  执行记忆检索处理
2025-09-10 23:00:27,767 - Digital - [cognitive_modules.memory.retrieval._retrieve_memories] - ℹ️  未找到相关记忆，返回基础记忆
2025-09-10 23:00:27,767 - Digital - [cognitive_modules.memory.retrieval.process] - ℹ️  检索到 2 条相关记忆
2025-09-10 23:00:27,767 - Digital - [thinking_chain.success] - ✅ 步骤[memory_retrieval]执行完成
2025-09-10 23:00:27,767 - Digital - [thinking_chain._execute_steps_async] - ℹ️  开始执行步骤[memory_association]: 记忆关联
2025-09-10 23:00:27,767 - Digital - [cognitive_modules.memory.association.process] - ℹ️  执行记忆关联处理
2025-09-10 23:00:27,768 - Digital - [cognitive_modules.memory.association.process] - ℹ️  构建了 1 个记忆关联
2025-09-10 23:00:27,768 - Digital - [thinking_chain.success] - ✅ 步骤[memory_association]执行完成
2025-09-10 23:00:27,768 - Digital - [thinking_chain._execute_steps_async] - ℹ️  开始执行步骤[emotion_analysis]: 情感分析
2025-09-10 23:00:27,768 - Digital - [cognitive_modules.emotion.state_analysis.process] - ℹ️  执行情感状态分析处理
2025-09-10 23:00:27,768 - Digital - [cognitive_modules.emotion.state_analysis.process] - ℹ️  情感分析结果: neutral, 强度: 0.10
2025-09-10 23:00:27,769 - Digital - [thinking_chain.success] - ✅ 步骤[emotion_analysis]执行完成
2025-09-10 23:00:27,769 - Digital - [thinking_chain._execute_steps_async] - ℹ️  步骤[cognitive_reasoning]满足跳过条件，跳过执行
2025-09-10 23:00:27,769 - Digital - [thinking_chain._execute_steps_async] - ℹ️  开始执行步骤[emotion_modulation]: 情感调节
2025-09-10 23:00:27,769 - Digital - [cognitive_modules.emotion.response_modulation.process] - ℹ️  执行情感响应调节处理
2025-09-10 23:00:27,769 - Digital - [cognitive_modules.emotion.response_modulation.process] - ℹ️  没有情感状态信息，使用默认中性情感
2025-09-10 23:00:27,769 - Digital - [cognitive_modules.emotion.response_modulation.process] - ℹ️  情感调节结果: balanced_moderate
2025-09-10 23:00:27,770 - Digital - [thinking_chain.success] - ✅ 步骤[emotion_modulation]执行完成
2025-09-10 23:00:27,770 - Digital - [thinking_chain._execute_steps_async] - ℹ️  开始执行步骤[final_decision]: 最终决策
2025-09-10 23:00:27,770 - Digital - [autonomy.conscious_decision.success] - ✅ ✅ 检测到有效技能结果: chat_skill, 内容长度: 13
2025-09-10 23:00:27,770 - Digital - [autonomy.conscious_decision.success] - ✅ 聊天技能 chat_skill 已执行成功，检查结果类型
2025-09-10 23:00:27,770 - Digital - [autonomy.conscious_decision.success] - ✅ 聊天技能返回AI响应: 明天面试放轻松 你肯定行的...
2025-09-10 23:00:27,771 - Digital - [thinking_chain.success] - ✅ 步骤[final_decision]执行完成
2025-09-10 23:00:27,771 - Digital - [thinking_chain.success] - ✅ 思维链路执行成功完成
2025-09-10 23:00:27,771 - Digital - [thinking_chain._finalize_execution] - ℹ️  思维链路执行耗时: 9.41秒
2025-09-10 23:00:27,771 - Digital - [thinking_chain.success] - ✅ 思维链路处理完成: session_821b7ee900e14107
2025-09-10 23:00:27,771 - Digital - [digital_life.success] - ✅ ✅ 数字生命体处理完成
2025-09-10 23:00:27,771 - Digital - [main.enhanced_process_message] - ℹ️  💬 使用process_input方法处理完成
2025-09-10 23:00:27,772 - Digital - [main.success] - ✅ 🎯 [req_6443987c8f50] 请求处理完成统计:
2025-09-10 23:00:27,772 - Digital - [main.success] - ✅    - 用户: wxid_jpcc3bco3rj022 (丛岗君)
2025-09-10 23:00:27,772 - Digital - [main.success] - ✅    - 线程: 140125535962880
2025-09-10 23:00:27,772 - Digital - [main.success] - ✅    - 处理时间: 9.41秒
2025-09-10 23:00:27,772 - Digital - [main.success] - ✅    - 响应长度: 13字符
2025-09-10 23:00:27,772 - Digital - [main.success] - ✅    - 会话: session_821b7ee900e14107
2025-09-10 23:00:27,772 - Digital - [main.chat_message_api] - ℹ️  API聊天响应: 明天面试放轻松 你肯定行的...
2025-09-10 23:00:27,773 - Digital - [werkzeug._log] - ℹ️  127.0.0.1 - - [10/Sep/2025 23:00:27] "POST /api/chat/message HTTP/1.1" 200 -
2025-09-10 23:00:58,165 - Digital - [neural_trigger_system._execute_trigger] - ℹ️  🔥 执行触发器: low_load_threshold - advanced_neural_learning
2025-09-10 23:01:03,460 - Digital - [core.evolution.evolution_engine._evolution_cycle] - ℹ️  🔄 开始第 5299 代进化
2025-09-10 23:01:24,037 - Digital - [core.evolution.evolution_engine._ai_analyze_needs] - ⚠️  AI返回内容中未找到有效的JSON格式
2025-09-10 23:01:24,038 - Digital - [core.evolution.evolution_engine._analyze_evolution_needs] - ℹ️  📊 进化需求分析: {'performance_improvement': 0.007000000000000006, 'satisfaction_improvement': 0, 'stability_improvement': 0}
2025-09-10 23:01:29,893 - Digital - [main.chat_message_api] - ℹ️  ====API聊天请求====: user_id=wxid_jpcc3bco3rj022, user_name=丛岗君, user_sex=1, isgroup=0, session_id = , is_Segment=0, _token=12c4521a-7270-4553-a2ac-3af10244a35b, _appid=wx_574bN70yW1q0vTSFaaSHU, message=我相信是的
2025-09-10 23:01:29,893 - Digital - [main.enhanced_process_message] - ℹ️  🚀 [req_60758984c8cc] 开始处理用户请求: user_id=wxid_jpcc3bco3rj022, thread=140125527570176
2025-09-10 23:01:29,893 - Digital - [main.enhanced_process_message] - ℹ️  📥 [req_60758984c8cc] 接收到用户请求: user_id=wxid_jpcc3bco3rj022, user_name=丛岗君
2025-09-10 23:01:29,893 - Digital - [main._validate_api_data] - ℹ️  ✅ 接收API数据: user_id=wxid_jpcc3bco3rj022, user_name=丛岗君, user_sex=1
2025-09-10 23:01:29,893 - Digital - [unified_user_manager.process_user_request] - ℹ️  🔄 [umgr_df188245] Thread-140125527570176 开始处理用户请求: user_id=wxid_jpcc3bco3rj022
2025-09-10 23:01:29,894 - Digital - [unified_user_manager.create_session] - ℹ️  创建会话: session_21ca49ab8f88416f (用户: wxid_jpcc3bco3rj022)
2025-09-10 23:01:29,894 - Digital - [unified_user_manager.process_user_request] - ℹ️  🔗 [umgr_df188245] 创建新会话: session_21ca49ab8f88416f
2025-09-10 23:01:29,894 - Digital - [unified_user_manager.success] - ✅ ✅ [umgr_df188245] 用户请求处理完成: wxid_jpcc3bco3rj022 (丛岗君)
2025-09-10 23:01:29,894 - Digital - [main.enhanced_process_message] - ℹ️  ✅ 使用API传入的用户名: 丛岗君
2025-09-10 23:01:29,894 - Digital - [main.enhanced_process_message] - ℹ️  ✅ 确定的最终用户名: 丛岗君
2025-09-10 23:01:29,894 - Digital - [main._verify_saved_data] - ℹ️  ✅ contacts.json验证通过: wxid_jpcc3bco3rj022 -> 丛岗君
2025-09-10 23:01:29,894 - Digital - [main._verify_saved_data] - ℹ️  ✅ unified_user_manager验证通过: wxid_jpcc3bco3rj022 -> 丛岗君
2025-09-10 23:01:29,894 - Digital - [main._verify_saved_data] - ℹ️  ✅ 数据保存验证通过: wxid_jpcc3bco3rj022 -> 丛岗君
2025-09-10 23:01:29,894 - Digital - [main.success] - ✅ 统一用户管理: wxid_jpcc3bco3rj022 (丛岗君) - 现有用户, 新会话
2025-09-10 23:01:29,894 - Digital - [main.enhanced_process_message] - ℹ️  收到用户消息: 我相信是的... [用户:wxid_jpcc3bco3rj022/丛岗君]
2025-09-10 23:01:29,894 - Digital - [main.enhanced_process_message] - ℹ️  🔗 使用传入的session_id: session_21ca49ab8f88416f
2025-09-10 23:01:29,895 - Digital - [cognitive_modules.decision.yanran_response_decision.handle_delayed_response_decision] - ℹ️  🔍 开始处理延迟回复决策 (用户: wxid_jpcc3bco3rj022)
2025-09-10 23:01:31,419 - Digital - [cognitive_modules.decision.yanran_response_decision.should_respond_to_message] - ℹ️  ✅ 完成响应决策 (用户: wxid_jpcc3bco3rj022): ResponseDecision.REPLY_NOW
2025-09-10 23:01:31,420 - Digital - [main.enhanced_process_message] - ℹ️  ⚙️ 器官系统状态: active - 活跃器官: 6
2025-09-10 23:01:31,420 - Digital - [main.enhanced_process_message] - ℹ️  🛡️ 开始安全防护检查...
2025-09-10 23:01:31,421 - Digital - [ai_safety_filter.filter_input] - ℹ️  🛡️ 开始安全过滤用户输入: 我相信是的...
2025-09-10 23:01:32,937 - Digital - [ai_safety_filter.filter_input] - ℹ️  🛡️ 安全过滤完成 - 风险等级: safe, 安全: True
2025-09-10 23:01:32,938 - Digital - [main.enhanced_process_message] - ℹ️  🛡️ 安全检查完成 - 风险等级: safe, 安全: True
2025-09-10 23:01:32,940 - Digital - [digital_life.process_input] - ℹ️  🔄 数字生命体处理输入: 我相信是的...
2025-09-10 23:01:32,940 - Digital - [cognitive_modules.memory.semantic_memory.search_by_content] - ℹ️  使用文本搜索: 我相信是的
2025-09-10 23:01:32,942 - Digital - [cognitive_modules.memory.semantic_memory.search_by_content] - ℹ️  找到 0 个记忆文件
2025-09-10 23:01:33,034 - Digital - [thinking_chain.process] - ℹ️  开始思维链路处理: session_21ca49ab8f88416f
2025-09-10 23:01:33,128 - Digital - [thinking_chain._execute_steps_async] - ℹ️  开始执行步骤[perception]: 感知处理
2025-09-10 23:01:33,130 - Digital - [intent_recognition.process] - ℹ️  开始意图识别: 我相信是的...
2025-09-10 23:01:33,130 - Digital - [intent_recognition.analyze_intent] - ℹ️  🧠 开始两层意图识别: 我相信是的...
2025-09-10 23:01:33,130 - Digital - [intent_recognition.analyze_intent] - ℹ️  🔍 第一层：关键词匹配识别
2025-09-10 23:01:33,130 - Digital - [intent_recognition.analyze_intent] - ℹ️  🤖 第一层未匹配，启动第二层：AI语义分析
2025-09-10 23:01:35,800 - Digital - [intent_recognition.success] - ✅ ✅ 第二层AI分析成功 - 意图: 交互或对话
2025-09-10 23:01:35,800 - Digital - [intent_recognition._finalize_intent_result] - ℹ️  🎯 意图识别完成: chat - 交互或对话 (置信度: 0.80) [ai_semantic_analysis]
2025-09-10 23:01:35,801 - Digital - [intent_context_manager.add_intent_context] - ℹ️  添加意图上下文: 用户=wxid_jpcc3bco3rj022, 意图=交互或对话
2025-09-10 23:01:35,801 - Digital - [chat_skill.execute] - ⚠️  💬 [chat_b302d300] 聊天技能收到空输入，拒绝处理
2025-09-10 23:01:35,801 - Digital - [intent_recognition.success] - ✅ 意图识别完成: chat - 交互或对话 (置信度: 0.80)
2025-09-10 23:01:35,802 - Digital - [thinking_chain.success] - ✅ 步骤[perception]执行完成
2025-09-10 23:01:35,802 - Digital - [thinking_chain._execute_steps_async] - ℹ️  开始执行步骤[initial_decision]: 初步决策
2025-09-10 23:01:35,802 - Digital - [thinking_chain.success] - ✅ 步骤[initial_decision]执行完成
2025-09-10 23:01:35,802 - Digital - [thinking_chain._execute_steps_async] - ℹ️  开始执行步骤[skill_execution]: 技能执行
2025-09-10 23:01:35,803 - Digital - [adapters.enhanced_context_builder.build_context_from_user_input] - ℹ️  🚀 为用户 wxid_jpcc3bco3rj022 构建增强动态上下文...
2025-09-10 23:01:37,304 - Digital - [adapters.legacy.get_related_history] - ℹ️  从数据库获取到历史记录: 用户 5 条, 助手 5 条
2025-09-10 23:01:37,512 - Digital - [adapters.enhanced_context_builder.build_context_from_user_input] - ℹ️  ✅ 最终确定的用户名: 丛岗君
2025-09-10 23:01:38,091 - Digital - [adapters.enhanced_context_builder.build_enhanced_dynamic_context] - ℹ️  🚀 开始构建增强动态上下文2.0...
2025-09-10 23:01:38,093 - Digital - [adapters.enhanced_context_builder.success] - ✅ ✅ 增强动态上下文2.0构建成功！
2025-09-10 23:01:38,093 - Digital - [adapters.enhanced_context_builder.build_enhanced_dynamic_context] - ℹ️  📊 组件可用性: 10/13 个组件可用
2025-09-10 23:01:41,921 - Digital - [thinking_chain.success] - ✅ 步骤[skill_execution]执行完成
2025-09-10 23:01:41,921 - Digital - [thinking_chain._execute_steps_async] - ℹ️  开始执行步骤[entity_extraction]: 实体提取
2025-09-10 23:01:41,922 - Digital - [cognitive_modules.perception.entity_extraction.process] - ℹ️  执行实体提取处理
2025-09-10 23:01:41,922 - Digital - [cognitive_modules.perception.entity_extraction.process] - ℹ️  提取实体 1 个
2025-09-10 23:01:41,922 - Digital - [thinking_chain.success] - ✅ 步骤[entity_extraction]执行完成
2025-09-10 23:01:41,923 - Digital - [thinking_chain._execute_steps_async] - ℹ️  开始执行步骤[memory_retrieval]: 记忆检索
2025-09-10 23:01:41,923 - Digital - [cognitive_modules.memory.retrieval.process] - ℹ️  执行记忆检索处理
2025-09-10 23:01:41,923 - Digital - [cognitive_modules.memory.retrieval._retrieve_memories] - ℹ️  未找到相关记忆，返回基础记忆
2025-09-10 23:01:41,923 - Digital - [cognitive_modules.memory.retrieval.process] - ℹ️  检索到 2 条相关记忆
2025-09-10 23:01:41,923 - Digital - [thinking_chain.success] - ✅ 步骤[memory_retrieval]执行完成
2025-09-10 23:01:41,923 - Digital - [thinking_chain._execute_steps_async] - ℹ️  开始执行步骤[memory_association]: 记忆关联
2025-09-10 23:01:41,924 - Digital - [cognitive_modules.memory.association.process] - ℹ️  执行记忆关联处理
2025-09-10 23:01:41,924 - Digital - [cognitive_modules.memory.association.process] - ℹ️  构建了 1 个记忆关联
2025-09-10 23:01:41,924 - Digital - [thinking_chain.success] - ✅ 步骤[memory_association]执行完成
2025-09-10 23:01:41,924 - Digital - [thinking_chain._execute_steps_async] - ℹ️  开始执行步骤[emotion_analysis]: 情感分析
2025-09-10 23:01:41,924 - Digital - [cognitive_modules.emotion.state_analysis.process] - ℹ️  执行情感状态分析处理
2025-09-10 23:01:41,925 - Digital - [cognitive_modules.emotion.state_analysis.process] - ℹ️  情感分析结果: neutral, 强度: 0.10
2025-09-10 23:01:41,925 - Digital - [thinking_chain.success] - ✅ 步骤[emotion_analysis]执行完成
2025-09-10 23:01:41,925 - Digital - [thinking_chain._execute_steps_async] - ℹ️  步骤[cognitive_reasoning]满足跳过条件，跳过执行
2025-09-10 23:01:41,925 - Digital - [thinking_chain._execute_steps_async] - ℹ️  开始执行步骤[emotion_modulation]: 情感调节
2025-09-10 23:01:41,925 - Digital - [cognitive_modules.emotion.response_modulation.process] - ℹ️  执行情感响应调节处理
2025-09-10 23:01:41,925 - Digital - [cognitive_modules.emotion.response_modulation.process] - ℹ️  没有情感状态信息，使用默认中性情感
2025-09-10 23:01:41,925 - Digital - [cognitive_modules.emotion.response_modulation.process] - ℹ️  情感调节结果: balanced_moderate
2025-09-10 23:01:41,926 - Digital - [thinking_chain.success] - ✅ 步骤[emotion_modulation]执行完成
2025-09-10 23:01:41,926 - Digital - [thinking_chain._execute_steps_async] - ℹ️  开始执行步骤[final_decision]: 最终决策
2025-09-10 23:01:41,926 - Digital - [autonomy.conscious_decision.success] - ✅ ✅ 检测到有效技能结果: chat_skill, 内容长度: 7
2025-09-10 23:01:41,926 - Digital - [autonomy.conscious_decision.success] - ✅ 聊天技能 chat_skill 已执行成功，检查结果类型
2025-09-10 23:01:41,926 - Digital - [autonomy.conscious_decision.success] - ✅ 聊天技能返回AI响应: 这就对啦 稳的...
2025-09-10 23:01:41,926 - Digital - [thinking_chain.success] - ✅ 步骤[final_decision]执行完成
2025-09-10 23:01:41,927 - Digital - [thinking_chain.success] - ✅ 思维链路执行成功完成
2025-09-10 23:01:41,927 - Digital - [thinking_chain._finalize_execution] - ℹ️  思维链路执行耗时: 8.99秒
2025-09-10 23:01:41,927 - Digital - [thinking_chain.success] - ✅ 思维链路处理完成: session_21ca49ab8f88416f
2025-09-10 23:01:41,927 - Digital - [digital_life.success] - ✅ ✅ 数字生命体处理完成
2025-09-10 23:01:41,927 - Digital - [main.enhanced_process_message] - ℹ️  💬 使用process_input方法处理完成
2025-09-10 23:01:41,927 - Digital - [main.success] - ✅ 🎯 [req_60758984c8cc] 请求处理完成统计:
2025-09-10 23:01:41,928 - Digital - [main.success] - ✅    - 用户: wxid_jpcc3bco3rj022 (丛岗君)
2025-09-10 23:01:41,928 - Digital - [main.success] - ✅    - 线程: 140125527570176
2025-09-10 23:01:41,928 - Digital - [main.success] - ✅    - 处理时间: 8.99秒
2025-09-10 23:01:41,928 - Digital - [main.success] - ✅    - 响应长度: 7字符
2025-09-10 23:01:41,928 - Digital - [main.success] - ✅    - 会话: session_21ca49ab8f88416f
2025-09-10 23:01:41,928 - Digital - [main.chat_message_api] - ℹ️  API聊天响应: 这就对啦 稳的...
2025-09-10 23:01:41,928 - Digital - [werkzeug._log] - ℹ️  127.0.0.1 - - [10/Sep/2025 23:01:41] "POST /api/chat/message HTTP/1.1" 200 -
2025-09-10 23:01:44,336 - Digital - [adapters.database_sync_manager._analyze_sync_data] - ℹ️  分析结果: 新用户 0个, 需更新 0个
2025-09-10 23:01:45,965 - Digital - [scripts_integration_service.get_current_activity_suggestions] - ℹ️  📊 生成了 2 个EnhancedActivity建议
2025-09-10 23:01:46,711 - Digital - [advanced_neural_consciousness.save_memory] - ❌ ❌ 原子性写入失败: 'AdvancedNeuralConsciousnessSystem' object has no attribute 'data_status'
2025-09-10 23:01:46,712 - Digital - [advanced_neural_consciousness.save_memory] - ❌ ❌ 临时文件不存在: /root/yanran_digital_life/data/neural_models/consciousness_memory.json.tmp_140125757826816_fe4e272e
2025-09-10 23:01:46,712 - Digital - [advanced_neural_consciousness.error_status] - ❌ 保存记忆网络数据失败: 临时文件创建失败: /root/yanran_digital_life/data/neural_models/consciousness_memory.json.tmp_140125757826816_fe4e272e
2025-09-10 23:01:46,712 - Digital - [neural_consciousness.save_model] - ℹ️  神经网络模型已保存到: /root/yanran_digital_life/data/neural_models/consciousness_enhancer.pkl
2025-09-10 23:01:46,824 - Digital - [advanced_neural_consciousness.save_memory] - ❌ ❌ 原子性写入失败: 'AdvancedNeuralConsciousnessSystem' object has no attribute 'data_status'
2025-09-10 23:01:46,824 - Digital - [advanced_neural_consciousness.save_memory] - ❌ ❌ 临时文件不存在: /root/yanran_digital_life/data/neural_models/consciousness_memory.json.tmp_140125741041408_140e2f39
2025-09-10 23:01:46,824 - Digital - [advanced_neural_consciousness.error_status] - ❌ 保存记忆网络数据失败: 临时文件创建失败: /root/yanran_digital_life/data/neural_models/consciousness_memory.json.tmp_140125741041408_140e2f39
2025-09-10 23:01:46,824 - Digital - [main._save_advanced_neural_models] - ⚠️  终极神经网络记忆保存失败
2025-09-10 23:01:47,196 - Digital - [advanced_neural_consciousness.save_memory] - ❌ ❌ 原子性写入失败: 'AdvancedNeuralConsciousnessSystem' object has no attribute 'data_status'
2025-09-10 23:01:47,196 - Digital - [advanced_neural_consciousness.save_memory] - ❌ ❌ 临时文件不存在: /root/yanran_digital_life/data/neural_models/consciousness_memory.json.tmp_140125687015168_14da883e
2025-09-10 23:01:47,197 - Digital - [advanced_neural_consciousness.error_status] - ❌ 保存记忆网络数据失败: 临时文件创建失败: /root/yanran_digital_life/data/neural_models/consciousness_memory.json.tmp_140125687015168_14da883e
2025-09-10 23:01:59,317 - Digital - [neural_trigger_system._execute_trigger] - ℹ️  🔥 执行触发器: low_load_threshold - advanced_neural_learning
2025-09-10 23:03:00,469 - Digital - [neural_trigger_system._execute_trigger] - ℹ️  🔥 执行触发器: low_load_threshold - advanced_neural_learning
2025-09-10 23:03:24,039 - Digital - [core.evolution.evolution_engine._evaluate_evolution_results] - ℹ️  🎉 进化成功！综合评分提升: 0.002
2025-09-10 23:03:24,039 - Digital - [core.evolution.evolution_engine._record_evolution_result] - ℹ️  📝 进化记录已保存: 第5299代
2025-09-10 23:03:24,040 - Digital - [core.evolution.evolution_engine._apply_intelligence_learning] - ℹ️  🧠 开始应用智能学习模块...
2025-09-10 23:03:24,040 - Digital - [core.evolution.evolution_engine.success] - ✅ 🎉 智能学习模块应用完成！
2025-09-10 23:03:24,040 - Digital - [core.evolution.evolution_engine._evolution_cycle] - ℹ️  ✅ 第 5299 代进化完成
2025-09-10 23:03:36,136 - Digital - [utilities.cache_manager.success] - ✅ 🔥 初始化缓存管理器...
2025-09-10 23:03:36,137 - Digital - [utilities.cache_manager._start_cleanup_thread] - ℹ️  🧹 缓存清理线程已启动
2025-09-10 23:03:36,137 - Digital - [utilities.cache_manager.success] - ✅ ✅ 缓存管理器初始化完成
2025-09-10 23:04:01,609 - Digital - [neural_trigger_system._execute_trigger] - ℹ️  🔥 执行触发器: low_load_threshold - advanced_neural_learning
2025-09-10 23:04:18,137 - Digital - [main.chat_message_api] - ℹ️  ====API聊天请求====: user_id=wxid_jpcc3bco3rj022, user_name=丛岗君, user_sex=1, isgroup=0, session_id = , is_Segment=0, _token=12c4521a-7270-4553-a2ac-3af10244a35b, _appid=wx_574bN70yW1q0vTSFaaSHU, message=也不知道深圳信银贵阳分中心的面试难不难
2025-09-10 23:04:18,138 - Digital - [main.enhanced_process_message] - ℹ️  🚀 [req_edc7257fe171] 开始处理用户请求: user_id=wxid_jpcc3bco3rj022, thread=140125527570176
2025-09-10 23:04:18,138 - Digital - [main.enhanced_process_message] - ℹ️  📥 [req_edc7257fe171] 接收到用户请求: user_id=wxid_jpcc3bco3rj022, user_name=丛岗君
2025-09-10 23:04:18,138 - Digital - [main._validate_api_data] - ℹ️  ✅ 接收API数据: user_id=wxid_jpcc3bco3rj022, user_name=丛岗君, user_sex=1
2025-09-10 23:04:18,138 - Digital - [unified_user_manager.process_user_request] - ℹ️  🔄 [umgr_c28bd878] Thread-140125527570176 开始处理用户请求: user_id=wxid_jpcc3bco3rj022
2025-09-10 23:04:18,138 - Digital - [unified_user_manager.create_session] - ℹ️  创建会话: session_d4d8e5adfea74ba8 (用户: wxid_jpcc3bco3rj022)
2025-09-10 23:04:18,138 - Digital - [unified_user_manager.process_user_request] - ℹ️  🔗 [umgr_c28bd878] 创建新会话: session_d4d8e5adfea74ba8
2025-09-10 23:04:18,138 - Digital - [unified_user_manager.success] - ✅ ✅ [umgr_c28bd878] 用户请求处理完成: wxid_jpcc3bco3rj022 (丛岗君)
2025-09-10 23:04:18,138 - Digital - [main.enhanced_process_message] - ℹ️  ✅ 使用API传入的用户名: 丛岗君
2025-09-10 23:04:18,138 - Digital - [main.enhanced_process_message] - ℹ️  ✅ 确定的最终用户名: 丛岗君
2025-09-10 23:04:18,138 - Digital - [main._verify_saved_data] - ℹ️  ✅ contacts.json验证通过: wxid_jpcc3bco3rj022 -> 丛岗君
2025-09-10 23:04:18,138 - Digital - [main._verify_saved_data] - ℹ️  ✅ unified_user_manager验证通过: wxid_jpcc3bco3rj022 -> 丛岗君
2025-09-10 23:04:18,138 - Digital - [main._verify_saved_data] - ℹ️  ✅ 数据保存验证通过: wxid_jpcc3bco3rj022 -> 丛岗君
2025-09-10 23:04:18,139 - Digital - [main.success] - ✅ 统一用户管理: wxid_jpcc3bco3rj022 (丛岗君) - 现有用户, 新会话
2025-09-10 23:04:18,139 - Digital - [main.enhanced_process_message] - ℹ️  收到用户消息: 也不知道深圳信银贵阳分中心的面试难不难... [用户:wxid_jpcc3bco3rj022/丛岗君]
2025-09-10 23:04:18,139 - Digital - [main.enhanced_process_message] - ℹ️  🔗 使用传入的session_id: session_d4d8e5adfea74ba8
2025-09-10 23:04:18,140 - Digital - [cognitive_modules.decision.yanran_response_decision.handle_delayed_response_decision] - ℹ️  🔍 开始处理延迟回复决策 (用户: wxid_jpcc3bco3rj022)
2025-09-10 23:04:19,610 - Digital - [cognitive_modules.decision.yanran_response_decision.should_respond_to_message] - ℹ️  ✅ 完成响应决策 (用户: wxid_jpcc3bco3rj022): ResponseDecision.REPLY_NOW
2025-09-10 23:04:19,610 - Digital - [main.enhanced_process_message] - ℹ️  ⚙️ 器官系统状态: active - 活跃器官: 6
2025-09-10 23:04:19,611 - Digital - [main.enhanced_process_message] - ℹ️  🛡️ 开始安全防护检查...
2025-09-10 23:04:19,611 - Digital - [ai_safety_filter.filter_input] - ℹ️  🛡️ 开始安全过滤用户输入: 也不知道深圳信银贵阳分中心的面试难不难...
2025-09-10 23:04:21,298 - Digital - [ai_safety_filter.filter_input] - ℹ️  🛡️ 安全过滤完成 - 风险等级: safe, 安全: True
2025-09-10 23:04:21,299 - Digital - [main.enhanced_process_message] - ℹ️  🛡️ 安全检查完成 - 风险等级: safe, 安全: True
2025-09-10 23:04:21,300 - Digital - [cognitive_modules.memory.semantic_memory.search_by_content] - ℹ️  使用文本搜索: 也不知道深圳信银贵阳分中心的面试难不难
2025-09-10 23:04:21,300 - Digital - [digital_life.process_input] - ℹ️  🔄 数字生命体处理输入: 也不知道深圳信银贵阳分中心的面试难不难...
2025-09-10 23:04:21,304 - Digital - [cognitive_modules.memory.semantic_memory.search_by_content] - ℹ️  找到 0 个记忆文件
2025-09-10 23:04:21,394 - Digital - [thinking_chain.process] - ℹ️  开始思维链路处理: session_d4d8e5adfea74ba8
2025-09-10 23:04:21,484 - Digital - [thinking_chain._execute_steps_async] - ℹ️  开始执行步骤[perception]: 感知处理
2025-09-10 23:04:21,487 - Digital - [intent_recognition.process] - ℹ️  开始意图识别: 也不知道深圳信银贵阳分中心的面试难不难...
2025-09-10 23:04:21,487 - Digital - [intent_recognition.analyze_intent] - ℹ️  🧠 开始两层意图识别: 也不知道深圳信银贵阳分中心的面试难不难...
2025-09-10 23:04:21,487 - Digital - [intent_recognition.analyze_intent] - ℹ️  🔍 第一层：关键词匹配识别
2025-09-10 23:04:21,487 - Digital - [intent_recognition.analyze_intent] - ℹ️  🤖 第一层未匹配，启动第二层：AI语义分析
2025-09-10 23:04:24,071 - Digital - [intent_recognition.success] - ✅ ✅ 第二层AI分析成功 - 意图: 信息查询
2025-09-10 23:04:24,071 - Digital - [intent_recognition._finalize_intent_result] - ℹ️  🎯 意图识别完成: query - 信息查询 (置信度: 0.80) [ai_semantic_analysis]
2025-09-10 23:04:24,072 - Digital - [intent_context_manager.add_intent_context] - ℹ️  添加意图上下文: 用户=wxid_jpcc3bco3rj022, 意图=信息查询
2025-09-10 23:04:24,072 - Digital - [intent_recognition.success] - ✅ 意图识别完成: query - 信息查询 (置信度: 0.80)
2025-09-10 23:04:24,072 - Digital - [thinking_chain.success] - ✅ 步骤[perception]执行完成
2025-09-10 23:04:24,073 - Digital - [thinking_chain._execute_steps_async] - ℹ️  开始执行步骤[initial_decision]: 初步决策
2025-09-10 23:04:24,073 - Digital - [autonomy.conscious_decision.success] - ✅ 🧠 自主决策：需要使用搜索+chat技能组合
2025-09-10 23:04:24,074 - Digital - [thinking_chain.success] - ✅ 步骤[initial_decision]执行完成
2025-09-10 23:04:24,074 - Digital - [thinking_chain._execute_steps_async] - ℹ️  开始执行步骤[skill_execution]: 技能执行
2025-09-10 23:04:24,899 - Digital - [search_skill._perform_search] - ℹ️  format_search: "信银贵阳分中心面试难度"
2025-09-10 23:05:02,839 - Digital - [neural_trigger_system._execute_trigger] - ℹ️  🔥 执行触发器: low_load_threshold - advanced_neural_learning
2025-09-10 23:05:27,099 - Digital - [search_skill.success] - ✅ ✅ WPSAI搜索模型调用成功
2025-09-10 23:05:27,099 - Digital - [skill_manager.success] - ✅ 🎯 搜索完成后，使用聊天技能生成AI响应
2025-09-10 23:05:27,100 - Digital - [adapters.enhanced_context_builder.build_context_from_user_input] - ℹ️  🚀 为用户 wxid_jpcc3bco3rj022 构建增强动态上下文...
2025-09-10 23:05:28,458 - Digital - [adapters.legacy.get_related_history] - ℹ️  从数据库获取到历史记录: 用户 5 条, 助手 5 条
2025-09-10 23:05:28,708 - Digital - [adapters.enhanced_context_builder.build_context_from_user_input] - ℹ️  ✅ 最终确定的用户名: 丛岗君
2025-09-10 23:05:29,261 - Digital - [adapters.enhanced_context_builder.build_enhanced_dynamic_context] - ℹ️  🚀 开始构建增强动态上下文2.0...
2025-09-10 23:05:29,264 - Digital - [adapters.enhanced_context_builder.success] - ✅ ✅ 增强动态上下文2.0构建成功！
2025-09-10 23:05:29,264 - Digital - [adapters.enhanced_context_builder.build_enhanced_dynamic_context] - ℹ️  📊 组件可用性: 10/13 个组件可用
2025-09-10 23:05:29,264 - Digital - [chat_skill._generate_ai_response] - ℹ️  ✅ 已将搜索结果集成到动态上下文中
2025-09-10 23:05:39,939 - Digital - [thinking_chain.success] - ✅ 步骤[skill_execution]执行完成
2025-09-10 23:05:39,939 - Digital - [thinking_chain._execute_steps_async] - ℹ️  开始执行步骤[entity_extraction]: 实体提取
2025-09-10 23:05:39,939 - Digital - [cognitive_modules.perception.entity_extraction.process] - ℹ️  执行实体提取处理
2025-09-10 23:05:39,939 - Digital - [cognitive_modules.perception.entity_extraction.process] - ℹ️  提取实体 1 个
2025-09-10 23:05:39,940 - Digital - [thinking_chain.success] - ✅ 步骤[entity_extraction]执行完成
2025-09-10 23:05:39,940 - Digital - [thinking_chain._execute_steps_async] - ℹ️  开始执行步骤[memory_retrieval]: 记忆检索
2025-09-10 23:05:39,940 - Digital - [cognitive_modules.memory.retrieval.process] - ℹ️  执行记忆检索处理
2025-09-10 23:05:39,940 - Digital - [cognitive_modules.memory.retrieval._retrieve_memories] - ℹ️  未找到相关记忆，返回基础记忆
2025-09-10 23:05:39,940 - Digital - [cognitive_modules.memory.retrieval.process] - ℹ️  检索到 2 条相关记忆
2025-09-10 23:05:39,941 - Digital - [thinking_chain.success] - ✅ 步骤[memory_retrieval]执行完成
2025-09-10 23:05:39,941 - Digital - [thinking_chain._execute_steps_async] - ℹ️  开始执行步骤[memory_association]: 记忆关联
2025-09-10 23:05:39,941 - Digital - [cognitive_modules.memory.association.process] - ℹ️  执行记忆关联处理
2025-09-10 23:05:39,941 - Digital - [cognitive_modules.memory.association.process] - ℹ️  构建了 1 个记忆关联
2025-09-10 23:05:39,941 - Digital - [thinking_chain.success] - ✅ 步骤[memory_association]执行完成
2025-09-10 23:05:39,941 - Digital - [thinking_chain._execute_steps_async] - ℹ️  开始执行步骤[emotion_analysis]: 情感分析
2025-09-10 23:05:39,942 - Digital - [cognitive_modules.emotion.state_analysis.process] - ℹ️  执行情感状态分析处理
2025-09-10 23:05:39,942 - Digital - [cognitive_modules.emotion.state_analysis.process] - ℹ️  情感分析结果: neutral, 强度: 0.10
2025-09-10 23:05:39,942 - Digital - [thinking_chain.success] - ✅ 步骤[emotion_analysis]执行完成
2025-09-10 23:05:39,942 - Digital - [thinking_chain._execute_steps_async] - ℹ️  步骤[cognitive_reasoning]满足跳过条件，跳过执行
2025-09-10 23:05:39,942 - Digital - [thinking_chain._execute_steps_async] - ℹ️  开始执行步骤[emotion_modulation]: 情感调节
2025-09-10 23:05:39,942 - Digital - [cognitive_modules.emotion.response_modulation.process] - ℹ️  执行情感响应调节处理
2025-09-10 23:05:39,942 - Digital - [cognitive_modules.emotion.response_modulation.process] - ℹ️  没有情感状态信息，使用默认中性情感
2025-09-10 23:05:39,942 - Digital - [cognitive_modules.emotion.response_modulation.process] - ℹ️  情感调节结果: balanced_moderate
2025-09-10 23:05:39,943 - Digital - [thinking_chain.success] - ✅ 步骤[emotion_modulation]执行完成
2025-09-10 23:05:39,943 - Digital - [thinking_chain._execute_steps_async] - ℹ️  开始执行步骤[final_decision]: 最终决策
2025-09-10 23:05:39,943 - Digital - [autonomy.conscious_decision.success] - ✅ ✅ 检测到有效技能结果: chat_skill, 内容长度: 66
2025-09-10 23:05:39,943 - Digital - [autonomy.conscious_decision.success] - ✅ 聊天技能 chat_skill 已执行成功，检查结果类型
2025-09-10 23:05:39,943 - Digital - [autonomy.conscious_decision.success] - ✅ 聊天技能返回AI响应: 中等偏上 主要卡三个点 金融基础得贴贵阳本地情况比如大数据金融试点 综合能力要会用STAR法则讲真案...
2025-09-10 23:05:39,944 - Digital - [thinking_chain.success] - ✅ 步骤[final_decision]执行完成
2025-09-10 23:05:39,944 - Digital - [thinking_chain.success] - ✅ 思维链路执行成功完成
2025-09-10 23:05:39,944 - Digital - [thinking_chain._finalize_execution] - ℹ️  思维链路执行耗时: 78.64秒
2025-09-10 23:05:39,944 - Digital - [thinking_chain.success] - ✅ 思维链路处理完成: session_d4d8e5adfea74ba8
2025-09-10 23:05:39,944 - Digital - [digital_life.success] - ✅ ✅ 数字生命体处理完成
2025-09-10 23:05:39,945 - Digital - [main.enhanced_process_message] - ℹ️  💬 使用process_input方法处理完成
2025-09-10 23:05:39,945 - Digital - [main.success] - ✅ 🎯 [req_edc7257fe171] 请求处理完成统计:
2025-09-10 23:05:39,945 - Digital - [main.success] - ✅    - 用户: wxid_jpcc3bco3rj022 (丛岗君)
2025-09-10 23:05:39,945 - Digital - [main.success] - ✅    - 线程: 140125527570176
2025-09-10 23:05:39,945 - Digital - [main.success] - ✅    - 处理时间: 78.65秒
2025-09-10 23:05:39,945 - Digital - [main.success] - ✅    - 响应长度: 66字符
2025-09-10 23:05:39,946 - Digital - [main.success] - ✅    - 会话: session_d4d8e5adfea74ba8
2025-09-10 23:05:39,946 - Digital - [main.chat_message_api] - ℹ️  API聊天响应: 中等偏上 主要卡三个点 金融基础得贴贵阳本地情况比如大数据金融试点 综合能力要会用STAR法则讲真案...
2025-09-10 23:05:39,946 - Digital - [werkzeug._log] - ℹ️  127.0.0.1 - - [10/Sep/2025 23:05:39] "POST /api/chat/message HTTP/1.1" 200 -
2025-09-10 23:06:03,993 - Digital - [neural_trigger_system._execute_trigger] - ℹ️  🔥 执行触发器: low_load_threshold - advanced_neural_learning
2025-09-10 23:06:25,042 - Digital - [core.evolution.evolution_engine._evolution_cycle] - ℹ️  🔄 开始第 5300 代进化
2025-09-10 23:06:32,908 - Digital - [core.evolution.evolution_engine._ai_analyze_needs] - ⚠️  AI返回内容中未找到有效的JSON格式
2025-09-10 23:06:32,908 - Digital - [core.evolution.evolution_engine._analyze_evolution_needs] - ℹ️  📊 进化需求分析: {'performance_improvement': 0.010000000000000009, 'satisfaction_improvement': 0, 'stability_improvement': 0}
2025-09-10 23:06:44,718 - Digital - [adapters.database_sync_manager._analyze_sync_data] - ℹ️  分析结果: 新用户 0个, 需更新 0个
2025-09-10 23:06:46,068 - Digital - [scripts_integration_service.get_current_activity_suggestions] - ℹ️  📊 生成了 1 个EnhancedActivity建议
2025-09-10 23:06:46,918 - Digital - [advanced_neural_consciousness.save_memory] - ❌ ❌ 原子性写入失败: 'AdvancedNeuralConsciousnessSystem' object has no attribute 'data_status'
2025-09-10 23:06:46,918 - Digital - [advanced_neural_consciousness.save_memory] - ❌ ❌ 临时文件不存在: /root/yanran_digital_life/data/neural_models/consciousness_memory.json.tmp_140125757826816_3008ed80
2025-09-10 23:06:46,918 - Digital - [advanced_neural_consciousness.error_status] - ❌ 保存记忆网络数据失败: 临时文件创建失败: /root/yanran_digital_life/data/neural_models/consciousness_memory.json.tmp_140125757826816_3008ed80
2025-09-10 23:06:46,926 - Digital - [neural_consciousness.save_model] - ℹ️  神经网络模型已保存到: /root/yanran_digital_life/data/neural_models/consciousness_enhancer.pkl
2025-09-10 23:06:47,045 - Digital - [advanced_neural_consciousness.save_memory] - ❌ ❌ 原子性写入失败: 'AdvancedNeuralConsciousnessSystem' object has no attribute 'data_status'
2025-09-10 23:06:47,045 - Digital - [advanced_neural_consciousness.save_memory] - ❌ ❌ 临时文件不存在: /root/yanran_digital_life/data/neural_models/consciousness_memory.json.tmp_140125741041408_7a3f6c27
2025-09-10 23:06:47,045 - Digital - [advanced_neural_consciousness.error_status] - ❌ 保存记忆网络数据失败: 临时文件创建失败: /root/yanran_digital_life/data/neural_models/consciousness_memory.json.tmp_140125741041408_7a3f6c27
2025-09-10 23:06:47,045 - Digital - [main._save_advanced_neural_models] - ⚠️  终极神经网络记忆保存失败
2025-09-10 23:06:47,399 - Digital - [advanced_neural_consciousness.save_memory] - ❌ ❌ 原子性写入失败: 'AdvancedNeuralConsciousnessSystem' object has no attribute 'data_status'
2025-09-10 23:06:47,399 - Digital - [advanced_neural_consciousness.save_memory] - ❌ ❌ 临时文件不存在: /root/yanran_digital_life/data/neural_models/consciousness_memory.json.tmp_140125687015168_cf3e1e0b
2025-09-10 23:06:47,399 - Digital - [advanced_neural_consciousness.error_status] - ❌ 保存记忆网络数据失败: 临时文件创建失败: /root/yanran_digital_life/data/neural_models/consciousness_memory.json.tmp_140125687015168_cf3e1e0b
2025-09-10 23:07:05,147 - Digital - [neural_trigger_system._execute_trigger] - ℹ️  🔥 执行触发器: low_load_threshold - advanced_neural_learning
